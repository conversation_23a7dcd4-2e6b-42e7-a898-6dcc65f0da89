/// 标准API响应模型
class ApiResponse<T> {
  /// 请求ID
  final String? id;
  
  /// 请求对象
  final String? requestObject;
  
  /// 操作类型
  final String? operation;
  
  /// 错误码（0表示成功）
  final int errorCode;
  
  /// 响应消息
  final String? message;
  
  /// 响应结果数据
  final T? result;

  ApiResponse({
    this.id,
    this.requestObject,
    this.operation,
    required this.errorCode,
    this.message,
    this.result,
  });

  /// 从JSON映射创建ApiStandardResponse
  factory ApiResponse.fromJson(Map<String, dynamic> json, T? Function(dynamic) fromJsonT) {
    return ApiResponse<T>(
      id: json['id'] as String?,
      requestObject: json['requestObject'] as String?,
      operation: json['operation'] as String?,
      errorCode: json['errorCode'] as int? ?? -1,
      message: json['message'] as String?,
      result: json['result'] != null ? fromJsonT(json['result']) : null,
    );
  }

  /// 转换为JSON映射
  Map<String, dynamic> toJson(dynamic Function(T?) toJsonT) {
    return {
      if (id != null) 'id': id,
      if (requestObject != null) 'requestObject': requestObject,
      if (operation != null) 'operation': operation,
      'errorCode': errorCode,
      if (message != null) 'message': message,
      if (result != null) 'result': toJsonT(result),
    };
  }

  /// 判断响应是否成功
  bool get isSuccess => errorCode == 0;
} 