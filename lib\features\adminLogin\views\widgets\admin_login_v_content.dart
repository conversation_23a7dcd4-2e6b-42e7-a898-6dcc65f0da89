// ignore_for_file: prefer_const_constructors

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:seasetting/seasetting.dart' hide AuthLoginType;
import '../../../../core/router/app_router.dart';
import '../../../../core/utils/window_util.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/gradient_border_container.dart';
import '../../../../shared/utils/asset_util.dart';
import '../../../../core/widgets/SeaTextInput.dart';
import '../../../auth/widget/gradientView.dart';
import '../../../changePassword/views/change_password_page.dart';
import 'admin_reader_card_login_view.dart';
import '../../view_models/admin_login_view_model.dart';

class AdminLoginVContent extends StatefulWidget {
  const AdminLoginVContent({
    Key? key,
    required this.items,
    required this.loginType,
    required this.onTypeSwitch,
    required this.onLogin,
  }) : super(key: key);

  final List<Map<String, dynamic>> items;
  final Rx<AuthLoginType> loginType;
  final Function(String) onTypeSwitch;
  final Future<bool> Function(String, {bool isNeedResume}) onLogin;

  @override
  State<AdminLoginVContent> createState() => _AdminLoginVContentState();
}

class _AdminLoginVContentState extends State<AdminLoginVContent> {
  List<String> images = ['banner1'];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(left: 40.p, right: 40.p, top: 0.p, bottom: 0.p),
      child: containerView(),
    );
  }

  Widget containerView() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 80.p),
      child: Column(
        // mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 100.p),
          Text(
            '管理员登录',
            style: TextStyle(
                fontSize: 48.p,
                fontWeight: FontWeight.w500,
                color: BPUtils.c_22,
                height: 48 / 64),
          ),
          SizedBox(height: 80.p),
          AdminAccountLoginView(
                onLogin: (password) {
                  widget.onLogin(password);
                },
              ),
          // SizedBox(height: 156.p),
          // Obx(() {
          //   if (widget.loginType.value == AuthLoginType.keyboardInput) {
          //     return AdminAccountLoginView(
          //       onLogin: (password) {
          //         widget.onLogin(password);
          //       },
          //     );
          //   } else {
          //     return AdminReaderCardLoginView();
          //   }
          // }),
          // SizedBox(height: 156.p),
          Spacer(),
          // getSeparateView(),
          // getSelectItems(),
        ],
      ),
    );
  }

  Widget getSeparateView() {
    return Padding(
      padding: EdgeInsets.only(
        bottom: 40.p,
        right: 205.p,
        left: 205.p,
      ),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 5.p,
              child: GradientView(
                shadowColor: Colors.transparent,
                colors: const [Colors.white, BPUtils.c_FF1D62FD],
                stops: const [0, 0.5],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25.p),
            child: Text(
              '登录方式',
              style: TextStyle(
                fontSize: 30.p,
                fontWeight: FontWeight.normal,
                color: BPUtils.c_FF1D62FD,
              ),
            ),
          ),
          Expanded(
            child: SizedBox(
              height: 5.p,
              child: GradientView(
                shadowColor: Colors.transparent,
                colors: const [Colors.white, BPUtils.c_FF1D62FD],
                stops: const [0, 0.5],
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getSelectItems() {
    return Padding(
      padding: EdgeInsets.only(
        left: 270.p,
        right: 270.p,
        // bottom: WindowUtill.scaleSize(128),
      ),
      child: GridView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisSpacing: 20.p,
            crossAxisCount: 2,
            childAspectRatio: 220 / 160),
        itemBuilder: (context, index) {
          Map<String, dynamic> item = widget.items[index];
          AuthLoginType loginType = item['type'] ?? AuthLoginType.readerCard;
          Color borderColor = (widget.loginType.value == loginType)
              ? BPUtils.c_FF1D62FD
              : Colors.transparent;
          return getItem(item, borderColor);
          // return Obx(() {
          //   Map<String, dynamic> item = widget.items[index];
          //   AuthLoginType loginType = item['type'] ?? AuthLoginType.readerCard;
          //   Color borderColor = (widget.loginType.value == loginType)
          //       ? BPUtils.c_FF1D62FD
          //       : Colors.transparent;
          //   return getItem(item, borderColor);
          // });
        },
        itemCount: widget.items.length,
      ),
    );
  }

  Widget getItem(Map<String, dynamic> item, Color borderColor) {
    return InkWell(
      onTap: () => widget.onTypeSwitch(item['title']),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: BPUtils.c_FFEDF0FD,
            borderRadius: BorderRadius.circular(20.p),
            border: Border.all(color: borderColor, width: 2.p)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              AssetUtil.fullPath(item['icon']),
              width: 64.p,
              height: 64.p,
              fit: BoxFit.fill,
            ),
            SizedBox(height: 10.p),
            Text(
              item['title'],
              style: TextStyle(
                fontSize: 24.p,
                fontWeight: FontWeight.w500,
                color: BPUtils.c_FF1D62FD,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 账号密码登录视图
class AdminAccountLoginView extends StatefulWidget {
  final Function(String) onLogin;

  const AdminAccountLoginView({
    Key? key,
    required this.onLogin,
  }) : super(key: key);

  @override
  State<AdminAccountLoginView> createState() => _AdminAccountLoginViewState();
}

class _AdminAccountLoginViewState extends State<AdminAccountLoginView> {
  String _password = '';
  TextEditingController ctr = TextEditingController();
  FocusNode pswFocus = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // SizedBox(height: 40.p),
        Container(
          // width: 800.p,
          // height: 70.p,
          decoration: BoxDecoration(
            // color: Colors.white,
            borderRadius: BorderRadius.circular(35.p),
          ),
          child: Row(
            children: [
              SizedBox(width: 30.p),
              Icon(
                Icons.lock,
                size: 40.p,
                color: BPUtils.c_FF1D62FD,
              ),
              SizedBox(width: 20.p),
              Expanded(
                  child: SeaTextInput(
                icon: '',
                hintText: '请输入管理员密码',
                controller: ctr,
                focusNode: pswFocus,
                needObscure: true,
              )),
            ],
          ),
        ),
        SizedBox(height: 40.p),
        Padding(
          padding:  EdgeInsets.only(right: 80.p),
          child: InkWell(onTap: (){
            AppNavigator.toChangePassword();
          },child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            Image.asset(
              AssetUtil.fullPath('editIcon'),
              width: 32.p,
              height: 32.p,
              fit: BoxFit.fill,
            ),
            Padding(
              padding: EdgeInsets.only(left: 15.p),
              child: Text(
                '修改密码',
                style: TextStyle(
                  fontSize: 30.p,
                  color: BPUtils.c_FF1D62FD,
                ),
              ),
            )
          ]),),
        ),
        SizedBox(height: 218.p),
        Padding(
          padding:  EdgeInsets.symmetric(horizontal: 80.p),
          child: CustomButton.filled(
            text: '登录',
            onTap: () {
              if (ctr.text.isNotEmpty) {
                widget.onLogin(ctr.text);
              }
            },
          ),
        ),
      ],
    );
  }
}
