import 'package:flutter/material.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';

class HandleItem extends StatelessWidget {
  final String title;
  final String? type;
  final String iconPath;
  final Map<String, dynamic>? map;
  final VoidCallback? onTap;

  const HandleItem({
    Key? key,
    required this.title,
    required this.iconPath,
    this.onTap,
    this.type, this.map,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 180.p,
        margin: EdgeInsets.symmetric(vertical: 25.p),
        padding: EdgeInsets.only(left: 32.p, right: 43.p),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment(-0.8, -0.7), // 136度角
            end: Alignment(0.8, 0.7),
            colors: [
              Color(0xB3FFFFFF), // rgba(255,255,255,0.7)
              Color(0x99DAEAFF), // rgba(218,234,255,0.6)
            ],
          ),
          borderRadius: BorderRadius.circular(48.p),
          border: Border.all(width: 2.p, color: const Color(0xFFFFFFFF)),
        ),
        child: Row(
          children: [
            Image.asset(
              iconPath,
              width: 148.p,
              height: 148.p,
              fit: BoxFit.contain,
            ),
            SizedBox(width: 40.p),
            Expanded(
              child: Row(
                children: [
                  Text(
                    title,
                    style: TextStyle(
                        fontSize: 54.p,
                        color: const Color(0xFF222222),
                        fontWeight: FontWeight.w500,
                        height: 54 / 60),
                  ),
                  if (type == 'clearBookBox')
                    RichText(
                        text: TextSpan(children: [
                      TextSpan(
                          text: ' (',
                          style: TextStyle(
                              fontSize: 54.p,
                              color: const Color(0xFF222222),
                              fontWeight: FontWeight.w500,
                              height: 54 / 60)),
                      TextSpan(
                          text: map!['bookBoxCount'].toString(),
                          style: TextStyle(
                              fontSize: 54.p,
                              color: const Color(0xFF2A88FF),
                              fontWeight: FontWeight.w500,
                              height: 54 / 60)),
                      TextSpan(
                        text: '本)',
                        style: TextStyle(
                            fontSize: 54.p,
                            color: const Color(0xFF222222),
                            fontWeight: FontWeight.w500,
                            height: 54 / 60),
                      )
                    ]))
                ],
              ),
            ),
            Image.asset(
              AssetUtil.fullPath('arrow_right.png'),
              width: 21.p,
              height: 42.p,
              fit: BoxFit.contain,
            ),
          ],
        ),
      ),
    );
  }
}
