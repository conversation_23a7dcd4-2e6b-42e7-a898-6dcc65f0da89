import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import '../../../core/database/db_manager.dart';
import '../../../core/database/interfaces/db_interface.dart';
import '../models/record_model.dart';

class QueryViewModel extends ChangeNotifier {
  // 常量定义
  static const int _maxRetries = 3;
  static const Duration _requestTimeout = Duration(seconds: 10);
  
  // 私有变量
  bool _isLoading = false;
  String _errorMessage = '';
  bool _isDisposed = false;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _endDate = DateTime.now();
  List<RecordModel> _records = [];
  DBInterface? _db;
  
  // Getters
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  DateTime get startDate => _startDate;
  DateTime get endDate => _endDate;
  List<RecordModel> get records => List.unmodifiable(_records);
  
  /// 安全执行函数：仅在组件未被销毁时执行
  void _safeRun(VoidCallback callback) {
    if (!_isDisposed) callback();
  }

  /// 安全修改状态：仅在组件未被销毁时执行并通知监听器
  void _safeUpdate(VoidCallback callback) {
    if (_isDisposed) return;
    callback();
    notifyListeners();
  }
  
  /// 显示错误信息
  void _showError(String message) {
    _safeUpdate(() {
      _errorMessage = message;
      _isLoading = false;
    });
    showToast(_errorMessage, position: ToastPosition.bottom);
  }
  
  /// 初始化ViewModel
  Future<void> init() async {
    if (_isDisposed) return;
    
    try {
      _db = await DBManager.getDBInstance();
      await fetchRecords();
    } catch (e) {
      _showError('初始化数据库失败: ${e.toString()}');
    }
  }
  
  /// 设置开始日期
  void setStartDate(DateTime date) {
    _safeUpdate(() {
      _startDate = date;
    });
  }
  
  /// 设置结束日期
  void setEndDate(DateTime date) {
    _safeUpdate(() {
      _endDate = date;
    });
  }
  
  /// 查询记录
  Future<void> fetchRecords() async {
    if (_isDisposed || _db == null) return;
    
    _safeUpdate(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      // 确保结束日期包含当天的所有时间
      final adjustedEndDate = DateTime(
        _endDate.year, 
        _endDate.month, 
        _endDate.day, 
        23, 59, 59
      );
      
      // 从数据库查询记录
      final result = await _db!.queryAccessLogs(
        startDate: _startDate, 
        endDate: adjustedEndDate
      );
      
      if (!result.success) {
        throw Exception(result.message);
      }
      
      final accessLogs = result.data ?? [];
      final fetchedRecords = accessLogs.map((log) => RecordModel.fromJson(log)).toList();
      
      _safeUpdate(() {
        _records = fetchedRecords;
        _isLoading = false;
      });
    } catch (e) {
      _showError('查询记录失败: ${e.toString()}');
    }
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}