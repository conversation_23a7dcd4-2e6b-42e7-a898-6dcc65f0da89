import 'dart:async';
import 'dart:math';
import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import 'package:base_package/base_package.dart';

/// 闸机扫描组件
class GateScanningWidget extends StatefulWidget {
  final VoidCallback? onClose;
  
  const GateScanningWidget({
    Key? key,
    this.onClose,
  }) : super(key: key);

  @override
  State<GateScanningWidget> createState() => _GateScanningWidgetState();
}

class _GateScanningWidgetState extends State<GateScanningWidget>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeController;
  late AnimationController _rotationController;
  late AnimationController _waveController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _waveAnimation;
  
  List<String> _scannedItems = [];
  Timer? _mockScanTimer;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _startMockScanning();
  }
  
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
    
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    ));
  }
  
  void _startAnimations() {
    _fadeController.forward();
    _rotationController.repeat();
    _waveController.repeat(reverse: true);
  }
  
  void _startMockScanning() {
    _mockScanTimer = Timer.periodic(Duration(milliseconds: 1500), (timer) {
      if (mounted && Random().nextBool()) {
        _addScannedItem();
      }
    });
  }
  
  void _addScannedItem() {
    final barcode = 'BOOK${Random().nextInt(1000).toString().padLeft(3, '0')}';
    setState(() {
      _scannedItems.add(barcode);
    });
  }
  
  void _closeOverlay() async {
    _mockScanTimer?.cancel();
    await _fadeController.reverse();
    widget.onClose?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withOpacity(0.8),
              child: Center(
                child: Container(
                  width: 700.p,
                  height: 500.p,
                  margin: EdgeInsets.all(40.p),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.p),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 20,
                        offset: Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 标题栏
                      _buildHeader(),
                      
                      // 扫描内容区域
                      Expanded(
                        child: _buildScanContent(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
  
  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.p),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.p),
          topRight: Radius.circular(20.p),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.radar,
            color: Colors.white,
            size: 32.p,
          ),
          SizedBox(width: 15.p),
          Expanded(
            child: Text(
              'RFID 扫描中',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24.p,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Text(
            '已扫描: ${_scannedItems.length}',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.p,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建扫描内容
  Widget _buildScanContent() {
    return Padding(
      padding: EdgeInsets.all(30.p),
      child: Row(
        children: [
          // 左侧扫描动画
          Expanded(
            flex: 2,
            child: _buildScanAnimation(),
          ),
          
          SizedBox(width: 30.p),
          
          // 右侧扫描列表
          Expanded(
            flex: 3,
            child: _buildScanList(),
          ),
        ],
      ),
    );
  }
  
  /// 构建扫描动画
  Widget _buildScanAnimation() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 旋转的雷达图标
        AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value,
              child: Icon(
                Icons.radar,
                size: 120.p,
                color: Colors.orange,
              ),
            );
          },
        ),
        
        SizedBox(height: 30.p),
        
        // 波纹效果
        AnimatedBuilder(
          animation: _waveAnimation,
          builder: (context, child) {
            return Container(
              width: 150.p,
              height: 150.p,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.orange.withOpacity(0.3 * (1 - _waveAnimation.value)),
                  width: 2,
                ),
              ),
              child: Transform.scale(
                scale: 0.5 + 0.5 * _waveAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.orange.withOpacity(0.1 * (1 - _waveAnimation.value)),
                  ),
                ),
              ),
            );
          },
        ),
        
        SizedBox(height: 20.p),
        
        // 提示文字
        Text(
          '正在扫描随身物品\n请稍候...',
          style: TextStyle(
            fontSize: 18.p,
            color: Colors.grey[600],
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
  
  /// 构建扫描列表
  Widget _buildScanList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '扫描到的物品',
          style: TextStyle(
            fontSize: 20.p,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        
        SizedBox(height: 20.p),
        
        Expanded(
          child: _scannedItems.isEmpty
              ? Center(
                  child: Text(
                    '暂未扫描到物品',
                    style: TextStyle(
                      fontSize: 16.p,
                      color: Colors.grey[500],
                    ),
                  ),
                )
              : ListView.builder(
                  itemCount: _scannedItems.length,
                  itemBuilder: (context, index) {
                    final item = _scannedItems[index];
                    return Container(
                      margin: EdgeInsets.only(bottom: 10.p),
                      padding: EdgeInsets.all(15.p),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(10.p),
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.book,
                            color: Colors.orange,
                            size: 24.p,
                          ),
                          SizedBox(width: 15.p),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item,
                                  style: TextStyle(
                                    fontSize: 16.p,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  '图书条码',
                                  style: TextStyle(
                                    fontSize: 12.p,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 20.p,
                          ),
                        ],
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }
  
  @override
  void dispose() {
    _mockScanTimer?.cancel();
    _fadeController.dispose();
    _rotationController.dispose();
    _waveController.dispose();
    super.dispose();
  }
}
