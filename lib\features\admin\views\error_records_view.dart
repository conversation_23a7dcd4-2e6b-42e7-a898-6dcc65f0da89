import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/router/app_router.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/widgets/countdown_timer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/footer.dart';
import '../../../shared/widgets/logo_banner.dart';
import '../view_models/error_records_view_model.dart';
import 'widgets/error_list.dart';

class ErrorRecordsView extends StatelessWidget {
  const ErrorRecordsView({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ErrorRecordsViewModel(),
      child: const ErrorRecordsContent(),
    );
  }
}

class ErrorRecordsContent extends StatefulWidget {
  const ErrorRecordsContent({super.key});

  @override
  State<ErrorRecordsContent> createState() => _ErrorRecordsContentState();
}

class _ErrorRecordsContentState extends State<ErrorRecordsContent> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ErrorRecordsViewModel>().init();
    });
  }
  @override
  Widget build(BuildContext context) {
    return BasePage(
      topWrapper: const LogoBanner(tailWidget: AutoCountdown()),
      mainWrapper: _buildMainContent(),
      bottomWrapper: Footer(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 80.p, vertical: 25.p),
          child: CustomButton.outline(
            text: '退出',
            onTap: () {
              AppNavigator.back();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    int number =
        Provider.of<ErrorRecordsViewModel>(context).errorRecords.length;
    return Padding(
      padding:  EdgeInsets.symmetric(horizontal: 40.p),
      child: Column(children: [
        if (number > 0)
          Container(
            height: 100.p,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text('总共有错误数据 $number 条',
                  style: const TextStyle(color: Colors.white)),
            ),
          ),
        SizedBox(height: 40.p),
        const Expanded(child: ErrorList())
      ]),
    );
  }
}
