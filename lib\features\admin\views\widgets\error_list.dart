import 'package:flutter/material.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';

import '../../../../core/models/book.dart';
import '../../../../core/utils/window_util.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../models/error.dart';
import '../../view_models/error_records_view_model.dart';

class ErrorList extends StatelessWidget {
  const ErrorList({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(context),
        SizedBox(height: 14.p),
        Expanded(
          child: _buildBookListContent(context),
        ),
      ],
    );
  }

  Widget _buildBookListContent(BuildContext context) {
    return Consumer<ErrorRecordsViewModel>(
      builder: (context, viewModel, child) {
        final errorRecords = viewModel.errorRecords;
        // 如果正在加载书籍，显示加载状态
        if (viewModel.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // 如果选中的书架没有图书，显示空状态
        if (errorRecords.isEmpty) {
          return const Center(
            child: Text(
              '暂无错误记录',
              style: TextStyle(
                fontSize: 32,
                color: Color(0xFF5C6066),
              ),
            ),
          );
        }

        return ListView.builder(
          physics: const BouncingScrollPhysics(),
          itemCount: errorRecords.length,
          itemBuilder: (context, index) {
            final errorRecord = errorRecords[index];
            return _buildListItem(context, errorRecord);
          },
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 40.p, vertical: 22.p),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color.fromRGBO(199, 224, 255, 0.7),
            Color.fromRGBO(230, 255, 251, 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(4),
        border: GradientBoxBorder(
          gradient: const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color(0xFF54A0FF),
              Color(0xFF3FE2C8),
            ],
          ),
          width: 1.p,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Row(
        children: [
          SizedBox(width: 6.p),
          _buildHeaderCell('格口号', width: 100.p),
          _buildSpacing(),
          Expanded(child: _buildHeaderCell('题名')),
          _buildSpacing(),
          SizedBox(width: 300.p, child: _buildHeaderCell('出错原因')),
          _buildSpacing(),
          SizedBox(width: 100.p, child: _buildHeaderCell('备注')),
          _buildSpacing(),
          SizedBox(
            width: 100.p,
            child: Align(
              alignment: Alignment.centerRight,
              child: _buildHeaderCell('操作'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(BuildContext context, ErrorRecord errorRecord) {
    // final slotNo = book.slot_no.split('-').last;
    return Container(
      margin: EdgeInsets.only(bottom: 14.p),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      clipBehavior: Clip.antiAlias,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 40.p, vertical: 18.p),
        decoration: BoxDecoration(
          border: Border(
            left: BorderSide(color: const Color(0xFF54A0FF), width: 6.p),
          ),
        ),
        child: Row(
          children: [
            _buildCell(errorRecord.slotNo??'-', width: 100.p),  // 格口号
            _buildSpacing(),
            Expanded(child: _buildCell(errorRecord.bookTitle??'-', maxLines: 2)),  // 书名
            _buildSpacing(),
            _buildCell(errorRecord.errorReason??'-', width: 300.p),
            _buildSpacing(),
            _buildCell(errorRecord.remark??'-', width: 100.p),
            _buildSpacing(),
            SizedBox(
              child: Align(
                alignment: Alignment.centerRight,
                child: CustomButton.filled(
                  width: 100.p,
                  height: 50.p,
                  textStyle: TextStyle(fontSize: 28.p,color: Colors.white),
                  text: '处理',
                  onTap: () {
                    context.read<ErrorRecordsViewModel>().updateErrorRecordStatus(errorRecord.id!);
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCell(String text, {double? width, int? maxLines}) {
    return SizedBox(
      width: width,
      child: Text(
        text,
        style: TextStyle(
          fontSize: 30.p,
          color: const Color(0xFF5C6066),
          fontWeight: FontWeight.w500,
          height: 20 / 16,
        ),
        maxLines: maxLines,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      ),
    );
  }

  Widget _buildCell(String text,
      {double? width,
      int? maxLines,
      Alignment alignment = Alignment.centerLeft}) {
    return SizedBox(
      width: width,
      child: Align(
        alignment: alignment,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 32.p,
            color: const Color(0xFF222222),
            fontWeight: FontWeight.w500,
            height: 22 / 18,
          ),
          maxLines: maxLines,
          overflow: maxLines != null ? TextOverflow.ellipsis : null,
        ),
      ),
    );
  }

  Widget _buildSpacing() => SizedBox(width: 50.p);
}
