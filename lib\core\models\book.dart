import 'package:base_package/base_package.dart';
import 'package:sea_socket/sea_socket.dart';

class Book {
  //  数据库字段
  String barcode;
  String uid;
  String title;
  String callno;
  String isbn;
  String author;
  String media_type;
  String price;
  String circulation_type;
  String permanent_location;
  String current_location;
  String publisher;
  String pages;
  String subject;
  String extra;
  String datetime;
  String slot_no;
  String status;    //  0：书柜中本来存在的书籍 1：本次上架的书籍 2：本次下架的书籍
  String upload;
  String reservation; //  N：未预约 Y：已预约




  Book({
    required this.barcode,
    required this.uid,
    required this.title,
    required this.callno,
    required this.isbn,
    required this.author,
    required this.media_type,
    required this.price,
    required this.circulation_type,
    required this.permanent_location,
    required this.current_location,
    required this.publisher,
    required this.pages,
    required this.subject,
    required this.extra,
    required this.datetime,
    required this.slot_no,
    required this.status,
    required this.upload,
    required this.reservation,
  });

  Map<String, dynamic> toJson() {
    return {
      "barcode": barcode,
      "uid": uid,
      "title": title,
      "callno": callno,
      "isbn": isbn,
      "author": author,
      "media_type": media_type,
      "price": price,
      "circulation_type": circulation_type,
      "permanent_location": permanent_location,
      "current_location": current_location,
      "publisher": publisher,
      "pages": pages,
      "subject": subject,
      "extra": extra,
      "datetime": datetime,
      "slot_no": slot_no,
      "status": status,
      "upload": upload,
      "reservation": reservation,
    };
  }

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      barcode: json["barcode"] ?? "",
      uid: json["uid"] ?? "",
      title: json["title"] ?? "",
      callno: json["callno"] ?? "",
      isbn: json["isbn"] ?? "",
      author: json["author"] ?? "",
      media_type: json["media_type"] ?? "",
      price: json["price"] ?? "",
      circulation_type: json["circulation_type"] ?? "",
      permanent_location: json["permanent_location"] ?? "",
      current_location: json["current_location"] ?? "",
      publisher: json["publisher"] ?? "",
      pages: json["pages"] ?? "",
      subject: json["subject"] ?? "",
      extra: json["extra"] ?? "",
      datetime: json["datetime"] ?? "",
      slot_no: json["slot_no"] ?? "",
      status: json["status"] ?? "",
      upload: json["upload"] ?? "",
      reservation: json["reservation"] ?? "",
    );
  }

  static Book fromSip2BookData(Sip2BookData book) {
    return Book(
      barcode: book.ItemIdentifier ?? "",
      uid: "",
      title: book.TitleIdentifier ?? "",
      callno: book.CallNo ?? "",
      isbn: book.ISBN ?? "",
      author: book.ItemAuthor ?? "",
      media_type: book.MediaType ?? '',
      price: book.OrgLocation ?? "",
      circulation_type: book.ItemCirtype ?? "",
      permanent_location: '',
      current_location: book.CurrentLocation ?? "",
      publisher: book.Publisher ?? "",
      pages: "",
      subject: book.Subject ?? "",
      extra: '',
      datetime: BPUtils.getNowTimeStamp(),
      slot_no: book.ShelfNo ?? '',
      status: '3',
      upload: '2',
      reservation: book.Reservation ?? '',
    );
  }

}