cmake_minimum_required(VERSION 3.14)
project(runner LANGUAGES CXX)

# Define the application target. To change its name, change BINARY_NAME in the
# top-level CMakeLists.txt, not the value here, or `flutter run` will no longer
# work.
#
# Any new source files that you add to the application should be added here.
add_executable(${BINARY_NAME} WIN32
  "flutter_window.cpp"
  "main.cpp"
  "utils.cpp"
  "win32_window.cpp"
  "${FLUTTER_MANAGED_DIR}/generated_plugin_registrant.cc"
  "../../src/opencv/face_detection.cpp"
  "flutter_face_bridge.cpp"
  "baidu_face_bridge.cpp"
  "Runner.rc"
  "runner.exe.manifest"
)

# Apply the standard set of build settings. This can be removed for applications
# that need different build settings.
apply_standard_settings(${BINARY_NAME})

# Override specific compile options to disable warnings as errors for face_detection.cpp
target_compile_options(${BINARY_NAME} PRIVATE /WX- /wd4996 /wd4244 /wd4267 /wd4189)

# Add preprocessor definitions for the build version.
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION=\"${FLUTTER_VERSION}\"")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_MAJOR=${FLUTTER_VERSION_MAJOR}")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_MINOR=${FLUTTER_VERSION_MINOR}")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_PATCH=${FLUTTER_VERSION_PATCH}")
target_compile_definitions(${BINARY_NAME} PRIVATE "FLUTTER_VERSION_BUILD=${FLUTTER_VERSION_BUILD}")

# Disable Windows macros that collide with C++ standard library functions.
target_compile_definitions(${BINARY_NAME} PRIVATE "NOMINMAX")
target_compile_definitions(${BINARY_NAME} PRIVATE "FACE_DETECTOR_EXPORTS")
target_compile_definitions(${BINARY_NAME} PRIVATE "_CRT_SECURE_NO_WARNINGS")

# Find OpenCV package
find_package(OpenCV REQUIRED)

# 设置百度人脸SDK路径（使用绝对路径）
set(BAIDU_FACE_SDK_DIR "D:/FaceOfflineSdk")
set(BAIDU_FACE_INCLUDE_DIR "${BAIDU_FACE_SDK_DIR}/FaceOfflineSdk/include")
set(BAIDU_FACE_LIB_DIR "${BAIDU_FACE_SDK_DIR}/FaceOfflineSdk/lib/x64")
set(BAIDU_FACE_DLL_DIR "${BAIDU_FACE_SDK_DIR}/x64")

# Add Baidu Face SDK include directories
target_include_directories(${BINARY_NAME} PRIVATE "${BAIDU_FACE_INCLUDE_DIR}")
target_include_directories(${BINARY_NAME} PRIVATE "${BAIDU_FACE_SDK_DIR}/FaceOfflineSdk")
target_include_directories(${BINARY_NAME} PRIVATE "${BAIDU_FACE_SDK_DIR}/FaceOfflineSdk/face_scene/include")
target_include_directories(${BINARY_NAME} PRIVATE "${BAIDU_FACE_SDK_DIR}/FaceOfflineSdk/third_party")
target_include_directories(${BINARY_NAME} PRIVATE "${BAIDU_FACE_SDK_DIR}/FaceOfflineSdk/util")

# Add dependency libraries and include directories. Add any application-specific
# dependencies here.
target_link_libraries(${BINARY_NAME} PRIVATE flutter flutter_wrapper_app)
target_link_libraries(${BINARY_NAME} PRIVATE "dwmapi.lib")
target_link_libraries(${BINARY_NAME} PRIVATE ${OpenCV_LIBS})

# 链接百度人脸识别SDK静态库
target_link_libraries(${BINARY_NAME} PRIVATE "${BAIDU_FACE_LIB_DIR}/BaiduFaceApi.lib")
target_link_libraries(${BINARY_NAME} PRIVATE "${BAIDU_FACE_LIB_DIR}/face_sdk.lib")
target_link_libraries(${BINARY_NAME} PRIVATE "${BAIDU_FACE_LIB_DIR}/json_vc71_libmt.lib")

target_include_directories(${BINARY_NAME} PRIVATE "${CMAKE_SOURCE_DIR}")
target_include_directories(${BINARY_NAME} PRIVATE ${OpenCV_INCLUDE_DIRS})
target_include_directories(${BINARY_NAME} PRIVATE "${CMAKE_SOURCE_DIR}/src/opencv")

# Run the Flutter tool portions of the build. This must not be removed.
add_dependencies(${BINARY_NAME} flutter_assemble)

# 复制D:\FaceOfflineSdk\x64下的所有DLL文件到构建目录
# 确保程序能够正常运行，不依赖PATH或其他复杂设置
add_custom_command(TARGET ${BINARY_NAME} POST_BUILD
  COMMAND ${CMAKE_COMMAND} -E copy_directory
    "D:/FaceOfflineSdk/x64"
    $<TARGET_FILE_DIR:${BINARY_NAME}>
  COMMAND ${CMAKE_COMMAND} -E echo "已复制所有SDK DLL文件到构建目录"
  COMMENT "复制百度SDK所有DLL文件..."
)
