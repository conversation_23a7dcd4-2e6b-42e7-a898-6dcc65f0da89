#include "flutter_window.h"

#include <optional>

#include "flutter/generated_plugin_registrant.h"
#include <flutter/method_channel.h>
#include <flutter/event_channel.h>
#include <flutter/event_stream_handler_functions.h>
#include <flutter/standard_method_codec.h>
#include <thread>
#include <atomic>
#include <memory>
#include <mutex>

// Global variables for event stream control
std::atomic<bool> g_event_stream_active{false};
std::unique_ptr<flutter::EventSink<flutter::EncodableValue>> g_event_sink;
std::thread g_event_thread;

// Global variables for face detection callback
std::mutex g_face_callback_mutex;

// Function to send face detection result to Flutter
void SendFaceDetectionResult(const unsigned char* imageData, int imageSize, float confidence) {
  std::lock_guard<std::mutex> lock(g_face_callback_mutex);
  if (g_event_stream_active && g_event_sink) {
    // Convert image data to vector
    std::vector<unsigned char> imageVector(imageData, imageData + imageSize);
    
    // Create EncodableMap with image data and confidence
    flutter::EncodableMap result;
    result[flutter::EncodableValue("imageData")] = flutter::EncodableValue(imageVector);
    result[flutter::EncodableValue("confidence")] = flutter::EncodableValue(static_cast<double>(confidence));
    result[flutter::EncodableValue("timestamp")] = flutter::EncodableValue(static_cast<int64_t>(time(nullptr)));
    
    g_event_sink->Success(flutter::EncodableValue(result));
    
    std::cout << "Face detection result sent to Flutter, confidence: " << confidence << std::endl;
  }
}

// External function from face_detection.cpp
extern "C" {
  // Function pointer type for face detection callback
  typedef void (*FaceDetectionCallback)(const unsigned char*, int, float);
  
  // Declaration of the function from face_detection.cpp
  void set_face_detection_callback(FaceDetectionCallback callback);
  
  void InitializeFaceDetectionCallback() {
    // Directly call the function from face_detection.cpp
    set_face_detection_callback(SendFaceDetectionResult);
    std::cout << "Face detection callback initialized successfully" << std::endl;
  }
}

FlutterWindow::FlutterWindow(const flutter::DartProject& project)
    : project_(project) {}

FlutterWindow::~FlutterWindow() {
  // Clean up event stream
  g_event_stream_active = false;
  if (g_event_thread.joinable()) {
    g_event_thread.join();
  }
}

bool FlutterWindow::OnCreate() {
  if (!Win32Window::OnCreate()) {
    return false;
  }

  RECT frame = GetClientArea();

  // The size here must match the window dimensions to avoid unnecessary surface
  // creation / destruction in the startup path.
  flutter_controller_ = std::make_unique<flutter::FlutterViewController>(
      frame.right - frame.left, frame.bottom - frame.top, project_);
  // Ensure that basic setup of the controller was successful.
  if (!flutter_controller_->engine() || !flutter_controller_->view()) {
    return false;
  }
  RegisterPlugins(flutter_controller_->engine());
  
  // Register EventChannel
  SetupEventChannel();
  
  SetChildContent(flutter_controller_->view()->GetNativeWindow());

  flutter_controller_->engine()->SetNextFrameCallback([&]() {
    this->Show();
  });

  // Flutter can complete the first frame before the "show window" callback is
  // registered. The following call ensures a frame is pending to ensure the
  // window is shown. It is a no-op if the first frame hasn't completed yet.
  flutter_controller_->ForceRedraw();

  return true;
}

void FlutterWindow::SetupEventChannel() {
  // Create EventChannel
  event_channel_ = std::make_unique<flutter::EventChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(), "face_detection_events",
      &flutter::StandardMethodCodec::GetInstance());

  // Set up event stream handler
  auto handler = std::make_unique<flutter::StreamHandlerFunctions<flutter::EncodableValue>>(
      [this](const flutter::EncodableValue* arguments,
         std::unique_ptr<flutter::EventSink<flutter::EncodableValue>>&& events)
      -> std::unique_ptr<flutter::StreamHandlerError<flutter::EncodableValue>> {
        // Stop any existing stream first
        this->StopEventStream();
        
        // Save event sink
        g_event_sink = std::move(events);
        
        // Start new event stream
        this->StartEventStream();
        
        return nullptr;
      },
      [this](const flutter::EncodableValue* arguments)
      -> std::unique_ptr<flutter::StreamHandlerError<flutter::EncodableValue>> {
        // Stop event stream
        this->StopEventStream();
        return nullptr;
      });

  event_channel_->SetStreamHandler(std::move(handler));
  
  // Create MethodChannel for control
  method_channel_ = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(), "face_detection",
      &flutter::StandardMethodCodec::GetInstance());

  method_channel_->SetMethodCallHandler(
      [this](const flutter::MethodCall<flutter::EncodableValue>& call,
         std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
        if (call.method_name().compare("startEventStream") == 0) {
          this->StartEventStream();
          result->Success(flutter::EncodableValue("Event stream started"));
        } else if (call.method_name().compare("stopEventStream") == 0) {
          this->StopEventStream();
          result->Success(flutter::EncodableValue("Event stream stopped"));
        } else {
          result->NotImplemented();
        }
      });
}

void FlutterWindow::StartEventStream() {
  // Stop any existing stream first
  StopEventStream();
  
  // Activate event stream for face detection callbacks
  g_event_stream_active = true;
  
  // Initialize face detection callback
  InitializeFaceDetectionCallback();
  
  // Log activation
  std::cout << "Face detection EventChannel activated" << std::endl;
}

void FlutterWindow::StopEventStream() {
  // Deactivate event stream
  g_event_stream_active = false;
  
  // Clear event sink
  g_event_sink.reset();
  
  // Log deactivation
  std::cout << "Face detection EventChannel deactivated" << std::endl;
}

void FlutterWindow::OnDestroy() {
  if (flutter_controller_) {
    flutter_controller_ = nullptr;
  }

  Win32Window::OnDestroy();
}

LRESULT
FlutterWindow::MessageHandler(HWND hwnd, UINT const message,
                              WPARAM const wparam,
                              LPARAM const lparam) noexcept {
  // Give Flutter, including plugins, an opportunity to handle window messages.
  if (flutter_controller_) {
    std::optional<LRESULT> result =
        flutter_controller_->HandleTopLevelWindowProc(hwnd, message, wparam,
                                                      lparam);
    if (result) {
      return *result;
    }
  }

  switch (message) {
    case WM_FONTCHANGE:
      flutter_controller_->engine()->ReloadSystemFonts();
      break;
  }

  return Win32Window::MessageHandler(hwnd, message, wparam, lparam);
}
