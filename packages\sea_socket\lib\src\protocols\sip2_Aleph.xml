<sip type="Aleph" desc="Aleph_aleph协议">
	<message id="93" name="Login" type="Req" desc="读者登录">
		<field value = "UIDAlgorithm"	length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="读者ID加密算法"/>
		<field value = "PWDAlgorithm"	length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="读者密码加密算法"/>
		<field value = "UserId"			length = "-1"	must="Y"	flag="CN"	default=""		force = ""   brief ="用户名(操作员代码)"/>
		<field value = "Password"  		length = "-1" 	must="Y"  	flag="CO" 	default="" 		force = ""   brief ="密码"/>
		<field value = "LocationCode"  	length = "-1" 	must="N"  	flag="CP" 	default=""		force = ""   brief ="馆藏地"/>
		<field value = "MsgSeqId"  		length = "-1" 	must="Y"  	flag="AY" 	default=""		force = ""   brief ="消息序列标识"/>
	</message>
	<message id="94" name="Login" type="Ans" desc="读者登录">
		<field value = "OK"   			length = "1"  	must="Y"  	flag="" 	default="" 	force = ""   brief ="是否登录成功，1成功0失败"/>
		<field value = "MsgSeqId"  		length = "-1" 	must="Y"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="35" name="Logout" type="Req" desc="结束读者事务">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"   	length = "-1"	must="Y"	flag="AO" 	default="" 	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"   length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	default=""	force = ""   brief ="终端机密码"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"	default=""	force = ""   brief ="读者密码"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="36" name="Logout" type="Ans" desc="结束读者事务">
		<field value = "EndSession"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="结束标志，Y:成功结束 ; N:未成功结束"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"   	length = "-1"	must="Y"	flag="AO" 	default="" 	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"   length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "ScreenMessage"   	length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"   		length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义打印信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>		
	<message id="99" name="SCStatus" type="Req" desc="SC 状态">
		<field value = "ip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "port"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "op"			length = "3"	must="Y"	flag=""		default="bor-info"	force = ""   brief ="操作方式"/>
		<field value = "library"	length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="馆代码"/>
		<field value = "bor_id"			length = "10"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="读者特征号"/>		
		<field value = "USER_NAME"		length = "-1"	must="N"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="用户名"/>
		<field value = "USER_PASSWORD"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="读者证号"/>
	</message>	
	<message id="98" name="SCStatus" type="Ans" desc="SC 状态">
		<field value = "OnLineStatus"		length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="在线状态，Y:在线 ，N:即将下线"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>
		<field value = "LibraryName"		length = "-1"	must="N"	flag="AM"	default=""	force = ""   brief ="图书馆名"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
	</message>	
	<message id="63" name="PatronInformation" type="Req" desc="查询读者证信息">
		<field value = "ip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "port"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "op"			length = "3"	must="Y"	flag=""		default="bor-info"	force = ""   brief ="操作方式"/>
		<field value = "library"	length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="馆代码"/>
		<field value = "XK"				length = "-1"   must="Y"	flag=""	default="2" force=""  brief="更改密码or创建读者" />
		<field value = "bor_id"			length = "10"	must="Y"	flag=""		default="620121198908044636"	force = ""   brief ="读者特征号"/>			
		<field value = "PatronPassword"	length = "10"	must="Y"	flag=""		default="123456"	force = ""   brief ="读者密码"/>
		<field value = "USER_NAME"		length = "-1"	must="N"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="用户名"/>
		<field value = "USER_PASSWORD"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="读者证号"/>
	</message>
	<message id="64" name="PatronInformation" type="Ans" desc="查询读者证信息">
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>	
		<field value = "PatronIdentifier"		length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"				length = "-1"	must="Y"	flag="AE"	default=""	force = ""   brief ="读者姓名"/>
		<field value = "ValidPatron"			length = "1"	must="N"	flag="BL"	default=""	force = ""   brief ="读者是否存在"/>
		<field value = "ValidPatronPassword"	length = "1"	must="N"	flag="CQ"	default=""	force = ""   brief ="读者密码是否正确"/>
		<field value = "PersonalIdentifier"		length = "1"	must="N"	flag="ID"	default=""	force = ""   brief ="身份证号"/>		
		<field value = "HomeAddress"	    	length = "-1"	must="N"	flag="BD"	default=""	force = ""   brief ="住址"/>
		<field value = "EmailAddress"	    	length = "-1"	must="N"	flag="BE"	default=""	force = ""   brief ="e-mail"/>
		<field value = "HomePhoneNumber"		length = "-1"	must="N"	flag="BF"	default=""	force = ""   brief ="联系电话"/>
		<field value = "Telephone"				length = "-1"	must="N"	flag="BF"	default=""	force = ""   brief ="联系电话"/>
		<field value = "MobilePhone"			length = "-1"	must="N"	flag="BF"	default=""	force = ""   brief ="联系电话"/>
		<field value = "PatronDetailStatus"     length = "-1"   must="Y"    flag=""     default=""  force = ""   brief = "读者状态" />
		<field value = "CurrentStatus"     length = "-1"   must="Y"    flag=""     default=""  force = ""   brief = "读者类型状态" />		
		<field value = "PatronSex"	length = "1"	must="N"	flag=""	default=""	force = ""   brief ="读者性别"/>
		<field value = "BirthDate"	    	length = "-1"	must="N"	flag=""	default=""	force = ""   brief ="生日"/>
		<field value = "PatronTypeCode"	    	length = "-1"	must="N"	flag=""	default=""	force = ""   brief ="读者职业"/>
		<field value = "PatronValidDate"		length = "-1"	must="N"	flag=""	default=""	force = ""   brief ="读者有效日期"/>
		<field value = "bor_id"  				length = "-1" 	must="Y"  	flag="" 	default=""	force = ""   brief ="读者特征号"/>
		<field value = "ChinaLean"				length = "-1"   must="Y"	flag="" default=""  force = ""   brief = "中文外借标志" />
		<field value = "ForeignLean"			length = "-1"   must="Y"	flag="" default=""  force = ""   brief = "英文外借标志" />
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
	</message>
	<message id="23" name="PatronStatus" type="Req" desc="查询读者证状态">
		<field value = "Language"			length = "3"	must="Y"	flag=""		default="001"	force = ""   brief ="语言"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="N"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	default=""	force = ""   brief ="终端机密码"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"	default=""	force = ""   brief ="读者证密码"/>
		<field value = "MsgSeqId"           length = "-1" 	must="Y"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="24" name="PatronStatus" type="Ans" desc="查询读者证状态">
		<field value = "PatronStatus"			length = "14"	must="Y"	flag=""		default=""	force = ""   brief ="卡状态暂时为空，14位空格占位"/>
		<field value = "Language"				length = "3"	must="Y"	flag=""		default=""	force = ""   brief ="语言"/>
		<field value = "TransactionDate"		length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>	
		<field value = "PatronIdentifier"		length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"				length = "-1"	must="Y"	flag="AE"	default=""	force = ""   brief ="读者姓名"/>
		<field value = "ValidPatron"			length = "1"	must="N"	flag="BL"	default=""	force = ""   brief ="读者是否存在"/>
		<field value = "ValidPatronPassword"	length = "1"	must="N"	flag="CQ"	default=""	force = ""   brief ="读者密码是否正确"/>
        <field value = "CurrencyType"	    	length = "3"	must="N"	flag="BH"	default=""	force = ""   brief ="流通货币类型"/>
        <field value = "FeeAmount"	        	length = "-1"	must="N"	flag="BV"	default=""	force = ""   brief ="金额"/>
		<field value = "ScreenMessage"			length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"				length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  				length = "-1" 	must="Y"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="91" name="CheckRepeat" type="Req" desc="查重">
		<field value = "ip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "port"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "op"			length = "3"	must="Y"	flag=""		default="bor-info"	force = ""   brief ="操作方式"/>
		<field value = "library"	length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="馆代码"/>
		<field value = "XK"				length = "-1"   must="Y"	flag=""	default="2" force=""  brief="更改密码or创建读者(aleph 只能通过修改密码协议来判断查重)" />
		<field value = "bor_id"			length = "10"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="读者特征号"/>		
		<field value = "USER_NAME"		length = "-1"	must="N"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="用户名"/>
		<field value = "USER_PASSWORD"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="读者证号"/>
		<field value = "ORACLE_IP"		length = "-1"	must="N"	flag=""	default=""	force = "***************"   brief ="读者证号"/>
		<field value = "ORACLE_Port"	length = "-1"	must="N"	flag=""	default=""	force = "1521"   brief ="读者证号"/>
		<field value = "ORACLE_ServiceName"	length = "-1"	must="N"	flag=""	default=""	force = "aleph20"   brief ="读者证号"/>
		<field value = "ORACLE_UserName"	length = "-1"	must="N"	flag=""	default=""	force = "aleph_selfecard"   brief ="读者证号"/>
		<field value = "ORACLE_UserPwd"	length = "-1"	must="N"	flag=""	default=""	force = "aleph_selfecard_pwd"   brief ="读者证号"/>
	</message>
	<message id="92" name="CheckRepeat" type="Ans" desc="查重">		
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonalIdentifier"	length = "-1"	must="N"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "OK"					length = "-1"	must="N"	flag="OK"		default=""	force = ""   brief ="操作是否成功 0:成功，1:失败"/>
		<field value = "Result"				length = "1"	must="N"	flag="AC"		default=""	force = ""   brief ="验证结果：0:不存在记录，1:存在记录"/>
	</message>
	<message id="81" name="CreateUser" type="Req" desc="创建读者">
		<field value = "ip"			length = "-1"   must= "Y"   flag=""     default="***********" force = "" brief="ip" />
		<field value = "port"       length = "-1"   must= "Y"   flag=""     default="8991"    force=""     brief="port" /> 
		<field value = "op"			length = "3"	must="Y"	flag=""		default="update-bor"	force = ""   brief ="操作方式"/>	
		<field value = "XK"				length = "-1"   must="Y"	flag=""	default="02" force=""  brief="更改密码or创建读者" />		
		<field value = "base"	length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="馆代码"/>
		<field value = "library"	length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="馆代码"/>
		<field value = "update_flag" length = "-1"  must="Y"    flag=""		default="Y"  force = "" brief="更新标记"/>
		<field value = "bor_id"			length = "10"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="读者特征号"/>		
		<field value = "USER_NAME"		length = "-1"	must="N"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="用户名"/>
		<field value = "USER_PASSWORD"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="登陆密码"/>
		<field value = "pid"			length = "-1"   must="Y"	flag=""	default="" force=""  brief="修改密码时的读者证号" /> 	
		<field value = "old-pwd"		length = "-1"   must="Y"	flag=""	default="" force=""  brief="旧密码" />
		<field value = "new-pwd"		length = "-1"   must="Y"	flag=""	default="" force=""  brief="新密码" />
		<field value = "cataloger"		length = "-1"   must="Y"	flag=""	default="WWW-ZZBZ1" force=""  brief="设备用户" />			
		<field value = "expiry_date"   length = "-1"   must="Y"	flag="" default="" force="" brief="延长截至日期" />
		<field value = "xml_full_req"   length = "-1"   must="Y"	flag="" default="" force="" brief="未知字段" />		
		<field value = "post_name"		length = "-1"   must="Y"    flag="" default="www"  force = ""   brief = "读者姓名"/>
		<field value = "post_sex"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者性别"/>
		<field value = "post_patronpwd"			length = "-1"   must="Y"	flag=""	default="" force=""  brief="读者证密码" /> 	
		<field value = "post_birth"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者生日"/>
		<field value = "post_patronid"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者读者类型"/>
		<field value = "post_birthdate"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者截至日期"/>
		<field value = "post_idno"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者读者类型"/>
		<field value = "post_add1"		length = "-1"   must="Y"    flag="" default=""  force = ""   brief = "读者截至日期"/>
		<field value = "post_add2"		length = "-1"   must="Y"    flag="" default=""  force = ""   brief = "读者读者类型"/>
		<field value = "post_add3"		length = "-1"   must="Y"    flag="" default=""  force = ""   brief = "读者截至日期"/>
		<field value = "post_add4"		length = "-1"   must="Y"    flag="" default=""  force = ""   brief = "读者读者类型"/>
		<field value = "post_startdate"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者开始日期"/>
		<field value = "post_stopdate"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者截至日期"/>
		<field value = "post_patrontypecode"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者读者类型"/>
		<field value = "post_expiredate"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者截至日期"/>
		<field value = "post_cellphone"		length = "-1"   must="Y"    flag="" default=""  force = ""   brief = "读者截至日期"/>
		<field value = "post_telphone"		length = "-1"   must="Y"    flag="" default=""  force = ""   brief = "读者读者类型"/>
		<field value = "post_patrontype"		length = "-1"   must="Y"    flag="" default="NLC50"  force = ""   brief = "读者截至日期"/>

	</message>
	<message id="82" name="CreateUser" type="Ans" desc="创建读者">
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "OK"					length = "-1"	must="Y"	flag="OK"		default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "ValidPatron"			length = "1"	must="N"	flag="BL"	default=""	force = ""   brief ="读者是否存在"/>
		<field value = "ValidPatronPassword"	length = "1"	must="N"	flag="CQ"	default=""	force = ""   brief ="读者密码是否正确"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
	</message>
	<message id="77" name="PhotoData" type="" desc="上传头像">
		<field value = "ftpip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "ftpport"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "ftpborid"			length = "10"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="读者特征号"/>	
		<field value = "ftpuserid"			length = "10"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="提交身份证号"/>			
		<field value = "ftpusername"		length = "-1"	must="N"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="用户名"/>
		<field value = "ftpuserpassword"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="读者证号"/>			
		<field value = "ftplocalpath"		length = "-1"	must="N"	flag=""	default="D:\\source\\selflibrary\\0SkyBase\\SkyPotocol\\Bin"	force = ""   brief ="照片本地路径"/>
		<field value = "ftpremotepath"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="照片远程路径"/>
	</message>
	<message id="78" name="PhotoData" type="" desc="上传头像">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "OK"					length = "1"	must="Y"	flag="OK"		default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>	
	</message>
	<message id="85" name="QueryUser" type="Req" desc="查询读者信息">
		<field value = "ip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "port"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "op"			length = "3"	must="Y"	flag=""		default="bor-info"	force = ""   brief ="操作方式"/>
		<field value = "library"	length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="馆代码"/>
		<field value = "XK"				length = "-1"   must="Y"	flag=""	default="2" force=""  brief="更改密码or创建读者" />
		<field value = "bor_id"			length = "10"	must="Y"	flag=""		default=""	force = ""   brief ="读者特征号"/>			
		<field value = "PatronPassword"	length = "10"	must="Y"	flag=""		default=""	force = ""   brief ="读者密码"/>
		<field value = "USER_NAME"		length = "-1"	must="N"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="用户名"/>
		<field value = "USER_PASSWORD"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="读者证号"/>
	</message>
	<message id="86" name="QueryUser" type="Ans" desc="查询读者信息">
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>	
		<field value = "PatronIdentifier"		length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"				length = "-1"	must="Y"	flag="AE"	default=""	force = ""   brief ="读者姓名"/>
		<field value = "ValidPatron"			length = "1"	must="N"	flag="BL"	default=""	force = ""   brief ="读者是否存在"/>
		<field value = "ValidPatronPassword"	length = "1"	must="N"	flag="CQ"	default=""	force = ""   brief ="读者密码是否正确"/>
		<field value = "HomeAddress"	    	length = "-1"	must="N"	flag="BD"	default=""	force = ""   brief ="住址"/>
		<field value = "EmailAddress"	    	length = "-1"	must="N"	flag="BE"	default=""	force = ""   brief ="e-mail"/>
		<field value = "HomePhoneNumber"		length = "-1"	must="N"	flag="BF"	default=""	force = ""   brief ="联系电话"/>
		<field value = "PatronDetailStatus"     length = "-1"   must="Y"    flag=""     default=""  force = ""   brief = "读者状态" />
		
		<field value = "PatronSex"	length = "1"	must="N"	flag=""	default=""	force = ""   brief ="读者性别"/>
		<field value = "BirthDate"	    	length = "-1"	must="N"	flag=""	default=""	force = ""   brief ="生日"/>
		<field value = "PatronTypeCode"	    	length = "-1"	must="N"	flag=""	default=""	force = ""   brief ="读者职业"/>
		<field value = "PatronValidDate"		length = "-1"	must="N"	flag=""	default=""	force = ""   brief ="读者有效日期"/>
		<field value = "bor_id"  				length = "-1" 	must="Y"  	flag="" 	default=""	force = ""   brief ="读者特征号"/>
		<field value = "ChinaLean"				length = "-1"   must="Y"	flag="" default=""  force = ""   brief = "中文外借标志" />
		<field value = "ForeignLean"			length = "-1"   must="Y"	flag="" default=""  force = ""   brief = "英文外借标志" />
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>	
	</message>
	<message id="53" name="QueryImprestFine" type="Req" desc="查询预付款金额">
		<field value = "yktip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "yktport"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "op"			length = "3"	must="Y"	flag=""		default="user"	force = ""   brief ="操作方式"/>
		<field value = "postsid"		length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="chargesid"/>
		<field value = "postidType"		length = "10"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="证件号类型(1:身份证 2.读者证)"/>		
		<field value = "postuserid"		length = "-1"	must="N"	flag=""		default="8888888888888888"	force = ""   brief ="证件号"/>		
	</message>
	<message id="54" name="QueryImprestFine" type="Ans" desc="查询预付款金额">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"			length = "-1"	must="Y"	flag="AE"		default=""	force = ""   brief ="读者姓名"/>
		<field value = "ChargeAmount"		length = "-1"	must="Y"	flag=""		default=""	force = ""   brief ="读者预付款"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>	
		
	</message>
	<message id="51" name="ChargeImprest" type="Req" desc="充预付款">
		<field value = "yktip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "yktport"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "op"			length = "3"	must="Y"	flag=""		default="charge"	force = ""   brief ="操作方式"/>
		<field value = "postsid"		length = "18"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="chargesid"/>
		<field value = "postidType"		length = "10"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="证件号类型(1:身份证 2.读者证)"/>		
		<field value = "postuserid"		length = "-1"	must="N"	flag=""		default="WWW-ZZBZ1"	force = ""   brief ="证件号"/>	
		<field value = "postmoney"		length = "-1"	must="N"	flag=""		default="WWW-ZZBZ1"	force = ""   brief ="钱的数目"/>	
	</message>
	<message id="52" name="ChargeImprest" type="Ans" desc="充预付款">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ChargeAmount"		length = "-1"	must="N"	flag="JE"		default=""	force = ""   brief ="读者预付款"/>
		<field value = "FiscalIdentifier"	length = "-1"	must="N"	flag="JK"		default=""	force = ""   brief ="财经记录流水号"/>		
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="55" name="DeductImprest" type="Req" desc="预付款扣费">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ArrearsId"			length = "-1"	must="Y"	flag="JG"		default=""	force = ""   brief ="读者欠费记录ID，如果XK=2,为空"/>
		<field value = "PatronTotalFine"	length = "-1"	must="Y"	flag="JF"		default=""	force = ""   brief ="读者欠费金额"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="XK"		default=""	force = ""   brief ="操作方式，0:指定记录号扣费，1:所有记录号扣费"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>	
	</message>
	<message id="56" name="DeductImprest" type="Ans" desc="预付款扣费">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "FiscalIdentifier"	length = "-1"	must="Y"	flag="JK"		default=""	force = ""   brief ="财经记录流水号"/>		
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="19" name="ItemStatusUpdate" type="Req" desc="更新图书状态">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "OrgLocation"		length = "-1"	must="Y"	flag="AQ"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "CurrentLocation"	length = "-1"	must="N"	flag="AP"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "HoldingState"		length = "-1"	must="N"	flag="HS"		default=""	force = ""   brief ="馆藏状态(1.编目 2.在馆 3.借出 4.丢失)"/>
		<field value = "ShelfNo"			length = "-1"	must="N"	flag="KP"		default=""	force = ""   brief ="架位号"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="20" name="ItemStatusUpdate" type="Ans" desc="更新图书状态">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="11" name="CheckOut" type="Req" desc="借书">
		<field value = "SCRenewalPolicy"	length = "1"	must="Y"	flag=""		    default="N"	force = ""   brief ="终端机是否可以处理续借操作"/>
		<field value = "NoBlock"			length = "1"	must="Y"	flag=""		    default="N"	force = ""   brief =""/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "NbDueDate"	        length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "OperationCode"		length = "-1"	must="Y"	flag="CN"		default=""	force = ""   brief ="操作员代码/本设备流通地点代码"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	    default=""	force = ""   brief ="终端机密码"/>
        <field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="图书附加信息"/>
        <field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"	    default=""	force = ""   brief ="读者证密码"/>
        <field value = "FeeAcknowledged"	length = "1"	must="N"	flag="BO"		default="N"	force = ""   brief ="额外费用信息告知"/>
        <field value = "Cancel"				length = "-1"	must="N"	flag="BI"	    default=""	force = ""   brief ="是否是取消命令"/>
		<field value = "JYBTransactionID"	length = "-1"	must="N"	flag="TD"	    default=""	force = ""   brief ="JYB交易ID"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="12" name="CheckOut" type="Ans" desc="借书">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="借书成功标志"/>
        <field value = "RenewalOK"	        length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否可进行续借"/>
        <field value = "MagneticMedia"		length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否有附件 Y：有附件；N：无附件；U：未知"/> 
        <field value = "Desensitize"	    length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否取消敏感处理"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "DueDate"			length = "-1"	must="Y"	flag="AH"		default=""	force = ""   brief ="图书应还日期"/>
        <field value = "FeeType"	        length = "2"	must="N"	flag="BT"		default=""	force = ""   brief ="费用类型"/>
        <field value = "SecurityInhibit"	length = "1"	must="N"	flag="CI"		default=""	force = ""   brief ="图书安全状态"/>
        <field value = "CurrencyType"	    length = "3"	must="N"	flag="BH"		default=""	force = ""   brief ="流通货币类型"/>
        <field value = "FeeAmount"	        length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="金额"/>
        <field value = "MediaType"	        length = "3"	must="N"	flag="CK"		default=""	force = ""   brief ="媒体类型"/>
        <field value = "ItemProperties"		length = "-1"	must="Y"	flag="CH"		default=""	force = ""   brief ="图书的简单信息：AT（是否有附件：Y表示有，N表示无）PR（图书价格）"/>
        <field value = "TransactionId"	    length = "-1"	must="N"	flag="BK"		default=""	force = ""   brief ="事务ID"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="09" name="CheckIn" type="Req" desc="还书">
		<field value = "NoBlock"			length = "1"	must="Y"	flag=""		    default="N"	force = ""   brief ="离线事务是否已处理"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "ReturnDate"     	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="图书实际还回时间"/>
		<field value = "CurrentLocation"	length = "-1"	must="Y"	flag="AP"		default=""	force = ""   brief ="当前图书所在位置,流通馆藏（理解为临时馆藏）"/>
		<field value = "OperationCode"		length = "-1"	must="Y"	flag="CN"		default=""	force = ""   brief ="操作员代码/本设备流通地点代码"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	    default=""	force = ""   brief ="终端机密码"/>
		<field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="图书附加信息"/>
        <field value = "Cancel"				length = "-1"	must="N"	flag="BI"	    default=""	force = ""   brief ="是否是取消命令"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="10" name="CheckIn" type="Ans" desc="还书">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否重做敏感处理"/> 
        <field value = "Resensitize"		length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="还书成功标志"/> 
        <field value = "MagneticMedia"		length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否有附件 Y：有附件；N：无附件；U：未知"/> 
        <field value = "Alert"		        length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否提示告警"/> 
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "PermanentLocation"	length = "-1"	must="Y"	flag="AQ"		default=""	force = ""   brief ="所属馆的馆藏地代码,固定馆藏"/>   
		<field value = "MediaType"			length = "-1"	must="N"	flag="CK"		default=""	force = ""   brief ="图书类型（普通图书001或期刊）"/>
		<field value = "ItemCirtype"		length = "-1"	must="N"	flag="CT"		default=""	force = ""   brief ="图书流通类型"/>
		<field value = "ItemProperties"		length = "-1"	must="Y"	flag="CH"		default=""	force = ""   brief ="图书的简单信息：AT（是否有附件：Y表示有，N表示无）PR（图书价格）"/>
		<field value = "Fee"				length = "4"	must="N"	flag="CF"	    default=""	force = ""   brief ="读者欠费金额"/>
		<field value = "SortBin"			length = "-1"	must="N"	flag="CL"	    default=""	force = ""   brief ="排序规则号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="17" name="ItemInformation" type="Req" desc="图书查询">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"		default=""	force = ""   brief ="终端机密码"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="18" name="ItemInformation" type="Ans" desc="图书查询">
		<field value = "CirculationStatus"	length = "2"	must="Y"	flag=""			default=""	force = ""   brief ="图书流通状态"/>
		<field value = "SecurityMaker"		length = "2"	must="Y"	flag=""			default=""	force = ""   brief ="安全标记"/>
		<field value = "FeeType"			length = "2"	must="Y"	flag=""			default=""	force = ""   brief ="费用类型"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="N"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "ItemAuthor"			length = "-1"	must="Y"	flag="AW"		default=""	force = ""   brief ="著者"/>
		<field value = "ISBN"				length = "-1"	must="Y"	flag="AK"		default=""	force = ""   brief ="ISBN号"/>
		<field value = "Reservation"		length = "-1"	must="Y"	flag="RE"		default=""	force = ""   brief ="是否有预约"/>
		<field value = "MediaType"			length = "-1"	must="N"	flag="CK"		default=""	force = ""   brief ="图书类型（普通图书001或期刊）"/>
		<field value = "ItemProperties"		length = "-1"	must="Y"	flag="CH"		default=""	force = ""   brief ="图书的简单信息：AT（是否有附件：Y表示有，N表示无）PR（图书价格）"/>
		<field value = "CallNo"				length = "-1"	must="N"	flag="KC"	    default=""	force = ""   brief ="索书号"/>
        <field value = "DueDate"			length = "-1"	must="N"	flag="AH"	    default=""	force = ""   brief ="截止日期"/>
		<field value = "ItemCirtype"		length = "-1"	must="N"	flag="CT"		default=""	force = ""   brief ="图书流通类型"/>
		<field value = "OrgLocation"		length = "-1"	must="Y"	flag="AQ"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "CurrentLocation"	length = "-1"	must="N"	flag="AP"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "ReservationRfid"	length = "-1"	must="N"	flag="BG"		default=""	force = ""   brief ="预约分配了该条码图书的读者证号"/>
		<field value = "Publisher"		    length = "-1"	must="N"	flag="PB"		default=""	force = ""   brief ="出版社"/>
		<field value = "Subject"			length = "-1"	must="N"	flag="SJ"		default=""	force = ""   brief ="主题词"/>
		<field value = "PageNum"			length = "-1"	must="N"	flag="PG"		default=""	force = ""   brief ="总页数"/>
		<field value = "ShelfNo"			length = "-1"	must="N"	flag="KP"		default=""	force = ""   brief ="架位号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
    <message id="29" name="Renew" type="Req" desc="图书续借">
		<field value = "ThirdPartyAllowed"	length = "1"	must="Y"	flag=""			default="N"	force = ""   brief ="是否允许第三方续借"/>
		<field value = "NoBlock"		    length = " 1"	must="Y"	flag=""		    default="N"	force = ""   brief ="离线事务是否已处理"/>
        <field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>    
		<field value = "NbDueDate"		    length = "18"	must="Y"	flag=""	    	default=""	force = ""   brief ="离线事务发生时间"/>
        <field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
        <field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证代码"/>
        <field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者密码"/>
		<field value = "OperationCode"		length = "-1"	must="Y"	flag="CN"		default=""	force = ""   brief ="操作员代码/本设备流通地点代码"/>				
        <field value = "ItemIdentifier"		length = "-1"	must="N"	flag="AB"		default=""	force = ""   brief ="图书代码"/>
        <field value = "TitleIdentifier"	length = "-1"	must="N"	flag="AJ"		default=""	force = ""   brief ="主题标识"/>
        <field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"		default=""	force = ""   brief ="终端机密码"/>
        <field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="图书附加信息"/>
        <field value = "FeeAcknowledged"	length = "1"	must="N"	flag="BO"		default="N"	force = ""   brief ="额外费用信息告知"/>
		<field value = "JYBTransactionID"	length = "-1"	must="N"	flag="TD"	    default=""	force = ""   brief ="JYB交易ID"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="30" name="Renew" type="Ans" desc="图书续借">
		<field value = "OK"	                length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="操作是否成功"/>
		<field value = "RenewalOK"	        length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否可进行续借"/>
        <field value = "MagneticMedia"	    length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否有附件"/>
        <field value = "Desensitize"	    length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否取消敏感处理"/>
        <field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
        <field value = "InstitutionId"	    length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
        <field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证代码"/>
        <field value = "ItemIdentifier"	    length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书代码"/>
        <field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="主题标识"/>
        <field value = "DueDate"	        length = "-1"	must="Y"	flag="AH"		default=""	force = ""   brief ="应还日期"/>
        <field value = "FeeType"	        length = "2"	must="N"	flag="BT"		default=""	force = ""   brief ="费用类型"/>
        <field value = "SecurityInhibit"	length = "1"	must="N"	flag="CI"		default=""	force = ""   brief ="图书安全状态"/>
        <field value = "CurrencyType"	    length = "3"	must="N"	flag="BH"		default=""	force = ""   brief ="流通货币类型"/>
        <field value = "FeeAmount"	        length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="金额"/>
        <field value = "MediaType"	        length = "3"	must="N"	flag="CK"		default=""	force = ""   brief ="媒体类型"/>
        <field value = "ItemProperties"	    length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="图书附加信息"/>
        <field value = "TransactionId"	    length = "-1"	must="N"	flag="BK"		default=""	force = ""   brief ="事务ID"/>
        <field value = "ScreenMessage"	    length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
        <field value = "PrintLine"	        length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义打印信息"/>
        <field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
    <message id="59" name="Deposit" type="Req" desc="存押金">
		<field value = "ip"			length = "-1"   must= "Y"   flag=""     default="***************" force = "" brief="ip" />
		<field value = "port"       length = "-1"   must= "Y"   flag=""     default="80"    force=""     brief="port" /> 
		<field value = "op"			length = "-1"	must="Y"	flag=""		default="bor-info"	force = ""   brief ="操作方式"/>
		<field value = "library"	length = "-1"	must="Y"	flag=""		default="nlc50"	force = ""   brief ="馆代码"/>
		<field value = "bor_id"			length = "-1"	must="Y"	flag=""		default="8888888888888888"	force = ""   brief ="读者特征号"/>	
		<field value = "update_flag"			length = "-1"	must="Y"	flag=""		default="update-bor"	force = ""   brief ="更新标记号"/>			
		<field value = "USER_NAME"		length = "-1"	must="N"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="用户名"/>
		<field value = "USER_PASSWORD"	length = "-1"	must="Y"	flag=""	default="WWW-ZZBZ1"	force = ""   brief ="读者证号"/>
		<field value = "xml_full_req"   length = "-1"   must="Y"	flag="" default="" force="" brief="未知字段" />
		<field value = "post_name"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者姓名"/>
		<field value = "post_sex"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者性别"/>
		<field value = "post_birth"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者生日"/>
		<field value = "post_patrontypecode"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者职业类型"/>
		<field value = "post_patroncurtype"	length = "-1"	must="Y"	flag=""	default=""	force = ""   brief ="读者当前存在类型"/>
		<field value = "post_expiredate"		length = "-1"   must="Y"    flag="" default="xxx"  force = ""   brief = "读者截至日期"/>	
		<field value = "postmoney" 	length = "-1"   must="Y" 	flag="" default=""     force = ""	brief = "押金总额" />	
		<field value = "post_chargeType" 	length = "-1"   must="Y" 	flag="" default=""     force = ""	brief = "读者证类型(中文/外文)" />
		<field value = "post_localIp" 	length = "-1"   must="Y" 	flag="" default=""     force = ""	brief = "本机ip" />
		<field value = "post_PayType"	length = "-1"	must="Y"	flag=""	default="CASH"	force = ""   brief ="支付类型"/>
		<field value = "post_PayNo"		length = "-1"	must="Y"	flag=""	default=""	force = ""   brief ="交易单号"/>
	</message>
	<message id="60" name="Deposit" type="Ans" desc="存押金">
		<field value = "OK"	                length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="操作是否成功"/>
        <field value = "InstitutionId"	    length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
        <field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证代码"/>
        <field value = "FeeAmount"	        length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="金额"/>
		<field value = "ScreenMessage"			length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>	
	</message>

</sip>