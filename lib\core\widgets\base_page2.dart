import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';

class BasePage2 extends StatelessWidget {
  final Widget? topWrapper;
  final Widget? mainWrapper;


  const BasePage2({
    super.key, 
    this.topWrapper, 
    this.mainWrapper, 
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Get.back();
        },
        child: const Icon(Icons.arrow_back),
      ),
      body: Center(
        child: Container(
          width: WindowUtil.width,
          height: WindowUtil.height,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AssetUtil.fullPath('base_bg2')),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              topWrapper?? const SizedBox(),
              if(mainWrapper != null) Expanded(
                child: mainWrapper!,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 