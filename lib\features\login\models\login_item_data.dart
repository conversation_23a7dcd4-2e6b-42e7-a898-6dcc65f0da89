import 'package:flutter/material.dart';
import 'package:seasetting/seasetting.dart';

class LoginItemData {
  final String? title;
  final AuthLoginType? type;
  final String? icon;
  final String? activeIcon;
  final Color? activeColor;

  const LoginItemData({
    this.title,
    this.type,
    this.icon,
    this.activeIcon,
    this.activeColor,
  });

  LoginItemData copyWith({
    String? title,
    AuthLoginType? type,
    String? icon,
    String? activeIcon,
    Color? activeColor,
  }) {
    return LoginItemData(
      title: title ?? this.title,
      type: type ?? this.type,
      icon: icon ?? this.icon,
      activeIcon: activeIcon ?? this.activeIcon,
      activeColor: activeColor ?? this.activeColor,
    );
  }
} 