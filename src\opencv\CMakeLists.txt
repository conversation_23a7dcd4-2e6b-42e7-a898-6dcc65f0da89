cmake_minimum_required(VERSION 3.10)
project(face_detection)

# 查找OpenCV包
find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})

# 已移除CURL依赖

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 设置Windows特定选项
if(WIN32)
    # 设置为动态库而不是静态库
    add_library(face_detector SHARED 
        face_detection.cpp
        # 注释掉camera_capture.cpp以避免符号冲突
        # camera_capture.cpp
    )

    # Windows平台需要使用__declspec(dllexport)
    target_compile_definitions(face_detector PRIVATE FACE_DETECTOR_EXPORTS)

    # 使用与OpenCV相同的运行时库（多线程DLL，/MD或/MDd）
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set_target_properties(face_detector PROPERTIES 
            COMPILE_FLAGS "/MDd" 
            LINK_FLAGS "/NODEFAULTLIB:libcmtd.lib"
        )
    else()
        set_target_properties(face_detector PROPERTIES 
            COMPILE_FLAGS "/MD" 
            LINK_FLAGS "/NODEFAULTLIB:libcmt.lib"
        )
    endif()

    # 链接库
    target_link_libraries(face_detector 
        ${OpenCV_LIBS}
    )

    # 设置C++11标准
    set_property(TARGET face_detector PROPERTY CXX_STANDARD 11)

    # 输出dll到Flutter项目的适当位置
    set_target_properties(face_detector PROPERTIES
        OUTPUT_NAME "libface_detector"
    )

    # 安装规则 (修改为根据配置安装) -- 移除 install 命令
    # install(TARGETS face_detector 
    #     RUNTIME DESTINATION ../../windows/runner/$<CONFIG>/data COMPONENT Runtime
    #     LIBRARY DESTINATION ../../windows/runner/$<CONFIG>/data COMPONENT Runtime
    #     ARCHIVE DESTINATION ../../windows/runner/$<CONFIG>/data COMPONENT Runtime
    # )
else()
    message(FATAL_ERROR "This CMake file is configured for Windows platform only.")
endif()

# 输出信息
message(STATUS "OpenCV库: ${OpenCV_LIBS}")