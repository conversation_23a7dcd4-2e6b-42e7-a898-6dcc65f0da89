{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/soft/Microsoft Visual Studio/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "D:/soft/Microsoft Visual Studio/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "D:/soft/Microsoft Visual Studio/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "D:/soft/Microsoft Visual Studio/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.29"}, "version": {"isDirty": false, "major": 3, "minor": 29, "patch": 5, "string": "3.29.5-msvc4", "suffix": "msvc4"}}, "objects": [{"jsonFile": "codemodel-v2-0005176a9e89b13cfff9.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-7a79b7492889534d6880.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0823d1a9f6b91cceec7a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-cb8d5b49387e92f0dcf6.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-7a79b7492889534d6880.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0823d1a9f6b91cceec7a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "codemodel-v2-0005176a9e89b13cfff9.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-cb8d5b49387e92f0dcf6.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}}}