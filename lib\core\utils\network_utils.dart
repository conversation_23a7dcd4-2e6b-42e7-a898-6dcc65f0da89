import 'dart:async';
import 'dart:io';

/// 网络工具类
/// 提供网络连接检测和重试机制
class NetworkUtils {
  static final NetworkUtils _instance = NetworkUtils._internal();
  static NetworkUtils get instance => _instance;
  NetworkUtils._internal();

  // 网络状态
  bool _isNetworkAvailable = true;
  final StreamController<bool> _networkStatusController = 
      StreamController<bool>.broadcast();

  /// 网络状态流
  Stream<bool> get networkStatusStream => _networkStatusController.stream;

  /// 当前网络是否可用
  bool get isNetworkAvailable => _isNetworkAvailable;

  /// 检查特定主机和端口的连接
  /// [host] 主机地址
  /// [port] 端口号
  /// [timeout] 超时时间
  Future<bool> checkConnection({
    required String host,
    required int port,
    Duration timeout = const Duration(seconds: 5),
  }) async {
    try {
      final socket = await Socket.connect(host, port, timeout: timeout);
      await socket.close();
      return true;
    } catch (e) {
      print('连接检查失败 $host:$port - $e');
      return false;
    }
  }

  /// 检查WebSocket连接
  /// [url] WebSocket URL
  /// [timeout] 超时时间
  Future<bool> checkWebSocketConnection({
    required String url,
    Duration timeout = const Duration(seconds: 5),
  }) async {
    try {
      final uri = Uri.parse(url);
      final host = uri.host;
      final port = uri.port;
      
      return await checkConnection(
        host: host,
        port: port,
        timeout: timeout,
      );
    } catch (e) {
      print('WebSocket连接检查失败 $url - $e');
      return false;
    }
  }

  /// 检查************服务器的连接状态
  Future<Map<int, bool>> check172ServerPorts() async {
    const host = '************';
    const ports = [4822, 4823, 9091];
    
    final results = <int, bool>{};
    
    for (final port in ports) {
      results[port] = await checkConnection(
        host: host,
        port: port,
        timeout: const Duration(seconds: 3),
      );
    }
    
    return results;
  }

  /// 启动网络状态监控
  void startNetworkMonitoring() {
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      final wasAvailable = _isNetworkAvailable;
      
      // 检查基本网络连接
      _isNetworkAvailable = await _checkBasicNetworkConnection();
      
      // 如果状态发生变化，通知监听者
      if (wasAvailable != _isNetworkAvailable) {
        _networkStatusController.add(_isNetworkAvailable);
        print('网络状态变化: ${_isNetworkAvailable ? '已连接' : '已断开'}');
      }
    });
  }

  /// 检查基本网络连接
  Future<bool> _checkBasicNetworkConnection() async {
    try {
      // 尝试连接多个常用服务器
      final results = await Future.wait([
        checkConnection(host: '*******', port: 53, timeout: const Duration(seconds: 3)),
        checkConnection(host: '***************', port: 53, timeout: const Duration(seconds: 3)),
        checkConnection(host: 'localhost', port: 80, timeout: const Duration(seconds: 2)),
      ]);
      
      // 如果任何一个连接成功，认为网络可用
      return results.any((result) => result);
    } catch (e) {
      print('网络连接检查失败: $e');
      return false;
    }
  }

  /// 重试网络操作
  /// [operation] 要重试的操作
  /// [maxRetries] 最大重试次数
  /// [delay] 重试间隔
  Future<T?> retryNetworkOperation<T>({
    required Future<T> Function() operation,
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 2),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (e.toString().contains('SocketException') ||
            e.toString().contains('WebSocket') ||
            e.toString().contains('远程计算机拒绝网络连接')) {
          
          if (attempts < maxRetries) {
            print('网络操作失败，第$attempts次重试 (最多$maxRetries次): $e');
            await Future.delayed(delay);
            continue;
          } else {
            print('网络操作最终失败，已重试$maxRetries次: $e');
            throw e;
          }
        } else {
          // 对于非网络错误，直接抛出
          throw e;
        }
      }
    }
    
    return null;
  }

  /// 检查是否为网络相关错误
  static bool isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('socket') ||
           errorString.contains('websocket') ||
           errorString.contains('connection') ||
           errorString.contains('network') ||
           errorString.contains('timeout') ||
           errorString.contains('远程计算机拒绝网络连接') ||
           errorString.contains('连接超时') ||
           errorString.contains('网络');
  }

  /// 检查是否为动态库相关错误
  static bool isLibraryError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('failed to lookup symbol') ||
           errorString.contains('error code 127') ||
           errorString.contains('iclosecard') ||
           errorString.contains('dynamic library') ||
           errorString.contains('dll');
  }

  /// 释放资源
  void dispose() {
    _networkStatusController.close();
  }
} 