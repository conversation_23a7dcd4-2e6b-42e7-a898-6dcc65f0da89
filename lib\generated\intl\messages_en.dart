// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "AccountPassword":
            MessageLookupByLibrary.simpleMessage("Account/Password"),
        "IncorrectPassword":
            MessageLookupByLibrary.simpleMessage("Incorrect password"),
        "Predeposit": MessageLookupByLibrary.simpleMessage("Prepaid (Yuan)"),
        "ReaderCardVerification":
            MessageLookupByLibrary.simpleMessage("Reader Card Verification"),
        "ScanAuth":
            MessageLookupByLibrary.simpleMessage("Scan QR Code Authentication"),
        "Sending": MessageLookupByLibrary.simpleMessage("Sending..."),
        "accountBalance":
            MessageLookupByLibrary.simpleMessage("Account Balance"),
        "accountInput": MessageLookupByLibrary.simpleMessage(
            "Enter patron ID or ID card number"),
        "accountLogin": MessageLookupByLibrary.simpleMessage("Account Login"),
        "accountRequired":
            MessageLookupByLibrary.simpleMessage("Account cannot be empty"),
        "addressInput": MessageLookupByLibrary.simpleMessage("Address"),
        "agreement": MessageLookupByLibrary.simpleMessage(
            "I have read and agreed to the above"),
        "alipay": MessageLookupByLibrary.simpleMessage("Alipay"),
        "alipayCreditRegister": MessageLookupByLibrary.simpleMessage(
            "Alipay Credit Score Registration"),
        "alipay_login":
            MessageLookupByLibrary.simpleMessage("Login with Alipay"),
        "alipay_scan_guide": MessageLookupByLibrary.simpleMessage(
            "Open Alipay App and Click Scan Icon in Top Left Corner"),
        "applyCard": MessageLookupByLibrary.simpleMessage("Apply for Card"),
        "at": MessageLookupByLibrary.simpleMessage("at"),
        "auth_fail_advice": MessageLookupByLibrary.simpleMessage(
            "If authorization fails, please check the failure reason on the page or contact Sesame Credit customer service"),
        "availableInLibrary":
            MessageLookupByLibrary.simpleMessage("In Library"),
        "available_borrow_count":
            MessageLookupByLibrary.simpleMessage("Available Borrowing Count"),
        "available_for_borrow":
            MessageLookupByLibrary.simpleMessage("Available for Borrowing"),
        "barcode": MessageLookupByLibrary.simpleMessage("Barcode"),
        "barcodeError":
            MessageLookupByLibrary.simpleMessage("Barcode parsing failed"),
        "barcode_number":
            MessageLookupByLibrary.simpleMessage("Barcode Number"),
        "birthDateInput": MessageLookupByLibrary.simpleMessage("Date of Birth"),
        "bookRecommendations":
            MessageLookupByLibrary.simpleMessage("Recommended Books for You"),
        "bookRecommendationsTitle":
            MessageLookupByLibrary.simpleMessage("Book Recommendations"),
        "bookTitle": MessageLookupByLibrary.simpleMessage("Book Title"),
        "bookTitleShort": MessageLookupByLibrary.simpleMessage("Title"),
        "book_overdue":
            MessageLookupByLibrary.simpleMessage("This Item is Overdue"),
        "booksToReturn":
            MessageLookupByLibrary.simpleMessage("Books to Return"),
        "borrowBook": MessageLookupByLibrary.simpleMessage("Borrow Book"),
        "borrowFailure": MessageLookupByLibrary.simpleMessage("Borrow Failure"),
        "borrowRequestFailed":
            MessageLookupByLibrary.simpleMessage("Borrow Request Failed"),
        "borrowSuccess": MessageLookupByLibrary.simpleMessage("Borrow Success"),
        "borrow_fail_request":
            MessageLookupByLibrary.simpleMessage("Borrow Request Failed"),
        "borrow_fail_rewrite_security": MessageLookupByLibrary.simpleMessage(
            "Borrow Failed, Security Tag Rewrite Failed"),
        "borrow_success": MessageLookupByLibrary.simpleMessage(
            "Document Successfully Borrowed"),
        "borrow_with_alipay_credit": MessageLookupByLibrary.simpleMessage(
            "With a Sesame Credit Score of 550 or above, scan the QR code on the screen to borrow books"),
        "borrowableCopies":
            MessageLookupByLibrary.simpleMessage("Available Copies"),
        "borrowed": MessageLookupByLibrary.simpleMessage("Borrowed"),
        "borrowedCopies":
            MessageLookupByLibrary.simpleMessage("Borrowed Copies"),
        "borrowing": MessageLookupByLibrary.simpleMessage("Borrowing"),
        "borrowingInProgress":
            MessageLookupByLibrary.simpleMessage("Borrowing in Progress"),
        "businessTableFail": MessageLookupByLibrary.simpleMessage(
            "Failed to process business system table"),
        "callNumber": MessageLookupByLibrary.simpleMessage("Call Number"),
        "captchaInput": MessageLookupByLibrary.simpleMessage("Enter captcha"),
        "captchaRequired":
            MessageLookupByLibrary.simpleMessage("Captcha cannot be empty"),
        "cardApplicationSuccess": MessageLookupByLibrary.simpleMessage(
            "You have successfully applied for a reader card"),
        "cardCreationFail":
            MessageLookupByLibrary.simpleMessage("Card creation failed"),
        "cardCreationSuccess":
            MessageLookupByLibrary.simpleMessage("Card creation successful"),
        "cardIdExhausted": MessageLookupByLibrary.simpleMessage(
            "All reader card IDs have been used"),
        "cardIdExhaustedContact": MessageLookupByLibrary.simpleMessage(
            "All reader card IDs have been used, please contact library staff"),
        "cardIdFail": MessageLookupByLibrary.simpleMessage(
            "Failed to retrieve reader card ID"),
        "cardIdFailContact": MessageLookupByLibrary.simpleMessage(
            "Failed to retrieve reader card ID, please contact library staff"),
        "cardIssuanceError":
            MessageLookupByLibrary.simpleMessage("Card issuance error"),
        "cardLefts": MessageLookupByLibrary.simpleMessage("Remaining cards"),
        "cardLogin": MessageLookupByLibrary.simpleMessage("Reader Card Login"),
        "cardMachineError": MessageLookupByLibrary.simpleMessage(
            "Card machine error, please contact library staff"),
        "cardRequestFail": MessageLookupByLibrary.simpleMessage(
            "Failed to request card creation"),
        "card_not_exist":
            MessageLookupByLibrary.simpleMessage("Reader Card Does Not Exist"),
        "cert_number":
            MessageLookupByLibrary.simpleMessage("Certificate Number"),
        "characters": MessageLookupByLibrary.simpleMessage("Characters"),
        "citizenCardLogin":
            MessageLookupByLibrary.simpleMessage("Citizen Card Login"),
        "clear": MessageLookupByLibrary.simpleMessage("Clear"),
        "collectReaderCard": MessageLookupByLibrary.simpleMessage(
            "Please collect your reader card from the dispenser"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "confirmBorrow": MessageLookupByLibrary.simpleMessage("Confirm Borrow"),
        "confirmButton": MessageLookupByLibrary.simpleMessage("Confirm"),
        "confirm_return":
            MessageLookupByLibrary.simpleMessage("Confirm Return"),
        "continueBorrowing":
            MessageLookupByLibrary.simpleMessage("Continue Borrowing"),
        "continueButton": MessageLookupByLibrary.simpleMessage("Continue"),
        "continue_returning":
            MessageLookupByLibrary.simpleMessage("Continue Returning"),
        "copy": MessageLookupByLibrary.simpleMessage("items"),
        "currencyUnit": MessageLookupByLibrary.simpleMessage("Yuan"),
        "currentDeposit":
            MessageLookupByLibrary.simpleMessage("Current Deposit"),
        "current_borrow_count":
            MessageLookupByLibrary.simpleMessage("Current Borrowing Count"),
        "debt": MessageLookupByLibrary.simpleMessage("Debt"),
        "debt_amount":
            MessageLookupByLibrary.simpleMessage("Debt Amount (Yuan)"),
        "defaultPassword":
            MessageLookupByLibrary.simpleMessage("Default Password"),
        "deposit": MessageLookupByLibrary.simpleMessage("Deposit"),
        "depositInput": MessageLookupByLibrary.simpleMessage("Deposit"),
        "depositNotSupported":
            MessageLookupByLibrary.simpleMessage("Deposit not supported"),
        "detail_info":
            MessageLookupByLibrary.simpleMessage("Detailed Information"),
        "details": MessageLookupByLibrary.simpleMessage("Details"),
        "dueDate": MessageLookupByLibrary.simpleMessage("Due Date"),
        "duplicateRegistration": MessageLookupByLibrary.simpleMessage(
            "You have already registered a reader card, please do not register again"),
        "eSSCRegistration": MessageLookupByLibrary.simpleMessage(
            "Electronic Social Security Card Registration"),
        "eSocialSecurityCardLogin": MessageLookupByLibrary.simpleMessage(
            "Electronic Social Security Card Login"),
        "electronicSocialSecurityCard": MessageLookupByLibrary.simpleMessage(
            "Electronic Social Security Card"),
        "enterPhoneNumber": MessageLookupByLibrary.simpleMessage(
            "For better service, please enter your mobile number"),
        "ethnicityInput": MessageLookupByLibrary.simpleMessage("Ethnicity"),
        "exit": MessageLookupByLibrary.simpleMessage("Exit"),
        "expirationInput":
            MessageLookupByLibrary.simpleMessage("Expiration Date"),
        "externalCardRegistration": MessageLookupByLibrary.simpleMessage(
            "External Card Registration (for Card Application)"),
        "faceMatchRegister":
            MessageLookupByLibrary.simpleMessage("Face Matching Registration"),
        "face_recognition":
            MessageLookupByLibrary.simpleMessage("Face Recognition"),
        "face_recognition_login":
            MessageLookupByLibrary.simpleMessage("Login with Face Recognition"),
        "face_to_camera":
            MessageLookupByLibrary.simpleMessage("Please Face the Camera"),
        "failure": MessageLookupByLibrary.simpleMessage("Failure"),
        "free_listening":
            MessageLookupByLibrary.simpleMessage("Free Listening"),
        "genderInput": MessageLookupByLibrary.simpleMessage("Gender"),
        "homePage": MessageLookupByLibrary.simpleMessage("Home Page"),
        "huiwen_qr_code":
            MessageLookupByLibrary.simpleMessage("HuiWen QR Code"),
        "idCardLogin": MessageLookupByLibrary.simpleMessage("ID Card Login"),
        "idNumberInput": MessageLookupByLibrary.simpleMessage("ID Number"),
        "identityCard": MessageLookupByLibrary.simpleMessage("ID Card"),
        "ineligibleAge": MessageLookupByLibrary.simpleMessage("Age Ineligible"),
        "ineligibleArea":
            MessageLookupByLibrary.simpleMessage("Area Ineligible"),
        "inputPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter Mobile Number"),
        "insertMoney": MessageLookupByLibrary.simpleMessage(
            "Please insert the banknote into the slot"),
        "invalidCredentials": MessageLookupByLibrary.simpleMessage(
            "Reader ID does not exist or incorrect password"),
        "issuingAuthInput":
            MessageLookupByLibrary.simpleMessage("Issuing Authority"),
        "loginButton": MessageLookupByLibrary.simpleMessage("Login"),
        "login_method": MessageLookupByLibrary.simpleMessage("Login Method"),
        "manualInput": MessageLookupByLibrary.simpleMessage("Manual Input"),
        "manualInputLogin":
            MessageLookupByLibrary.simpleMessage("Manual Input Login"),
        "manualInputRegistration":
            MessageLookupByLibrary.simpleMessage("Manual Input Registration"),
        "moneyAcceptanceInfo": MessageLookupByLibrary.simpleMessage(
            "This machine only accepts RMB 10, 20, 50, and 100 yuan. No change is provided. Any remaining balance will be credited to your prepaid account."),
        "nameInput": MessageLookupByLibrary.simpleMessage("Name"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "noBooksAvailable":
            MessageLookupByLibrary.simpleMessage("No Books Available"),
        "noCardTip": MessageLookupByLibrary.simpleMessage(
            "The card has been used up and the reader\'s card cannot be processed temporarily. Please contact the staff！"),
        "noDeposit": MessageLookupByLibrary.simpleMessage("Deposit-free"),
        "no_books_to_return":
            MessageLookupByLibrary.simpleMessage("No Books to Return"),
        "note": MessageLookupByLibrary.simpleMessage("Note"),
        "overdue": MessageLookupByLibrary.simpleMessage("Overdue"),
        "overdue_count":
            MessageLookupByLibrary.simpleMessage("Overdue Document Count"),
        "passwordFormatError":
            MessageLookupByLibrary.simpleMessage("Incorrect password format"),
        "passwordInput": MessageLookupByLibrary.simpleMessage("Enter Password"),
        "passwordLengthError":
            MessageLookupByLibrary.simpleMessage("Incorrect password length"),
        "paymentCodeConfig":
            MessageLookupByLibrary.simpleMessage("Payment Code Configuration"),
        "phoneError":
            MessageLookupByLibrary.simpleMessage("Invalid phone number"),
        "phoneForService": MessageLookupByLibrary.simpleMessage(
            "For better service, please enter your phone number"),
        "phoneInput":
            MessageLookupByLibrary.simpleMessage("Enter your phone number"),
        "phoneOrCaptchaRequired": MessageLookupByLibrary.simpleMessage(
            "Phone number or captcha cannot be empty"),
        "place": MessageLookupByLibrary.simpleMessage("Place"),
        "placeBookOnSensor": MessageLookupByLibrary.simpleMessage(
            "Please place the document to be returned on the sensor area"),
        "placeCardOnSensor": MessageLookupByLibrary.simpleMessage(
            "Please place the reader card on the sensor area"),
        "placeCardOnSensorForCard": MessageLookupByLibrary.simpleMessage(
            "Please place the card on the sensor area"),
        "placeDocument": MessageLookupByLibrary.simpleMessage(
            "Place Document on Sensing Area"),
        "placeIdCardOnSensor": MessageLookupByLibrary.simpleMessage(
            "Please place the ID card on the sensor area"),
        "prepaid_balance":
            MessageLookupByLibrary.simpleMessage("Prepaid Balance"),
        "printReceipt": MessageLookupByLibrary.simpleMessage(
            "Do you want to print a receipt?"),
        "processingCard": MessageLookupByLibrary.simpleMessage(
            "Processing reader card, please wait..."),
        "qrCode": MessageLookupByLibrary.simpleMessage(" QR Code"),
        "qrCodeLogin": MessageLookupByLibrary.simpleMessage("QR Code Login"),
        "qrCodeRegistration": MessageLookupByLibrary.simpleMessage(
            "QR Code Display Registration"),
        "query": MessageLookupByLibrary.simpleMessage("Query"),
        "quit": MessageLookupByLibrary.simpleMessage("Quit"),
        "readerCardAppliedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "You have successfully applied for a reader card"),
        "readerCardID": MessageLookupByLibrary.simpleMessage("Reader Card ID"),
        "readerFailure": MessageLookupByLibrary.simpleMessage(
            "Reader initialization failed"),
        "readerNotFound":
            MessageLookupByLibrary.simpleMessage("Reader not configured"),
        "readerTableFail": MessageLookupByLibrary.simpleMessage(
            "Failed to process reader table"),
        "reader_card": MessageLookupByLibrary.simpleMessage("Reader Card"),
        "reader_closing": MessageLookupByLibrary.simpleMessage(
            "Reader is Closing, Please Try Again Later"),
        "regenerate": MessageLookupByLibrary.simpleMessage("Regenerate"),
        "register": MessageLookupByLibrary.simpleMessage(" Register"),
        "remainingTime": MessageLookupByLibrary.simpleMessage("Remaining Time"),
        "renew": MessageLookupByLibrary.simpleMessage("Renew"),
        "renewBook": MessageLookupByLibrary.simpleMessage("Renew Book"),
        "renewInProgress":
            MessageLookupByLibrary.simpleMessage("Renewal in progress..."),
        "renewOrQuery": MessageLookupByLibrary.simpleMessage("Renew/Query"),
        "renew_fail": MessageLookupByLibrary.simpleMessage("Renewal Failed"),
        "renew_success":
            MessageLookupByLibrary.simpleMessage("Renewal Success"),
        "requestingInfo": MessageLookupByLibrary.simpleMessage(
            "Requesting reader information"),
        "resend": MessageLookupByLibrary.simpleMessage("s"),
        "returnBook": MessageLookupByLibrary.simpleMessage("Return Book"),
        "returnDate": MessageLookupByLibrary.simpleMessage("Return Date"),
        "returnFailure": MessageLookupByLibrary.simpleMessage(
            "Return Failed, Request Failed"),
        "returnFailureRewrite": MessageLookupByLibrary.simpleMessage(
            "Return Failed, Security Tag Rewrite Failed"),
        "returnSuccess": MessageLookupByLibrary.simpleMessage(
            "Document Successfully Returned"),
        "return_date": MessageLookupByLibrary.simpleMessage("Return Date"),
        "return_date_recorded":
            MessageLookupByLibrary.simpleMessage("Recorded Return Date"),
        "return_fail": MessageLookupByLibrary.simpleMessage("Return Failed"),
        "return_success":
            MessageLookupByLibrary.simpleMessage("Return Success"),
        "returningInProgress": MessageLookupByLibrary.simpleMessage(
            "Returning in Progress, Do Not Repeat"),
        "returningStatus":
            MessageLookupByLibrary.simpleMessage("Returning in progress..."),
        "scanESCBarcode": MessageLookupByLibrary.simpleMessage(
            "Scan your electronic social security card QR code"),
        "scanPayRegister":
            MessageLookupByLibrary.simpleMessage("Scan to Pay Registration"),
        "scanToListen": MessageLookupByLibrary.simpleMessage("Scan to Listen"),
        "scanYour": MessageLookupByLibrary.simpleMessage("Scan your"),
        "scan_e_social_card": MessageLookupByLibrary.simpleMessage(
            "Please Scan Your Electronic Social Security Card"),
        "scan_huiwen_qr": MessageLookupByLibrary.simpleMessage(
            "Please Scan Your HuiWen QR Code"),
        "scan_wechat_qr": MessageLookupByLibrary.simpleMessage(
            "Please Scan Your WeChat QR Code"),
        "secondGenIDCardRegistration": MessageLookupByLibrary.simpleMessage(
            "Second Generation ID Card Registration"),
        "selectCardType": MessageLookupByLibrary.simpleMessage(
            "Please select the button below to apply for the corresponding type of library card"),
        "selectOperationType":
            MessageLookupByLibrary.simpleMessage("Select Operation Type"),
        "selectReaderCardType":
            MessageLookupByLibrary.simpleMessage("Select Reader Card Type"),
        "selectRegistrationMethod":
            MessageLookupByLibrary.simpleMessage("Select Registration Method"),
        "select_all": MessageLookupByLibrary.simpleMessage("Select All"),
        "selfServiceMachine": MessageLookupByLibrary.simpleMessage(
            "Self-Service Borrowing and Returning Machine"),
        "sendVerificationCode":
            MessageLookupByLibrary.simpleMessage("Send Verification Code"),
        "sensingArea": MessageLookupByLibrary.simpleMessage("Sensing Area"),
        "sequenceNumber": MessageLookupByLibrary.simpleMessage("No"),
        "serviceNotice": MessageLookupByLibrary.simpleMessage(
            "Self-service card issuance notice"),
        "shanghaiShenShenCodeRegister": MessageLookupByLibrary.simpleMessage(
            "Shanghai ShenShen Code Registration"),
        "socialSecurityCard":
            MessageLookupByLibrary.simpleMessage("Social Security Card"),
        "socialSecurityCardLogin":
            MessageLookupByLibrary.simpleMessage("Social Security Card Login"),
        "spaceLogin": MessageLookupByLibrary.simpleMessage(" Login"),
        "sscRegistration": MessageLookupByLibrary.simpleMessage(
            "Social Security Card Registration"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "submittingData": MessageLookupByLibrary.simpleMessage(
            "Submitting data, please wait..."),
        "success": MessageLookupByLibrary.simpleMessage("Success"),
        "takeCardAndBooks": MessageLookupByLibrary.simpleMessage(
            "Please take your reader card and borrowed books, and verify the printed receipt!"),
        "take_away_card_books": MessageLookupByLibrary.simpleMessage(
            "Please Take Your Reader Card and Borrowed Books"),
        "tencentEPassRegister":
            MessageLookupByLibrary.simpleMessage("Tencent E-Pass Registration"),
        "unsupportedCardType":
            MessageLookupByLibrary.simpleMessage("Unsupported card type"),
        "unsupportedCardTypeContact": MessageLookupByLibrary.simpleMessage(
            "Unsupported card type, please contact library staff"),
        "unsupportedType":
            MessageLookupByLibrary.simpleMessage("Unsupported type"),
        "validCaptcha":
            MessageLookupByLibrary.simpleMessage("Enter correct captcha"),
        "validPhoneInput":
            MessageLookupByLibrary.simpleMessage("Enter valid phone number"),
        "view_details": MessageLookupByLibrary.simpleMessage("View Details"),
        "waitingResult":
            MessageLookupByLibrary.simpleMessage("Waiting for result"),
        "wechatLogin": MessageLookupByLibrary.simpleMessage("WeChat Login"),
        "wechatScanAuth": MessageLookupByLibrary.simpleMessage(
            "WeChat Scan QR Code Authentication"),
        "wechat_qr_code":
            MessageLookupByLibrary.simpleMessage("WeChat QR Code"),
        "wechat_scan": MessageLookupByLibrary.simpleMessage("Scan with WeChat"),
        "yes": MessageLookupByLibrary.simpleMessage("Yes"),
        "yourPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Your Phone Number")
      };
}
