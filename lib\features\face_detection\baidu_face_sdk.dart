import 'dart:async';
import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';
import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'sdk_config_manager.dart';

// 人脸特征结构体封装
class FaceFeature {
  final List<double> data;
  
  FaceFeature(this.data);
  
  @override
  String toString() {
    return 'FaceFeature(size: ${data.length})';
  }
}

// 人脸框结构体
class FaceBoxInfo {
  final int index;
  final double centerX;
  final double centerY;
  final double width;
  final double height;
  final double score;
  
  FaceBoxInfo({
    required this.index,
    required this.centerX,
    required this.centerY,
    required this.width,
    required this.height,
    required this.score,
  });
  
  @override
  String toString() {
    return 'FaceBoxInfo(index: $index, center: ($centerX, $centerY), size: ${width}x$height, score: $score)';
  }
}

// 人脸识别结果
class FaceRecognitionResult {
  final String userId;
  final String groupId;
  final String? userInfo;
  final double score;
  
  FaceRecognitionResult({
    required this.userId,
    required this.groupId,
    this.userInfo,
    required this.score,
  });
  
  @override
  String toString() {
    return 'FaceRecognitionResult(userId: $userId, groupId: $groupId, score: $score)';
  }
}

// 百度人脸识别SDK封装
class BaiduFaceSDK {
  static const String _dllName = 'BaiduFaceApi.dll';
  static DynamicLibrary? _lib;
  
  // 是否已初始化
  static bool _isInitialized = false;
  
  // 函数指针
  static late final int Function(Pointer<Utf8>) _sdkInit;
  static late final void Function(Pointer<Pointer<Utf8>>) _getDeviceId;
  static late final void Function(Pointer<Pointer<Utf8>>) _sdkVersion;
  static late final bool Function() _isAuth;
  
  // 人脸检测和特征提取
  static late final int Function(Pointer<Void>, Pointer<Void>, Pointer<Int32>, Pointer<Void>, int) _detect;
  static late final int Function(Pointer<Void>, Pointer<Void>, Pointer<Void>, Pointer<Void>, int) _faceFeature;
  
  // 人脸库管理
  static late final void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>) _userAdd;
  static late final void Function(Pointer<Pointer<Utf8>>, Pointer<Utf8>, Pointer<Utf8>) _userDelete;
  static late final bool Function() _loadDbFace;
  static late final int Function(Pointer<Utf8>) _dbFaceCount;
  
  // 人脸识别
  static late final void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, Pointer<Utf8>, Pointer<Utf8>, int) _identify;
  static late final void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, int) _identifyWithAll;
  
  // 初始化SDK
  static Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      if (Platform.isWindows) {
        // Windows平台下加载动态库
        final configManager = SdkConfigManager.instance;
        final sdkPath = await configManager.getSdkPath();
        
        final List<String> possiblePaths = [
          // 优先从配置的SDK路径加载
          path.join(sdkPath, 'x64', _dllName),
          
          // 尝试从应用运行目录加载（向后兼容）
          path.join(Directory.current.path, 'data', _dllName),
          
          // 尝试从release目录加载（开发环境）
          path.join(Directory.current.path, 'windows', 'runner', 'Release', _dllName),
          
          // 尝试从debug目录加载（开发环境）
          path.join(Directory.current.path, 'windows', 'runner', 'Debug', _dllName),
        ];
        
        for (final libPath in possiblePaths) {
          try {
            if (File(libPath).existsSync()) {
              print('尝试加载百度人脸SDK: $libPath');
              _lib = DynamicLibrary.open(libPath);
              break;
            }
          } catch (e) {
            print('尝试加载 $libPath 失败: $e');
          }
        }
        
        if (_lib == null) {
          throw Exception('无法加载百度人脸识别SDK, 请确认BaiduFaceApi.dll已正确安装');
        }
      } else {
        throw Exception('百度人脸识别SDK目前仅支持Windows平台');
      }
      
      // 绑定函数
      _sdkInit = _lib!
          .lookup<NativeFunction<Int32 Function(Pointer<Utf8>)>>('sdk_init')
          .asFunction();
          
      _getDeviceId = _lib!
          .lookup<NativeFunction<Void Function(Pointer<Pointer<Utf8>>)>>('get_device_id')
          .asFunction();
          
      _sdkVersion = _lib!
          .lookup<NativeFunction<Void Function(Pointer<Pointer<Utf8>>)>>('sdk_version')
          .asFunction();
          
      _isAuth = _lib!
          .lookup<NativeFunction<Bool Function()>>('is_auth')
          .asFunction();
          
      _detect = _lib!
          .lookup<NativeFunction<Int32 Function(Pointer<Void>, Pointer<Void>, Pointer<Int32>, Pointer<Void>, Int32)>>('detect')
          .asFunction<int Function(Pointer<Void>, Pointer<Void>, Pointer<Int32>, Pointer<Void>, int)>();
          
      _faceFeature = _lib!
          .lookup<NativeFunction<Int32 Function(Pointer<Void>, Pointer<Void>, Pointer<Void>, Pointer<Void>, Int32)>>('face_feature')
          .asFunction<int Function(Pointer<Void>, Pointer<Void>, Pointer<Void>, Pointer<Void>, int)>();
      
      _userAdd = _lib!
          .lookup<NativeFunction<Void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>)>>('user_add')
          .asFunction();
          
      _userDelete = _lib!
          .lookup<NativeFunction<Void Function(Pointer<Pointer<Utf8>>, Pointer<Utf8>, Pointer<Utf8>)>>('user_delete')
          .asFunction();
          
      _loadDbFace = _lib!
          .lookup<NativeFunction<Bool Function()>>('load_db_face')
          .asFunction();
          
      _dbFaceCount = _lib!
          .lookup<NativeFunction<Int32 Function(Pointer<Utf8>)>>('db_face_count')
          .asFunction();
          
      _identify = _lib!
          .lookup<NativeFunction<Void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, Pointer<Utf8>, Pointer<Utf8>, Int32)>>('identify')
          .asFunction<void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, Pointer<Utf8>, Pointer<Utf8>, int)>();
          
      _identifyWithAll = _lib!
          .lookup<NativeFunction<Void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, Int32)>>('identify_with_all')
          .asFunction<void Function(Pointer<Pointer<Utf8>>, Pointer<Void>, int)>();
      
      // 初始化SDK，设置模型路径
      final modelsDir = await _getModelsDirectory();
      final result = await _initSDK(modelsDir);
      
      if (result == 0) {
        _isInitialized = true;
        print('✅ 百度人脸识别SDK初始化成功');
        
        // 获取设备ID和版本信息
        final deviceId = await getDeviceId();
        final version = await getSdkVersion();
        final authStatus = isAuthenticated();
        
        print('📱 设备ID: $deviceId');
        print('📊 SDK版本: $version');
        print('🔑 授权状态: ${authStatus ? "已授权" : "未授权"}');
        
        // 加载人脸库
        final dbLoaded = loadFaceDatabase();
        print('💾 人脸库加载${dbLoaded ? "成功" : "失败"}');
        
        if (dbLoaded) {
          final faceCount = getFaceCount();
          print('👤 人脸库中共有 $faceCount 个人脸');
        }
        
        return true;
      } else {
        print('❌ 百度人脸识别SDK初始化失败, 错误码: $result');
        return false;
      }
      
    } catch (e) {
      print('❌ 百度人脸识别SDK初始化异常: $e');
      return false;
    }
  }
  
  // 获取模型目录路径
  static Future<String> _getModelsDirectory() async {
    try {
      // 从配置管理器获取SDK路径
      final configManager = SdkConfigManager.instance;
      final sdkPath = await configManager.getSdkPath();
      final modelsPath = path.join(sdkPath, 'models');
      
      // 检查配置路径下的models目录是否存在
      if (Directory(modelsPath).existsSync()) {
        return modelsPath;
      }
      
      // 如果配置路径不存在，使用应用程序目录（向后兼容）
      final appDir = await getApplicationDocumentsDirectory();
      final fallbackModelsDir = path.join(appDir.path, 'baidu_face_models');
      
      // 确保目录存在
      if (!Directory(fallbackModelsDir).existsSync()) {
        Directory(fallbackModelsDir).createSync(recursive: true);
      }
      
      print('警告: 配置的SDK路径下未找到models目录，使用备用路径: $fallbackModelsDir');
      return fallbackModelsDir;
    } catch (e) {
      print('获取模型目录失败: $e');
      return 'models'; // 默认路径
    }
  }
  
  // 初始化SDK
  static Future<int> _initSDK(String modelsPath) async {
    final pathPtr = modelsPath.toNativeUtf8();
    try {
      return _sdkInit(pathPtr);
    } finally {
      malloc.free(pathPtr);
    }
  }
  
  // 获取设备ID
  static Future<String> getDeviceId() async {
    final resultPtr = malloc<Pointer<Utf8>>();
    try {
      _getDeviceId(resultPtr);
      final result = resultPtr.value.toDartString();
      return result;
    } finally {
      malloc.free(resultPtr);
    }
  }
  
  // 获取SDK版本
  static Future<String> getSdkVersion() async {
    final resultPtr = malloc<Pointer<Utf8>>();
    try {
      _sdkVersion(resultPtr);
      final result = resultPtr.value.toDartString();
      return result;
    } finally {
      malloc.free(resultPtr);
    }
  }
  
  // 检查授权状态
  static bool isAuthenticated() {
    return _isAuth();
  }
  
  // 加载人脸库
  static bool loadFaceDatabase() {
    return _loadDbFace();
  }
  
  // 获取人脸库中的人脸数量
  static int getFaceCount([String? groupId]) {
    final groupIdPtr = groupId != null ? groupId.toNativeUtf8() : nullptr;
    try {
      return _dbFaceCount(groupIdPtr);
    } finally {
      if (groupId != null) {
        malloc.free(groupIdPtr);
      }
    }
  }
  
  // 添加用户到人脸库
  static Future<String> addUser(Uint8List imageData, String userId, String groupId, [String? userInfo]) async {
    // TODO: 实现将图像数据转换为cv::Mat并调用native方法
    // 这部分需要进一步实现图像数据的转换逻辑
    return "暂未实现图像数据转换";
  }
  
  // 根据特征值添加用户到人脸库
  static Future<String> addUserByFeature(FaceFeature feature, String userId, String groupId, [String? userInfo]) async {
    // TODO: 实现将FaceFeature转换为native结构体并调用native方法
    return "暂未实现特征值转换";
  }
  
  // 从人脸库中删除用户
  static Future<String> deleteUser(String userId, String groupId) async {
    final resultPtr = malloc<Pointer<Utf8>>();
    final userIdPtr = userId.toNativeUtf8();
    final groupIdPtr = groupId.toNativeUtf8();
    
    try {
      _userDelete(resultPtr, userIdPtr, groupIdPtr);
      final result = resultPtr.value.toDartString();
      return result;
    } finally {
      malloc.free(resultPtr);
      malloc.free(userIdPtr);
      malloc.free(groupIdPtr);
    }
  }
  
  // 1:N人脸识别搜索
  static Future<List<FaceRecognitionResult>> identifyFace(Uint8List imageData, [String? groupIdList]) async {
    // TODO: 实现将图像数据转换为cv::Mat并调用native方法
    // 这部分需要进一步实现图像数据的转换逻辑和结果解析
    return [];
  }
  
  // 人脸特征值1:N搜索
  static Future<List<FaceRecognitionResult>> identifyByFeature(FaceFeature feature, [String? groupIdList]) async {
    // TODO: 实现将FaceFeature转换为native结构体并调用native方法
    return [];
  }
  
  // 提取人脸特征值
  static Future<List<FaceFeature>> extractFeature(Uint8List imageData) async {
    // TODO: 实现将图像数据转换为cv::Mat，调用人脸检测和特征提取
    return [];
  }
  
  // 比较两个人脸特征值的相似度
  static Future<double> compareFeatures(FaceFeature feature1, FaceFeature feature2) async {
    // TODO: 实现将FaceFeature转换为native结构体并调用比对方法
    return 0.0;
  }
  
  // 释放资源
  static void dispose() {
    // 释放资源的逻辑
    _isInitialized = false;
    print('百度人脸识别SDK资源已释放');
  }
} 