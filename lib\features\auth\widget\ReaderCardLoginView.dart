// ignore_for_file: must_be_immutable

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import '../../../generated/l10n.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';
import 'AlipayLoginView.dart';
import 'gradientView.dart';

class ReaderCardLoginView extends StatelessWidget {
  ReaderCardLoginView(this.loginType, {Key? key}) : super(key: key);
  AuthLoginType loginType;

  @override
  Widget build(BuildContext context) {
    String icon = 'images/common/';
    if (loginType == AuthLoginType.IDCard) {
      icon += '刷身份证.gif';
    } else if (loginType == AuthLoginType.socailSecurityCard) {
      icon += '刷社保卡.gif';
    } else {
      icon += '刷读者证.gif';
    }
    return Consumer2<CurrentLayoutProvider, SettingProvider>(
        builder: (context, layoutProvider, settingProvider, child) {
      if (settingProvider.getTheme() == ThemeType.ximalaya) {
        return ReaderCardXmlyLogin(loginType, icon);
      } else if (settingProvider.getTheme() == ThemeType.child) {
        return ReaderCardChildLogin(loginType, icon);
      } else {
        return ReaderCardBlueLogin(loginType, icon);
      }
    });
  }
}

class ReaderCardChildLogin extends StatelessWidget {
  ReaderCardChildLogin(this.loginType, this.icon, {Key? key}) : super(key: key);
  AuthLoginType loginType;
  String icon;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => loginType == AuthLoginTypeMap[element.type]).firstOrNull;

    String name = '';
    if (loginType == AuthLoginType.socailSecurityCard) {
      name = S.current.socialSecurityCard;
    } else if (loginType == AuthLoginType.readerCard) {
      name = S.current.reader_card;
    } else if (loginType == AuthLoginType.IDCard) {
      name = S.current.identityCard;
    }

    return ListView(
      shrinkWrap: true,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title:'$name${S.current.spaceLogin}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
                child: Container(
              margin: EdgeInsets.only(
                top: 134.p,
                bottom: 71.p,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 40.p,
                vertical: 7.p,
              ),
              decoration: BoxDecoration(
                color: BPUtils.c_FFCCEFFF,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.p),
                  topRight: Radius.circular(16.p),
                  bottomRight: Radius.circular(16.p),
                  bottomLeft: Radius.circular(4.p),
                ),
              ),
              child: Text(
                (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:'${S.current.place}$name${S.current.at}$name${S.current.sensingArea}',
                style: TextStyle(
                  color: BPUtils.c_FF1481D7,
                  fontWeight: FontWeight.normal,
                  fontSize: 28.p,
                ),
              ),
            ))
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ChildThemeYellowBorder(
              child: Image.asset(
                icon,
                width: 760.p,
                height: 500.p,
                key: Key(icon),
              ),
            )
          ],
        ),
      ],
    );
  }
}

class ReaderCardBlueLogin extends StatelessWidget {
  ReaderCardBlueLogin(this.loginType, this.icon, {Key? key}) : super(key: key);
  AuthLoginType loginType;
  String icon;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => loginType == AuthLoginTypeMap[element.type]).firstOrNull;

    String name = '';
    if (loginType == AuthLoginType.socailSecurityCard) {
      name = S.current.socialSecurityCard;
    } else if (loginType == AuthLoginType.readerCard) {
      name = S.current.reader_card;
    } else if (loginType == AuthLoginType.IDCard) {
      name = S.current.identityCard;
    }

    return ListView(
      shrinkWrap: true,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title:'$name${S.current.spaceLogin}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: BPUtils.c_ff12215F,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
                child: Container(
              margin: EdgeInsets.only(
                top: 134.p,
                bottom: 71.p,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 40.p,
                vertical: 7.p,
              ),
              decoration: BoxDecoration(
                color: BPUtils.c_FFB7CDFF,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.p),
                  topRight: Radius.circular(16.p),
                  bottomRight: Radius.circular(16.p),
                  bottomLeft: Radius.circular(4.p),
                ),
              ),
              child: Text(
                (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:'${S.current.place}$name${S.current.at}$name${S.current.sensingArea}',
                style: TextStyle(
                  color: BPUtils.c_FF1D62FD,
                  fontWeight: FontWeight.normal,
                  fontSize: 28.p,
                ),
              ),
            ))
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              icon,
              width: 760.p,
              height: 500.p,
              key: Key(icon),
            ),
          ],
        ),
      ],
    );
  }
}

class ReaderCardXmlyLogin extends StatelessWidget {
  ReaderCardXmlyLogin(this.loginType, this.icon, {Key? key}) : super(key: key);
  AuthLoginType loginType;
  String icon;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => loginType == AuthLoginTypeMap[element.type]).firstOrNull;

    String name = '';
    if (loginType == AuthLoginType.socailSecurityCard) {
      name = S.current.socialSecurityCard;
    } else if (loginType == AuthLoginType.readerCard) {
      name = S.current.reader_card;
    } else if (loginType == AuthLoginType.IDCard) {
      name = S.current.identityCard;
    }

    return ListView(
      shrinkWrap: true,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title:'$name${S.current.spaceLogin}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: BPUtils.c_ff12215F,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 154.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
                child: Padding(padding: EdgeInsets.symmetric(horizontal: 40.p),child: GradientView(
                  radius: BorderRadius.only(
                    topLeft: Radius.circular(20.p),
                    topRight: Radius.circular(20.p),
                    bottomLeft: Radius.circular(4.p),
                    bottomRight: Radius.circular(20.p),
                  ),
                  shadowColor: Colors.transparent,
                  stops: const [0, 0.5, 1],
                  colors: const [
                    BPUtils.c_FFBBFBFF,
                    BPUtils.c_FFAFDAFF,
                    BPUtils.c_FFC3CAFF,
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: 5.p,
                        horizontal: 35.p),
                    child: Text(
                      (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:'${S.current.place}$name${S.current.at}$name${S.current.sensingArea}',
                      style: TextStyle(
                        color: BPUtils.c_FF1D62FD,
                        fontWeight: FontWeight.normal,
                        fontSize: 28.p,
                      ),
                    ),
                  ),
                ),)),
          ],
        ),
        SizedBox(height: 70.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              icon,
              width: 760.p,
              height: 500.p,
              key: Key(icon),
            ),
          ],
        ),
      ],
    );
  }
} 