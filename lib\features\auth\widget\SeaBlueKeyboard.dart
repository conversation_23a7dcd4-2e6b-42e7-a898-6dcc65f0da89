// ignore_for_file: avoid_print

import 'dart:io';
import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';
import 'gradientView.dart';

enum BlueKeyboardLetterType {
  number, // 数字
  letter, // 字母
  signal, // 符号
  clear, // 清空
  backspace, // 删除
  cap, // 大小写
  sure, // 确定
  checkoutNum, // 切换成数字
  checkoutLetter, // 切换成字母
  checkoutSignal, // 切换成符号
}

class BlueKeyboardLetterData {
  String text;
  String capText;
  int flex;
  int left;
  int right;
  double height;
  BlueKeyboardLetterType type;

  BlueKeyboardLetterData(this.text, this.capText, this.type, this.flex,
      this.left, this.right, this.height);
}

enum SeaBlueKeyboardType { number, letter, signal, number2 }

class SeaBlueKeyboard extends StatefulWidget {
  SeaBlueKeyboard(this.type,
      {Key? key, this.controller, this.onSubmitted, this.focus})
      : super(key: key);
  SeaBlueKeyboardType type;
  FocusNode? focus;
  TextEditingController? controller;
  Function(TextEditingController? controller)? onSubmitted;

  static show(
      {required SeaBlueKeyboardType type,
      TextEditingController? controller,
      Function(TextEditingController? controller)? onSubmitted,
      FocusNode? focus}) {
    if (Platform.isAndroid || Platform.isIOS) return;
    Get.bottomSheet(
      SeaBlueKeyboard(
        type,
        controller: controller,
        focus: focus,
        onSubmitted: onSubmitted,
      ),
      barrierColor: Colors.transparent,
      enableDrag: false,
    );
  }

  @override
  State<SeaBlueKeyboard> createState() => _SeaBlueKeyboardState();
}

class _SeaBlueKeyboardState extends State<SeaBlueKeyboard> {
  bool isCap = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    addFocusListen();
  }

  @override
  onBack() {
    // TODO: implement onBack
    Get.back();
  }

  @override
  dispose() {
    super.dispose();
    removeFocusListen();
    widget.focus?.unfocus();
  }

  addFocusListen() {
    widget.focus?.addListener(focusListener);
  }

  removeFocusListen() {
    widget.focus?.removeListener(focusListener);
  }

  focusListener() {
    if (widget.focus?.hasFocus != true) {
      print('widget.focus?.requestFocus()');
      widget.focus?.requestFocus();
    }
  }

  void _moveCursorToEnd() {
    // 通过将选择的开始和结束位置设置到文本的末尾，移动光标到最后
    widget.controller?.selection = TextSelection.fromPosition(TextPosition(
      offset: widget.controller?.text.length ?? 0,
    ));
  }

  onTap(BlueKeyboardLetterData data) {
    if (data.type == BlueKeyboardLetterType.checkoutLetter) {
      isCap = false;
      widget.type = SeaBlueKeyboardType.letter;
      setState(() {});
    } else if (data.type == BlueKeyboardLetterType.checkoutNum) {
      isCap = false;
      widget.type = SeaBlueKeyboardType.number;
      setState(() {});
    } else if (data.type == BlueKeyboardLetterType.checkoutSignal) {
      isCap = false;
      widget.type = SeaBlueKeyboardType.signal;
      setState(() {});
    } else if (data.type == BlueKeyboardLetterType.sure) {
      if (widget.onSubmitted != null) {
        widget.onSubmitted?.call(widget.controller);
      } else {
        Get.back();
      }
    } else if (data.type == BlueKeyboardLetterType.clear) {
      widget.controller?.clear();
    } else if (data.type == BlueKeyboardLetterType.backspace) {
      if (widget.controller?.text.isNotEmpty ?? false) {
        widget.controller!.text = widget.controller!.text
            .substring(0, widget.controller!.text.length - 1);
      }
    } else if (data.type == BlueKeyboardLetterType.cap) {
      setState(() {
        isCap = !isCap;
      });
    } else {
      if (widget.controller != null) {
        widget.controller!.text += isCap ? data.capText : data.text;
      }
    }
    _moveCursorToEnd();
    // resetPageTimer();
  }

  @override
  Widget build(BuildContext context) {
    Widget temp;
    if (widget.type == SeaBlueKeyboardType.number ||
        widget.type == SeaBlueKeyboardType.number2) {
      temp = SeaBlueNumberKeyboard(onTap);
    } else if (widget.type == SeaBlueKeyboardType.letter) {
      temp = SeaBlueLettersKeyboard(onTap, isCap);
    } else {
      temp = SeaBlueSignalKeyboard(onTap);
    }
    return SizedBox(
      width: WindowUtil.width,
      height: 630.p,
      child: Consumer<SettingProvider>(
        builder: (ctx, setting, child) {
          return GradientView(
            shadowColor: Colors.transparent,
            colors: const [
              BPUtils.c_FFB5EBFF,
              Colors.white,
            ],
            stops: const [0, 0.5],
            child: temp,
          );
        },
      ),
    );
  }
}

class SeaBlueNumberKeyboard extends StatelessWidget {
  SeaBlueNumberKeyboard(this.onTap, {Key? key}) : super(key: key);
  Function(BlueKeyboardLetterData data) onTap;

  List<List<BlueKeyboardLetterData>> list = [
    [
      BlueKeyboardLetterData(
          '1', "1", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '2', "2", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '3', "3", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.backspace, 220, 0, 0, 110),
    ],
    [
      BlueKeyboardLetterData(
          '4', "4", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '5', "5", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '6', "6", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '0', "0", BlueKeyboardLetterType.number, 220, 0, 0, 110),
    ],
    [
      BlueKeyboardLetterData(
          '7', "7", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '8', "8", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '9', "9", BlueKeyboardLetterType.number, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.clear, 220, 0, 0, 110),
    ],
    [
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.checkoutLetter, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.checkoutSignal, 220, 0, 16, 110),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.sure, 455, 0, 0, 110),
    ],
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: 76.p,
          vertical: 68.p),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(children: getRow(list[0], context)),
          SizedBox(height: 18.p),
          Row(children: getRow(list[1], context)),
          SizedBox(height: 18.p),
          Row(children: getRow(list[2], context)),
          SizedBox(height: 18.p),
          Row(children: getRow(list[3], context)),
        ],
      ),
    );
  }

  List<Widget> getRow(List<BlueKeyboardLetterData> list, BuildContext context) {
    List<Widget> rets = [];
    for (int i = 0; i < list.length; i++) {
      rets.addAll(seaNewKeyboardGetButton(
          data: list[i], onPressed: () => onTap(list[i])));
    }
    return rets;
  }
}

class SeaBlueLettersKeyboard extends StatelessWidget {
  SeaBlueLettersKeyboard(this.onTap, this.isCap, {Key? key}) : super(key: key);
  Function(BlueKeyboardLetterData data) onTap;
  List<List<BlueKeyboardLetterData>> list = [
    [
      BlueKeyboardLetterData(
          'q', "Q", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'w', "W", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'e', "E", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'r', "R", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          't', "T", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'y', "Y", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'u', "U", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'i', "I", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'o', "O", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'p', "P", BlueKeyboardLetterType.letter, 90, 0, 0, 120),
    ],
    [
      BlueKeyboardLetterData(
          'a', "A", BlueKeyboardLetterType.letter, 90, 52, 14, 120),
      BlueKeyboardLetterData(
          's', "S", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'd', "D", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'f', "F", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'g', "G", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'h', "H", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'j', "J", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'k', "K", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'l', "L", BlueKeyboardLetterType.letter, 90, 0, 52, 120),
    ],
    [
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.cap, 120, 0, 36, 120),
      BlueKeyboardLetterData(
          'z', "Z", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'x', "X", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'c', "C", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'v', "V", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'b', "B", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'n', "N", BlueKeyboardLetterType.letter, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          'm', "M", BlueKeyboardLetterType.letter, 90, 0, 36, 120),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.backspace, 120, 0, 0, 120),
    ],
    [
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.checkoutNum, 120, 0, 36, 120),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.checkoutSignal, 120, 0, 36, 120),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.sure, 714, 0, 0, 120),
    ],
  ];
  bool isCap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 27.p,
        vertical: 42.p,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[0], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: getRow(list[1], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[2], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[3], context)),
        ],
      ),
    );
  }

  List<Widget> getRow(List<BlueKeyboardLetterData> list, BuildContext context) {
    List<Widget> rets = [];
    for (int i = 0; i < list.length; i++) {
      rets.addAll(seaNewKeyboardGetButton(
          data: list[i], onPressed: () => onTap(list[i])));
    }
    return rets;
  }
}

class SeaBlueSignalKeyboard extends StatelessWidget {
  SeaBlueSignalKeyboard(this.onTap, {Key? key}) : super(key: key);
  Function(BlueKeyboardLetterData data) onTap;
  List<List<BlueKeyboardLetterData>> list = [
    [
      BlueKeyboardLetterData(
          '!', "!", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '@', "@", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '#', "#", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '\$', "\$", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '%', "%", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '^', "^", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '&', "&", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '-', "-", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '*', "*", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '(', "(", BlueKeyboardLetterType.signal, 90, 0, 14, 120)
    ],
    [
      BlueKeyboardLetterData(
          ')', ")", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '_', "_", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '+', "+", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '|', "|", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '.', ".", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          ',', ",", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '?', "?", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '/', "/", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
    ],
    [
      BlueKeyboardLetterData(
          '~', "~", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '{', "{", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '}', "}", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '"', '"', BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '=', "=", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          ';', ";", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          ':', ":", BlueKeyboardLetterType.signal, 90, 0, 14, 120),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.backspace, 90, 0, 14, 120),
    ],
    [
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.checkoutNum, 120, 0, 36, 120),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.checkoutLetter, 120, 0, 36, 120),
      BlueKeyboardLetterData(
          '', "", BlueKeyboardLetterType.sure, 870, 0, 0, 120),
    ],
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 27.p,
        vertical: 42.p,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[0], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: getRow(list[1], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[2], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[3], context)),
        ],
      ),
    );
  }

  List<Widget> getRow(List<BlueKeyboardLetterData> list, BuildContext context) {
    List<Widget> rets = [];
    for (int i = 0; i < list.length; i++) {
      rets.addAll(seaNewKeyboardGetButton(
          data: list[i], onPressed: () => onTap(list[i])));
    }
    return rets;
  }
}

List<Widget> seaNewKeyboardGetButton(
    {required BlueKeyboardLetterData data, required VoidCallback onPressed}) {
  SettingProvider? setting = Get.context?.read<SettingProvider>();
  if (setting?.getTheme() == ThemeType.child) {
    return seaChildKeyboardGetButton(data: data, onPressed: onPressed);
  } else {
    return seaBlueKeyboardGetButton(data: data, onPressed: onPressed);
  }
}

List<Widget> seaChildKeyboardGetButton(
    {required BlueKeyboardLetterData data, required VoidCallback onPressed}) {
  List<Widget> list = [];
  if (data.left > 0) {
    list.add(Expanded(flex: data.left, child: Container()));
  }

  if (data.type == BlueKeyboardLetterType.backspace) {
    list.add(
      getBlueButtonContent(
        flex: data.flex,
        child: GradientView(
          colors: const [BPUtils.c_FF36B6F3, BPUtils.c_FF36B6F3],
          stops: const [0, 1],
          spreadRadius: 0,
          shadowBlurRadius: 0,
          shadowColor: Colors.transparent,
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          shadowOffset: const Offset(0, 0),
          child: SizedBox(
            height: data.height.p,
            child: Center(
              child: Image.asset(
                AssetUtil.fullPath('key_clear'),
                width: 48.p,
                height: 48.p,
              ),
            ),
          ),
        ),
        onPressed: onPressed,
        height: data.height,
      ),
    );
  } else if (data.type == BlueKeyboardLetterType.clear) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFFEC72D, BPUtils.c_FFFEC72D],
        stops: const [0, 1],
        spreadRadius: 0,
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        shadowBlurRadius: 4.p,
        shadowColor: BPUtils.c_4DFEC72D,
        shadowOffset: Offset(0, 4.p),
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child:
                getBlueText('清空', color: Colors.white, fontSize: 40),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.checkoutSignal) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFFD7821, BPUtils.c_FFFD7821],
        stops: const [0, 1],
        spreadRadius: 0,
        shadowBlurRadius: 0,
        shadowOffset: const Offset(0, 0),
        shadowColor: Colors.transparent,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('字符',
                color: Colors.white, fontSize: 40),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.checkoutLetter) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFFD7821, BPUtils.c_FFFD7821],
        stops: const [0, 1],
        spreadRadius: 0,
        shadowBlurRadius: 0,
        shadowOffset: const Offset(0, 0),
        shadowColor: Colors.transparent,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('ABC', color: Colors.white, fontSize: 40),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.checkoutNum) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFFD7821, BPUtils.c_FFFD7821],
        stops: const [0, 1],
        spreadRadius: 0,
        shadowBlurRadius: 0,
        shadowOffset: const Offset(0, 0),
        shadowColor: Colors.transparent,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('123', color: Colors.white, fontSize: 40),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.sure) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFFD7821, BPUtils.c_FFFD7821],
        stops: const [0, 1],
        spreadRadius: 0,
        shadowBlurRadius: 0,
        shadowOffset: const Offset(0, 0),
        shadowColor: Colors.transparent,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('确定',
                color: Colors.white, fontSize: 40),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.cap) {
    list.add(
      getBlueButtonContent(
        flex: data.flex,
        child: GradientView(
          colors: const [BPUtils.c_FF36B6F3, BPUtils.c_FF36B6F3],
          stops: const [0, 1],
          spreadRadius: 0,
          shadowBlurRadius: 0,
          shadowColor: Colors.transparent,
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          shadowOffset: const Offset(0, 0),
          child: SizedBox(
            height: data.height.p,
            child: Center(
              child: Image.asset(
                AssetUtil.fullPath('key_shift'),
                width: 48.p,
                height: 48.p,
              ),
            ),
          ),
        ),
        onPressed: onPressed,
        height: data.height,
      ),
    );
  } else if (data.type == BlueKeyboardLetterType.letter ||
      data.type == BlueKeyboardLetterType.number ||
      data.type == BlueKeyboardLetterType.signal) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFFEC72D, BPUtils.c_FFFEC72D],
        stops: const [0, 1],
        spreadRadius: 0,
        shadowBlurRadius: 4.p,
        shadowColor: BPUtils.c_4DFEC72D,
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        shadowOffset: Offset(0, 4.p),
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText(data.text, color: Colors.white, fontSize: 40),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  }
  if (data.right > 0) {
    list.add(Expanded(flex: data.right, child: Container()));
  }

  return list;
}

List<Widget> seaBlueKeyboardGetButton(
    {required BlueKeyboardLetterData data, required VoidCallback onPressed}) {
  List<Widget> list = [];
  if (data.left > 0) {
    list.add(Expanded(flex: data.left, child: Container()));
  }

  if (data.type == BlueKeyboardLetterType.backspace) {
    list.add(
      getBlueButtonContent(
        flex: data.flex,
        child: GradientView(
          colors: const [BPUtils.c_FF1D62FD, Colors.white],
          stops: const [0, 0.98],
          spreadRadius: 0,
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          child: SizedBox(
            height: data.height.p,
            child: Center(
              child: Image.asset(
                AssetUtil.fullPath('key_clear'),
                width: 48.p,
                height: 48.p,
              ),
            ),
          ),
        ),
        onPressed: onPressed,
        height: data.height,
      ),
    );
  } else if (data.type == BlueKeyboardLetterType.clear) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FF1D62FD, Colors.white],
        stops: const [0, 0.98],
        spreadRadius: 0,
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('清空', color: Colors.white),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.checkoutSignal) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('字符'),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.checkoutLetter) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('ABC'),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.checkoutNum) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('123'),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.sure) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [],
        stops: const [],
        color: BPUtils.c_FF1D62FD,
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText('确定', color: Colors.white),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == BlueKeyboardLetterType.cap) {
    list.add(
      getBlueButtonContent(
        flex: data.flex,
        child: GradientView(
          colors: const [BPUtils.c_FF1D62FD, Colors.white],
          stops: const [0, 0.98],
          spreadRadius: 0,
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          child: SizedBox(
            height: data.height.p,
            child: Center(
              child: Image.asset(
                AssetUtil.fullPath('key_shift'),
                width: 48.p,
                height: 48.p,
              ),
            ),
          ),
        ),
        onPressed: onPressed,
        height: data.height,
      ),
    );
  } else if (data.type == BlueKeyboardLetterType.letter ||
      data.type == BlueKeyboardLetterType.number ||
      data.type == BlueKeyboardLetterType.signal) {
    list.add(getBlueButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getBlueText(data.text),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  }
  if (data.right > 0) {
    list.add(Expanded(flex: data.right, child: Container()));
  }

  return list;
}

Widget getBlueText(String text,
    {Color color = BPUtils.c_22, double fontSize = 48}) {
  return Text(
    text,
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    style: TextStyle(
      color: color,
      fontWeight: FontWeight.w600,
      fontSize: fontSize.p,
    ),
  );
}

Widget getBlueButtonContent(
    {required int flex,
    VoidCallback? onPressed,
    required Widget child,
    required double height}) {
  RxDouble scale = 1.0.obs;
  return Expanded(
    flex: flex,
    child: InkWell(
      onTapDown: (TapDownDetails details) {
        scale.value = 1.1;
      },
      onTapUp: (TapUpDetails details) {
        scale.value = 1;
      },
      onTap: onPressed,
      child: Obx(
        () => Transform.scale(
          scale: scale.value,
          child: Container(
            color: Colors.transparent,
            child: child,
          ),
        ),
      ),
    ),
  );
}
