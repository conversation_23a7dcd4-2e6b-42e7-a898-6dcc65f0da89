﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0B391900-FBB5-3B06-82A9-C324877E0BF0}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>face_detector</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\face_pro\test\face\src\opencv\out\build2\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">face_detector.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">libface_detector</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\face_pro\test\face\src\opencv\out\build2\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">face_detector.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">libface_detector</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\face_pro\test\face\src\opencv\out\build2\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">face_detector.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">libface_detector</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\face_pro\test\face\src\opencv\out\build2\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">face_detector.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">libface_detector</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/softWare/opencv/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR="Debug";face_detector_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR=\"Debug\";face_detector_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110d.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/face_pro/test/face/src/opencv/out/build2/lib/Debug/libface_detector.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/face_pro/test/face/src/opencv/out/build2/bin/Debug/libface_detector.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/softWare/opencv/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR="Release";face_detector_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR=\"Release\";face_detector_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/face_pro/test/face/src/opencv/out/build2/lib/Release/libface_detector.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/face_pro/test/face/src/opencv/out/build2/bin/Release/libface_detector.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/softWare/opencv/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR="MinSizeRel";face_detector_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR=\"MinSizeRel\";face_detector_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/face_pro/test/face/src/opencv/out/build2/lib/MinSizeRel/libface_detector.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/face_pro/test/face/src/opencv/out/build2/bin/MinSizeRel/libface_detector.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/softWare/opencv/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR="RelWithDebInfo";face_detector_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;FACE_DETECTOR_EXPORTS;CMAKE_INTDIR=\"RelWithDebInfo\";face_detector_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\softWare\opencv\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;D:\softWare\opencv\opencv\build\x64\vc16\lib\opencv_world4110.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/face_pro/test/face/src/opencv/out/build2/lib/RelWithDebInfo/libface_detector.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/face_pro/test/face/src/opencv/out/build2/bin/RelWithDebInfo/libface_detector.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\face_pro\test\face\src\opencv\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/face_pro/test/face/src/opencv/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\softWare\cmake-4.0.0-rc1-windows-x86_64\bin\cmake.exe -SE:/face_pro/test/face/src/opencv -BE:/face_pro/test/face/src/opencv/out/build2 --check-stamp-file E:/face_pro/test/face/src/opencv/out/build2/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig-version.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCXXCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeRCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/face_pro/test/face/src/opencv/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\softWare\cmake-4.0.0-rc1-windows-x86_64\bin\cmake.exe -SE:/face_pro/test/face/src/opencv -BE:/face_pro/test/face/src/opencv/out/build2 --check-stamp-file E:/face_pro/test/face/src/opencv/out/build2/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig-version.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCXXCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeRCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/face_pro/test/face/src/opencv/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\softWare\cmake-4.0.0-rc1-windows-x86_64\bin\cmake.exe -SE:/face_pro/test/face/src/opencv -BE:/face_pro/test/face/src/opencv/out/build2 --check-stamp-file E:/face_pro/test/face/src/opencv/out/build2/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig-version.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCXXCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeRCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/face_pro/test/face/src/opencv/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\softWare\cmake-4.0.0-rc1-windows-x86_64\bin\cmake.exe -SE:/face_pro/test/face/src/opencv -BE:/face_pro/test/face/src/opencv/out/build2 --check-stamp-file E:/face_pro/test/face/src/opencv/out/build2/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\softWare\cmake-4.0.0-rc1-windows-x86_64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig-version.cmake;D:\softWare\opencv\opencv\build\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\softWare\opencv\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeCXXCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeRCCompiler.cmake;E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\4.0.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\face_pro\test\face\src\opencv\out\build2\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\face_pro\test\face\src\opencv\face_detection.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="E:\face_pro\test\face\src\opencv\out\build2\ZERO_CHECK.vcxproj">
      <Project>{03E19B24-05A9-316E-A22E-B4966F7A0A79}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>