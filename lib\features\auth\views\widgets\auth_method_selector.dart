import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/auth_result.dart';
import '../../view_models/auth_view_model.dart';

class AuthMethodSelector extends StatelessWidget {
  const AuthMethodSelector({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<AuthViewModel>(context);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildMethodButton(
          context,
          '人脸识别',
          Icons.face,
          AuthMethod.face,
          viewModel,
        ),
        const SizedBox(width: 16),
        _buildMethodButton(
          context,
          '身份证',
          Icons.credit_card,
          AuthMethod.idCard,
          viewModel,
        ),
        const SizedBox(width: 16),
        _buildMethodButton(
          context,
          '二维码',
          Icons.qr_code,
          AuthMethod.qrCode,
          viewModel,
        ),
      ],
    );
  }

  Widget _buildMethodButton(
    BuildContext context,
    String label,
    IconData icon,
    AuthMethod method,
    AuthViewModel viewModel,
  ) {
    final isSelected = viewModel.currentMethod == method;
    
    return ElevatedButton.icon(
      onPressed: () => viewModel.setAuthMethod(method),
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        primary: isSelected ? Theme.of(context).primaryColor : null,
        onPrimary: isSelected ? Colors.white : null,
      ),
    );
  }
} 