import 'dart:io';

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/window_util.dart';
import '../../shared/utils/asset_util.dart';
import '../../features/auth/widget/gradientView.dart';

enum KeyboardLetterType {
  number, // 数字
  letter, // 字母
  signal, // 符号
  clear, // 清空
  backspace, // 删除
  cap, // 大小写
  sure, // 确定
  checkoutNum, // 切换成数字
  checkoutLetter, // 切换成字母
  checkoutSignal, // 切换成符号
}

class KeyboardLetterData {
  String text;
  String capText;
  int flex;
  int left;
  int right;
  double height;
  KeyboardLetterType type;

  KeyboardLetterData(this.text, this.capText, this.type, this.flex, this.left,
      this.right, this.height);
}

enum SeaKeyboardType { number, letter, signal, number2 }

class SeaKeyboard extends StatefulWidget {
  SeaKeyboard(this.type,
      {Key? key, this.controller, this.onSubmitted, this.focus})
      : super(key: key);
  SeaKeyboardType type;
  FocusNode? focus;
  TextEditingController? controller;
  Function(TextEditingController? controller)? onSubmitted;

  static show(
      {required SeaKeyboardType type,
      TextEditingController? controller,
      Function(TextEditingController? controller)? onSubmitted,
      FocusNode? focus}) {
    if (Platform.isAndroid || Platform.isIOS) return;
    Get.bottomSheet(
      SeaKeyboard(
        type,
        controller: controller,
        focus: focus,
        onSubmitted: onSubmitted,
      ),
      barrierColor: Colors.transparent,
    );
  }

  @override
  State<SeaKeyboard> createState() => _SeaKeyboardState();
}

class _SeaKeyboardState extends State<SeaKeyboard> {
  bool isCap = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    widget.focus?.requestFocus();
  }

  onTap(KeyboardLetterData data) {
    if (data.type == KeyboardLetterType.checkoutLetter) {
      isCap = false;
      widget.type = SeaKeyboardType.letter;
      setState(() {});
    } else if (data.type == KeyboardLetterType.checkoutNum) {
      isCap = false;
      widget.type = SeaKeyboardType.number;
      setState(() {});
    } else if (data.type == KeyboardLetterType.checkoutSignal) {
      isCap = false;
      widget.type = SeaKeyboardType.signal;
      setState(() {});
    } else if (data.type == KeyboardLetterType.sure) {
      if (widget.onSubmitted != null){
        widget.onSubmitted?.call(widget.controller);
      }else{
        Get.back();
      }
    } else if (data.type == KeyboardLetterType.clear) {
      widget.controller?.clear();
    } else if (data.type == KeyboardLetterType.backspace) {
      if (widget.controller?.text.isNotEmpty ?? false) {
        widget.controller!.text = widget.controller!.text
            .substring(0, widget.controller!.text.length - 1);
      }
    } else if (data.type == KeyboardLetterType.cap) {
      setState(() {
        isCap = !isCap;
      });
    } else {
      if (widget.controller != null) {
        widget.controller!.text += isCap ? data.capText : data.text;
      }
    }

    setState(() {
      widget.focus?.requestFocus();
    });
  }


  @override
  Widget build(BuildContext context) {
    Widget temp;
    if (widget.type == SeaKeyboardType.number ||
        widget.type == SeaKeyboardType.number2) {
      temp = SeaNumberKeyboard(onTap);
    } else if (widget.type == SeaKeyboardType.letter) {
      temp = SeaLettersKeyboard(onTap, isCap);
    } else {
      temp = SeaSignalKeyboard(onTap);
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
          width: WindowUtil.width,
          height: 630.p,
          child: GradientView(
            shadowColor: Colors.transparent,
            colors: const [
              BPUtils.c_FFB5EBFF,
              Colors.white,
            ],
            stops: const [0, 0.5],
            child: temp,
          ),
        )
      ],
    );
  }
}

class SeaNumberKeyboard extends StatelessWidget {
  SeaNumberKeyboard(this.onTap, {Key? key}) : super(key: key);
  Function(KeyboardLetterData data) onTap;

  List<List<KeyboardLetterData>> list = [
    [
      KeyboardLetterData('1', "1", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('2', "2", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('3', "3", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('', "", KeyboardLetterType.backspace, 220, 0, 0, 110),
    ],
    [
      KeyboardLetterData('4', "4", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('5', "5", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('6', "6", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('0', "0", KeyboardLetterType.number, 220, 0, 0, 110),
    ],
    [
      KeyboardLetterData('7', "7", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('8', "8", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('9', "9", KeyboardLetterType.number, 220, 0, 16, 110),
      KeyboardLetterData('', "", KeyboardLetterType.clear, 220, 0, 0, 110),
    ],
    [
      KeyboardLetterData(
          '', "", KeyboardLetterType.checkoutLetter, 220, 0, 16, 110),
      KeyboardLetterData(
          '', "", KeyboardLetterType.checkoutSignal, 220, 0, 16, 110),
      KeyboardLetterData('', "", KeyboardLetterType.sure, 455, 0, 0, 110),
    ],
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: 76.p,
          vertical: 68.p),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(children: getRow(list[0], context)),
          SizedBox(height: 18.p),
          Row(children: getRow(list[1], context)),
          SizedBox(height: 18.p),
          Row(children: getRow(list[2], context)),
          SizedBox(height: 18.p),
          Row(children: getRow(list[3], context)),
        ],
      ),
    );
  }

  List<Widget> getRow(List<KeyboardLetterData> list, BuildContext context) {
    List<Widget> rets = [];
    for (int i = 0; i < list.length; i++) {
      rets.addAll(
          seaKeyboardGetButton(data: list[i], onPressed: () => onTap(list[i])));
    }
    return rets;
  }
}

class SeaLettersKeyboard extends StatelessWidget {
  SeaLettersKeyboard(this.onTap, this.isCap, {Key? key}) : super(key: key);
  Function(KeyboardLetterData data) onTap;
  List<List<KeyboardLetterData>> list = [
    [
      KeyboardLetterData('q', "Q", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('w', "W", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('e', "E", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('r', "R", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('t', "T", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('y', "Y", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('u', "U", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('i', "I", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('o', "O", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('p', "P", KeyboardLetterType.letter, 90, 0, 0, 120),
    ],
    [
      KeyboardLetterData('a', "A", KeyboardLetterType.letter, 90, 52, 14, 120),
      KeyboardLetterData('s', "S", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('d', "D", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('f', "F", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('g', "G", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('h', "H", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('j', "J", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('k', "K", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('l', "L", KeyboardLetterType.letter, 90, 0, 52, 120),
    ],
    [
      KeyboardLetterData('', "", KeyboardLetterType.cap, 120, 0, 36, 120),
      KeyboardLetterData('z', "Z", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('x', "X", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('c', "C", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('v', "V", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('b', "B", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('n', "N", KeyboardLetterType.letter, 90, 0, 14, 120),
      KeyboardLetterData('m', "M", KeyboardLetterType.letter, 90, 0, 36, 120),
      KeyboardLetterData('', "", KeyboardLetterType.backspace, 120, 0, 0, 120),
    ],
    [
      KeyboardLetterData(
          '', "", KeyboardLetterType.checkoutNum, 120, 0, 36, 120),
      KeyboardLetterData(
          '', "", KeyboardLetterType.checkoutSignal, 120, 0, 36, 120),
      KeyboardLetterData('', "", KeyboardLetterType.sure, 714, 0, 0, 120),
    ],
  ];
  bool isCap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 27.p,
        vertical: 42.p,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[0], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: getRow(list[1], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[2], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[3], context)),
        ],
      ),
    );
  }

  List<Widget> getRow(List<KeyboardLetterData> list, BuildContext context) {
    List<Widget> rets = [];
    for (int i = 0; i < list.length; i++) {
      rets.addAll(
          seaKeyboardGetButton(data: list[i], onPressed: () => onTap(list[i])));
    }
    return rets;
  }
}

class SeaSignalKeyboard extends StatelessWidget {
  SeaSignalKeyboard(this.onTap, {Key? key}) : super(key: key);
  Function(KeyboardLetterData data) onTap;
  List<List<KeyboardLetterData>> list = [
    [
      KeyboardLetterData('!', "!", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('@', "@", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('#', "#", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('\$', "\$", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('%', "%", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('^', "^", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('&', "&", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('-', "-", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('*', "*", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('(', "(", KeyboardLetterType.signal, 90, 0, 14, 120)
    ],
    [
      KeyboardLetterData(')', ")", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('_', "_", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('+', "+", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('|', "|", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('.', ".", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData(',', ",", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('?', "?", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('/', "/", KeyboardLetterType.signal, 90, 0, 14, 120),
    ],
    [
      KeyboardLetterData('~', "~", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('{', "{", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('}', "}", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('"', '"', KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('=', "=", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData(';', ";", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData(':', ":", KeyboardLetterType.signal, 90, 0, 14, 120),
      KeyboardLetterData('', "", KeyboardLetterType.backspace, 90, 0, 14, 120),
    ],
    [
      KeyboardLetterData(
          '', "", KeyboardLetterType.checkoutNum, 120, 0, 36, 120),
      KeyboardLetterData(
          '', "", KeyboardLetterType.checkoutLetter, 120, 0, 36, 120),
      KeyboardLetterData('', "", KeyboardLetterType.sure, 870, 0, 0, 120),
    ],
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 27.p,
        vertical: 42.p,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[0], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: getRow(list[1], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[2], context)),
          SizedBox(height: 22.p),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: getRow(list[3], context)),
        ],
      ),
    );
  }

  List<Widget> getRow(List<KeyboardLetterData> list, BuildContext context) {
    List<Widget> rets = [];
    for (int i = 0; i < list.length; i++) {
      rets.addAll(
          seaKeyboardGetButton(data: list[i], onPressed: () => onTap(list[i])));
    }
    return rets;
  }
}

List<Widget> seaKeyboardGetButton(
    {required KeyboardLetterData data, required VoidCallback onPressed}) {
  List<Widget> list = [];
  if (data.left > 0) {
    list.add(Expanded(flex: data.left, child: Container()));
  }

  if (data.type == KeyboardLetterType.backspace) {
    list.add(
      getButtonContent(
        flex: data.flex,
        child: GradientView(
          colors: const [BPUtils.c_FF1D62FD, Colors.white],
          stops: const [0, 0.98],
          spreadRadius: 0,
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          child: SizedBox(
            height: data.height.p,
            child: Center(
              child: Image.asset(
                AssetUtil.fullPath('key_clear'),
                width: 48.p,
                height: 48.p,
              ),
            ),
          ),
        ),
        onPressed: onPressed,
        height: data.height,
      ),
    );
  } else if (data.type == KeyboardLetterType.clear) {
    list.add(getButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FF1D62FD, Colors.white],
        stops: const [0, 0.98],
        spreadRadius: 0,
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getText('清空', color: Colors.white),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == KeyboardLetterType.checkoutSignal) {
    list.add(getButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getText('字符'),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == KeyboardLetterType.checkoutLetter) {
    list.add(getButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getText('ABC'),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == KeyboardLetterType.checkoutNum) {
    list.add(getButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getText('123'),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == KeyboardLetterType.sure) {
    list.add(getButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [],
        stops: const [],
        color: BPUtils.c_FF1D62FD,
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getText('确定', color: Colors.white),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  } else if (data.type == KeyboardLetterType.cap) {
    list.add(
      getButtonContent(
        flex: data.flex,
        child: GradientView(
          colors: const [BPUtils.c_FF1D62FD, Colors.white],
          stops: const [0, 0.98],
          spreadRadius: 0,
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          child: SizedBox(
            height: data.height.p,
            child: Center(
              child: Image.asset(
                AssetUtil.fullPath('key_shift'),
                width: 48.p,
                height: 48.p,
              ),
            ),
          ),
        ),
        onPressed: onPressed,
        height: data.height,
      ),
    );
  } else if (data.type == KeyboardLetterType.letter ||
      data.type == KeyboardLetterType.number ||
      data.type == KeyboardLetterType.signal) {
    list.add(getButtonContent(
      flex: data.flex,
      child: GradientView(
        colors: const [BPUtils.c_FFDCE7FE, Colors.white],
        stops: const [0, 0.8],
        spreadRadius: 0,
        child: SizedBox(
          height: data.height.p,
          child: Center(
            child: getText(data.text),
          ),
        ),
      ),
      onPressed: onPressed,
      height: data.height,
    ));
  }
  if (data.right > 0) {
    list.add(Expanded(flex: data.right, child: Container()));
  }

  return list;
}

Widget getText(String text, {Color color = BPUtils.c_22}) {
  return Text(
    text,
    maxLines: 1,
    overflow: TextOverflow.ellipsis,
    style: TextStyle(
      color: color,
      fontWeight: FontWeight.w500,
      fontSize: 48.p,
    ),
  );
}

Widget getButtonContent(
    {required int flex,
    VoidCallback? onPressed,
    required Widget child,
    required double height}) {
  return Expanded(
    flex: flex,
    child: MaterialButton(
      onPressed: onPressed,
      child: child,
    ),
  );
}
