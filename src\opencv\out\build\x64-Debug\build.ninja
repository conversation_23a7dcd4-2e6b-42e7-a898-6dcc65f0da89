# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.29

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: face_detection
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:\gdwork\code\a3g\src\opencv\out\build\x64-Debug\
# =============================================================================
# Object build statements for SHARED_LIBRARY target face_detector


#############################################
# Order-only phony target for face_detector

build cmake_object_order_depends_target_face_detector: phony || .

build CMakeFiles\face_detector.dir\face_detection.cpp.obj: CXX_COMPILER__face_detector_unscanned_Debug D$:\gdwork\code\a3g\src\opencv\face_detection.cpp || cmake_object_order_depends_target_face_detector
  DEFINES = -DFACE_DETECTOR_EXPORTS -Dface_detector_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 /MDd
  INCLUDES = -external:ID:\Users\Administrator\Downloads\opencv\build\include -external:W0
  OBJECT_DIR = CMakeFiles\face_detector.dir
  OBJECT_FILE_DIR = CMakeFiles\face_detector.dir
  TARGET_COMPILE_PDB = CMakeFiles\face_detector.dir\
  TARGET_PDB = bin\libface_detector.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target face_detector


#############################################
# Link the shared library bin\libface_detector.dll

build bin\libface_detector.dll lib\libface_detector.lib: CXX_SHARED_LIBRARY_LINKER__face_detector_Debug CMakeFiles\face_detector.dir\face_detection.cpp.obj | D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib
  LANGUAGE_COMPILE_FLAGS = /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /NODEFAULTLIB:libcmtd.lib
  LINK_LIBRARIES = D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  D:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\opencv_world4110d.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\face_detector.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  RESTAT = 1
  TARGET_COMPILE_PDB = CMakeFiles\face_detector.dir\
  TARGET_FILE = bin\libface_detector.dll
  TARGET_IMPLIB = lib\libface_detector.lib
  TARGET_PDB = bin\libface_detector.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\gdwork\code\a3g\src\opencv\out\build\x64-Debug && "D:\soft\Microsoft Visual Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D D:\gdwork\code\a3g\src\opencv\out\build\x64-Debug && "D:\soft\Microsoft Visual Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SD:\gdwork\code\a3g\src\opencv -BD:\gdwork\code\a3g\src\opencv\out\build\x64-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build face_detector: phony bin\libface_detector.dll

build libface_detector.dll: phony bin\libface_detector.dll

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/gdwork/code/a3g/src/opencv/out/build/x64-Debug

build all: phony bin\libface_detector.dll

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles\3.29.5-msvc4\CMakeCCompiler.cmake CMakeFiles\3.29.5-msvc4\CMakeCXXCompiler.cmake CMakeFiles\3.29.5-msvc4\CMakeRCCompiler.cmake CMakeFiles\3.29.5-msvc4\CMakeSystem.cmake D$:\Users\Administrator\Downloads\opencv\build\OpenCVConfig-version.cmake D$:\Users\Administrator\Downloads\opencv\build\OpenCVConfig.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVConfig.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVModules.cmake D$:\gdwork\code\a3g\src\opencv\CMakeLists.txt D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCCompilerABI.c D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystem.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\FindPackageMessage.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles\3.29.5-msvc4\CMakeCCompiler.cmake CMakeFiles\3.29.5-msvc4\CMakeCXXCompiler.cmake CMakeFiles\3.29.5-msvc4\CMakeRCCompiler.cmake CMakeFiles\3.29.5-msvc4\CMakeSystem.cmake D$:\Users\Administrator\Downloads\opencv\build\OpenCVConfig-version.cmake D$:\Users\Administrator\Downloads\opencv\build\OpenCVConfig.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVConfig.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake D$:\Users\Administrator\Downloads\opencv\build\x64\vc16\lib\OpenCVModules.cmake D$:\gdwork\code\a3g\src\opencv\CMakeLists.txt D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCCompilerABI.c D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystem.cmake.in D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\FindPackageMessage.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake D$:\soft\Microsoft$ Visual$ Studio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
