import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:hardware/hardware.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:seasetting/seasetting.dart';
import 'package:oktoast/oktoast.dart';

import '../../../core/router/app_router.dart';

/// 登录类型枚举
enum AuthLoginType {
  readerCard,  // 管理员卡登录
  keyboardInput,  // 密码登录
}

class AdminLoginViewModel extends ChangeNotifier {
  // 登录类型，默认为管理员卡登录
  final Rx<AuthLoginType> loginType = AuthLoginType.readerCard.obs;
  
  // 阅读器状态
  ReaderErrorType readerErrorType = ReaderErrorType.undefine;
  
  // 是否正在请求
  bool isRequesting = false;
  
  // 登录选项
  final List<Map<String, dynamic>> loginOptions = [
    {
      'title': '管理员卡',
      'icon': 'empty_icon',
      'type': AuthLoginType.readerCard,
    },
    {
      'title': '密码登录',
      'icon': 'empty_icon',
      'type': AuthLoginType.keyboardInput,
    },
  ];

  // 初始化函数
  void init(BuildContext context) {
    openReader();
  }

  @override
  void dispose() {
    closeReader();
    super.dispose();
  }

  // 切换登录类型
  Future<void> switchLoginType(String typeName) async {
    AuthLoginType? newType;
    
    if (typeName == '管理员卡') {
      newType = AuthLoginType.readerCard;
    } else if (typeName == '密码登录') {
      newType = AuthLoginType.keyboardInput;
    }

    if (newType != null && loginType.value != newType) {
      if (loginType.value == AuthLoginType.readerCard) {
        // 先关闭阅读器
        await closeReader();
      }
      
      loginType.value = newType;
      
      if (loginType.value == AuthLoginType.readerCard) {
        // 打开阅读器
        openReader();
      }
      
      notifyListeners();
    }
  }

  // 打开读卡器
  void openReader() {
    BuildContext? context = Get.context;
    if (context == null) return;
    
    Map<String, List<HWReaderSettingData>>? map =
        context.read<SettingProvider>().readerConfigData?.authMap;
        
    List<HWReaderSettingData>? readers = map?['读者证认证'];
    
    if (readers?.isNotEmpty ?? false) {
      // 监听阅读器状态
      ReaderManager.instance.controller.addListener(onReaderStateChange);
      context.read<HWTagProvider>().addListener(onCardDetected);
      
      ReaderManager.instance.changeReaders(jsonEncode(readers!)).then((value) {
        ReaderManager.instance.open();
        ReaderManager.instance.untilDeteted();
      });
    } else {
      showToast('未配置读者证阅读器');
    }
  }

  // 关闭读卡器
  Future<void> closeReader() async {
    BuildContext? context = Get.context;
    if (context == null) return;
    
    Map<String, List<HWReaderSettingData>>? map =
        context.read<SettingProvider>().readerConfigData?.authMap;
        
    List<HWReaderSettingData>? readers = map?['读者证认证'];
    
    if (readers?.isNotEmpty ?? false) {
      ReaderManager.instance.controller.removeListener(onReaderStateChange);
      context.read<HWTagProvider>().removeListener(onCardDetected);
      ReaderManager.instance.stopInventory();
      await Future.delayed(const Duration(milliseconds: 100));
      ReaderManager.instance.close();
      await _waitUntilReaderClosed();
    }
  }

  // 等待读卡器完全关闭
  Future<void> _waitUntilReaderClosed() async {
    bool isClosed = false;
    while (!isClosed) {
      await Future.delayed(const Duration(milliseconds: 200), () {
        bool isOpen = ReaderManager.instance.isConnectReader;
        if (isOpen == false) {
          isClosed = true;
        }
      });
    }
  }

  // 监听阅读器状态变化
  void onReaderStateChange() {
    ReaderErrorType type = ReaderManager.instance.controller.type;
    readerErrorType = type;
    notifyListeners();
  }

  // 监听读卡事件
  void onCardDetected() {
    BuildContext? context = Get.context;
    if (context == null) return;
    
    HWTagProvider provider = context.read<HWTagProvider>();
    List<HWTagData> tagList = provider.tagList;
    
    if (tagList.isNotEmpty) {
      bool isNeedResume = true;
      
      for (var tag in tagList) {
        if (tag.barCode?.isNotEmpty ?? false) {
          isNeedResume = false;
          verifyAdmin(tag.barCode!, isNeedResume: true);
          break;
        }
      }
      
      if (isNeedResume) {
        ReaderManager.instance.resumeInventory();
      }
    }
  }

  // 验证管理员
  Future<bool> verifyAdmin(String password, {bool isNeedResume = false}) async {
    isRequesting = true;
    notifyListeners();
    
    try {
      DBSettingInterface db = await DBSettingManager.getDBInstance();
      UserData? userData = await db.verifyUserBy(UserData.adminUser, password);

      if (userData != null) {
        await closeReader();
        // 登录成功，跳转到管理员页面
        AppNavigator.toAdmin2();
        return true;
      } else {
        if (isNeedResume) {
          ReaderManager.instance.resumeInventory();
        }
        showToast('账号或密码错误');
        return false;
      }
    } catch (e) {
      showToast('验证失败: ${e.toString()}');
      return false;
    } finally {
      isRequesting = false;
      notifyListeners();
    }
  }

} 