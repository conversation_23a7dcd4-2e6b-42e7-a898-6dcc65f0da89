import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:oktoast/oktoast.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

// 包导入
import 'package:base_package/base_package.dart';
import 'package:hardware/hardware.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:sealog/sealog.dart';
import 'package:seasetting/seasetting.dart';

// 项目导入
import '../../../core/providers/user_provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/generate_reader_setting_data.dart';
import '../../login/models/operation_type.dart';
import '../services/reader_service.dart';

/// 认证错误类型枚举
enum AuthErrorType {
  network,      // 网络错误
  hardware,     // 硬件错误
  validation,   // 验证错误
  auth,         // 认证错误
  permission,   // 权限错误
  timeout,      // 超时错误
  unknown       // 未知错误
}

/// 认证错误类 - 封装认证过程中可能出现的各种错误
class AuthError implements Exception {
  final String message;
  final AuthErrorType type;
  final dynamic originalError;

  AuthError(this.message, this.type, [this.originalError]);
  
  @override
  String toString() => message;
  
  /// 创建网络错误
  static AuthError network(String message, [dynamic error]) => 
      AuthError('网络错误: $message', AuthErrorType.network, error);
  
  /// 创建硬件错误
  static AuthError hardware(String message, [dynamic error]) => 
      AuthError('硬件错误: $message', AuthErrorType.hardware, error);
      
  /// 创建认证错误
  static AuthError auth(String message, [dynamic error]) => 
      AuthError('认证错误: $message', AuthErrorType.auth, error);
      
  /// 创建超时错误
  static AuthError timeout(String message, [dynamic error]) => 
      AuthError('超时错误: $message', AuthErrorType.timeout, error);
}

/// 认证视图模型 - 处理用户认证相关的业务逻辑
class AuthViewModel extends ChangeNotifier {
  // =============== 常量 ===============
  static const int _maxRetries = 3;
  static const Duration _requestTimeout = Duration(seconds: 10);
  static const Duration _retryDelay = Duration(seconds: 1);

  // =============== 构造函数 ===============
  AuthViewModel({
    required this.context,
    required this.authLoginType,
    required this.operationType,
    ReaderService? readerService,
  }) : _readerService = readerService ?? ReaderService.instance {
    _tagProvider = context.read<HWTagProvider>();
    _settingProvider = Provider.of<SettingProvider>(context, listen: false);
  }

  // =============== 公开属性 ===============
  final BuildContext context;
  final AuthLoginType authLoginType;
  final OperationType operationType;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Sip2PatronInfoData? get readerInfo => _readerInfo;
  bool get isReaderOpen => _openedReaders != null && _openedReaders!.isNotEmpty;

  // =============== 私有属性 ===============
  final ReaderService _readerService;
  late final HWTagProvider _tagProvider;
  late final SettingProvider _settingProvider;

  bool _isLoading = false;
  String? _errorMessage;
  List<HWReaderSettingData>? _openedReaders;
  Sip2PatronInfoData? _readerInfo;
  bool _isDisposed = false;
  bool _isListening = false;

  late final ReaderAuthPswConfig? authConfig;



  // =============== 公开方法 ===============

  /// 初始化视图模型
  Future<void> init() async {
    if (_isDisposed) return;

    try {
      _setLoading(true);
      await _initializeReader();

      authConfig = _settingProvider.readerConfigData?.authPswConfig.firstWhere(
            (element) => AuthLoginTypeMap[element.type] == authLoginType,
        orElse: () => ReaderAuthPswConfig(type: '读者证认证', isNeedPsw: false),
      );
    } catch (e) {
      _handleError(e);
    } finally {
      _setLoading(false);
    }
  }

  /// 请求读者信息
  /// 
  /// [barcode] 条形码
  /// [uid] 用户ID
  /// [psw] 密码（可选）
  /// [info] ID卡信息（可选）
  /// [isNeedResume] 是否需要恢复扫描（可选）
  Future<void> requestReaderInfo(
    String barcode,
    String uid, {
    String? psw,
    HWIDCardInfo? info,
    bool isNeedResume = false,
  }) async {
    if (_isDisposed) return;

    try {
      _setLoading(true);
      await _processReaderInfo(barcode, uid, psw, info, isNeedResume);
    } catch (e) {
      _handleError(e);
    } finally {
      _setLoading(false);
    }
  }

  /// 重新初始化读卡器
  Future<void> reinitializeReader() async {
    if (_isDisposed) return;

    try {
      _setLoading(true);
      
      // 先清理现有资源
      await _cleanup();
      
      // 重新初始化读卡器
      await _initializeReader();
    } catch (e) {
      _handleError(e);
    } finally {
      _setLoading(false);
    }
  }

  /// 清理所有资源
  Future<void> cleanupResources() async {
    if (_isDisposed) return;
    
    try {
      await _cleanup();
    } catch (e) {
      debugPrint('清理资源时发生错误: $e');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _cleanup();
    super.dispose();
  }

  // =============== 私有方法 ===============

  /// 初始化读卡器
  Future<void> _initializeReader() async {
    _tagProvider.clearTagList();
    await _readerService.closeReader();
    await _openReader(authLoginType);
    await LightManager.instance.operateAuthType(authLoginType, true);
  }

  /// 打开读卡器
  Future<void> _openReader(AuthLoginType type) async {
    final readers = _getReadersForType(type);
    // List<HWReaderSettingData> readers = [];
    // var  item =  HWReaderSettingGenerator.generate(readerType:12);
    // readers.add(item);
    openedReaders = readers;
    if (readers?.isEmpty ?? true) {
      if (type != AuthLoginType.keyboardInput) {
        throw AuthError.hardware('未配置阅读器', '请检查设备配置');
      }
      return;
    }
    
    _setupReaderListeners();
    await _readerService.openReader(readers!);
    _openedReaders = readers;
  }

  /// 设置读卡器监听器
  void _setupReaderListeners() {
    if (_isListening) return;
    
    ReaderManager.instance.controller.addListener(_readerStateListener);
    _tagProvider.addListener(_cardListener);
    _isListening = true;

    debugPrint('已设置读卡器监听器');
  }

  /// 移除读卡器监听器
  void _removeReaderListeners() {
    if (!_isListening) return;
    
    ReaderManager.instance.controller.removeListener(_readerStateListener);
    _tagProvider.removeListener(_cardListener);
    _isListening = false;
    
    debugPrint('已移除读卡器监听器');
  }

  /// 获取指定类型的读卡器配置
  List<HWReaderSettingData>? _getReadersForType(AuthLoginType type) {
    final readerConfig = _settingProvider.readerConfigData;
    if (readerConfig == null) return null;

    final key = AuthLoginMapReverse[type];
    if (key == null) return null;

    return readerConfig.authMap[key] ?? [];
  }

  /// 监听读卡器状态
  void _readerStateListener() {
    if (_isDisposed) return;
    
    // 获取当前读卡器状态 - 如果需要可以根据状态做不同处理
    final type = ReaderManager.instance.controller.type;
    debugPrint('读卡器状态变化: $type');
  }

  /// 扫描稳定阈值 - 连续几次相同数量视为稳定
  static const int SCAN_STABILITY_THRESHOLD = 3;
  /// 连续几次扫描数量相同的计数
  int _stableCount = 0;

  /// 最近几次扫描的数量记录
  final List<int> _lastScanCounts = [];

  Future<void> _onTagScanned() async {
    try {
      HWTagProvider? provider = Get.context?.read<HWTagProvider>();
      print('扫描到标签数量: ${provider?.tagList.length}');

      // 记录当前扫描数量
      int currentCount = provider?.tagList?.length ?? 0;
      _lastScanCounts.add(currentCount);
      if (_lastScanCounts.length > SCAN_STABILITY_THRESHOLD) {
        _lastScanCounts.removeAt(0);
      }

      // 检查是否连续几次数量相等
      if (_lastScanCounts.length == SCAN_STABILITY_THRESHOLD) {
        bool isStable =
        _lastScanCounts.every((count) => count == _lastScanCounts[0]);
        if (isStable) {
          _stableCount++; // 增加稳定计数器
          if (_stableCount >= SCAN_STABILITY_THRESHOLD) {
            // 确保连续三次稳定
            // 只有在连续三次数量相同时才继续处理
            List<HWTagData> tagList = provider?.tagList ?? [];
            if (tagList.isEmpty) return;
            print('连续稳定次数');
            // _processTagData(tagList);
            // await stopScan();
            // await processBarcodeMapAfterScan();
          }
        } else {
          // _stableCount = 0; // 如果不稳定，重置计数器
        }
      }
    } catch (e) {
      print('标签扫描异常: $e');
      // await stopScan();
      // WidgetsBinding.instance.addPostFrameCallback((_) {
      //   handleOperationError(e);
      //   isOperating = false;
      //   notifyListeners();
      //   Get.back();
      //   if (showErrorDialogFunc != null) {
      //     showErrorDialogFunc!(e);
      //   }
      //   _triggerOperationComplete();
      // });
    }
  }

  /// 监听读卡
  void _cardListener() async {
    if (_isDisposed) return;
    if (!(_tagProvider.readerList.isNotEmpty)) {
      if (_tagProvider.tagList.isNotEmpty) {
        ReaderManager.instance.resumeInventory();
      }
      return;
    }
    if (_tagProvider.type != HWTagType.addedItem) {
      ReaderManager.instance.resumeInventory();
      return;
    }
    try {
      final readerList = [..._tagProvider.readerList];
      if (readerList.isNotEmpty) {
        await _processCardData(readerList);
      }
    } catch (e) {
      print('处理卡片数据时发生错误: $e');
      ReaderManager.instance.resumeInventory();
    }
  }

  /// 处理卡片数据
  Future<void> _processCardData(List<HWTagData> tagList) async {
    bool isNeedResume = true;
    for (var tag in tagList) {
      final barcode = tag.barCode;
      if (barcode?.isNotEmpty ?? false) {
        isNeedResume = false;
        try {
          // 检查是否为管理员卡
          final type = await SettingUtil.verifyAdminCardNum(barcode!);
          if (type != null) {
            await _cleanupAndNavigateToAdmin(type);
            break;
          } else {
            // 处理普通读者卡
              final info = HWIDCardInfo.fromJson(tag.info ?? {});
              await requestReaderInfo(
                barcode,
                tag.uid ?? '',
                info: info,
                isNeedResume: true,
              );
            // }

            break;
          }
        } catch (e) {
          debugPrint('验证卡片时发生错误: $e');
          isNeedResume = true;
          break;
        }
      }
    }

    if (isNeedResume) {
      ReaderManager.instance.resumeInventory();
    }
  }

  /// 处理读者信息
  Future<void> _processReaderInfo(
    String barcode,
    String uid,
    String? psw,
    HWIDCardInfo? info,
    bool isNeedResume,
  ) async {
    final cardTypes = _getCardTypes();
    final readerInfo = await _fetchReaderInfo(barcode, uid, psw, cardTypes);

    if (readerInfo?.ValidPatron == 'Y') {

      if(readerInfo?.ValidPatronPassword != 'Y'){
        showToast((readerInfo?.ScreenMessage?.isNotEmpty ?? false) ? (readerInfo?.ScreenMessage ?? '') : '密码错误');
        return;
      }
      await _handleValidPatron(readerInfo!, barcode, uid, info);
    } else {
      // if (isNeedResume) {
      //   ReaderManager.instance.resumeInventory();
      // }
      // await _handleInvalidPatron(readerInfo, barcode, uid, info);
       showToast((readerInfo?.ScreenMessage?.isNotEmpty ?? false) ? (readerInfo?.ScreenMessage ?? '') :  '读者证不存在或密码错误');
       return;
    }
  }

  /// 获取卡片类型列表
  List<String>? _getCardTypes() {
    final cardTypes = _openedReaders?.firstOrNull?.info?.valueForKey('cardType');
    if (cardTypes == null) return null;
    
    return cardTypes.split(',')
      ..removeWhere((element) => element.trim().isEmpty);
  }

  /// 获取读者信息
  Future<Sip2PatronInfoData?> _fetchReaderInfo(
    String barcode, 
    String uid, 
    String? psw, 
    List<String>? cardtypeList
  ) async {
    if (cardtypeList?.isNotEmpty ?? false) {
      // 尝试每种卡类型
      for (String cardType in cardtypeList!) {
        try {
          final readerInfo = await _tryGetReaderInfo(barcode, uid, psw, cardType);
          if (readerInfo?.isSuccess ?? false) {
            return readerInfo;
          }
        } catch (e) {
          print('使用卡类型 $cardType 获取读者信息失败: $e');
          // 继续尝试下一种卡类型
          continue;
        }
      }
    }
    
    // 无卡类型或所有卡类型都失败时，使用默认请求
    return await _tryGetReaderInfo(barcode, uid, psw);
  }

  /// 尝试获取读者信息
  Future<Sip2PatronInfoData?> _tryGetReaderInfo(
    String barcode,
    String uid,
    String? psw, [
    String? cardType,
  ]) async {
    int retryCount = 0;
      try {
        final result = await NewSip2Request.instance.getReaderInfo(
          barcode,
          uid,
          psw: psw,
          CardType: cardType,
        ).timeout(_requestTimeout);
        
        return result;
      } catch (e) {
        rethrow;
      }

    return null;
  }

  /// 处理有效读者
  Future<void> _handleValidPatron(
    Sip2PatronInfoData readerInfo,
    String barcode,
    String uid,
    HWIDCardInfo? info,
  ) async {
    final record = _createRecordData(
      barcode: barcode,
      uid: uid,
      info: info,
      readerInfo: readerInfo,
      isSuccess: true,
    );

    await _cleanup();
    await _saveLoginRecord(record);

    _readerInfo = readerInfo;
    if (!_isDisposed) {
      notifyListeners();
    }

    await _checkAuthPassword(readerInfo);
  }

  /// 处理无效读者
  Future<void> _handleInvalidPatron(
    Sip2PatronInfoData? readerInfo,
    String barcode,
    String uid,
    HWIDCardInfo? info,
  ) async {
    final error = (readerInfo?.ScreenMessage?.isNotEmpty ?? false)
        ? readerInfo!.ScreenMessage!
        : '读者卡不存在';

    final record = _createRecordData(
      barcode: barcode,
      uid: uid,
      info: info,
      readerInfo: readerInfo,
      isSuccess: false,
      errorMessage: error,
    );

    await _saveLoginRecord(record);
    _errorMessage = error;
    if (!_isDisposed) {
      notifyListeners();
    }
  }

  /// 创建登录记录
  SeaRecordData _createRecordData({
    required String barcode,
    required String uid,
    required bool isSuccess,
    HWIDCardInfo? info,
    Sip2PatronInfoData? readerInfo,
    String? errorMessage,
  }) {
    return SeaRecordData.fromJson({})
      ..operate_type = SeaSettingRecordMap[SeaSettingRecordType.login] ?? ''
      ..auth_type = SeaSettingAuthMap[authLoginType] ?? ''
      ..barcode = barcode
      ..uid = uid
      ..dateTime = BPUtils.getNowTimeStamp()
      ..readerIdCard = info?.idnum ?? ''
      ..readerName = info?.name ?? ''
      ..result = isSuccess ? '0' : '-1'
      ..readerId = readerInfo?.PatronIdentifier ?? ''
      ..reason = errorMessage ?? '';
  }

  /// 保存登录记录
  Future<void> _saveLoginRecord(SeaRecordData record) async {
    try {
      final db = await DBSettingManager.getDBInstance();
      await db.insertRecord(record);
      debugPrint('保存登录记录成功: ${record.barcode}');
    } catch (e) {
      debugPrint('保存登录记录失败: $e');
    }
  }

  /// 检查认证密码
  Future<void> _checkAuthPassword(Sip2PatronInfoData readerInfo) async {
    if (!context.mounted || _isDisposed) return;

    final authConfig = _settingProvider.readerConfigData?.authPswConfig.firstWhere(
      (element) => AuthLoginTypeMap[element.type] == authLoginType,
      orElse: () => ReaderAuthPswConfig(type: '读者证认证', isNeedPsw: false),
    );

    if (authConfig == null || !authConfig.isNeedPsw) {
      _navigateToNextPage(readerInfo);
    } else {
      _navigateToNextPage(readerInfo);
      // // 实现密码验证逻辑
      // await _showPasswordDialog(readerInfo);
    }
  }

  /// 显示密码验证对话框
  Future<void> _showPasswordDialog(Sip2PatronInfoData readerInfo) async {

    // Get.to(() => AccountAuthPage(
    //   list: const [],
    //   type: operationType,
    //   loginType: authLoginType,
    //   readerInfo: readerInfo,
    // ));

    
    // 暂时直接跳转
    _navigateToNextPage(readerInfo);
  }

  /// 导航到下一个页面
  void _navigateToNextPage(Sip2PatronInfoData readerInfo) {
    if (!context.mounted || _isDisposed) return;
    
    context.read<UserProvider>().updateUserInfo(readerInfo);

    // 在视图模型中，我们不直接导航，而是通过更新状态来触发视图层的导航
    // 视图层会通过监听 UserProvider 的 isAuthenticated 状态来决定显示哪个界面
  }

  /// 清理资源并导航到管理页面
  Future<void> _cleanupAndNavigateToAdmin(int type) async {
    await _cleanup();
    EasyLoading.dismiss();
    AppNavigator.untilHome();
    SettingUtil.jumpAdminPage(type);
  }

  /// 清理资源
  Future<void> _cleanup() async {
    _removeReaderListeners();
    await _readerService.closeReader(showToast: false);
    await LightManager.instance.operateAuthType(authLoginType, false);
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isDisposed) return;
    
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 处理错误
  void _handleError(dynamic error) {
    if (_isDisposed) return;

    String errorMessage;
    
    if (error is AuthError) {
      errorMessage = error.message;
    } else if (error is ReaderServiceException) {
      errorMessage = error.message;
    } else if (error.toString().contains('信号灯超时时间已到') || 
        error.toString().contains('SocketException')) {
      errorMessage = '网络连接超时，请检查网络后重试';
    } else {
      errorMessage = error.toString().replaceAll('Exception: ', '');
    }

    _errorMessage = errorMessage;
    _setLoading(false);

    if (context.mounted) {
      showToast(
        errorMessage,
        position: ToastPosition.bottom,
        duration: const Duration(seconds: 3),
      );
    }
  }

  List<HWReaderSettingData>? openedReaders;


  onScanContent(String value) async{
    if (value.isEmpty) return;
    if (openedReaders?.isNotEmpty ?? false) {
      CoderCommonInterface? coder = getCoder(null, openedReaders!.first);
      if (coder != null){
        String ret = await coder.decodeData(value, value, setting:openedReaders!.first);
        Map<String,dynamic> map = jsonDecode(ret);
        HWTagData tag = HWTagData.fromJson(map);
        if (tag.barCode?.isNotEmpty ?? false){
          await requestReaderInfo(tag.barCode ??'', tag.barCode??'',info: HWIDCardInfo.fromJson(tag.info??{}));
        }
      }
    }
  }
  onScanContent2(String value) async{}

  CoderCommonInterface? getCoder(
      int? tagFrequency, HWReaderSettingData setting) {
    CoderCommonInterface? coder;
    if (setting.readerType == 6) {
      //  双频阅读器 单独处理
      HWExtraSettingData? extraData = setting.extras?.firstOrNull;

      if (setting.selectedCardType == '高频&超高频') {
        if (extraData != null || extraData is HWDFExtraData) {
          if (tagFrequency == 0) {
            // 高频
            coder = SECoderMap[extraData?.valueForKey('hfDecoderType')];
          } else {
            // 超高频
            coder = SECoderMap[extraData?.valueForKey('uhfDecoderType')];
          }
        }
      } else {
        print(extraData?.valueForKey('decoderType'));
        coder = SECoderMap[extraData?.valueForKey('decoderType')];
      }
    } else {
      coder = SECoderMap[setting.info?.valueForKey('decoderType')];
    }
    return coder;
  }




}