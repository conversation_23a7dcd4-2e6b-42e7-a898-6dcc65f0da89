import 'package:flutter/material.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';

class CustomCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;
  final bool enabled;

  const CustomCheckbox({
    super.key,
    required this.value,
    this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: enabled ? () => onChanged?.call(!value) : null,
      child: Container(
        width: 40.p,
        height: 40.p,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(4.p)),
          border: value? null: Border.all(
            color: const Color(0xFF54A0FF),
            width: 1.p,
          ),
        ),
        child: value
            ? Image.asset(
                AssetUtil.fullPath('checkbox_checked.png'),
                width: 40.p,
                height: 40.p,
              )
            : null,
      ),
    );
  }
}