import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/setting/SettingProvider.dart';

import '../../../core/router/app_router.dart';
import '../../../core/router/route_extension.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/widgets/countdown_timer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/footer.dart';
import '../../../shared/widgets/logo_banner.dart';
import '../models/operation_type.dart';
import '../view_models/login_view_model.dart';
import 'widgets/auth_options.dart';

class LoginTypeView extends StatelessWidget {
  final OperationType operationType;

  const LoginTypeView({super.key, required this.operationType});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      topWrapper: const LogoBanner(tailWidget: AutoCountdown()),
      mainWrapper: ChangeNotifierProvider(
          create: (context) =>
              LoginViewModel(context: context, operationType: operationType),
          builder: (context, child) {
            return const AuthOptions();
          }),
      bottomWrapper: Footer(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 80.p, vertical: 25.p),
          child: CustomButton.outline(
            text: '退出',
            onTap: () {
              AppNavigator.back();
            },
          ),
        ),
      ),
    );
  }
}
