2025-07-30 18:04:25.037104: 61
2025-07-30 18:04:25.040094: getDBPath : C:\Users\<USER>\AppData\Roaming\com.example\a3g\a3g_setting.db
2025-07-30 18:04:25.267485: socket 连接成功,isBroadcast:false
2025-07-30 18:04:25.268484: changeSocketStatus:true
2025-07-30 18:04:25.268484: Sip2HeartBeatManager start loginACS:false askACS:false
2025-07-30 18:04:25.268484: Sip2HeartBeatManager start loginACS:false askACS:true
2025-07-30 18:04:25.269481: 发送心跳
2025-07-30 18:04:25.269481: Req msgType：Sip2MsgType.scStatus ,length:43， ret:  99TC00522.00|CP2341,E43A6E7CA721|AY1AZF6CB
2025-07-30 18:04:25.292421: 已添加配置变化监听器
2025-07-30 18:04:25.292421: 认证优先级管理器: 开始加载认证方式
2025-07-30 18:04:25.294428: 配置的排序: [人脸识别认证, 电子社保卡认证, 微信二维码认证, 身份证认证, 社保卡认证, 读者证认证, 腾讯E证通认证, 手动输入认证, 市民卡认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-07-30 18:04:25.295412: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-07-30 18:04:25.295412: 认证优先级管理器: 按配置顺序添加 人脸识别认证 -> 人脸识别
2025-07-30 18:04:25.295412: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-07-30 18:04:25.295412: 认证优先级管理器: 按配置顺序添加 身份证认证 -> 身份证
2025-07-30 18:04:25.295412: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-07-30 18:04:25.296408: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-07-30 18:04:25.296408: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-07-30 18:04:25.296408: 认证优先级管理器: 最终排序结果: 人脸识别 -> 电子社保卡 -> 身份证 -> 社保卡 -> 读者证 -> 微信扫码
2025-07-30 18:04:25.296408: 认证优先级管理器: 主要认证方式: 人脸识别
2025-07-30 18:04:25.296408: 更新认证方式显示文字: 人脸识别/电子社保卡/身份证/社保卡/读者证/微信扫码
2025-07-30 18:04:25.298405: Rsp : 98YYYYNN18018020250730    180429AO|AF状态查询码不正确|AG
2025-07-30 18:04:25.645478: dispose IndexPage
2025-07-30 18:04:25.646477: IndexPage dispose
2025-07-30 18:04:40.323228: 手动触发跳转到认证页面
2025-07-30 18:04:40.352182: AuthView初始化 - 主认证方式: 人脸识别
2025-07-30 18:04:40.353148: 尝试初始化多认证系统
2025-07-30 18:04:40.353148: 多认证管理器状态变更: initializing
2025-07-30 18:04:40.354147: 认证优先级管理器: 开始加载认证方式
2025-07-30 18:04:40.354147: 配置的排序: [人脸识别认证, 电子社保卡认证, 微信二维码认证, 身份证认证, 社保卡认证, 读者证认证, 腾讯E证通认证, 手动输入认证, 市民卡认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-07-30 18:04:40.354147: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-07-30 18:04:40.355143: 认证优先级管理器: 按配置顺序添加 人脸识别认证 -> 人脸识别
2025-07-30 18:04:40.355143: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-07-30 18:04:40.355143: 认证优先级管理器: 按配置顺序添加 身份证认证 -> 身份证
2025-07-30 18:04:40.355143: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-07-30 18:04:40.356148: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-07-30 18:04:40.356148: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-07-30 18:04:40.356148: 认证优先级管理器: 最终排序结果: 人脸识别 -> 电子社保卡 -> 身份证 -> 社保卡 -> 读者证 -> 微信扫码
2025-07-30 18:04:40.356148: 认证优先级管理器: 主要认证方式: 人脸识别
2025-07-30 18:04:40.356148: 多认证管理器: 从优先级管理器加载的认证方式: 人脸识别 -> 电子社保卡 -> 身份证 -> 社保卡 -> 读者证 -> 微信扫码
2025-07-30 18:04:40.357136: 多认证管理器: 当前默认显示方式: 人脸识别
2025-07-30 18:04:40.357136: 初始化人脸认证服务
2025-07-30 18:04:40.357136: 📷 开始人脸识别认证服务初始化流程...
2025-07-30 18:04:40.357136: 🔧 第一步：初始化摄像头系统...
2025-07-30 18:04:40.358134: 📷 检查人脸检测器初始化状态...
2025-07-30 18:04:40.358134: 🔧 开始初始化人脸检测器...
2025-07-30 18:04:40.358134: 尝试加载库: C:\Users\<USER>\Desktop\Release (4)\Release\windows\runner\Debug\libface_detector.dll
2025-07-30 18:04:40.358134: 文件不存在: C:\Users\<USER>\Desktop\Release (4)\Release\windows\runner\Debug\libface_detector.dll
2025-07-30 18:04:40.358134: 尝试加载库: C:\Users\<USER>\Desktop\Release (4)\Release\windows\runner\Debug\data\libface_detector.dll
2025-07-30 18:04:40.359131: 文件不存在: C:\Users\<USER>\Desktop\Release (4)\Release\windows\runner\Debug\data\libface_detector.dll
2025-07-30 18:04:40.359131: 尝试加载库: C:\Users\<USER>\Desktop\Release (4)\Release\libface_detector.dll
2025-07-30 18:04:40.359131: 成功加载库: C:\Users\<USER>\Desktop\Release (4)\Release\libface_detector.dll
2025-07-30 18:04:40.359131: 成功加载系统负载和处理间隔函数
2025-07-30 18:04:44.397334: 开始初始化门锁连接...
2025-07-30 18:04:44.398332: 创建门锁连接: 主门锁设备 (COM1)
2025-07-30 18:04:44.398332: 门锁继电器数据监听已启动
2025-07-30 18:04:44.398332: 门锁继电器连接成功: COM1
2025-07-30 18:04:44.399328: 设置认证方法: AuthMethod.face, 当前方法: AuthMethod.face
2025-07-30 18:04:44.399328: ✅ 人脸检测器初始化完成，耗时: 21ms
2025-07-30 18:04:44.400327: 🔍 检测可用摄像头设备...
2025-07-30 18:04:44.400327: ✅ 检测到 2 个摄像头设备: [0, 1]
2025-07-30 18:04:44.400327: 📹 启动摄像头（带重试机制）...
2025-07-30 18:04:44.400327: 📹 尝试使用摄像头ID: 0
2025-07-30 18:04:44.400327: 📹 摄像头启动尝试 1/2，ID: 0
2025-07-30 18:04:44.401323: 门锁连接成功: COM1
2025-07-30 18:04:44.401323: 门锁连接初始化完成，共建立 1 个连接
2025-07-30 18:04:44.401323: changeReaders
2025-07-30 18:04:44.401323: createIsolate isOpen:false,isOpening:false
2025-07-30 18:04:44.402320: ✅ 摄像头启动成功，ID: 0，尝试次数: 1，耗时: 2180ms，后端: DirectShow
2025-07-30 18:04:44.402320: 🔧 配置摄像头参数...
2025-07-30 18:04:44.402320: ✅ 摄像头参数配置完成
2025-07-30 18:04:44.402320: 🤖 预加载人脸检测模型...
2025-07-30 18:04:44.403318: 预加载DNN模型...
2025-07-30 18:04:44.403318: DNN模型预加载完成，获取到 0 个结果
2025-07-30 18:04:44.403318: ✅ 人脸检测模型预加载完成
2025-07-30 18:04:44.403318: 🔍 验证摄像头功能...
2025-07-30 18:04:44.404316: ⚠️ 摄像头功能验证失败，但继续执行
2025-07-30 18:04:44.404316: ✅ 人脸检测器初始化完成
2025-07-30 18:04:44.404316: ✅ 摄像头系统初始化成功
2025-07-30 18:04:44.404316: 🔧 第二步：初始化百度人脸识别SDK...
2025-07-30 18:04:44.405312: 配置文件不存在，使用默认配置: C:\Users\<USER>\Desktop\Release (4)\Release\sdk_config.json
2025-07-30 18:04:44.405312: 从配置获取SDK路径: D:\FaceOfflineSdk
2025-07-30 18:04:52.824798: ✅ SDK路径验证成功
2025-07-30 18:04:52.825797: 🔧 配置SDK路径: D:\FaceOfflineSdk
2025-07-30 18:04:52.825797: SDK未初始化，无法设置路径
2025-07-30 18:04:52.825797: ✅ SDK路径配置完成，错误码: -100
2025-07-30 18:04:52.825797: 🚀 开始初始化百度人脸识别SDK...
2025-07-30 18:04:52.825797: 百度SDK初始化成功, 人脸数据库加载结果: 1
2025-07-30 18:04:52.831782: ✅ 百度人脸识别SDK初始化成功
2025-07-30 18:04:52.831782: ✅ 百度SDK状态: 已初始化，人脸数量: 839，授权状态: 已授权
2025-07-30 18:04:52.831782: ✅ 百度人脸识别SDK初始化成功
2025-07-30 18:04:52.831782: 🔧 第三步：启动系统监控...
2025-07-30 18:04:52.832777: ✅ 系统监控启动成功
2025-07-30 18:04:52.832777: ✅ 人脸识别认证服务初始化完成
2025-07-30 18:04:52.832777: 人脸认证服务初始化成功
2025-07-30 18:04:52.832777: 初始化人脸认证服务
2025-07-30 18:04:52.832777: 人脸识别 认证服务初始化成功
2025-07-30 18:04:52.833774: 初始化读卡器认证服务
2025-07-30 18:04:52.833774: 读卡器认证服务初始化成功
2025-07-30 18:04:52.833774: 初始化共享读卡器认证服务
2025-07-30 18:04:52.833774: 电子社保卡 认证服务初始化成功
2025-07-30 18:04:52.834771: 身份证 认证服务初始化成功
2025-07-30 18:04:52.834771: 社保卡 认证服务初始化成功
2025-07-30 18:04:52.834771: 读者证 认证服务初始化成功
2025-07-30 18:04:52.834771: 初始化二维码扫描认证服务
2025-07-30 18:04:52.835776: 初始化二维码扫码器
2025-07-30 18:04:52.835776: 二维码扫码器初始化完成
2025-07-30 18:04:52.836766: 二维码扫描认证服务初始化成功
2025-07-30 18:04:52.836766: 初始化共享二维码扫描认证服务
2025-07-30 18:04:52.836766: 微信扫码 认证服务初始化成功
2025-07-30 18:04:52.836766: 认证服务初始化完成，共初始化 6 种认证方式
2025-07-30 18:04:52.836766: 多认证管理器状态变更: idle
2025-07-30 18:04:52.837764: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.face, AuthMethod.eletricSocialSecurityCard, AuthMethod.idCard, AuthMethod.socialSecurityCard, AuthMethod.readerCard, AuthMethod.wechatScanQRCode]
2025-07-30 18:04:52.837764: 检测到多种认证方式(6种)，启用多认证模式
2025-07-30 18:04:52.837764: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 18:04:52.837764: 多认证管理器状态变更: listening
2025-07-30 18:04:52.837764: 启动所有认证方式监听: [AuthMethod.face, AuthMethod.eletricSocialSecurityCard, AuthMethod.idCard, AuthMethod.socialSecurityCard, AuthMethod.readerCard, AuthMethod.wechatScanQRCode]
2025-07-30 18:04:52.838760: 准备启动 3 个物理认证服务
2025-07-30 18:04:52.838760: 开始人脸认证监听
2025-07-30 18:04:52.838760: 🚀 开始人脸识别监听（使用FaceCapturePolling方式）...
2025-07-30 18:04:52.838760: 🆕 创建新的人脸捕获轮询服务...
2025-07-30 18:04:52.838760: ✅ 人脸捕获轮询服务初始化成功
2025-07-30 18:04:52.839758: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 18:04:52.839758: ✅ 人脸识别监听启动成功
2025-07-30 18:04:52.839758: 人脸认证监听启动成功
2025-07-30 18:04:52.839758: 人脸识别 认证服务启动成功
2025-07-30 18:04:52.840755: 开始读卡器认证监听
2025-07-30 18:04:52.840755: 强制重新配置读卡器以确保状态一致性
2025-07-30 18:04:52.840755: 完全重置读卡器连接和监听器状态...
2025-07-30 18:04:52.840755: 已移除读卡器状态监听器
2025-07-30 18:04:52.840755: 已移除标签数据监听器
2025-07-30 18:04:52.841753: 所有卡片监听器已移除
2025-07-30 18:04:52.841753: stopInventory newPort:null
2025-07-30 18:04:52.844746: createIsolate newport null
2025-07-30 18:04:53.043214: 发送 关闭 阅读器newPort:SendPort
2025-07-30 18:04:53.043214: 读卡器连接已完全关闭
2025-07-30 18:04:53.048230: 读卡器连接和监听器状态已完全重置
2025-07-30 18:04:53.052207: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 18:04:53.053189: 添加设备配置: 身份证认证 -> 1个设备
2025-07-30 18:04:53.053189: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 18:04:53.054191: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 18:04:53.054191: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-07-30 18:04:53.055185: 添加设备配置: 微信扫码认证 -> 1个设备
2025-07-30 18:04:53.055185: 验证读卡器配置: 类型=5, 解码器=不解析
2025-07-30 18:04:53.055185: 添加有效设备: type=5, id=5
2025-07-30 18:04:53.056180: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 18:04:53.056180: 添加有效设备: type=2, id=2
2025-07-30 18:04:53.056180: 验证读卡器配置: 类型=12, 解码器=平台社保卡解码
2025-07-30 18:04:53.056180: 添加有效设备: type=12, id=12
2025-07-30 18:04:53.057178: 总共加载了3个设备配置
2025-07-30 18:04:53.057178: changeReaders
2025-07-30 18:04:53.057178: createIsolate isOpen:false,isOpening:true
2025-07-30 18:04:53.058175: open():SendPort
2025-07-30 18:04:53.058175: untilDetcted():SendPort
2025-07-30 18:04:53.059172: 读卡器配置完成，共 3 个设备
2025-07-30 18:04:53.059172: 已移除读卡器状态监听器
2025-07-30 18:04:53.059172: 已移除标签数据监听器
2025-07-30 18:04:53.060169: 所有卡片监听器已移除
2025-07-30 18:04:53.060169: 已添加读卡器状态监听器
2025-07-30 18:04:53.060169: 已添加标签数据监听器
2025-07-30 18:04:53.061167: 开始监听卡片数据 - 所有监听器已就绪
2025-07-30 18:04:53.061167: 读卡器认证监听启动成功
2025-07-30 18:04:53.061167: 电子社保卡、身份证、社保卡、读者证 认证服务启动成功
2025-07-30 18:04:53.062166: 开始二维码扫描认证监听
2025-07-30 18:04:53.062166: 开始二维码扫描监听
2025-07-30 18:04:53.063163: 二维码扫描认证监听启动成功
2025-07-30 18:04:53.064161: 微信扫码 认证服务启动成功
2025-07-30 18:04:53.064161: 所有认证服务启动完成，成功启动 3 个服务
2025-07-30 18:04:53.065158: 当前可用的认证方式: 人脸识别、电子社保卡、身份证、社保卡、读者证、微信扫码
2025-07-30 18:04:53.065158: 多认证系统初始化完成，正在同时监听以下认证方式: 人脸识别、电子社保卡、身份证、社保卡、读者证、微信扫码
2025-07-30 18:04:53.065158: subThread :ReaderCommand.close
2025-07-30 18:04:53.066155: commandRsp:ReaderCommand.close
2025-07-30 18:04:53.067152: cacheUsedReaders:()
2025-07-30 18:04:53.067152: close:done
2025-07-30 18:04:53.068149: changeType:ReaderErrorType.closeSuccess
2025-07-30 18:04:53.069145: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 18:04:53.069145: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 18:04:53.070144: already close all reader
2025-07-30 18:04:55.684203: 构建叠加认证区域，当前方式: AuthMethod.face
2025-07-30 18:04:55.685200: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 18:04:55.685200: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-07-30 18:04:55.686198: 构建认证区域，认证方式: AuthMethod.idCard
2025-07-30 18:04:55.686198: 构建身份证认证界面
2025-07-30 18:04:55.686198: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-07-30 18:04:55.686198: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 18:04:55.687195: 构建读者证认证界面
2025-07-30 18:04:55.687195: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-07-30 18:04:55.687195: === 构建多认证底部组件 ===
2025-07-30 18:04:55.688192: 显示底部组件: false
2025-07-30 18:04:55.688192: 反馈状态: AuthFeedbackState.idle
2025-07-30 18:04:55.688192: 用户信息: null (null)
2025-07-30 18:04:55.688192: 底部组件被隐藏，返回空组件
2025-07-30 18:04:55.689190: 📷 CameraPreviewWidget: 检查摄像头检测器初始化状态...
2025-07-30 18:04:55.689190: ✅ CameraPreviewWidget: 摄像头检测器已初始化，跳过初始化步骤
2025-07-30 18:04:55.689190: 📹 CameraPreviewWidget: 启动摄像头 0...
2025-07-30 18:04:55.689190: CameraPreviewWidget: 开始初始化人脸识别服务...
2025-07-30 18:04:55.689190: 人脸识别认证服务已初始化
2025-07-30 18:04:55.691185: Instance of 'SysConfigData'
2025-07-30 18:04:55.691185: Instance of 'SysConfigData'
2025-07-30 18:04:55.692182: ✅ CameraPreviewWidget: 摄像头启动成功，耗时: 2617ms
2025-07-30 18:04:55.692182: CameraPreviewWidget: 预加载DNN模型...
2025-07-30 18:04:55.693180: CameraPreviewWidget: DNN模型预加载完成，获取到 0 个结果
2025-07-30 18:04:55.693180: 人脸识别已在监听中
2025-07-30 18:04:55.693180: CameraPreviewWidget: 帧捕获已启动，间隔: 60ms
2025-07-30 18:04:55.694176: CameraPreviewWidget: 人脸检测器已启动，检测间隔: 80ms
2025-07-30 18:04:55.694176: ✅ CameraPreviewWidget: 摄像头初始化完成
2025-07-30 18:04:55.694176: CameraPreviewWidget: 人脸识别服务初始化成功
2025-07-30 18:04:55.695174: subThread :ReaderCommand.readerList
2025-07-30 18:04:55.695174: commandRsp:ReaderCommand.readerList
2025-07-30 18:04:55.696172: readerList：3,readerSetting：3
2025-07-30 18:04:55.696172: cacheUsedReaders:3
2025-07-30 18:04:55.697170: subThread :ReaderCommand.open
2025-07-30 18:04:55.697170: commandRsp:ReaderCommand.open
2025-07-30 18:04:55.698165: ICC_Reader_Open ret:3084
2025-07-30 18:04:55.698165: open reader readerType ：5 ret：0
2025-07-30 18:04:55.698165: wsurl:************:9091
2025-07-30 18:04:55.699164: subThread :ReaderCommand.untilDetected
2025-07-30 18:04:55.699164: commandRsp:ReaderCommand.untilDetected
2025-07-30 18:04:55.701159: open():SendPort
2025-07-30 18:04:55.701159: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:04:55.700)
2025-07-30 18:04:55.702155: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 18:04:55.700)
2025-07-30 18:04:55.702155: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 18:04:55.702155: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 18:04:55.703152: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 18:04:55.703152: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 285.3966979980469, 数据大小: 0 bytes
2025-07-30 18:04:55.703152: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 18:04:55.703152: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 18:04:55.704149: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 1544 bytes, 实际置信度: 285.3966979980469
2025-07-30 18:04:55.704149: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 18:04:55.704149: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 18:04:55.705146: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 18:04:55.705146: ⏹️ 停止人脸捕获轮询
2025-07-30 18:04:55.705146: 暂停人脸轮询，开始认证流程
2025-07-30 18:04:56.006342: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 18:04:56.006342: untilDetcted():SendPort
2025-07-30 18:04:56.006342: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "4bbdf606680db1ca5bab2b33eb763edd",
		"log_id" : "1753869896006",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 52.82,
				"user_id" : "T36062202100204"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 18:04:56.006342: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 18:04:56.007338: 🎯 识别结果 1: 用户ID=T36062202100204, 分组=hh_group, 得分=52.82
2025-07-30 18:04:56.007338: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 18:04:56.007338: 认证完成，恢复人脸轮询
2025-07-30 18:04:56.007338: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:04:56.008336: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:04:56.008336: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:04:56.008336: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:04:56.009333: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:04:56.010332: subThread :ReaderCommand.readerList
2025-07-30 18:04:56.010332: commandRsp:ReaderCommand.readerList
2025-07-30 18:04:56.011328: readerList：1,readerSetting：1
2025-07-30 18:04:56.011328: cacheUsedReaders:3
2025-07-30 18:04:56.012327: subThread :ReaderCommand.open
2025-07-30 18:04:56.012327: commandRsp:ReaderCommand.open
2025-07-30 18:04:56.012327: wsurl:************:9091
2025-07-30 18:04:56.013324: subThread :ReaderCommand.untilDetected
2025-07-30 18:04:56.013324: commandRsp:ReaderCommand.untilDetected
2025-07-30 18:04:56.495038: AuthView: 检测到 1 个人脸
2025-07-30 18:04:56.576843: AuthView: 检测到 1 个人脸
2025-07-30 18:04:56.662589: AuthView: 检测到 1 个人脸
2025-07-30 18:04:56.735421: AuthView: 检测到 1 个人脸
2025-07-30 18:04:56.815177: AuthView: 检测到 1 个人脸
2025-07-30 18:04:56.896960: AuthView: 检测到 1 个人脸
2025-07-30 18:04:56.980740: AuthView: 检测到 1 个人脸
2025-07-30 18:04:58.008988: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:04:58.008)
2025-07-30 18:04:58.009984: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 18:04:58.008)
2025-07-30 18:04:58.009984: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 18:04:58.010981: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 18:04:58.010981: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 18:04:58.010981: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 261.0579528808594, 数据大小: 0 bytes
2025-07-30 18:04:58.011978: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 18:04:58.011978: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 18:04:58.011978: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3712 bytes, 实际置信度: 261.0579528808594
2025-07-30 18:04:58.012975: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 18:04:58.012975: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 18:04:58.012975: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 18:04:58.013974: ⏹️ 停止人脸捕获轮询
2025-07-30 18:04:58.016980: 暂停人脸轮询，开始认证流程
2025-07-30 18:04:58.205460: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 18:04:58.206458: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "78ebaa37e6b2382891c4cbc2c01042cd",
		"log_id" : "1753869898205",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 88.35,
				"user_id" : "T36072300020331"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 18:04:58.206458: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 18:04:58.206458: 🎯 识别结果 1: 用户ID=T36072300020331, 分组=hh_group, 得分=88.35
2025-07-30 18:04:58.206458: 人脸识别成功，得分: 88.35，用户ID: T36072300020331
2025-07-30 18:04:58.207456: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-07-30 18:04:58.207456: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-07-30 18:04:58.207456: 🔍 开始请求读者信息: 用户ID=T36072300020331
2025-07-30 18:04:58.207456: 多认证管理器: 人脸识别获得认证请求锁
2025-07-30 18:04:58.208453: Req msgType：Sip2MsgType.queryUser ,length:89， ret:  4520250730    180458AO|AAT36072300020331|XOT36072300020331|CP2341,E43A6E7CA721|AY2AZECCF
2025-07-30 18:04:58.837773: Rsp : 4620250730180502|AA|AE|OK1|OX|BZ0000|AS|CB0000|AU|XB0|AT|XA0|BU|XC|CQN|BLY|BH|BV0|CC0|BD|BE|BF|AF未办理读者证|AY1AZ1B2E
2025-07-30 18:04:58.860710: info:{TransactionDate: 20250730180502, InstitutionId: null, PatronIdentifier: , PatronIdentifierInfo: , PatronPassword: null, LibraryName: null, Telephone: null, MobilePhone: null, EmailAddress: , HomeAddress: , FeeAmount: 0, ChargeAmount: null, PersonalIdentifier: null, PatronTypeCode: null, PatronRegisterDate: 0, BirthDate: null, Nation: null, NativePlace: null, Remark: null, PatronValidDate: null, PatronStartDate: null, PatronSex: null, OK: 1, ScreenMessage: 未办理读者证}
2025-07-30 18:04:58.861710: Req msgType：Sip2MsgType.patronInformation ,length:65， ret:  1720250730180458|AAT36072300020331|CP2341,E43A6E7CA721|AY3AZF214
2025-07-30 18:04:59.203806: Rsp : 1820250730180502|AAT36072300020331|AE曾庆林|BZ0004|AS|CB0001|AU36060300167738|XB0|AT36072300020331|XA0|BU|XC|CQY|BLY|BH|BV0|CC0.00|BD社保卡办证|BE|BF|AF查询成功|AY1AZ097C
2025-07-30 18:04:59.220769: rspInfo:{PatronStatus: , Language: , TransactionDate: 20250730180502, HoldItemsCount: 0001, OverdueItemCount: 0, ChargedItemCount: , FineItemCount: , RecallItemsCount: , UnavailableItemsCount: , InstitutionId: , PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: T36072300020331, PersonName: 曾庆林, HoldItemsLimit: 0004, OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: 0.00, CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: , ChargeAmount: , PatronTotalFine: 0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 36060300167738, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 查询成功, PrintLine: , MsgSeqId: 1AZ097C}
2025-07-30 18:04:59.221746: patronInfo:{PatronStatus: , Language: , TransactionDate: 20250730180502, HoldItemsCount: 0001, OverdueItemCount: 0, ChargedItemCount: , FineItemCount: , RecallItemsCount: , UnavailableItemsCount: , InstitutionId: , PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: T36072300020331, PersonName: 曾庆林, HoldItemsLimit: 0004, OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: 0.00, CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: , ChargeAmount: , PatronTotalFine: 0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 36060300167738, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 查询成功, PrintLine: , MsgSeqId: 1AZ097C}
2025-07-30 18:04:59.221746: ✅ 读者信息获取成功: 姓名=曾庆林, ID=T36072300020331
2025-07-30 18:04:59.222747: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-07-30 18:04:59.223742: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-07-30 18:04:59.223742: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 18:04:59.224736: 多认证管理器状态变更: authenticating
2025-07-30 18:04:59.224736: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-07-30 18:04:59.224736: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 18:04:59.225734: 多认证管理器状态变更: completed
2025-07-30 18:04:59.225734: 多认证管理器: 调用认证后处理服务 - 用户: 曾庆林, 认证方式: 人脸识别
2025-07-30 18:04:59.226732: 执行认证后处理流程，共2个处理器
2025-07-30 18:04:59.226732: 执行处理器: DoorLockHandler
2025-07-30 18:04:59.228739: 开始执行自动开门处理 - 用户: 曾庆林, 认证方式: 人脸识别
2025-07-30 18:04:59.231752: 找到启用的门锁配置: 主门锁设备
2025-07-30 18:04:59.235708: 多认证管理器: 认证请求锁将在5秒后释放
2025-07-30 18:04:59.235708: 收到多认证结果: 人脸识别 - success
2025-07-30 18:04:59.236704: 认证成功，更新主显示方式为: 人脸识别
2025-07-30 18:04:59.236704: === 更新认证反馈状态 ===
2025-07-30 18:04:59.237701: 结果状态: AuthStatus.success
2025-07-30 18:04:59.237701: 用户姓名: 曾庆林
2025-07-30 18:04:59.237701: 用户ID: T36072300020331
2025-07-30 18:04:59.238698: 设置反馈状态为success，显示底部组件: true
2025-07-30 18:04:59.238698: 反馈状态: AuthFeedbackState.success
2025-07-30 18:04:59.238698: 用户信息: 曾庆林 (T36072300020331)
2025-07-30 18:04:59.238698: 使用门锁配置: 主门锁设备 (COM1)
2025-07-30 18:04:59.239695: 使用认证页面的门锁连接: COM1
2025-07-30 18:04:59.239695: 使用继电器通道: 1
2025-07-30 18:04:59.239695: 使用继电器通道: 1
2025-07-30 18:04:59.240692: 开锁前先执行关锁动作，确保门锁处于关闭状态...
2025-07-30 18:04:59.240692: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-07-30 18:04:59.240692: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-07-30 18:04:59.240692: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-07-30 18:04:59.272608: 构建叠加认证区域，当前方式: AuthMethod.face
2025-07-30 18:04:59.273605: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 18:04:59.274622: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-07-30 18:04:59.275600: 构建认证区域，认证方式: AuthMethod.idCard
2025-07-30 18:04:59.275600: 构建身份证认证界面
2025-07-30 18:04:59.275600: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-07-30 18:04:59.275600: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 18:04:59.276597: 构建读者证认证界面
2025-07-30 18:04:59.276597: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-07-30 18:04:59.280608: === 构建多认证底部组件 ===
2025-07-30 18:04:59.284592: 显示底部组件: true
2025-07-30 18:04:59.284592: 反馈状态: AuthFeedbackState.success
2025-07-30 18:04:59.285574: 用户信息: 曾庆林 (T36072300020331)
2025-07-30 18:04:59.285574: 创建AuthFeedbackWidget
2025-07-30 18:04:59.286573: 实际认证方式: AuthMethod.face
2025-07-30 18:04:59.286573: Instance of 'SysConfigData'
2025-07-30 18:04:59.287569: Instance of 'SysConfigData'
2025-07-30 18:04:59.287569: === AuthFeedbackWidget 构建内容 ===
2025-07-30 18:04:59.287569: 当前状态: AuthFeedbackState.success
2025-07-30 18:04:59.288565: 返回成功组件 (详细信息)
2025-07-30 18:04:59.288565: === 构建成功组件（详细信息） ===
2025-07-30 18:04:59.288565: 用户: 曾庆林, ID: T36072300020331
2025-07-30 18:04:59.289562: === AuthFeedbackWidget 状态变化 ===
2025-07-30 18:04:59.289562: 新状态: AuthFeedbackState.success
2025-07-30 18:04:59.289562: 用户信息: 曾庆林 (T36072300020331)
2025-07-30 18:04:59.289562: 启动定时器，3秒后切换到通行模式
2025-07-30 18:04:59.298541: === AuthFeedbackWidget 构建内容 ===
2025-07-30 18:04:59.300536: 当前状态: AuthFeedbackState.success
2025-07-30 18:04:59.301530: 返回成功组件 (详细信息)
2025-07-30 18:04:59.301530: === 构建成功组件（详细信息） ===
2025-07-30 18:04:59.302531: 用户: 曾庆林, ID: T36072300020331
2025-07-30 18:05:00.210104: 认证冷却期结束，重新开始人脸识别监听
2025-07-30 18:05:00.212116: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 18:05:00.215112: 认证完成，恢复人脸轮询
2025-07-30 18:05:01.938482: AuthView: 检测到 1 个人脸
2025-07-30 18:05:02.015273: AuthView: 检测到 1 个人脸
2025-07-30 18:05:02.095062: AuthView: 检测到 1 个人脸
2025-07-30 18:05:02.211750: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:02.211)
2025-07-30 18:05:02.212748: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 18:05:02.211)
2025-07-30 18:05:02.213746: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 18:05:02.213746: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 18:05:02.214741: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 18:05:02.215741: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 299.7664489746094, 数据大小: 0 bytes
2025-07-30 18:05:02.215741: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 18:05:02.216742: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 18:05:02.216742: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 1866 bytes, 实际置信度: 299.7664489746094
2025-07-30 18:05:02.217767: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 18:05:02.217767: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 18:05:02.218731: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 18:05:02.218731: ⏹️ 停止人脸捕获轮询
2025-07-30 18:05:02.218731: 暂停人脸轮询，开始认证流程
2025-07-30 18:05:02.465071: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 18:05:02.465071: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "9d960832201e03f1f9d8d3634ce6a3f0",
		"log_id" : "1753869902464",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 64.15,
				"user_id" : "T36072300020331"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 18:05:02.466068: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 18:05:02.466068: 🎯 识别结果 1: 用户ID=T36072300020331, 分组=hh_group, 得分=64.15
2025-07-30 18:05:02.467066: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 18:05:02.467066: 认证完成，恢复人脸轮询
2025-07-30 18:05:02.469059: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 0
2025-07-30 18:05:02.469059: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-07-30 18:05:02.470058: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-07-30 18:05:02.470058: 开锁前关锁动作执行失败: 操作失败
2025-07-30 18:05:02.470058: 同步等待500毫秒确保关锁动作完成...
2025-07-30 18:05:02.470058: 定时器触发，切换到通行模式
2025-07-30 18:05:02.471055: 认证反馈状态切换到通行模式，启动2秒隐藏定时器
2025-07-30 18:05:02.492: === AuthFeedbackWidget 构建内容 ===
2025-07-30 18:05:02.493: 当前状态: AuthFeedbackState.passMode
2025-07-30 18:05:02.493: 返回通行模式组件
2025-07-30 18:05:02.494996: === 构建通行模式组件 ===
2025-07-30 18:05:02.496988: 用户: 曾庆林, ID: T36072300020331
2025-07-30 18:05:02.496988: 认证方式: AuthMethod.face
2025-07-30 18:05:02.975711: 正在打开绿灯...
2025-07-30 18:05:02.975711: 准备发送命令 [GLED打开], 命令键: 31_37
2025-07-30 18:05:02.976704: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-07-30 18:05:02.976704: 等待响应 [GLED打开], 超时时间: 3000ms
2025-07-30 18:05:04.226362: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-07-30 18:05:04.471708: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:04.470)
2025-07-30 18:05:04.473704: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:04.470)
2025-07-30 18:05:04.474702: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:04.475699: 认证完成，还原到默认显示方式
2025-07-30 18:05:04.490657: 构建叠加认证区域，当前方式: AuthMethod.face
2025-07-30 18:05:04.494687: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 18:05:04.495644: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-07-30 18:05:04.497656: 构建认证区域，认证方式: AuthMethod.idCard
2025-07-30 18:05:04.508610: 构建身份证认证界面
2025-07-30 18:05:04.509605: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-07-30 18:05:04.509605: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 18:05:04.510603: 构建读者证认证界面
2025-07-30 18:05:04.510603: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-07-30 18:05:04.510603: === 构建多认证底部组件 ===
2025-07-30 18:05:04.511600: 显示底部组件: false
2025-07-30 18:05:04.511600: 反馈状态: AuthFeedbackState.idle
2025-07-30 18:05:04.511600: 用户信息: null (null)
2025-07-30 18:05:04.512596: 底部组件被隐藏，返回空组件
2025-07-30 18:05:04.512596: Instance of 'SysConfigData'
2025-07-30 18:05:04.512596: Instance of 'SysConfigData'
2025-07-30 18:05:05.981687: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 0
2025-07-30 18:05:05.983665: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-07-30 18:05:05.983665: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-07-30 18:05:05.984661: 绿灯打开失败: 操作失败
2025-07-30 18:05:05.984661: 正在开门...
2025-07-30 18:05:05.984661: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-07-30 18:05:05.985658: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-07-30 18:05:05.985658: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-07-30 18:05:06.469370: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:06.469)
2025-07-30 18:05:06.469370: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:06.469)
2025-07-30 18:05:06.470362: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:07.229385: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-07-30 18:05:07.230332: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 18:05:07.230332: 多认证管理器状态变更: listening
2025-07-30 18:05:07.248283: 构建叠加认证区域，当前方式: AuthMethod.face
2025-07-30 18:05:07.249281: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 18:05:07.250296: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-07-30 18:05:07.251274: 构建认证区域，认证方式: AuthMethod.idCard
2025-07-30 18:05:07.252271: 构建身份证认证界面
2025-07-30 18:05:07.252271: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-07-30 18:05:07.252271: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 18:05:07.253268: 构建读者证认证界面
2025-07-30 18:05:07.253268: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-07-30 18:05:07.253268: === 构建多认证底部组件 ===
2025-07-30 18:05:07.254267: 显示底部组件: false
2025-07-30 18:05:07.254267: 反馈状态: AuthFeedbackState.idle
2025-07-30 18:05:07.256288: 用户信息: null (null)
2025-07-30 18:05:07.259296: 底部组件被隐藏，返回空组件
2025-07-30 18:05:07.263243: Instance of 'SysConfigData'
2025-07-30 18:05:07.263243: Instance of 'SysConfigData'
2025-07-30 18:05:08.468024: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:08.468)
2025-07-30 18:05:08.471015: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:08.468)
2025-07-30 18:05:08.474029: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:08.986652: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 0
2025-07-30 18:05:08.991641: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-07-30 18:05:08.992620: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-07-30 18:05:08.993621: 开门失败: 操作失败
2025-07-30 18:05:08.993621: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-07-30 18:05:08.993621: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-07-30 18:05:08.994616: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-07-30 18:05:10.472684: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:10.470)
2025-07-30 18:05:10.473675: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:10.470)
2025-07-30 18:05:10.474659: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:11.988604: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-07-30 18:05:11.989603: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-07-30 18:05:11.989603: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-07-30 18:05:11.989603: 门锁操作完成，连接由认证页面管理
2025-07-30 18:05:11.990601: 自动开门失败 - 用户: 曾庆林
2025-07-30 18:05:11.990601: 执行处理器: WelcomeHandler
2025-07-30 18:05:11.992647: 显示欢迎信息给: 曾庆林
2025-07-30 18:05:11.994611: 欢迎用户: 曾庆林，认证方式: 人脸识别
2025-07-30 18:05:11.998602: 认证后处理流程执行完毕
2025-07-30 18:05:11.999579: 多认证管理器: 认证后处理流程执行完毕
2025-07-30 18:05:12.469321: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:12.469)
2025-07-30 18:05:12.469321: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:12.469)
2025-07-30 18:05:12.470320: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:14.097976: ready error :SocketException: 信号灯超时时间已到
 (OS Error: 信号灯超时时间已到
, errno = 121), address = ************, port = 51990
2025-07-30 18:05:14.100975: ready done
2025-07-30 18:05:14.101958: open reader readerType ：2 ret：-1
2025-07-30 18:05:14.375243: 去开启 读 监听
2025-07-30 18:05:14.376226: widget.port.isOpen:true
2025-07-30 18:05:14.412129: 打开COM7读写成功
2025-07-30 18:05:14.413125: Error: WebSocketChannelException: WebSocketChannelException: SocketException: 信号灯超时时间已到
 (OS Error: 信号灯超时时间已到
, errno = 121), address = ************, port = 51990
2025-07-30 18:05:14.415123: open reader readerType ：12 ret：0
2025-07-30 18:05:14.416119: [[5, 0], [2, -1], [12, 0]]
2025-07-30 18:05:14.416119: changeType:ReaderErrorType.openSuccess
2025-07-30 18:05:14.417113: 读卡器状态变化: ReaderErrorType.openSuccess
2025-07-30 18:05:14.419123: 读卡器状态变化: ReaderErrorType.openSuccess
2025-07-30 18:05:14.422118: 读卡器连接成功
2025-07-30 18:05:14.426114: 读卡器连接成功，确保扫描状态正常
2025-07-30 18:05:14.427088: 恢复读卡器扫描状态...
2025-07-30 18:05:14.428098: 读卡器扫描状态已恢复
2025-07-30 18:05:14.432072: WebSocket 连接已关闭
2025-07-30 18:05:14.435063: subThread :ReaderCommand.resumeInventory
2025-07-30 18:05:14.435063: commandRsp:ReaderCommand.resumeInventory
2025-07-30 18:05:14.469982: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:14.468)
2025-07-30 18:05:14.471968: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:14.468)
2025-07-30 18:05:14.475964: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:16.473613: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:16.472)
2025-07-30 18:05:16.474618: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:16.472)
2025-07-30 18:05:16.474618: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:16.896509: ready error :SocketException: 信号灯超时时间已到
 (OS Error: 信号灯超时时间已到
, errno = 121), address = ************, port = 51991
2025-07-30 18:05:16.900490: ready done
2025-07-30 18:05:16.901470: open reader readerType ：2 ret：-1
2025-07-30 18:05:16.901470: isConnect:false
2025-07-30 18:05:16.902470: disconnect
2025-07-30 18:05:16.903467: [[2, -1]]
2025-07-30 18:05:16.903467: changeType:ReaderErrorType.openFail
2025-07-30 18:05:16.904461: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 18:05:16.904461: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 18:05:16.904461: 读卡器连接失败，尝试重新连接
2025-07-30 18:05:16.905458: 处理读卡器连接错误
2025-07-30 18:05:18.469279: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:18.469)
2025-07-30 18:05:18.470275: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:18.469)
2025-07-30 18:05:18.471272: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:18.908103: 尝试重新配置读卡器
2025-07-30 18:05:18.909101: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 18:05:18.910100: 添加设备配置: 身份证认证 -> 1个设备
2025-07-30 18:05:18.910100: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 18:05:18.910100: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 18:05:18.911095: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-07-30 18:05:18.911095: 添加设备配置: 微信扫码认证 -> 1个设备
2025-07-30 18:05:18.912092: 验证读卡器配置: 类型=5, 解码器=不解析
2025-07-30 18:05:18.913090: 添加有效设备: type=5, id=5
2025-07-30 18:05:18.913090: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 18:05:18.913090: 添加有效设备: type=2, id=2
2025-07-30 18:05:18.914087: 验证读卡器配置: 类型=12, 解码器=平台社保卡解码
2025-07-30 18:05:18.914087: 添加有效设备: type=12, id=12
2025-07-30 18:05:18.914087: 总共加载了3个设备配置
2025-07-30 18:05:18.915085: stopInventory newPort:SendPort
2025-07-30 18:05:18.916084: subThread :ReaderCommand.stopInventory
2025-07-30 18:05:18.916084: commandRsp:ReaderCommand.stopInventory
2025-07-30 18:05:19.016811: 发送 关闭 阅读器newPort:SendPort
2025-07-30 18:05:19.016811: 读卡器连接已完全关闭
2025-07-30 18:05:19.017809: changeReaders
2025-07-30 18:05:19.017809: createIsolate isOpen:true,isOpening:false
2025-07-30 18:05:19.017809: open():SendPort
2025-07-30 18:05:19.017809: untilDetcted():SendPort
2025-07-30 18:05:19.018808: 读卡器配置完成，共 3 个设备
2025-07-30 18:05:19.020800: subThread :ReaderCommand.close
2025-07-30 18:05:19.023796: commandRsp:ReaderCommand.close
2025-07-30 18:05:19.026808: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 5, type: 3, readerType: 5, selectedCardType: 身份证, extras: []}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析, domain: ************:9091, account: syncDev, psw: syncDev123!, faceDomain: http://10.80.255.253:9000, type: 大连图书馆}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 2, type: 3, readerType: 2, selectedCardType: null, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 平台社保卡解码, patronUrl: http://10.115.175.224:15078/api/v1, dataType: cardNo, accessKey: null, appArea: yujiang, appKey: null, appSecret: null, basicCode: 360603, bussiType: null, libCode: null, machineUUID: RE24125, method: null, secretKey: null, tradeCode: null, port: COM7, openCmd: null, closeCmd: null}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-07-30 18:05:19.028781: close:HD100Bridge
2025-07-30 18:05:19.028781: ICC_Reader_Close ret:0
2025-07-30 18:05:19.029782: close:HWFaceBridge
2025-07-30 18:05:19.029782: isConnect:false
2025-07-30 18:05:19.029782: disconnect
2025-07-30 18:05:19.030777: close:ScanerBridge
2025-07-30 18:05:19.047755: subThread :ReaderCommand.readerList
2025-07-30 18:05:19.048728: commandRsp:ReaderCommand.readerList
2025-07-30 18:05:19.049725: readerList：3,readerSetting：3
2025-07-30 18:05:19.050726: cacheUsedReaders:3
2025-07-30 18:05:19.051725: subThread :ReaderCommand.open
2025-07-30 18:05:19.052720: commandRsp:ReaderCommand.open
2025-07-30 18:05:19.052720: ICC_Reader_Open ret:2896
2025-07-30 18:05:19.052720: open reader readerType ：5 ret：0
2025-07-30 18:05:19.053713: wsurl:************:9091
2025-07-30 18:05:19.054714: subThread :ReaderCommand.untilDetected
2025-07-30 18:05:19.056722: commandRsp:ReaderCommand.untilDetected
2025-07-30 18:05:19.063694: close:done
2025-07-30 18:05:19.065682: changeType:ReaderErrorType.closeSuccess
2025-07-30 18:05:19.073678: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 18:05:19.078673: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 18:05:19.082638: already close all reader
2025-07-30 18:05:20.468931: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:20.468)
2025-07-30 18:05:20.468931: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:20.468)
2025-07-30 18:05:20.469927: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:22.470609: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:22.469)
2025-07-30 18:05:22.471580: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:22.469)
2025-07-30 18:05:22.472577: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:22.655086: AuthView: 检测到 1 个人脸
2025-07-30 18:05:22.734876: AuthView: 检测到 1 个人脸
2025-07-30 18:05:22.821638: AuthView: 检测到 1 个人脸
2025-07-30 18:05:22.894468: AuthView: 检测到 1 个人脸
2025-07-30 18:05:22.975234: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.056011: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.135826: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.215616: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.297367: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.375156: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.453959: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.539722: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.614519: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.694306: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.779088: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.853880: AuthView: 检测到 1 个人脸
2025-07-30 18:05:23.934666: AuthView: 检测到 1 个人脸
2025-07-30 18:05:24.020434: AuthView: 检测到 1 个人脸
2025-07-30 18:05:24.096231: AuthView: 检测到 1 个人脸
2025-07-30 18:05:24.469233: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:24.468)
2025-07-30 18:05:24.469233: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 18:05:24.468)
2025-07-30 18:05:24.470231: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 18:05:24.470231: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 18:05:24.470231: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 18:05:24.471228: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 167.1580047607422, 数据大小: 0 bytes
2025-07-30 18:05:24.471228: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 18:05:24.471228: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 18:05:24.472226: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 2486 bytes, 实际置信度: 167.1580047607422
2025-07-30 18:05:24.472226: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 18:05:24.473225: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 18:05:24.476227: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 18:05:24.479229: ⏹️ 停止人脸捕获轮询
2025-07-30 18:05:24.482201: 暂停人脸轮询，开始认证流程
2025-07-30 18:05:24.665707: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 18:05:24.665707: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "4eff1abadb6b5fa5b511ef0d4a2d5531",
		"log_id" : "1753869924665",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 91.82,
				"user_id" : "T36072300020331"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 18:05:24.665707: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 18:05:24.666704: 🎯 识别结果 1: 用户ID=T36072300020331, 分组=hh_group, 得分=91.82
2025-07-30 18:05:24.666704: 人脸识别成功，得分: 91.82，用户ID: T36072300020331
2025-07-30 18:05:24.666704: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-07-30 18:05:24.666704: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-07-30 18:05:24.667702: 🔍 开始请求读者信息: 用户ID=T36072300020331
2025-07-30 18:05:24.667702: 多认证管理器: 人脸识别获得认证请求锁
2025-07-30 18:05:24.667702: Req msgType：Sip2MsgType.queryUser ,length:89， ret:  4520250730    180524AO|AAT36072300020331|XOT36072300020331|CP2341,E43A6E7CA721|AY4AZECD3
2025-07-30 18:05:25.193301: Rsp : 4620250730180528|AA|AE|OK1|OX|BZ0000|AS|CB0000|AU|XB0|AT|XA0|BU|XC|CQN|BLY|BH|BV0|CC0|BD|BE|BF|AF未办理读者证|AY1AZ1B26
2025-07-30 18:05:25.216236: info:{TransactionDate: 20250730180528, InstitutionId: null, PatronIdentifier: , PatronIdentifierInfo: , PatronPassword: null, LibraryName: null, Telephone: null, MobilePhone: null, EmailAddress: , HomeAddress: , FeeAmount: 0, ChargeAmount: null, PersonalIdentifier: null, PatronTypeCode: null, PatronRegisterDate: 0, BirthDate: null, Nation: null, NativePlace: null, Remark: null, PatronValidDate: null, PatronStartDate: null, PatronSex: null, OK: 1, ScreenMessage: 未办理读者证}
2025-07-30 18:05:25.217233: Req msgType：Sip2MsgType.patronInformation ,length:65， ret:  1720250730180525|AAT36072300020331|CP2341,E43A6E7CA721|AY5AZF217
2025-07-30 18:05:25.331944: 发送心跳
2025-07-30 18:05:25.598213: Rsp : 1820250730180528|AAT36072300020331|AE曾庆林|BZ0004|AS|CB0001|AU36060300167738|XB0|AT36072300020331|XA0|BU|XC|CQY|BLY|BH|BV0|CC0.00|BD社保卡办证|BE|BF|AF查询成功|AY1AZ0974
2025-07-30 18:05:25.614172: rspInfo:{PatronStatus: , Language: , TransactionDate: 20250730180528, HoldItemsCount: 0001, OverdueItemCount: 0, ChargedItemCount: , FineItemCount: , RecallItemsCount: , UnavailableItemsCount: , InstitutionId: , PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: T36072300020331, PersonName: 曾庆林, HoldItemsLimit: 0004, OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: 0.00, CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: , ChargeAmount: , PatronTotalFine: 0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 36060300167738, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 查询成功, PrintLine: , MsgSeqId: 1AZ0974}
2025-07-30 18:05:25.615170: patronInfo:{PatronStatus: , Language: , TransactionDate: 20250730180528, HoldItemsCount: 0001, OverdueItemCount: 0, ChargedItemCount: , FineItemCount: , RecallItemsCount: , UnavailableItemsCount: , InstitutionId: , PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: T36072300020331, PersonName: 曾庆林, HoldItemsLimit: 0004, OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: 0.00, CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: , ChargeAmount: , PatronTotalFine: 0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 36060300167738, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 查询成功, PrintLine: , MsgSeqId: 1AZ0974}
2025-07-30 18:05:25.615170: ✅ 读者信息获取成功: 姓名=曾庆林, ID=T36072300020331
2025-07-30 18:05:25.616167: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-07-30 18:05:25.617163: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-07-30 18:05:25.617163: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 18:05:25.618160: 多认证管理器状态变更: authenticating
2025-07-30 18:05:25.618160: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-07-30 18:05:25.618160: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 18:05:25.618160: 多认证管理器状态变更: completed
2025-07-30 18:05:25.619158: 多认证管理器: 调用认证后处理服务 - 用户: 曾庆林, 认证方式: 人脸识别
2025-07-30 18:05:25.619158: 执行认证后处理流程，共2个处理器
2025-07-30 18:05:25.620173: 执行处理器: DoorLockHandler
2025-07-30 18:05:25.623159: 开始执行自动开门处理 - 用户: 曾庆林, 认证方式: 人脸识别
2025-07-30 18:05:25.626148: 找到启用的门锁配置: 主门锁设备
2025-07-30 18:05:25.627139: 多认证管理器: 认证请求锁将在5秒后释放
2025-07-30 18:05:25.628140: 收到多认证结果: 人脸识别 - success
2025-07-30 18:05:25.629134: 认证成功，更新主显示方式为: 人脸识别
2025-07-30 18:05:25.629134: === 更新认证反馈状态 ===
2025-07-30 18:05:25.629134: 结果状态: AuthStatus.success
2025-07-30 18:05:25.630128: 用户姓名: 曾庆林
2025-07-30 18:05:25.630128: 用户ID: T36072300020331
2025-07-30 18:05:25.630128: 设置反馈状态为success，显示底部组件: true
2025-07-30 18:05:25.631126: 反馈状态: AuthFeedbackState.success
2025-07-30 18:05:25.631126: 用户信息: 曾庆林 (T36072300020331)
2025-07-30 18:05:25.631126: 使用门锁配置: 主门锁设备 (COM1)
2025-07-30 18:05:25.632128: 使用认证页面的门锁连接: COM1
2025-07-30 18:05:25.632128: 使用继电器通道: 1
2025-07-30 18:05:25.632128: 使用继电器通道: 1
2025-07-30 18:05:25.633122: 开锁前先执行关锁动作，确保门锁处于关闭状态...
2025-07-30 18:05:25.633122: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-07-30 18:05:25.633122: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-07-30 18:05:25.634117: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-07-30 18:05:25.639105: 构建叠加认证区域，当前方式: AuthMethod.face
2025-07-30 18:05:25.639105: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 18:05:25.640103: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-07-30 18:05:25.641120: 构建认证区域，认证方式: AuthMethod.idCard
2025-07-30 18:05:25.643111: 构建身份证认证界面
2025-07-30 18:05:25.644091: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-07-30 18:05:25.644091: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 18:05:25.644091: 构建读者证认证界面
2025-07-30 18:05:25.645089: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-07-30 18:05:25.645089: === 构建多认证底部组件 ===
2025-07-30 18:05:25.645089: 显示底部组件: true
2025-07-30 18:05:25.646099: 反馈状态: AuthFeedbackState.success
2025-07-30 18:05:25.646099: 用户信息: 曾庆林 (T36072300020331)
2025-07-30 18:05:25.647084: 创建AuthFeedbackWidget
2025-07-30 18:05:25.647084: 实际认证方式: AuthMethod.face
2025-07-30 18:05:25.647084: Instance of 'SysConfigData'
2025-07-30 18:05:25.648081: Instance of 'SysConfigData'
2025-07-30 18:05:25.648081: === AuthFeedbackWidget 构建内容 ===
2025-07-30 18:05:25.648081: 当前状态: AuthFeedbackState.success
2025-07-30 18:05:25.649078: 返回成功组件 (详细信息)
2025-07-30 18:05:25.649078: === 构建成功组件（详细信息） ===
2025-07-30 18:05:25.649078: 用户: 曾庆林, ID: T36072300020331
2025-07-30 18:05:25.650074: === AuthFeedbackWidget 状态变化 ===
2025-07-30 18:05:25.650074: 新状态: AuthFeedbackState.success
2025-07-30 18:05:25.650074: 用户信息: 曾庆林 (T36072300020331)
2025-07-30 18:05:25.651071: 启动定时器，3秒后切换到通行模式
2025-07-30 18:05:25.653068: Req msgType：Sip2MsgType.scStatus ,length:43， ret:  99TC00522.00|CP2341,E43A6E7CA721|AY6AZF6C6
2025-07-30 18:05:25.672016: Rsp : 98YYYYNN18018020250730    180529AO|AF状态查询码不正确|AG
2025-07-30 18:05:25.673014: === AuthFeedbackWidget 构建内容 ===
2025-07-30 18:05:25.675012: 当前状态: AuthFeedbackState.success
2025-07-30 18:05:25.676009: 返回成功组件 (详细信息)
2025-07-30 18:05:25.676009: === 构建成功组件（详细信息） ===
2025-07-30 18:05:25.676009: 用户: 曾庆林, ID: T36072300020331
2025-07-30 18:05:26.670374: 认证冷却期结束，重新开始人脸识别监听
2025-07-30 18:05:26.677329: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 18:05:26.678326: 认证完成，恢复人脸轮询
2025-07-30 18:05:28.631112: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 0
2025-07-30 18:05:28.634114: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-07-30 18:05:28.637089: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-07-30 18:05:28.637089: 开锁前关锁动作执行失败: 操作失败
2025-07-30 18:05:28.638086: 同步等待500毫秒确保关锁动作完成...
2025-07-30 18:05:28.643074: 定时器触发，切换到通行模式
2025-07-30 18:05:28.644083: 认证反馈状态切换到通行模式，启动2秒隐藏定时器
2025-07-30 18:05:28.656043: === AuthFeedbackWidget 构建内容 ===
2025-07-30 18:05:28.657039: 当前状态: AuthFeedbackState.passMode
2025-07-30 18:05:28.659030: 返回通行模式组件
2025-07-30 18:05:28.660027: === 构建通行模式组件 ===
2025-07-30 18:05:28.660027: 用户: 曾庆林, ID: T36072300020331
2025-07-30 18:05:28.660027: 认证方式: AuthMethod.face
2025-07-30 18:05:28.671001: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:28.671)
2025-07-30 18:05:28.673007: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:28.671)
2025-07-30 18:05:28.673991: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:29.136753: 正在打开绿灯...
2025-07-30 18:05:29.137751: 准备发送命令 [GLED打开], 命令键: 31_37
2025-07-30 18:05:29.137751: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-07-30 18:05:29.138748: 等待响应 [GLED打开], 超时时间: 3000ms
2025-07-30 18:05:30.618789: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-07-30 18:05:30.645739: 认证完成，还原到默认显示方式
2025-07-30 18:05:30.666662: 构建叠加认证区域，当前方式: AuthMethod.face
2025-07-30 18:05:30.667659: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 18:05:30.667659: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-07-30 18:05:30.668655: 构建认证区域，认证方式: AuthMethod.idCard
2025-07-30 18:05:30.670654: 构建身份证认证界面
2025-07-30 18:05:30.672650: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-07-30 18:05:30.672650: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 18:05:30.673643: 构建读者证认证界面
2025-07-30 18:05:30.673643: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-07-30 18:05:30.674648: === 构建多认证底部组件 ===
2025-07-30 18:05:30.674648: 显示底部组件: false
2025-07-30 18:05:30.675652: 反馈状态: AuthFeedbackState.idle
2025-07-30 18:05:30.675652: 用户信息: null (null)
2025-07-30 18:05:30.676635: 底部组件被隐藏，返回空组件
2025-07-30 18:05:30.676635: Instance of 'SysConfigData'
2025-07-30 18:05:30.676635: Instance of 'SysConfigData'
2025-07-30 18:05:30.726501: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:30.723)
2025-07-30 18:05:30.726501: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:30.723)
2025-07-30 18:05:30.727499: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:32.145711: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 0
2025-07-30 18:05:32.146703: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-07-30 18:05:32.146703: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-07-30 18:05:32.147701: 绿灯打开失败: 操作失败
2025-07-30 18:05:32.147701: 正在开门...
2025-07-30 18:05:32.147701: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-07-30 18:05:32.148698: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-07-30 18:05:32.148698: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-07-30 18:05:32.676290: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:32.676)
2025-07-30 18:05:32.676290: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:32.676)
2025-07-30 18:05:32.677300: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:33.622770: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-07-30 18:05:33.623755: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 18:05:33.624752: 多认证管理器状态变更: listening
2025-07-30 18:05:33.641710: 构建叠加认证区域，当前方式: AuthMethod.face
2025-07-30 18:05:33.642705: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 18:05:33.644699: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-07-30 18:05:33.644699: 构建认证区域，认证方式: AuthMethod.idCard
2025-07-30 18:05:33.644699: 构建身份证认证界面
2025-07-30 18:05:33.645695: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-07-30 18:05:33.645695: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 18:05:33.646693: 构建读者证认证界面
2025-07-30 18:05:33.646693: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-07-30 18:05:33.646693: === 构建多认证底部组件 ===
2025-07-30 18:05:33.649687: 显示底部组件: false
2025-07-30 18:05:33.652694: 反馈状态: AuthFeedbackState.idle
2025-07-30 18:05:33.654672: 用户信息: null (null)
2025-07-30 18:05:33.654672: 底部组件被隐藏，返回空组件
2025-07-30 18:05:33.655669: Instance of 'SysConfigData'
2025-07-30 18:05:33.655669: Instance of 'SysConfigData'
2025-07-30 18:05:34.671974: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:34.671)
2025-07-30 18:05:34.673951: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:34.671)
2025-07-30 18:05:34.673951: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:35.149675: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 0
2025-07-30 18:05:35.152678: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-07-30 18:05:35.154662: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-07-30 18:05:35.154662: 开门失败: 操作失败
2025-07-30 18:05:35.155658: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-07-30 18:05:35.155658: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-07-30 18:05:35.155658: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-07-30 18:05:36.671604: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:36.671)
2025-07-30 18:05:36.672603: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:36.671)
2025-07-30 18:05:36.674610: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:38.153653: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-07-30 18:05:38.159629: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-07-30 18:05:38.160626: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-07-30 18:05:38.160626: 门锁操作完成，连接由认证页面管理
2025-07-30 18:05:38.161624: 自动开门失败 - 用户: 曾庆林
2025-07-30 18:05:38.162621: 执行处理器: WelcomeHandler
2025-07-30 18:05:38.162621: 显示欢迎信息给: 曾庆林
2025-07-30 18:05:38.163614: 欢迎用户: 曾庆林，认证方式: 人脸识别
2025-07-30 18:05:38.163614: 认证后处理流程执行完毕
2025-07-30 18:05:38.163614: 多认证管理器: 认证后处理流程执行完毕
2025-07-30 18:05:38.674271: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:38.673)
2025-07-30 18:05:38.675247: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:38.673)
2025-07-30 18:05:38.675247: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:40.204175: ready error :SocketException: 信号灯超时时间已到
 (OS Error: 信号灯超时时间已到
, errno = 121), address = ************, port = 51995
2025-07-30 18:05:40.205157: ready done
2025-07-30 18:05:40.206156: open reader readerType ：2 ret：-1
2025-07-30 18:05:40.206156: changeType:ReaderErrorType.openFail
2025-07-30 18:05:40.207159: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 18:05:40.208154: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 18:05:40.208154: 读卡器连接失败，尝试重新连接
2025-07-30 18:05:40.209147: 处理读卡器连接错误
2025-07-30 18:05:40.209147: open Concurrent modification during iteration: Instance(length:0) of '_GrowableList'.
2025-07-30 18:05:40.210143: Error: WebSocketChannelException: WebSocketChannelException: SocketException: 信号灯超时时间已到
 (OS Error: 信号灯超时时间已到
, errno = 121), address = ************, port = 51995
2025-07-30 18:05:40.210143: WebSocket 连接已关闭
2025-07-30 18:05:40.678892: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:40.678)
2025-07-30 18:05:40.679886: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:40.678)
2025-07-30 18:05:40.679886: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:42.215787: 尝试重新配置读卡器
2025-07-30 18:05:42.216780: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 18:05:42.216780: 添加设备配置: 身份证认证 -> 1个设备
2025-07-30 18:05:42.217777: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 18:05:42.218771: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 18:05:42.218771: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-07-30 18:05:42.219768: 添加设备配置: 微信扫码认证 -> 1个设备
2025-07-30 18:05:42.219768: 验证读卡器配置: 类型=5, 解码器=不解析
2025-07-30 18:05:42.220766: 添加有效设备: type=5, id=5
2025-07-30 18:05:42.220766: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 18:05:42.220766: 添加有效设备: type=2, id=2
2025-07-30 18:05:42.221763: 验证读卡器配置: 类型=12, 解码器=平台社保卡解码
2025-07-30 18:05:42.221763: 添加有效设备: type=12, id=12
2025-07-30 18:05:42.222761: 总共加载了3个设备配置
2025-07-30 18:05:42.222761: stopInventory newPort:SendPort
2025-07-30 18:05:42.225759: subThread :ReaderCommand.stopInventory
2025-07-30 18:05:42.230759: commandRsp:ReaderCommand.stopInventory
2025-07-30 18:05:42.325497: 发送 关闭 阅读器newPort:SendPort
2025-07-30 18:05:42.328496: 读卡器连接已完全关闭
2025-07-30 18:05:42.331497: changeReaders
2025-07-30 18:05:42.332468: createIsolate isOpen:true,isOpening:false
2025-07-30 18:05:42.333467: open():SendPort
2025-07-30 18:05:42.333467: untilDetcted():SendPort
2025-07-30 18:05:42.334464: 读卡器配置完成，共 3 个设备
2025-07-30 18:05:42.336457: subThread :ReaderCommand.close
2025-07-30 18:05:42.336457: commandRsp:ReaderCommand.close
2025-07-30 18:05:42.338480: cacheUsedReaders:()
2025-07-30 18:05:42.339449: close:done
2025-07-30 18:05:42.340457: changeType:ReaderErrorType.closeSuccess
2025-07-30 18:05:42.345461: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 18:05:42.348424: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 18:05:42.348424: already close all reader
2025-07-30 18:05:42.349423: subThread :ReaderCommand.readerList
2025-07-30 18:05:42.349423: commandRsp:ReaderCommand.readerList
2025-07-30 18:05:42.350422: readerList：3,readerSetting：3
2025-07-30 18:05:42.350422: cacheUsedReaders:3
2025-07-30 18:05:42.351418: subThread :ReaderCommand.open
2025-07-30 18:05:42.351418: commandRsp:ReaderCommand.open
2025-07-30 18:05:42.352415: ICC_Reader_Open ret:3620
2025-07-30 18:05:42.352415: open reader readerType ：5 ret：0
2025-07-30 18:05:42.353413: wsurl:************:9091
2025-07-30 18:05:42.354411: subThread :ReaderCommand.untilDetected
2025-07-30 18:05:42.354411: commandRsp:ReaderCommand.untilDetected
2025-07-30 18:05:42.670575: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 18:05:42.670)
2025-07-30 18:05:42.671561: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 18:05:42.670)
2025-07-30 18:05:42.671561: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 18:05:42.963784: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:43.463450: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:43.503341: CameraPreviewWidget: 开始清理资源...
2025-07-30 18:05:43.505341: CameraPreviewWidget: 停止帧捕获定时器
2025-07-30 18:05:43.508344: CameraPreviewWidget: 停止人脸识别服务...
2025-07-30 18:05:43.513315: 🛑 停止人脸识别监听...
2025-07-30 18:05:43.514308: ⏹️ 停止人脸捕获轮询
2025-07-30 18:05:43.515307: ✅ 人脸识别监听已停止
2025-07-30 18:05:43.516302: CameraPreviewWidget: 资源清理完成
2025-07-30 18:05:43.517302: 停止所有认证方式监听
2025-07-30 18:05:43.518297: 停止人脸识别认证监听
2025-07-30 18:05:43.518297: 停止人脸认证监听
2025-07-30 18:05:43.519293: 停止电子社保卡认证监听
2025-07-30 18:05:43.519293: 停止读卡器认证监听
2025-07-30 18:05:43.520290: 停止身份证认证监听
2025-07-30 18:05:43.520290: 停止读卡器认证监听
2025-07-30 18:05:43.521290: 停止社保卡认证监听
2025-07-30 18:05:43.521290: 停止读卡器认证监听
2025-07-30 18:05:43.522286: 停止读者证认证监听
2025-07-30 18:05:43.522286: 停止读卡器认证监听
2025-07-30 18:05:43.522286: 停止微信扫码认证监听
2025-07-30 18:05:43.523286: 停止二维码扫描认证监听
2025-07-30 18:05:43.523286: 开始清理门锁连接...
2025-07-30 18:05:43.524296: 门锁继电器已断开连接
2025-07-30 18:05:43.525278: 门锁连接已断开: COM1
2025-07-30 18:05:43.525278: 门锁连接清理完成
2025-07-30 18:05:43.525278: 认证成功后清理完成，读卡器连接和监听器保持开启以供后续使用
2025-07-30 18:05:43.526276: CameraPreviewWidget: 人脸识别服务已清理
2025-07-30 18:05:43.526276: FaceRecognitionAuthService监听已停止
2025-07-30 18:05:43.527278: 已移除读卡器状态监听器
2025-07-30 18:05:43.528269: 已移除标签数据监听器
2025-07-30 18:05:43.528269: 所有卡片监听器已移除
2025-07-30 18:05:43.528269: 没有活跃的读卡器连接需要暂停
2025-07-30 18:05:43.529267: 已移除读卡器状态监听器
2025-07-30 18:05:43.530273: 已移除标签数据监听器
2025-07-30 18:05:43.530273: 所有卡片监听器已移除
2025-07-30 18:05:43.531266: 没有活跃的读卡器连接需要暂停
2025-07-30 18:05:43.532260: 已移除读卡器状态监听器
2025-07-30 18:05:43.533257: 已移除标签数据监听器
2025-07-30 18:05:43.533257: 所有卡片监听器已移除
2025-07-30 18:05:43.534254: 没有活跃的读卡器连接需要暂停
2025-07-30 18:05:43.534254: 已移除读卡器状态监听器
2025-07-30 18:05:43.535264: 已移除标签数据监听器
2025-07-30 18:05:43.535264: 所有卡片监听器已移除
2025-07-30 18:05:43.536248: 没有活跃的读卡器连接需要暂停
2025-07-30 18:05:43.536248: 二维码扫描认证监听已停止
2025-07-30 18:05:43.537245: 微信扫码认证服务监听已停止
2025-07-30 18:05:43.537245: 串口数据监听结束
2025-07-30 18:05:43.538243: 认证结果流订阅已取消
2025-07-30 18:05:43.538243: 读卡器认证监听已停止（连接保持）
2025-07-30 18:05:43.538243: 电子社保卡认证服务监听已停止
2025-07-30 18:05:43.539241: 读卡器认证监听已停止（连接保持）
2025-07-30 18:05:43.539241: 身份证认证服务监听已停止
2025-07-30 18:05:43.539241: 读卡器认证监听已停止（连接保持）
2025-07-30 18:05:43.540237: 社保卡认证服务监听已停止
2025-07-30 18:05:43.540237: 读卡器认证监听已停止（连接保持）
2025-07-30 18:05:43.540237: 读者证认证服务监听已停止
2025-07-30 18:05:43.541234: 人脸认证监听已停止
2025-07-30 18:05:43.541234: 人脸识别认证服务监听已停止
2025-07-30 18:05:43.542232: 多认证管理器状态变更: idle
2025-07-30 18:05:43.542232: 所有认证方式监听已停止
2025-07-30 18:05:43.956125: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:44.456789: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:44.955453: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:45.456114: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:45.955777: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:46.455440: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:46.956103: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:47.455766: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:47.955432: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:48.456094: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:48.955755: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:49.456418: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:49.956081: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:50.455746: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:50.955409: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:51.455072: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:51.955733: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:52.455397: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:52.956058: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:53.455722: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:53.955386: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:54.455050: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:54.955712: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:55.455376: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:55.955040: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:56.456701: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:56.955366: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:57.456026: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:57.955689: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:58.455354: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:58.956015: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:59.455679: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:05:59.955346: PICC_Reader_ReadIDMsg ret:4294967294
2025-07-30 18:06:00.457010: PICC_Reader_ReadIDMsg ret:4294967294
