import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_libserialport/flutter_libserialport.dart';
import 'package:seasetting/seasetting.dart';

import '../../../core/router/app_router.dart';
import '../../login/models/operation_type.dart';

/// 雷达检测服务
/// 负责通过串口监听雷达信号，检测人员靠近
class RadarDetectionService {
  // 单例实例
  static final RadarDetectionService _instance = RadarDetectionService._internal();
  static RadarDetectionService get instance => _instance;
  RadarDetectionService._internal();

  // 串口相关
  SerialPort? _serialPort;
  SerialPortReader? _reader;
  StreamSubscription? _subscription;

  // 状态管理
  bool _isInitialized = false;
  bool _isListening = false;
  String? _errorMessage;

  // 检测配置
  String _portName = 'COM1'; // 默认串口
  int _baudRate = 9600; // 默认波特率
  
  // 检测状态
  bool _personDetected = false;
  Timer? _detectionTimer;
  Timer? _cooldownTimer;
  
  // 配置参数
  static const Duration _detectionCooldown = Duration(seconds: 10); // 检测冷却时间
  static const Duration _detectionDebounce = Duration(seconds: 2);  // 防抖时间

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;
  String? get errorMessage => _errorMessage;
  bool get personDetected => _personDetected;

  /// 初始化雷达检测服务
  /// [portName] 串口名称，如 'COM1', '/dev/ttyUSB0'
  /// [baudRate] 波特率，默认9600
  Future<void> initialize({
    String portName = 'COM1',
    int baudRate = 9600,
  }) async {
    if (_isInitialized) {
      return;
    }

    try {
      print('初始化雷达检测服务 - 端口: $portName, 波特率: $baudRate');

      _portName = portName;
      _baudRate = baudRate;

      // 检查串口是否可用
      if (!await _checkPortAvailable()) {
        throw Exception('串口 $_portName 不可用');
      }

      _isInitialized = true;
      _clearError();
      print('雷达检测服务初始化成功');
    } catch (e) {
      _setError('雷达检测服务初始化失败: $e');
      throw e;
    }
  }

  /// 开始监听雷达信号
  Future<void> startListening() async {
    if (!_isInitialized) {
      throw Exception('服务未初始化，请先调用initialize()');
    }

    if (_isListening) {
      print('雷达检测已在监听中');
      return;
    }

    try {
      print('开始雷达检测监听');

      // 打开串口
      await _openSerialPort();

      // 开始读取数据
      _startReading();

      _isListening = true;
      _clearError();
      print('雷达检测监听启动成功');
    } catch (e) {
      _setError('启动雷达检测监听失败: $e');
      throw e;
    }
  }

  /// 停止监听雷达信号
  Future<void> stopListening() async {
    if (!_isListening) {
      return;
    }

    try {
      print('停止雷达检测监听');

      // 取消检测定时器
      _detectionTimer?.cancel();
      _detectionTimer = null;

      _cooldownTimer?.cancel();
      _cooldownTimer = null;

      // 关闭串口
      await _closeSerialPort();

      _isListening = false;
      _personDetected = false;
      print('雷达检测监听已停止');
    } catch (e) {
      _setError('停止雷达检测监听失败: $e');
    }
  }

  /// 重置检测状态
  void resetDetection() {
    _personDetected = false;
    _detectionTimer?.cancel();
    _detectionTimer = null;
    print('雷达检测状态已重置');
  }

  /// 获取可用的串口列表
  static List<String> getAvailablePorts() {
    try {
      return SerialPort.availablePorts;
    } catch (e) {
      print('获取可用串口失败: $e');
      return [];
    }
  }

  /// 检查指定串口是否可用
  Future<bool> _checkPortAvailable() async {
    try {
      final availablePorts = SerialPort.availablePorts;
      return availablePorts.contains(_portName);
    } catch (e) {
      print('检查串口可用性失败: $e');
      return false;
    }
  }

  /// 打开串口
  Future<void> _openSerialPort() async {
    try {
      _serialPort = SerialPort(_portName);
      
      // 配置串口参数
      final config = SerialPortConfig();
      config.baudRate = _baudRate;
      config.bits = 8;
      config.stopBits = 1;
      config.parity = SerialPortParity.none;
      
      _serialPort!.config = config;
      
      // 打开串口
      if (!_serialPort!.openReadWrite()) {
        throw Exception('无法打开串口 $_portName: ${SerialPort.lastError}');
      }

      print('串口 $_portName 打开成功');
    } catch (e) {
      throw Exception('打开串口失败: $e');
    }
  }

  /// 关闭串口
  Future<void> _closeSerialPort() async {
    try {
      // 取消数据订阅
      await _subscription?.cancel();
      _subscription = null;

      // 关闭串口
      if (_serialPort?.isOpen == true) {
        _serialPort!.close();
        print('串口 $_portName 已关闭');
      }

      _serialPort = null;
      _reader = null;
    } catch (e) {
      print('关闭串口失败: $e');
    }
  }

  /// 开始读取串口数据
  void _startReading() {
    if (_serialPort == null || !_serialPort!.isOpen) {
      throw Exception('串口未打开');
    }

    try {
      _reader = SerialPortReader(_serialPort!);
      
      _subscription = _reader!.stream.listen(
        _onDataReceived,
        onError: (error) {
          print('串口读取错误: $error');
          _setError('串口读取错误: $error');
        },
        onDone: () {
          print('串口读取完成');
        },
      );

      print('开始监听串口数据');
    } catch (e) {
      throw Exception('开始读取串口数据失败: $e');
    }
  }

  /// 处理接收到的串口数据
  void _onDataReceived(List<int> data) {
    if (!_isListening) return;

    try {
      // 将字节数据转换为字符串
      final dataString = String.fromCharCodes(data).trim();
      
      if (dataString.isNotEmpty) {
        print('雷达数据: $dataString');
        _processRadarData(dataString);
      }
    } catch (e) {
      print('处理雷达数据失败: $e');
    }
  }

  /// 处理雷达数据
  void _processRadarData(String data) {
    // 检查是否为人员检测信号
    // 这里的判断逻辑需要根据实际雷达设备的协议来实现
    bool isPersonDetected = _isPersonDetectionSignal(data);
    
    if (isPersonDetected && !_personDetected) {
      _onPersonDetected();
    }
  }

  /// 判断是否为人员检测信号
  /// 这个方法需要根据实际使用的雷达设备协议来实现
  bool _isPersonDetectionSignal(String data) {
    // 示例：检查数据中是否包含特定的检测信号
    // 实际应用中需要根据雷达设备的具体协议来判断
    
    // 常见的判断方式：
    // 1. 检查特定的命令码或标识符
    // 2. 检查数据长度和格式
    // 3. 验证校验和
    
    // 示例实现（需要根据实际设备调整）
    if (data.contains('DETECT') || 
        data.contains('MOTION') || 
        data.contains('PERSON') ||
        data.startsWith('01') ||  // 可能的检测信号前缀
        data.length == 8) {      // 特定长度的数据包
      return true;
    }
    
    return false;
  }

  /// 检测到人员时的处理
  void _onPersonDetected() {
    if (_cooldownTimer?.isActive == true) {
      print('雷达检测在冷却期间，忽略此次检测');
      return;
    }

    print('雷达检测到人员靠近');
    _personDetected = true;

    // 防抖处理：延迟一段时间确认检测
    _detectionTimer?.cancel();
    _detectionTimer = Timer(_detectionDebounce, () {
      if (_personDetected && _isListening) {
        _triggerAuthPage();
      }
    });
  }

  /// 触发跳转到认证页面
  void _triggerAuthPage() {
    print('触发跳转到认证页面');
    
    try {
      // 跳转到认证页面
      AppNavigator.toAuth(
        authLoginType: AuthLoginType.faceAuth,
        operationType: OperationType.borrow,
      );
      
      // 开始冷却期
      _startCooldownPeriod();
      
    } catch (e) {
      print('跳转到认证页面失败: $e');
    }
  }

  /// 开始冷却期
  void _startCooldownPeriod() {
    _personDetected = false;
    
    _cooldownTimer?.cancel();
    _cooldownTimer = Timer(_detectionCooldown, () {
      print('雷达检测冷却期结束');
    });
  }

  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'service': 'RadarDetectionService',
      'initialized': _isInitialized,
      'listening': _isListening,
      'port': _portName,
      'baudRate': _baudRate,
      'personDetected': _personDetected,
      'error': _errorMessage,
      'portOpen': _serialPort?.isOpen ?? false,
    };
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    print('雷达检测服务错误: $error');
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }

  /// 释放资源
  void dispose() {
    stopListening();
    _isInitialized = false;
  }
} 