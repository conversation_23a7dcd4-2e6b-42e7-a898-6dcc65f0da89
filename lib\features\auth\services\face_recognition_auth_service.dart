import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';

import '../../face_detection/face_detector.dart';
import '../../face_detection/baidu_face_recognition.dart';
import '../../face_detection/sdk_config_manager.dart';
import '../../face_detection/face_capture_polling.dart';
import '../models/auth_result.dart';
import '../view_models/auth_view_model.dart';

/// 人脸识别认证服务
/// 集成 face_detector_app.dart 中的人脸识别功能到认证系统
class FaceRecognitionAuthService {
  // 状态管理
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isRecognizing = false;
  bool _faceDetectorInitialized = false;
  String? _errorMessage;

  // 回调函数
  Function(String userId, String? userInfo)? _onFaceRecognized;
  Function(String error)? _onError;

  // 认证结果流控制器 - 用于与多认证管理器通信
  StreamController<AuthResult>? _authResultController;

  /// 认证结果流 - 供多认证管理器监听
  Stream<AuthResult> get authResultStream {
    _authResultController ??= StreamController<AuthResult>.broadcast();
    return _authResultController!.stream;
  }

  // 摄像头和检测相关
  Timer? _frameTimer;
  Timer? _detectionTimer;
  Timer? _systemLoadTimer;
  Timer? _adaptiveQualityTimer;
  Timer? _restartListeningTimer; // 新增：用于延迟重启监听的定时器

  // 人脸捕获轮询服务 - 使用与 face_detector_app.dart 相同的方式
  FaceCapturePolling? _faceCapturePolling;
  
  // 性能参数 - 与 face_detector_app.dart 完全一致
  int _jpegQuality = 80;
  int _frameInterval = 50; // 20fps
  int _detectionInterval = 80;
  int _highFPSInterval = 33; // 30fps
  bool _isHighFPSEnabled = true;
  double _smoothingFactor = 0.2;
  bool _adaptiveQualityEnabled = true;
  int _systemLoad = 0;

  // 重启监听配置
  static const int _restartListeningDelaySeconds = 2; // 识别成功后2秒重启监听，避免重复识别

  // 认证状态控制
  bool _isAuthenticating = false; // 是否正在进行认证流程
  DateTime? _lastAuthTime; // 最后一次认证时间

  // 帧数据
  Uint8List? _jpegImageData;
  List<Map<dynamic, dynamic>> _faces = [];
  bool _isProcessing = false;
  int _frameCount = 0;
  int _actualFPS = 0;
  DateTime _lastFPSUpdate = DateTime.now();
  
  // 百度SDK状态
  bool _baiduSdkInitialized = false;
  String _baiduStatusMessage = '未初始化';
  

  
  // 单例模式
  static final FaceRecognitionAuthService _instance = FaceRecognitionAuthService._internal();
  factory FaceRecognitionAuthService() => _instance;
  FaceRecognitionAuthService._internal();
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;
  bool get isRecognizing => _isRecognizing;
  String? get errorMessage => _errorMessage;
  bool get baiduSdkInitialized => _baiduSdkInitialized;
  String get baiduStatusMessage => _baiduStatusMessage;
  int get actualFPS => _actualFPS;
  int get systemLoad => _systemLoad;
  
  /// 初始化人脸识别服务 - 改进版本，分步骤初始化
  Future<void> initialize({
    Function(String userId, String? userInfo)? onFaceRecognized,
    Function(String error)? onError,
  }) async {
    if (_isInitialized) {
      print('人脸识别认证服务已初始化');
      return;
    }

    try {
      print('📷 开始人脸识别认证服务初始化流程...');

      // 设置回调函数
      _onFaceRecognized = onFaceRecognized;
      _onError = onError;

      // 第一步：初始化人脸检测器和摄像头
      print('🔧 第一步：初始化摄像头系统...');
      try {
        await _initializeFaceDetector();
        print('✅ 摄像头系统初始化成功');
      } catch (e) {
        print('❌ 摄像头系统初始化失败: $e');
        // 摄像头初始化失败是致命错误，直接抛出
        throw Exception('摄像头系统初始化失败: $e');
      }

      // 第二步：初始化百度人脸识别SDK（非致命错误）
      print('🔧 第二步：初始化百度人脸识别SDK...');
      try {
        await _initializeBaiduSDK();
        print('✅ 百度人脸识别SDK初始化成功');
      } catch (e) {
        print('⚠️ 百度人脸识别SDK初始化失败，但继续执行: $e');
        // 百度SDK初始化失败不是致命错误，记录但继续
        _setError('百度SDK初始化失败: $e');
      }

      // 第三步：启动系统监控
      print('🔧 第三步：启动系统监控...');
      try {
        _startSystemMonitoring();
        print('✅ 系统监控启动成功');
      } catch (e) {
        print('⚠️ 系统监控启动失败，但继续执行: $e');
      }

      _isInitialized = true;
      _clearError();
      print('✅ 人脸识别认证服务初始化完成');
    } catch (e) {
      _setError('人脸识别认证服务初始化失败: $e');
      print('❌ 人脸识别认证服务初始化失败: $e');

      // 尝试清理已初始化的资源
      try {
        await _cleanup();
      } catch (cleanupError) {
        print('⚠️ 清理资源时出错: $cleanupError');
      }

      throw e;
    }
  }

  /// 清理资源
  Future<void> _cleanup() async {
    try {
      if (_faceDetectorInitialized) {
        await FaceDetector.stopCamera();
        _faceDetectorInitialized = false;
      }
    } catch (e) {
      print('清理摄像头资源时出错: $e');
    }
  }
  
  /// 初始化人脸检测器 - 复用 face_detector_app.dart 逻辑
  Future<void> _initializeFaceDetector() async {
    try {
      print("📷 检查人脸检测器初始化状态...");
      final faceDetectorStartTime = DateTime.now();

      if (!FaceDetector.isInitialized) {
        print("🔧 开始初始化人脸检测器...");
        await FaceDetector.initialize();
        final faceDetectorEndTime = DateTime.now();
        final faceDetectorDuration = faceDetectorEndTime.difference(faceDetectorStartTime).inMilliseconds;
        print("✅ 人脸检测器初始化完成，耗时: ${faceDetectorDuration}ms");
      } else {
        print("✅ 人脸检测器已初始化，跳过初始化步骤");
      }

      // 第一步：检测可用摄像头
      print("🔍 检测可用摄像头设备...");
      final availableCameras = FaceDetector.detectAvailableCameras();
      if (availableCameras.isEmpty) {
        throw Exception("未检测到可用的摄像头设备");
      }
      print("✅ 检测到 ${availableCameras.length} 个摄像头设备: $availableCameras");

      // 第二步：启动摄像头，增加重试机制和超时保护
      print("📹 启动摄像头（带重试机制）...");
      bool cameraStarted = false;
      String lastError = "";
      String lastBackend = "";

      // 尝试使用检测到的摄像头ID
      for (final cameraId in availableCameras) {
        print("📹 尝试使用摄像头ID: $cameraId");

        for (int attempt = 1; attempt <= 2; attempt++) {
          try {
            print("📹 摄像头启动尝试 $attempt/2，ID: $cameraId");
            final cameraStartTime = DateTime.now();

            // 使用超时机制防止卡死
            cameraStarted = await FaceDetector.startCameraWithID(cameraId).timeout(
              const Duration(seconds: 15), // 增加超时时间到15秒
              onTimeout: () {
                print("⏰ 摄像头启动超时（15秒），ID: $cameraId");
                return false;
              },
            );

            final cameraEndTime = DateTime.now();
            final cameraDuration = cameraEndTime.difference(cameraStartTime).inMilliseconds;

            if (cameraStarted) {
              lastBackend = FaceDetector.getCurrentBackend();
              print("✅ 摄像头启动成功，ID: $cameraId，尝试次数: $attempt，耗时: ${cameraDuration}ms，后端: $lastBackend");
              break;
            } else {
              lastError = FaceDetector.getLastError();
              print("❌ 摄像头启动失败，ID: $cameraId，尝试次数: $attempt，错误: $lastError");

              if (attempt < 2) {
                print("⏳ 等待2秒后重试...");
                await Future.delayed(const Duration(seconds: 2));
              }
            }
          } catch (e) {
            lastError = "摄像头启动异常: $e";
            print("❌ $lastError");

            if (attempt < 2) {
              print("⏳ 等待2秒后重试...");
              await Future.delayed(const Duration(seconds: 2));
            }
          }
        }

        if (cameraStarted) break;

        // 如果当前摄像头失败，等待一下再尝试下一个
        if (!cameraStarted && availableCameras.indexOf(cameraId) < availableCameras.length - 1) {
          print("⏳ 当前摄像头失败，等待3秒后尝试下一个摄像头...");
          await Future.delayed(const Duration(seconds: 3));
        }
      }

      if (!cameraStarted) {
        final detailedError = "摄像头启动失败，已尝试所有可用设备。最后错误: $lastError";
        print("❌ $detailedError");
        throw Exception(detailedError);
      }

      // 设置摄像头参数
      print("🔧 配置摄像头参数...");
      try {
        FaceDetector.setJpegQuality(80);
        FaceDetector.setUseHaarDetector(false);
        FaceDetector.setSmoothingFactor(0.2);
        FaceDetector.setProcessingInterval(30);
        print("✅ 摄像头参数配置完成");
      } catch (e) {
        print("⚠️ 摄像头参数配置失败，但继续执行: $e");
      }

      // 预加载模型
      print("🤖 预加载人脸检测模型...");
      try {
        await _preloadModel();
        print("✅ 人脸检测模型预加载完成");
      } catch (e) {
        print("⚠️ 人脸检测模型预加载失败，但继续执行: $e");
      }

      // 验证摄像头是否真的可用
      print("🔍 验证摄像头功能...");
      try {
        final testFrame = await FaceDetector.getCurrentFrame();
        if (testFrame != null && testFrame.isNotEmpty) {
          print("✅ 摄像头功能验证成功，帧数据大小: ${testFrame.length} bytes");
        } else {
          print("⚠️ 摄像头功能验证失败，但继续执行");
        }
      } catch (e) {
        print("⚠️ 摄像头功能验证异常，但继续执行: $e");
      }

      print("✅ 人脸检测器初始化完成");
    } catch (e) {
      print("❌ 初始化人脸检测器失败: $e");
      throw e;
    }
  }
  
  /// 初始化百度人脸识别SDK - 复用 face_detector_app.dart 逻辑
  Future<void> _initializeBaiduSDK() async {
    try {
      final configManager = SdkConfigManager.instance;
      final sdkPath = await configManager.getSdkPath();
      print("从配置获取SDK路径: $sdkPath");
      
      // 验证SDK路径
      final validation = await configManager.validateSdkPath(sdkPath);
      if (!validation.isValid) {
        print("⚠️ SDK路径验证失败: ${validation.error}");
        _baiduStatusMessage = 'SDK路径验证失败';
        return;
      }
      
      print("✅ SDK路径验证成功");
      
      // 设置SDK路径配置
      print("🔧 配置SDK路径: $sdkPath");
      final setResult = BaiduFaceRecognition.setSdkPath(sdkPath);
      print("✅ SDK路径配置完成，错误码: $setResult");
      
      // 初始化SDK
      print("🚀 开始初始化百度人脸识别SDK...");
      final baiduInitialized = await BaiduFaceRecognition.initialize(modelPath: sdkPath);
      
      if (baiduInitialized) {
        print("✅ 百度人脸识别SDK初始化成功");
        _baiduSdkInitialized = true;
        
        // 检查授权状态和人脸数量
        bool isAuthorized = BaiduFaceRecognition.isAuthorized();
        int faceCount = BaiduFaceRecognition.getFaceCount();
        _baiduStatusMessage = '人脸库中共有 $faceCount 个人脸 ${isAuthorized ? "(已授权)" : "(未授权)"}';
        
        print('✅ 百度SDK状态: 已初始化，人脸数量: $faceCount，授权状态: ${isAuthorized ? "已授权" : "未授权"}');
      } else {
        print("⚠️ 百度人脸识别SDK初始化失败");
        _baiduStatusMessage = '百度SDK初始化失败';
      }
    } catch (e) {
      print("⚠️ 百度人脸识别SDK初始化异常: $e");
      _baiduStatusMessage = 'SDK初始化异常: $e';
    }
  }
  
  /// 预加载模型 - 与原始实现一致
  Future<void> _preloadModel() async {
    try {
      print('预加载DNN模型...');
      final dynamic result = FaceDetector.detectFacesOnly();
      print('DNN模型预加载完成，获取到 ${result is List ? result.length : 0} 个结果');
    } catch (e) {
      print('DNN模型预加载失败: $e');
    }
  }
  
  /// 启动系统监控 - 复用原始的性能监控逻辑
  void _startSystemMonitoring() {
    // 每秒更新实际帧率
    Timer.periodic(const Duration(seconds: 1), (_) {
      _updateActualFPS();
      _systemLoad = FaceDetector.getSystemLoad();
    });
    
    // 启动自适应质量控制
    _startAdaptiveQualityControl();
  }
  
  /// 更新实际帧率
  void _updateActualFPS() {
    final now = DateTime.now();
    final elapsedSeconds = now.difference(_lastFPSUpdate).inMilliseconds / 1000.0;
    
    if (elapsedSeconds > 0) {
      _actualFPS = (_frameCount / elapsedSeconds).round();
      _frameCount = 0;
      _lastFPSUpdate = now;
    }
  }
  
  /// 启动自适应质量控制 - 与原始实现完全一致
  void _startAdaptiveQualityControl() {
    if (!_adaptiveQualityEnabled) return;

    _adaptiveQualityTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!_isInitialized || !_adaptiveQualityEnabled) {
        timer.cancel();
        return;
      }

      _systemLoad = FaceDetector.getSystemLoad();

      // 根据系统负载动态调整JPEG质量和处理间隔
      if (_systemLoad > 85) {
        FaceDetector.setJpegQuality(45);
        _detectionInterval = 120;
        _frameInterval = 80;
      } else if (_systemLoad > 75) {
        FaceDetector.setJpegQuality(55);
        _detectionInterval = 100;
        _frameInterval = 67;
      } else if (_systemLoad > 60) {
        FaceDetector.setJpegQuality(65);
        _detectionInterval = 80;
        _frameInterval = 50;
      } else if (_systemLoad > 40) {
        FaceDetector.setJpegQuality(75);
        _detectionInterval = 70;
        _frameInterval = 40;
      } else {
        FaceDetector.setJpegQuality(85);
        _detectionInterval = 50;
        _frameInterval = 33;
      }

      if (_isHighFPSEnabled) {
        _frameInterval = _highFPSInterval;
      }

      // 重启定时器以应用新间隔
      if (_frameTimer != null) {
        _stopFrameCapture();
        _startFrameCapture();
      }

      if (_detectionTimer != null) {
        _stopDetection();
        _startDetection();
      }
    });
  }

  /// 开始监听人脸识别
  Future<void> startListening() async {
    if (!_isInitialized) {
      throw Exception('服务未初始化，请先调用initialize()');
    }

    if (_isListening) {
      print('人脸识别已在监听中');
      return;
    }

    try {
      print('🚀 开始人脸识别监听（使用FaceCapturePolling方式）...');

      // 重置认证状态，确保可以进行新的识别
      _isAuthenticating = false;
      _lastAuthTime = null;

      // 如果之前有轮询服务，先强制重置
      if (_faceCapturePolling != null) {
        print('🔄 清理之前的人脸捕获轮询服务...');
        _faceCapturePolling!.forceReset();
        _faceCapturePolling = null;

        // 给底层足够时间完成清理
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // 初始化并启动 FaceCapturePolling 服务
      print('🆕 创建新的人脸捕获轮询服务...');
      _faceCapturePolling = FaceCapturePolling();

      // 设置回调函数 - 优化轮询机制
      _faceCapturePolling!.onFaceCaptured = (imageData, confidence) {
        print('🎯 检测到新的人脸数据，开始获取...');

        // 如果正在认证过程中，跳过新的识别
        if (_isAuthenticating) {
          print('正在认证过程中，跳过新的人脸识别');
          return;
        }

        // 检查距离上次认证的时间间隔
        if (_lastAuthTime != null) {
          final timeSinceLastAuth = DateTime.now().difference(_lastAuthTime!);
          if (timeSinceLastAuth.inSeconds < _restartListeningDelaySeconds) {
            print('距离上次认证时间过短(${timeSinceLastAuth.inSeconds}秒)，跳过识别');
            return;
          }
        }

        // 如果百度SDK已初始化且置信度足够高，则自动进行人脸识别
        if (_baiduSdkInitialized && confidence > 0.8) {
          // 设置认证状态，记录认证时间
          _isAuthenticating = true;
          _lastAuthTime = DateTime.now();

          // 暂停轮询，等待认证结果
          _pausePollingForAuthentication();
          _performBaiduRecognitionWithImageData(imageData);
        }
      };

      // 设置没有人脸时的回调
      _faceCapturePolling!.onNoFaceDetected = () {
        // 可以在这里处理没有人脸的情况
      };

      _faceCapturePolling!.onError = (error) {
        _onError?.call('人脸捕获错误: $error');
        // 🚨 重要：任何轮询错误都触发服务重启
        print('⚠️ 人脸捕获轮询出错，立即安排服务重启: $error');
        Future.delayed(const Duration(milliseconds: 500), () {
          if (_isListening && _isInitialized) {
            _restartFaceRecognitionService();
          }
        });
      };

      // 启动轮询，每2秒检查一次
      _faceCapturePolling!.startPolling(interval: const Duration(seconds: 2));

      _isListening = true;
      _clearError();
      print('✅ 人脸识别监听启动成功');
    } catch (e) {
      _setError('启动人脸识别监听失败: $e');
      rethrow;
    }
  }

  /// 停止监听人脸识别
  Future<void> stopListening() async {
    if (!_isListening) {
      return;
    }

    print('🛑 停止人脸识别监听...');

    // 停止 FaceCapturePolling 服务
    _faceCapturePolling?.stopPolling();
    _faceCapturePolling = null;

    _stopFrameCapture();
    _stopDetection();
    _adaptiveQualityTimer?.cancel();
    _restartListeningTimer?.cancel(); // 清理重启定时器

    // 重置认证状态
    _isAuthenticating = false;
    _isRecognizing = false;
    _isListening = false;

    print('✅ 人脸识别监听已停止');
  }

  /// 开始帧捕获 - 与原始实现完全一致
  void _startFrameCapture() {
    _stopFrameCapture();

    final currentInterval = _isHighFPSEnabled ? _highFPSInterval : _frameInterval;

    _frameTimer = Timer.periodic(Duration(milliseconds: currentInterval), (_) {
      _frameCount++;

      if (_isProcessing) {
        return;
      }

      _getFrameFromDetector();
    });
  }

  /// 停止帧捕获
  void _stopFrameCapture() {
    _frameTimer?.cancel();
    _frameTimer = null;
  }

  /// 从检测器获取帧数据 - 优化版本，及时清理旧数据
  Future<bool> _getFrameFromDetector() async {
    try {
      _isProcessing = true;

      // 清理旧的图像数据，避免内存累积
      _jpegImageData = null;

      final Uint8List? imageData = await FaceDetector.getJpegImageData();
      if (imageData == null || imageData.isEmpty) {
        _isProcessing = false;
        return false;
      }

      _jpegImageData = imageData;
      _isProcessing = false;
      return true;
    } catch (e) {
      _isProcessing = false;
      return false;
    }
  }

  /// 开始人脸检测和识别
  void _startDetection() {
    _stopDetection();

    _detectionTimer = Timer.periodic(Duration(milliseconds: _detectionInterval), (timer) {
      if (!_isListening) {
        _stopDetection();
        return;
      }

      _performFaceRecognition();
    });
  }

  /// 停止人脸检测
  void _stopDetection() {
    _detectionTimer?.cancel();
    _detectionTimer = null;
  }

  /// 执行人脸识别 - 核心方法，集成百度人脸识别
  Future<void> _performFaceRecognition() async {
    if (!_isListening || _isRecognizing || !_baiduSdkInitialized || _jpegImageData == null) {
      return;
    }

    _isRecognizing = true;

    try {
      // 使用微任务避免阻塞UI线程
      scheduleMicrotask(() async {
        try {
          final results = await BaiduFaceRecognition.identifyWithImageData(_jpegImageData!);
          _processFaceRecognitionResults(results);
        } catch (e) {
          // 将系统错误转换为用户友好的中文错误信息
          String userFriendlyError = _convertToUserFriendlyError(e.toString());

          print('人脸识别失败: $userFriendlyError');
          _onError?.call('人脸识别失败: $userFriendlyError');

          // 🚨 重要修改：任何百度SDK错误都触发服务重启，确保系统稳定性
          print('⚠️ 检测到人脸识别错误，准备重启人脸识别服务: $userFriendlyError');

          // 发送失败结果，确保锁机制能够正常工作
          _authResultController?.add(AuthResult(
            method: AuthMethod.face,
            status: AuthStatus.failureNoMatch,
            errorMessage: '人脸识别失败: $e',
            timestamp: DateTime.now(),
          ));

          // 重置认证状态
          _isAuthenticating = false;

          // 🔄 强制重启服务 - 任何错误都重启，确保服务稳定性
          print('🔄 执行人脸识别服务重启（任何错误都重启策略）...');
          _restartFaceRecognitionService();
        } finally {
          _isRecognizing = false;
        }
      });

      return; // 立即返回，不阻塞
    } catch (e) {
      _onError?.call('启动人脸识别失败: $e');
    } finally {
      // 注意：这里不设置 _isRecognizing = false，因为识别在微任务中进行
    }
  }

  /// 获取当前帧数据
  Uint8List? getCurrentFrame() {
    return _jpegImageData;
  }

  /// 获取性能信息
  Map<String, dynamic> getPerformanceInfo() {
    return {
      'actualFPS': _actualFPS,
      'systemLoad': _systemLoad,
      'isRecognizing': _isRecognizing,
      'baiduSdkInitialized': _baiduSdkInitialized,
      'baiduStatusMessage': _baiduStatusMessage,
    };
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }

  /// 将系统错误转换为用户友好的中文错误信息
  String _convertToUserFriendlyError(String originalError) {
    // 处理Dart系统错误
    if (originalError.contains('Cannot remove from a fixed-length list')) {
      return '内存处理异常，系统将自动重启人脸识别服务';
    }

    // 处理百度SDK的具体错误信息（已经转换过的）
    if (originalError.contains('人脸特征提取失败，请确保图像中有清晰的人脸')) {
      return '人脸特征提取失败，请确保图像中有清晰的人脸';
    } else if (originalError.contains('未检测到人脸，请正对摄像头')) {
      return '未检测到人脸，请正对摄像头';
    } else if (originalError.contains('SDK授权过期，请联系管理员')) {
      return 'SDK授权过期，请联系管理员';
    } else if (originalError.contains('人脸数据库为空，请先注册人脸')) {
      return '人脸数据库为空，请先注册人脸';
    } else if (originalError.contains('图像格式错误，请检查图像数据')) {
      return '图像格式错误，请检查图像数据';
    } else if (originalError.contains('图像质量过低，请在光线充足的环境下拍摄')) {
      return '图像质量过低，请在光线充足的环境下拍摄';
    } else if (originalError.contains('人脸过小，请靠近摄像头')) {
      return '人脸过小，请靠近摄像头';
    } else if (originalError.contains('人脸角度过大，请正对摄像头')) {
      return '人脸角度过大，请正对摄像头';
    } else if (originalError.contains('人脸被遮挡，请露出完整的面部')) {
      return '人脸被遮挡，请露出完整的面部';
    } else if (originalError.contains('人脸识别库异常，请重试')) {
      return '人脸识别库异常，请重试';
    } else if (originalError.contains('参数错误，请检查输入参数')) {
      return '参数错误，请检查输入参数';
    } else if (originalError.contains('内存不足，请重启应用')) {
      return '内存不足，请重启应用';
    } else if (originalError.contains('网络错误，请检查网络连接')) {
      return '网络错误，请检查网络连接';
    } else if (originalError.contains('授权文件损坏，请联系管理员')) {
      return '授权文件损坏，请联系管理员';
    } else if (originalError.contains('模型文件加载失败，请重启应用')) {
      return '模型文件加载失败，请重启应用';
    }

    // 处理原始的百度SDK错误信息
    else if (originalError.contains('get face feature fail')) {
      return '人脸特征提取失败，请重新尝试';
    } else if (originalError.contains('errno')) {
      return '人脸识别服务异常，系统将自动重启';
    }

    // 默认处理
    else {
      return '人脸识别异常，系统将自动重启服务';
    }
  }

  /// 暂停轮询进行认证
  void _pausePollingForAuthentication() {
    if (_faceCapturePolling != null && _faceCapturePolling!.isPolling) {
      _faceCapturePolling!.stopPolling();
      print('暂停人脸轮询，开始认证流程');
    }
  }

  /// 恢复轮询
  void _resumePolling() {
    if (_faceCapturePolling != null && !_faceCapturePolling!.isPolling && _isListening) {
      _faceCapturePolling!.startPolling(interval: const Duration(seconds: 2));
      print('认证完成，恢复人脸轮询');
    }
  }

  /// 安排延迟恢复轮询
  void _schedulePollingResume() {
    // 取消之前的重启定时器（如果存在）
    _restartListeningTimer?.cancel();

    // 设置延迟重启定时器 - 给多认证管理器足够时间处理认证结果
    _restartListeningTimer = Timer(Duration(seconds: _restartListeningDelaySeconds), () {
      if (_isListening && _isInitialized) {
        // 重置认证状态，允许新的识别
        _isAuthenticating = false;
        print('认证冷却期结束，重新开始人脸识别监听');

        // 恢复轮询
        _resumePolling();
      }
    });
  }



  /// 使用图像数据进行百度人脸识别 - 优化版本，避免内存泄漏
  Future<void> _performBaiduRecognitionWithImageData(Uint8List imageData) async {
    if (_isRecognizing) {
      return; // 避免重复识别
    }

    _isRecognizing = true;

    try {
      // 创建图像数据的副本，避免引用原始数据
      final imageDataCopy = Uint8List.fromList(imageData);

      // 使用 Future.microtask 替代 scheduleMicrotask，更好的错误处理
      await Future.microtask(() async {
        try {
          final results = await BaiduFaceRecognition.identifyWithImageData(
            imageDataCopy,
            isJpeg: true
          );

          if (results.isNotEmpty) {
            // 找到得分最高的识别结果
            final bestResult = results.reduce((a, b) => a.score > b.score ? a : b);

            // 检查得分是否达到阈值（与原始实现一致）
            if (bestResult.score > 70.0) {
              print('人脸识别成功，得分: ${bestResult.score}，用户ID: ${bestResult.userId}');

              // 通过认证结果流通知多认证管理器
              // 这样可以确保多认证管理器正确处理状态转换
              final authResult = AuthResult(
                method: AuthMethod.face,
                status: AuthStatus.success,
                userId: bestResult.userId,
                userName: bestResult.userInfo ?? '人脸识别用户',
                timestamp: DateTime.now(),
              );

              print('🚀 FaceRecognitionAuthService: 发送认证结果到流: ${authResult.method} - ${authResult.status}');
              _authResultController?.add(authResult);

              // 同时保持原有回调的兼容性
              _onFaceRecognized?.call(bestResult.userId, bestResult.userInfo);

              // 启动延迟恢复轮询的定时器
              _schedulePollingResume();
            } else {
              // 识别失败，立即恢复轮询
              _isAuthenticating = false;
              _resumePolling();
            }
          }
        } finally {
          // 注意：Uint8List.fromList创建的是固定长度列表，不需要手动清理
          // Dart的垃圾回收器会自动处理内存释放
        }
      });
    } catch (e) {
      // 将系统错误转换为用户友好的中文错误信息
      String userFriendlyError = _convertToUserFriendlyError(e.toString());

      print('人脸识别失败: $userFriendlyError');
      _onError?.call('人脸识别失败: $userFriendlyError');

      // 🚨 重要修改：任何百度SDK错误都触发服务重启，确保系统稳定性
      print('⚠️ 检测到人脸识别错误，准备重启人脸识别服务: $userFriendlyError');

      // 发送失败结果，确保锁机制能够正常工作
      _authResultController?.add(AuthResult(
        method: AuthMethod.face,
        status: AuthStatus.failureNoMatch,
        errorMessage: '人脸识别失败: $e',
        timestamp: DateTime.now(),
      ));

      // 重置认证状态
      _isAuthenticating = false;

      // 🔄 强制重启服务 - 任何错误都重启，确保服务稳定性
      print('🔄 执行人脸识别服务重启（任何错误都重启策略）...');
      _restartFaceRecognitionService();
    } finally {
      _isRecognizing = false;
    }
  }

  /// 处理人脸识别结果
  void _processFaceRecognitionResults(List<dynamic> results) {
    if (results.isNotEmpty) {
      // 找到得分最高的识别结果
      final bestResult = results.reduce((a, b) => a.score > b.score ? a : b);

      // 检查得分是否达到阈值（与原始实现一致）
      if (bestResult.score > 70.0) {
        print('人脸识别成功，得分: ${bestResult.score}，用户ID: ${bestResult.userId}');

        // 通过认证结果流通知多认证管理器
        _authResultController?.add(AuthResult(
          method: AuthMethod.face,
          status: AuthStatus.success,
          userId: bestResult.userId,
          userName: bestResult.userInfo ?? '人脸识别用户',
          timestamp: DateTime.now(),
        ));

        // 同时保持原有回调的兼容性
        _onFaceRecognized?.call(bestResult.userId, bestResult.userInfo);

        // 启动延迟恢复轮询的定时器
        _schedulePollingResume();
      } else {
        // 识别失败，立即恢复轮询
        _isAuthenticating = false;
        _resumePolling();
      }
    } else {
      // 没有识别结果，立即恢复轮询
      _isAuthenticating = false;
      _resumePolling();
    }
  }

  /// 清理内存和资源
  void _cleanupMemory() {
    // 清理图像数据
    _jpegImageData = null;

    // 在调试模式下可以添加额外的清理逻辑
    // 注意：不建议手动触发垃圾回收
  }

  /// 重置认证状态 - 允许外部调用重置认证状态
  void resetAuthenticationState() {
    _isAuthenticating = false;
    _lastAuthTime = null; // 清除上次认证时间
    print('认证状态已重置，允许新的人脸识别');

    // 恢复轮询
    _resumePolling();
  }

  /// 重启人脸识别服务 - 处理严重错误时的完整重启
  Future<void> _restartFaceRecognitionService() async {
    try {
      print('🔄 开始重启人脸识别服务...');

      // 1. 停止当前轮询服务
      _faceCapturePolling?.stopPolling();
      _faceCapturePolling?.dispose();
      _faceCapturePolling = null;

      // 2. 重置所有状态
      _isAuthenticating = false;
      _isRecognizing = false;
      _lastAuthTime = null;

      // 3. 取消所有定时器
      _restartListeningTimer?.cancel();
      _restartListeningTimer = null;

      // 4. 短暂延迟，让系统稳定
      await Future.delayed(const Duration(milliseconds: 500));

      // 5. 重新初始化轮询服务 - 增强状态检查
      if (_isListening && _isInitialized) {
        print('🔄 重新初始化人脸捕获轮询服务...');

        _faceCapturePolling = FaceCapturePolling();

        // 重新设置回调函数
        _faceCapturePolling!.onFaceCaptured = (imageData, confidence) {
          print('🎯 检测到新的人脸数据，开始获取...');

          // 如果正在认证过程中，跳过新的识别
          if (_isAuthenticating) {
            print('正在认证过程中，跳过新的人脸识别');
            return;
          }

          // 检查距离上次认证的时间间隔
          if (_lastAuthTime != null) {
            final timeSinceLastAuth = DateTime.now().difference(_lastAuthTime!);
            if (timeSinceLastAuth.inSeconds < _restartListeningDelaySeconds) {
              print('距离上次认证时间过短(${timeSinceLastAuth.inSeconds}秒)，跳过识别');
              return;
            }
          }

          // 如果百度SDK已初始化且置信度足够高，则自动进行人脸识别
          if (_baiduSdkInitialized && confidence > 0.8) {
            // 设置认证状态，记录认证时间
            _isAuthenticating = true;
            _lastAuthTime = DateTime.now();

            // 暂停轮询，等待认证结果
            _pausePollingForAuthentication();
            _performBaiduRecognitionWithImageData(imageData);
          }
        };

        _faceCapturePolling!.onNoFaceDetected = () {
          // 可以在这里处理没有人脸的情况
        };

        _faceCapturePolling!.onError = (error) {
          _onError?.call('人脸捕获错误: $error');
          // 🚨 重要：任何轮询错误都触发服务重启
          print('⚠️ 人脸捕获轮询出错，立即安排服务重启: $error');
          Future.delayed(const Duration(milliseconds: 500), () {
            if (_isListening && _isInitialized) {
              _restartFaceRecognitionService();
            }
          });
        };

        // 启动轮询
        _faceCapturePolling!.startPolling(interval: const Duration(seconds: 2));
        print('✅ 人脸识别服务重启完成');
      } else {
        print('⚠️ 服务未在监听状态，跳过重启');
      }

    } catch (e) {
      print('❌ 重启人脸识别服务失败: $e');
      _onError?.call('重启人脸识别服务失败: $e');

      // 如果重启失败，尝试简单的轮询恢复
      _resumePolling();
    }
  }

  /// 获取服务健康状态
  Map<String, dynamic> getHealthStatus() {
    return {
      'isInitialized': _isInitialized,
      'isListening': _isListening,
      'isAuthenticating': _isAuthenticating,
      'isRecognizing': _isRecognizing,
      'baiduSdkInitialized': _baiduSdkInitialized,
      'faceCapturePollingActive': _faceCapturePolling?.isPolling ?? false,
      'lastAuthTime': _lastAuthTime?.toIso8601String(),
      'errorMessage': _errorMessage,
    };
  }

  /// 强制重置服务状态（紧急恢复用）
  Future<void> forceReset() async {
    try {
      print('🔄 强制重置人脸识别服务状态...');

      // 停止所有活动
      _faceCapturePolling?.stopPolling();
      _restartListeningTimer?.cancel();

      // 重置所有状态标志
      _isAuthenticating = false;
      _isRecognizing = false;
      _lastAuthTime = null;
      _errorMessage = null;

      // 如果服务正在监听，重新启动轮询
      if (_isListening && _isInitialized && _baiduSdkInitialized) {
        await Future.delayed(const Duration(milliseconds: 500));
        await _restartFaceRecognitionService();
      }

      print('✅ 人脸识别服务状态已强制重置');
    } catch (e) {
      print('❌ 强制重置人脸识别服务失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    stopListening();
    _adaptiveQualityTimer?.cancel();
    _systemLoadTimer?.cancel();
    _restartListeningTimer?.cancel(); // 清理重启定时器
    _faceCapturePolling?.dispose(); // 清理轮询服务

    // 关闭认证结果流
    _authResultController?.close();
    _authResultController = null;

    // 清理内存
    _cleanupMemory();

    _isInitialized = false;
  }
}
