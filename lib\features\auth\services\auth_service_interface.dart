import 'dart:async';
import 'package:flutter/material.dart';

import '../models/auth_result.dart';

/// 认证服务接口
/// 定义所有认证服务必须实现的标准操作
abstract class AuthServiceInterface {
  /// 认证结果流
  Stream<AuthResult> get authResultStream;

  /// 是否正在监听
  bool get isListening;

  /// 是否已初始化
  bool get isInitialized;

  /// 错误信息
  String? get errorMessage;

  /// 初始化认证服务
  /// [context] BuildContext用于获取配置和依赖
  Future<void> initialize(BuildContext context);

  /// 开始监听认证事件
  Future<void> startListening();

  /// 停止监听认证事件
  Future<void> stopListening();

  /// 释放资源
  void dispose();

  /// 获取服务状态信息
  Map<String, dynamic> getStatus();

  /// 重置服务状态
  Future<void> reset();
} 