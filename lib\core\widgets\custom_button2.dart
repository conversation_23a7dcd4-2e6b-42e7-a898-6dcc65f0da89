import 'package:flutter/material.dart';
import 'package:gradient_borders/gradient_borders.dart';

import '../utils/window_util.dart';

enum ButtonType {
  filled, // 渐变填充
  outline, // 描边样式
}

class CustomButton extends StatelessWidget {
  final String text;
  final TextStyle? textStyle;
  final VoidCallback? onTap;
  final double? width;
  final double height;
  final double borderRadius;
  final bool disabled;
  final ButtonType buttonType;

  const CustomButton._({
    Key? key,
    required this.buttonType,
    required this.text,
    this.textStyle,
    this.onTap,
    this.width,
    this.height = 100,
    this.borderRadius = 44,
    this.disabled = false,
  }) : super(key: key);

  // 实色渐变按钮
  factory CustomButton.filled({
    required String text,
    TextStyle? textStyle, // 添加textStyle参数
    VoidCallback? onTap,
    double? width,
    double? height,
    bool disabled = false,
  }) {
    return CustomButton._(
      text: text,
      buttonType: ButtonType.filled,
      textStyle: textStyle ??
           TextStyle(
            fontSize: 48.p,
            fontWeight: FontWeight.w500,
            color: Colors.white,
             height: 48/56
          ),
      onTap: onTap,
      width: width,
      height: height ?? 100.p,
      disabled: disabled,
    );
  }

  // 轮廓按钮
  factory CustomButton.outline({
    required String text,
    TextStyle? textStyle, // 添加textStyle参数
    VoidCallback? onTap,
    double? width,
    double? height,
    bool disabled = false,
  }) {
    return CustomButton._(
      text: text,
      buttonType: ButtonType.outline,
      textStyle: textStyle ??
           TextStyle(
            fontSize: 48.p,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF54A0FF),
             height: 48/56
          ),
      onTap: onTap,
      width: width,
      height: height ?? 100.p,
      disabled: disabled,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget button = Container(
      height: height,
      decoration: _getDecoration(),
      child: Center(
        child: Text(
          text,
          style: textStyle,
        ),
      ),
    );

    if (width != null) {
      button = SizedBox(
        width: width,
        child: button,
      );
    }

    return GestureDetector(
      onTap: disabled ? null : onTap,
      child: button,
    );
  }

  BoxDecoration _getDecoration() {
    // 统一的渐变配置
    const LinearGradient blueGreenGradient = LinearGradient(
      begin: Alignment.centerLeft, // 90度
      end: Alignment.centerRight,
      colors: [
        Color(0xFF54A0FF), // rgba(84, 160, 255, 1)
        Color(0xFF3FE2C8), // rgba(63, 226, 200, 1)
      ],
    );

    switch (buttonType) {
      case ButtonType.filled:
        return BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF54A0FF),
              Color(0x6654A0FF),  // 0x66 = 40% opacity
            ],
            stops: [0.0, 1.0],
          ),
          borderRadius: BorderRadius.circular(60.p),
          border: const GradientBoxBorder(
            gradient: blueGreenGradient, // 添加渐变边框
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF1D62FD).withOpacity(0.2),
              offset: const Offset(0, 6),
              blurRadius: 20,
            ),
          ],
        );

      case ButtonType.outline:
        return BoxDecoration(
          color: Colors.white.withOpacity(0.3),
          borderRadius: BorderRadius.circular(60.p),
          border:  GradientBoxBorder(
            gradient: blueGreenGradient, // 使用相同的渐变
            width: 2.p,
          ),
        );
    }
  }
}
