// ignore_for_file: avoid_print

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';
import 'SeaBlueKeyboard.dart';

class SeaBlueTextInput extends StatefulWidget {
  SeaBlueTextInput(
      {this.focusNode,
      this.controller,
      this.style,
      this.textAlign = TextAlign.start,
      this.enabled,
      this.keyboardType,
      this.onChanged,
      this.onSubmitted,
      this.hintText,
      this.onTap,
      this.needObscure = false,
      this.icon,
      this.height,
      this.borderRadius,
      this.backgroundColor,
      this.padding,
      Key? key})
      : super(key: key);
  String? icon;
  double? height;
  double? borderRadius;
  Color? backgroundColor;
  EdgeInsets? padding;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final TextStyle? style;
  final TextAlign textAlign;
  final bool? enabled;
  TextInputType? keyboardType;
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final GestureTapCallback? onTap;
  bool needObscure;

  @override
  State<SeaBlueTextInput> createState() => _SeaBlueTextInputState();
}

class _SeaBlueTextInputState extends State<SeaBlueTextInput> {
  RxBool hasFocus = false.obs;
  RxBool obscureText = false.obs;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    widget.focusNode?.addListener(focusListener);
    obscureText.value = widget.needObscure;
  }

  focusListener() {
    hasFocus.value = widget.focusNode?.hasFocus ?? false;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    widget.focusNode?.removeListener(focusListener);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
          height: widget.height ?? 100.p,
          padding: widget.padding ??
              EdgeInsets.symmetric(horizontal: 50.p),
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? BPUtils.c_FFF6F7FA,
            borderRadius: BorderRadius.circular(
                widget.borderRadius ?? 50.p),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  right: 20.p,
                ),
                child: (widget.icon?.isEmpty ?? true)
                    ? Container()
                    : Image.asset(
                        AssetUtil.fullPath(widget.icon!),
                        width: 48.p,
                        height: 48.p,
                        fit: BoxFit.fill,
                      ),
              ),
              Expanded(
                child: Stack(
                  children: [
                    Container(
                      height: widget.height ?? 100.p,
                      alignment: Alignment.center,
                      child: TextField(
                        obscureText: obscureText.value,
                        showCursor: true,
                        controller: widget.controller,
                        focusNode: widget.focusNode,
                        textAlign: TextAlign.start,
                        textAlignVertical: TextAlignVertical.center,
                        style: widget.style ??
                            TextStyle(
                                color: BPUtils.c_FF212121,
                                fontSize: 32.p,
                                textBaseline: TextBaseline.alphabetic),
                        decoration: InputDecoration(
                          hintText: widget.hintText,
                          hintStyle: TextStyle(
                            color: BPUtils.c_FF7A8499,
                            fontSize: widget.style?.fontSize ??
                                32.p,
                            fontWeight:
                                widget.style?.fontWeight ?? FontWeight.normal,
                            textBaseline: TextBaseline.alphabetic,
                          ),
                          border: const OutlineInputBorder(
                            borderSide: BorderSide.none,
                            gapPadding: 0,
                          ),
                          filled: true,
                          fillColor: Colors.transparent,
                          contentPadding: EdgeInsets.zero,
                        ),
                        // onTap: ontap,
                      ),
                    ),
                    Positioned(
                      top: 0,
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: InkWell(
                        onTap: ontap,
                        child: Container(
                          color: Colors.transparent,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  Visibility(
                    visible: widget.needObscure,
                    child: InkWell(
                      onTap: () {
                        obscureText.value = !(obscureText.value);
                        obscureText.refresh();
                        print(obscureText.value);
                      },
                      child: Padding(
                        padding:
                            EdgeInsets.only(right: 25.p),
                        child: Image.asset(
                          AssetUtil.fullPath(
                              obscureText.value ? 'hide_icon' : 'show_icon'),
                          width: 44.p,
                          height: 44.p,
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      widget.controller?.text = '';
                      setState(() {});
                    },
                    child: Padding(
                      padding: EdgeInsets.all(0.p),
                      child: Image.asset(
                        AssetUtil.fullPath('清除'),
                        width: 44.p,
                        height: 44.p,
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ));
  }

  ontap() {
    print('TextField onTap');
    widget.onTap?.call();
    // SeaBlueKeyboard.show(
    //     type: SeaBlueKeyboardType.number,
    //     controller: widget.controller,
    //     focus: widget.focusNode,
    //     onSubmitted: (ctr) {
    //       if (widget.onSubmitted != null) {
    //         widget.onSubmitted?.call(widget.controller?.text ?? '');
    //       } else {
    //         Get.back();
    //       }
    //     });
  }
}
