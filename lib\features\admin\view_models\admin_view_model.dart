import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/database/db_manager.dart';
import '../../../core/database/interfaces/db_interface.dart';
import '../../../core/router/app_router.dart';
import '../../../shared/utils/asset_util.dart';

class AdminViewModel extends ChangeNotifier {
  late DBInterface db;
  bool _isLoading = false;
  String? _errorMessage;
  String? bookBoxCount;



  Future<void> init() async {
    try {
      db = await DBManager.getDBInstance();
      await getBookBoxCount();
    } catch (e) {
      _errorMessage = '初始化数据库失败: $e';
      notifyListeners();
    }
  }

  Future<void> resetBookCount() async {
    try {
      _isLoading = true;
      notifyListeners();
      await db.setBookBoxCount(0);
      _isLoading = false;
      notifyListeners();
      Get.snackbar(
        '成功',
        '书箱书籍数量已清零',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
      );
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      _showError('清空书箱书籍数量失败: $e');
    }
  }
  // 获取书箱书籍数量
  Future<void> getBookBoxCount() async {
    try {
      _isLoading = true;
      notifyListeners();
      var result = await db.getBookBoxCount();
      if(result.success){
        bookBoxCount = result.data.toString();
      }
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      _showError('获取书箱书籍数量失败');
    }
  }

  void _showError(String message) {
    if (message.isNotEmpty) {
      Get.snackbar(
        '错误',
        message,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }
}

class HandleModel {
  final String? title;
  final String? type;
  final String? icon;
  final Map<String, dynamic>? map;
  final Function? onTap;

  const HandleModel( {
    this.title,
    this.type,
    this.icon,
    this.onTap,
    this.map,
  });

  HandleModel copyWith({
    String? title,
    String? type,
    String? icon,
    Function? onTap,
    Map<String, dynamic>? map,
  }) {
    return HandleModel(
      title: title ?? this.title,
      type: type ?? this.type,
      icon: icon ?? this.icon,
      onTap: onTap ?? this.onTap,
      map: map ?? this.map,
    );
  }
}
