import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'baidu_face_sdk.dart';
import 'face_capture_polling.dart';

/// 人脸识别管理器 - 负责集成百度人脸识别SDK与人脸检测轮询系统
class FaceRecognitionManager {
  // 单例模式
  static final FaceRecognitionManager _instance = FaceRecognitionManager._internal();
  
  factory FaceRecognitionManager() {
    return _instance;
  }
  
  FaceRecognitionManager._internal();
  
  // 人脸捕获轮询器
  late FaceCapturePolling _faceCapturePolling;
  
  // 是否已初始化
  bool _isInitialized = false;
  
  // 轮询状态
  bool _isPolling = false;
  
  // 轮询状态的getter
  bool get isPolling => _isPolling;
  
  // 用于通知UI的Stream
  final _recognitionResultController = StreamController<RecognitionEvent>.broadcast();
  Stream<RecognitionEvent> get recognitionStream => _recognitionResultController.stream;
  
  // 是否在处理人脸识别
  bool _isProcessingFace = false;
  
  // 初始化
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      // 1. 初始化百度人脸识别SDK
      final sdkInitialized = await BaiduFaceSDK.initialize();
      if (!sdkInitialized) {
        _recognitionResultController.add(
          RecognitionEvent(
            type: RecognitionEventType.error,
            message: '百度人脸识别SDK初始化失败',
          )
        );
        return false;
      }
      
      // 2. 初始化人脸捕获轮询器
      _faceCapturePolling = FaceCapturePolling();
      _faceCapturePolling.onFaceCaptured = _onFaceCaptured;
    _faceCapturePolling.onNoFaceDetected = _onNoFaceDetected;
      _faceCapturePolling.onError = _onPollingError;
      
      _isInitialized = true;
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.info,
          message: '人脸识别管理器初始化成功',
        )
      );
      
      return true;
    } catch (e) {
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别管理器初始化失败: $e',
        )
      );
      return false;
    }
  }
  
  // 开始人脸识别轮询
  void startRecognition({Duration interval = const Duration(seconds: 1)}) {
    if (!_isInitialized) {
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别管理器尚未初始化',
        )
      );
      return;
    }
    
    if (_isPolling) {
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.warning,
          message: '人脸识别轮询已在运行中',
        )
      );
      return;
    }
    
    _isPolling = true;
    _faceCapturePolling.startPolling(interval: interval);
    
    _recognitionResultController.add(
      RecognitionEvent(
        type: RecognitionEventType.info,
        message: '开始人脸识别轮询，间隔: ${interval.inMilliseconds}ms',
      )
    );
  }
  
  // 停止人脸识别轮询
  void stopRecognition() {
    if (!_isPolling) {
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.warning,
          message: '人脸识别轮询未在运行',
        )
      );
      return;
    }
    
    _isPolling = false;
    _faceCapturePolling.stopPolling();
    
    _recognitionResultController.add(
      RecognitionEvent(
        type: RecognitionEventType.info,
        message: '停止人脸识别轮询',
      )
    );
  }
  
  // 处理没有检测到人脸的情况
  void _onNoFaceDetected() {
    // 发送清除事件
    _recognitionResultController.add(
      RecognitionEvent(
        type: RecognitionEventType.noFaceDetected,
        message: '未检测到人脸，清除显示',
      )
    );
    print('🧹 人脸识别管理器：未检测到人脸，已清除');
  }
  
  // 处理捕获到的人脸
  void _onFaceCaptured(Uint8List imageData, double confidence) async {
    if (_isProcessingFace) {
      // 如果正在处理人脸，跳过此帧
      print('跳过此帧，因为当前正在处理人脸识别');
      return;
    }
    
    _isProcessingFace = true;
    print('📷 捕获到人脸图像，置信度: $confidence');
    
    try {
      // 通知UI已捕获到人脸
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.faceCaptured,
          message: '捕获到人脸图像，置信度: $confidence',
          faceImage: imageData,
          confidence: confidence,
        )
      );
      
      // 执行1:N人脸识别
      final recognitionResults = await BaiduFaceSDK.identifyFace(imageData);
      
      if (recognitionResults.isEmpty) {
        // 未找到匹配的人脸
        print('❌ 未找到匹配的人脸');
        _recognitionResultController.add(
          RecognitionEvent(
            type: RecognitionEventType.noMatch,
            message: '未找到匹配的人脸',
            faceImage: imageData,
            confidence: confidence,
          )
        );
      } else {
        // 找到匹配的人脸
        final bestMatch = recognitionResults.first;
        print('✅ 找到匹配的人脸: ${bestMatch.userId}, 得分: ${bestMatch.score}');
        
        _recognitionResultController.add(
          RecognitionEvent(
            type: RecognitionEventType.matchFound,
            message: '找到匹配的人脸: ${bestMatch.userId}',
            faceImage: imageData,
            confidence: confidence,
            recognitionResult: bestMatch,
          )
        );
      }
    } catch (e) {
      print('❌ 人脸识别处理错误: $e');
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别处理错误: $e',
        )
      );
    } finally {
      _isProcessingFace = false;
    }
  }
  
  // 处理轮询错误
  void _onPollingError(String error) {
    print('❌ 人脸捕获轮询错误: $error');
    _recognitionResultController.add(
      RecognitionEvent(
        type: RecognitionEventType.error,
        message: '人脸捕获轮询错误: $error',
      )
    );
  }
  
  // 手动注册人脸
  Future<bool> registerFace(Uint8List faceImage, String userId, String groupId, [String? userInfo]) async {
    if (!_isInitialized) {
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别管理器尚未初始化',
        )
      );
      return false;
    }
    
    try {
      final result = await BaiduFaceSDK.addUser(faceImage, userId, groupId, userInfo);
      print('📝 注册人脸结果: $result');
      
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.registered,
          message: '注册人脸成功: $userId',
          faceImage: faceImage,
        )
      );
      
      return true;
    } catch (e) {
      print('❌ 注册人脸错误: $e');
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '注册人脸错误: $e',
        )
      );
      return false;
    }
  }
  
  // 删除注册的人脸
  Future<bool> deleteFace(String userId, String groupId) async {
    if (!_isInitialized) {
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别管理器尚未初始化',
        )
      );
      return false;
    }
    
    try {
      final result = await BaiduFaceSDK.deleteUser(userId, groupId);
      print('🗑️ 删除人脸结果: $result');
      
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.info,
          message: '删除人脸成功: $userId',
        )
      );
      
      return true;
    } catch (e) {
      print('❌ 删除人脸错误: $e');
      _recognitionResultController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '删除人脸错误: $e',
        )
      );
      return false;
    }
  }
  
  // 获取人脸库中的人脸数量
  int getFaceCount([String? groupId]) {
    if (!_isInitialized) {
      return -1;
    }
    
    return BaiduFaceSDK.getFaceCount(groupId);
  }
  
  // 清理资源
  void dispose() {
    if (_isPolling) {
      stopRecognition();
    }
    
    _recognitionResultController.close();
    BaiduFaceSDK.dispose();
    _isInitialized = false;
    
    print('🧹 人脸识别管理器资源已清理');
  }
}

// 人脸识别事件类型
enum RecognitionEventType {
  info,        // 普通信息
  warning,     // 警告
  error,       // 错误
  faceCaptured,// 捕获到人脸
  noFaceDetected, // 没有检测到人脸
  matchFound,  // 找到匹配
  noMatch,     // 未找到匹配
  registered,  // 注册成功
}

// 人脸识别事件
class RecognitionEvent {
  final RecognitionEventType type;
  final String message;
  final Uint8List? faceImage;
  final double? confidence;
  final FaceRecognitionResult? recognitionResult;
  
  RecognitionEvent({
    required this.type,
    required this.message,
    this.faceImage,
    this.confidence,
    this.recognitionResult,
  });
} 