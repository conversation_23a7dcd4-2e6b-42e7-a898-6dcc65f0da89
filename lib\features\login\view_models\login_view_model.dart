import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';

import '../../../shared/utils/asset_util.dart';
import '../models/login_item_data.dart';
import '../models/operation_type.dart';

class LoginViewModel extends ChangeNotifier {
  final BuildContext context;
  final OperationType operationType;
  final List<LoginItemData?> _loginTypes = [];
  LoginItemData? _selectedLoginType;

  // 登录方式列表
  final List<LoginItemData?> allLoginTypes = [
    LoginItemData(
      title: '读者证认证',
      type: AuthLoginType.readerCard,
      icon: AssetUtil.fullPath('reader_card_auth.png'),
    ),
    LoginItemData(
      title: '身份证认证',
      type: AuthLoginType.IDCard,
      icon: AssetUtil.fullPath('id_card_auth.png'),
    ),
    LoginItemData(
      title: '腾讯E证通认证',
      type: AuthLoginType.tencentTCard,
      icon: AssetUtil.fullPath('tencent_card_auth.png'),
    ),
    LoginItemData(
      title: '手动输入认证',
      type: AuthLoginType.keyboardInput,
      icon: AssetUtil.fullPath('keyboard_auth.png'),
    ),
    LoginItemData(
      title: '人脸识别认证',
      type: AuthLoginType.faceAuth,
      icon: AssetUtil.fullPath('face_auth.png'),
    ),
    LoginItemData(
      title: '社保卡认证',
      type: AuthLoginType.socailSecurityCard,
      icon: AssetUtil.fullPath('social_security_auth.png'),
    ),
    LoginItemData(
      title: '市民卡认证',
      type: AuthLoginType.citizenCard,
      icon: AssetUtil.fullPath('citizen_card_auth.png'),
    ),
    LoginItemData(
      title: '微信二维码认证',
      type: AuthLoginType.wechatQRCode,
      icon: AssetUtil.fullPath('wechat_auth.png'),
    ),
    LoginItemData(
      title: '借阅宝认证',
      type: AuthLoginType.jieYueBao,
      icon: AssetUtil.fullPath('borrow_treasure_auth.png'),
    ),
    LoginItemData(
      title: '支付宝扫码认证',
      type: AuthLoginType.alipayQRCode,
      icon: AssetUtil.fullPath('alipay_auth.png'),
    ),
    LoginItemData(
      title: '芝麻信用码认证',
      type: AuthLoginType.aliCreditQRCode,
      icon: AssetUtil.fullPath('zhima_credit_auth.png'),
    ),
    LoginItemData(
      title: '支付宝扫码认证（阿里信用）',
      type: AuthLoginType.alipayQRCode_credit,
      icon: AssetUtil.fullPath('alipay_auth.png'),
    ),
    LoginItemData(
      title: '微信/支付宝认证',
      type: AuthLoginType.wecharOrAlipay,
      icon: AssetUtil.fullPath('wechat_auth.png'),
    ),
    LoginItemData(
      title: '二维码读者认证',
      type: AuthLoginType.readerQRCode,
      icon: AssetUtil.fullPath('reader_qr_auth.png'),
    ),
    LoginItemData(
      title: 'IMI身份认证',
      type: AuthLoginType.IMIAuth,
      icon: AssetUtil.fullPath('id_card_auth.png'),
    ),
    LoginItemData(
      title: '电子社保卡认证',
      type: AuthLoginType.eletricSocialSecurityCard,
      icon: AssetUtil.fullPath('e_social_security_auth.png'),
    ),
    LoginItemData(
      title: '拍照配置',
      type: AuthLoginType.takePhoto,
      icon: AssetUtil.fullPath('social_security_auth.png'),
    ),
    LoginItemData(
      title: '上海随申码认证',
      type: AuthLoginType.shangHaiQRCode,
      icon: AssetUtil.fullPath('reader_qr_auth.png'),
    ),
    LoginItemData(
      title: '微信扫码认证',
      type: AuthLoginType.wechatScanQRCode,
      icon: AssetUtil.fullPath('wechat_auth.png'),
    ),
    LoginItemData(
      title: '汇文二维码',
      type: AuthLoginType.huiwenQRCode,
      icon: AssetUtil.fullPath('reader_qr_auth.png'),
    ),
  ];

  List<LoginItemData?> get loginTypes => _loginTypes;
  LoginItemData? get selectedLoginType => _selectedLoginType;

  LoginViewModel({
    required this.context,
    required this.operationType,
  }) {
    _configLoginTypes();
  }

  SettingProvider get _settingProvider =>
      Provider.of<SettingProvider>(context, listen: false);

  void _configLoginTypes() {
    _loginTypes.clear();
    final readerConfig = _settingProvider.readerConfigData;
    final authMap = readerConfig?.authMap;
    final loginTypeOrders = readerConfig?.loginTypeOrders ?? [];

    if (authMap == null) return;

    // 1. 筛选出启用的登录方式及其对应的 Key
    final Map<String, LoginItemData> enabledTypesMap = {};
    for (var item in allLoginTypes) {
      if (item != null) {
        final key = AuthLoginMapReverse[item.type];
        // 检查 key 是否有效，并且在 authMap 中有配置
        if (key != null && (authMap[key] ?? []).isNotEmpty) {
          enabledTypesMap[key] = item;
        }
      }
    }

    for (String orderKey in loginTypeOrders) {
      if (enabledTypesMap.containsKey(orderKey)) {
        _loginTypes.add(enabledTypesMap[orderKey]);
        enabledTypesMap.remove(orderKey);
      }
    }
    _loginTypes.addAll(enabledTypesMap.values);


    notifyListeners();
  }

  Future<void> setLoginType(LoginItemData item) async {
    if (_selectedLoginType?.type == item.type) return;

    _selectedLoginType = item;
    notifyListeners();

    // if (context.mounted) {
    //   Navigator.push(
    //     context,
    //     MaterialPageRoute(
    //       builder: (_) => AuthView(
    //         authLoginType: item.type ?? AuthLoginType.unknow,
    //         operationType: operationType,
    //       ),
    //     ),
    //   );
    // }
  }
}