import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';
import 'package:seasetting/setting/mini_smart_library/models/db_result.dart';

import '../../../core/database/db_manager.dart';
import '../../../core/database/interfaces/db_interface.dart';
import '../models/error.dart';

class ErrorRecordsViewModel extends ChangeNotifier {
  late DBInterface db;

  bool isLoading = false;
  String? _errorMessage;

  List<ErrorRecord> _errorRecords = [];

  List<ErrorRecord> get errorRecords => _errorRecords;

  set errorRecords(List<ErrorRecord> value) {
    _errorRecords = value;
    notifyListeners();
  }

  Future<void> init() async {
    try {
      db = await DBManager.getDBInstance();
      await getErrorRecord();
    } catch (e) {
      _errorMessage = '初始化数据库失败: $e';
      notifyListeners();
    }
  }

  // 添加错误记录
  Future<void> getErrorRecord() async {
    try {
      isLoading = true;
      var result = await db.getErrorRecords(status: '0');
      if (result.success) {
        errorRecords =
            result.data?.map((e) => ErrorRecord.fromJson(e)).toList() ?? [];
      } else {
        _errorMessage = '获取错误记录失败';
      }
    } catch (e) {
      _errorMessage = '获取错误记录失败';
    } finally {
      isLoading = false;
      notifyListeners();
      showToast(_errorMessage!);
    }
  }

  Future<void> addErrorRecord({
    String? slotNo,
    String? bookTitle,
    String? errorReason,
    String? status,
    String? operator,
  }) async {
    try {
      isLoading = true;
      var result = await db.addErrorRecord(ErrorRecord(
        slotNo: slotNo,
        bookTitle: bookTitle,
        errorReason: errorReason,
        status: '0',
        operator: operator,
      ));
      if (result.success) {
        _errorMessage = '添加错误记录成功';
        getErrorRecord();
      } else {
        _errorMessage = '添加错误记录失败';
      }
    } catch (e) {
      _errorMessage = '添加错误记录失败';
    } finally {
      isLoading = false;
      notifyListeners();
      showToast(_errorMessage!);
    }
  }

  Future<void> updateErrorRecordStatus(int id) async {
    try {
      isLoading = true;
      var result = await db.updateErrorRecordStatus(id: id, status: '1');
      if (result.success) {
        _errorMessage = '处理成功';
        getErrorRecord();
      } else {
        _errorMessage = '处理失败';
      }
    } catch (e) {
      _errorMessage = '处理失败';
    } finally {
      isLoading = false;
      notifyListeners();
      showToast(_errorMessage!);
    }
  }
}
