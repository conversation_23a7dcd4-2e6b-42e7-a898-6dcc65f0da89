^E:\FACE_PRO\TEST\FACE\SRC\OPENCV\OUT\BUILD2\CMAKEFILES\77F71F86F8EF91EEAFBCB6237E54C518\GENERATE.STAMP.RULE
setlocal
D:\softWare\cmake-4.0.0-rc1-windows-x86_64\bin\cmake.exe -SE:/face_pro/test/face/src/opencv -BE:/face_pro/test/face/src/opencv/out/build2 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/face_pro/test/face/src/opencv/out/build2/face_detection.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
