{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "face_detection", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "face_detector::@6890427a1f51a3e7e1df", "jsonFile": "target-face_detector-Debug-740d11c577af75d01f5e.json", "name": "face_detector", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/gdwork/code/a3g/src/opencv/out/build/x64-Debug", "source": "D:/gdwork/code/a3g/src/opencv"}, "version": {"major": 2, "minor": 7}}