import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:a3g/generated/l10n.dart';
import 'package:seasetting/seasetting.dart';

import 'core/providers/app_info_provider.dart';
import 'core/router/app_router.dart';
import 'features/home/<USER>/index_page.dart';


class App extends StatelessWidget {

  const App({Key? key}) : super(key: key);




  @override
  Widget build(BuildContext context) {
    return OKToast(
      textStyle: const TextStyle(color: Colors.white, fontSize: 30),
      textPadding: const EdgeInsets.all(15),
      child: Consumer2<AppInfoProvider, CurrentLocale>(
        builder: (context, appInfo, locale, child) {
          return GetMaterialApp(
            initialRoute: AppRoutes.index,
            navigatorKey: GlobalNavi.navigatorKey,
            getPages: AppPages.pages,
            defaultTransition: Transition.rightToLeft,
            transitionDuration: const Duration(milliseconds: 350),
            navigatorObservers: [RouterObserver()],
            scrollBehavior: const MaterialScrollBehavior().copyWith(
                scrollbars: true,
                dragDevices: BPUtils.touchLikeDeviceTypes
            ),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              S.delegate
            ],
            supportedLocales: const [
              Locale('zh', 'CN'),
              Locale('en', 'US'),
              Locale('zh_Hant', 'CN'),
            ],
            theme: appInfo.currentTheme,
            darkTheme: appInfo.currentTheme,
            locale: locale.value,
            builder: (context, child) {
              final easyloading = EasyLoading.init();
              child = easyloading(context, child);
              return child;
            },
          );
        },
      ),
    );
  }
}


