// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh_Hant locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh_Hant';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "AccountPassword": MessageLookupByLibrary.simpleMessage("帳號密碼"),
        "IncorrectPassword": MessageLookupByLibrary.simpleMessage("密码唔啱"),
        "Predeposit": MessageLookupByLibrary.simpleMessage("預存(元)"),
        "ReaderCardVerification": MessageLookupByLibrary.simpleMessage("讀者證認證"),
        "ScanAuth": MessageLookupByLibrary.simpleMessage("二维码驗證"),
        "Sending": MessageLookupByLibrary.simpleMessage("正喺发送中.."),
        "accountBalance": MessageLookupByLibrary.simpleMessage("賬戶餘額"),
        "accountInput": MessageLookupByLibrary.simpleMessage("請輸入讀者證號或者身份證號碼"),
        "accountLogin": MessageLookupByLibrary.simpleMessage("帳號登入"),
        "accountRequired": MessageLookupByLibrary.simpleMessage("帳號唔可以為空"),
        "addressInput": MessageLookupByLibrary.simpleMessage("住址"),
        "agreement": MessageLookupByLibrary.simpleMessage("我已經閱讀並同意以上內容"),
        "alipay": MessageLookupByLibrary.simpleMessage("支付寶"),
        "alipayCreditRegister": MessageLookupByLibrary.simpleMessage("阿里信用分註冊"),
        "alipay_login": MessageLookupByLibrary.simpleMessage("支付寶登錄"),
        "alipay_scan_guide":
            MessageLookupByLibrary.simpleMessage("打開手機端支付寶應用 點擊左上角掃一掃圖標"),
        "applyCard": MessageLookupByLibrary.simpleMessage("辦證"),
        "at": MessageLookupByLibrary.simpleMessage("放喺"),
        "auth_fail_advice":
            MessageLookupByLibrary.simpleMessage("若授權失敗，請查看頁面失敗原因或咨詢芝麻信用客服"),
        "availableInLibrary": MessageLookupByLibrary.simpleMessage("喺館"),
        "available_borrow_count": MessageLookupByLibrary.simpleMessage("可借總數"),
        "available_for_borrow": MessageLookupByLibrary.simpleMessage("可借閱"),
        "barcode": MessageLookupByLibrary.simpleMessage("條碼"),
        "barcodeError": MessageLookupByLibrary.simpleMessage("條碼解析失敗"),
        "barcode_number": MessageLookupByLibrary.simpleMessage("條碼號"),
        "birthDateInput": MessageLookupByLibrary.simpleMessage("出生日期"),
        "bookRecommendations": MessageLookupByLibrary.simpleMessage("猜你喜歡嘅書籍"),
        "bookRecommendationsTitle":
            MessageLookupByLibrary.simpleMessage("好書推薦"),
        "bookTitle": MessageLookupByLibrary.simpleMessage("書名"),
        "bookTitleShort": MessageLookupByLibrary.simpleMessage("題名"),
        "book_overdue": MessageLookupByLibrary.simpleMessage("該單冊已逾期"),
        "booksToReturn": MessageLookupByLibrary.simpleMessage("待還書"),
        "borrowBook": MessageLookupByLibrary.simpleMessage("借書"),
        "borrowFailure": MessageLookupByLibrary.simpleMessage("借書失敗"),
        "borrowRequestFailed": MessageLookupByLibrary.simpleMessage("請求借書失敗"),
        "borrowSuccess": MessageLookupByLibrary.simpleMessage("借書成功"),
        "borrow_fail_request":
            MessageLookupByLibrary.simpleMessage("借書失敗，請求借書失敗"),
        "borrow_fail_rewrite_security":
            MessageLookupByLibrary.simpleMessage("借書失敗，改寫防盜位失敗"),
        "borrow_success": MessageLookupByLibrary.simpleMessage("成功借閱文獻"),
        "borrow_with_alipay_credit": MessageLookupByLibrary.simpleMessage(
            "芝麻分只要550分以上，打開支付寶掃一掃螢幕中嘅二維碼即可開啟借書"),
        "borrowableCopies": MessageLookupByLibrary.simpleMessage("可借閱(本)"),
        "borrowed": MessageLookupByLibrary.simpleMessage("已借閱"),
        "borrowedCopies": MessageLookupByLibrary.simpleMessage("已借閱(本)"),
        "borrowing": MessageLookupByLibrary.simpleMessage("喺借"),
        "borrowingInProgress": MessageLookupByLibrary.simpleMessage("借書中..."),
        "businessTableFail": MessageLookupByLibrary.simpleMessage("處理業務系統表失敗"),
        "callNumber": MessageLookupByLibrary.simpleMessage("索書號"),
        "captchaInput": MessageLookupByLibrary.simpleMessage("請輸入驗證碼"),
        "captchaRequired": MessageLookupByLibrary.simpleMessage("驗證碼唔可以為空"),
        "cardApplicationSuccess":
            MessageLookupByLibrary.simpleMessage("您已經成功申請咗讀者證"),
        "cardCreationFail": MessageLookupByLibrary.simpleMessage("辦證失敗"),
        "cardCreationSuccess": MessageLookupByLibrary.simpleMessage("辦證成功"),
        "cardIdExhausted": MessageLookupByLibrary.simpleMessage("當前讀者證號已用盡"),
        "cardIdExhaustedContact":
            MessageLookupByLibrary.simpleMessage("當前讀者證號已用盡，請聯繫圖書館工作人員"),
        "cardIdFail": MessageLookupByLibrary.simpleMessage("讀者證號獲取失敗"),
        "cardIdFailContact":
            MessageLookupByLibrary.simpleMessage("讀者證號獲取失敗，請聯繫圖書館工作人員"),
        "cardIssuanceError": MessageLookupByLibrary.simpleMessage("發卡錯誤"),
        "cardLefts": MessageLookupByLibrary.simpleMessage("卡剩餘"),
        "cardLogin": MessageLookupByLibrary.simpleMessage("讀者證登錄"),
        "cardMachineError":
            MessageLookupByLibrary.simpleMessage("發卡機錯誤，請聯繫圖書館工作人員"),
        "cardRequestFail": MessageLookupByLibrary.simpleMessage("請求辦證失敗"),
        "card_not_exist": MessageLookupByLibrary.simpleMessage("讀者證唔存在"),
        "cert_number": MessageLookupByLibrary.simpleMessage("證號"),
        "characters": MessageLookupByLibrary.simpleMessage("字符"),
        "citizenCardLogin": MessageLookupByLibrary.simpleMessage("市民卡登錄"),
        "clear": MessageLookupByLibrary.simpleMessage("清空"),
        "collectReaderCard":
            MessageLookupByLibrary.simpleMessage("請從出卡口取走您嘅讀者證"),
        "confirm": MessageLookupByLibrary.simpleMessage("確認"),
        "confirmBorrow": MessageLookupByLibrary.simpleMessage("確認借書"),
        "confirmButton": MessageLookupByLibrary.simpleMessage("確定"),
        "confirm_return": MessageLookupByLibrary.simpleMessage("確認還書"),
        "continueBorrowing": MessageLookupByLibrary.simpleMessage("繼續借書"),
        "continueButton": MessageLookupByLibrary.simpleMessage("繼續"),
        "continue_returning": MessageLookupByLibrary.simpleMessage("繼續還書"),
        "copy": MessageLookupByLibrary.simpleMessage("本"),
        "currencyUnit": MessageLookupByLibrary.simpleMessage("元"),
        "currentDeposit": MessageLookupByLibrary.simpleMessage("本次存入"),
        "current_borrow_count": MessageLookupByLibrary.simpleMessage("現借總數"),
        "debt": MessageLookupByLibrary.simpleMessage("欠款"),
        "debt_amount": MessageLookupByLibrary.simpleMessage("欠款(元)"),
        "defaultPassword": MessageLookupByLibrary.simpleMessage("預設密碼"),
        "deposit": MessageLookupByLibrary.simpleMessage("押金"),
        "depositInput": MessageLookupByLibrary.simpleMessage("押金"),
        "depositNotSupported":
            MessageLookupByLibrary.simpleMessage("暫時唔支援收取押金"),
        "detail_info": MessageLookupByLibrary.simpleMessage("詳情信息"),
        "details": MessageLookupByLibrary.simpleMessage("詳情"),
        "dueDate": MessageLookupByLibrary.simpleMessage("應還時間"),
        "duplicateRegistration":
            MessageLookupByLibrary.simpleMessage("您已經辦理過讀者證，請勿重複辦證"),
        "eSSCRegistration": MessageLookupByLibrary.simpleMessage("電子社保卡登記"),
        "eSocialSecurityCardLogin":
            MessageLookupByLibrary.simpleMessage("電子社保卡登錄"),
        "electronicSocialSecurityCard":
            MessageLookupByLibrary.simpleMessage("電子社保卡"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("為咗更好嘅為您服務，請輸入您嘅手機號碼"),
        "ethnicityInput": MessageLookupByLibrary.simpleMessage("民族"),
        "exit": MessageLookupByLibrary.simpleMessage("退出"),
        "expirationInput": MessageLookupByLibrary.simpleMessage("有效期限"),
        "externalCardRegistration":
            MessageLookupByLibrary.simpleMessage("外部刷卡（辦證）"),
        "faceMatchRegister": MessageLookupByLibrary.simpleMessage("人臉匹配註冊"),
        "face_recognition": MessageLookupByLibrary.simpleMessage("人臉識別"),
        "face_recognition_login":
            MessageLookupByLibrary.simpleMessage("人臉識別登錄"),
        "face_to_camera": MessageLookupByLibrary.simpleMessage("請您正視前方攝像頭"),
        "failure": MessageLookupByLibrary.simpleMessage("失敗"),
        "free_listening": MessageLookupByLibrary.simpleMessage("免費收聽啦"),
        "genderInput": MessageLookupByLibrary.simpleMessage("性別"),
        "homePage": MessageLookupByLibrary.simpleMessage("首頁"),
        "huiwen_qr_code": MessageLookupByLibrary.simpleMessage("匯文二維碼"),
        "idCardLogin": MessageLookupByLibrary.simpleMessage("身份證登錄"),
        "idNumberInput": MessageLookupByLibrary.simpleMessage("身份證號"),
        "identityCard": MessageLookupByLibrary.simpleMessage("身份證"),
        "ineligibleAge":
            MessageLookupByLibrary.simpleMessage("對唔住，您唔符合此讀者證辦理年齡條件"),
        "ineligibleArea":
            MessageLookupByLibrary.simpleMessage("對唔住，您唔符合此讀者證辦理區域條件"),
        "inputPhoneNumber": MessageLookupByLibrary.simpleMessage("請輸入手機號"),
        "insertMoney": MessageLookupByLibrary.simpleMessage("請您將紙幣放入收鈔口"),
        "invalidCredentials":
            MessageLookupByLibrary.simpleMessage("讀者證唔存在或密碼錯誤"),
        "issuingAuthInput": MessageLookupByLibrary.simpleMessage("簽發機關"),
        "loginButton": MessageLookupByLibrary.simpleMessage("登入"),
        "login_method": MessageLookupByLibrary.simpleMessage("登錄方式"),
        "manualInput": MessageLookupByLibrary.simpleMessage("手動輸入"),
        "manualInputLogin": MessageLookupByLibrary.simpleMessage("手動輸入登錄"),
        "manualInputRegistration":
            MessageLookupByLibrary.simpleMessage("手動輸入註冊"),
        "moneyAcceptanceInfo": MessageLookupByLibrary.simpleMessage(
            "本機僅接收10元、20元、50元、100元人民幣，不設找零，如有餘額將存入預付款賬戶"),
        "nameInput": MessageLookupByLibrary.simpleMessage("姓名"),
        "no": MessageLookupByLibrary.simpleMessage("否"),
        "noBooksAvailable": MessageLookupByLibrary.simpleMessage("無可借書籍"),
        "noCardTip":
            MessageLookupByLibrary.simpleMessage("卡已经用完，暂时唔可以办理读者证，请联络工作人员！"),
        "noDeposit": MessageLookupByLibrary.simpleMessage("免押金"),
        "no_books_to_return": MessageLookupByLibrary.simpleMessage("無待歸還書籍"),
        "note": MessageLookupByLibrary.simpleMessage("備註"),
        "overdue": MessageLookupByLibrary.simpleMessage("超期"),
        "overdue_count": MessageLookupByLibrary.simpleMessage("超期文獻數"),
        "passwordFormatError": MessageLookupByLibrary.simpleMessage("密碼格式唔正確"),
        "passwordInput": MessageLookupByLibrary.simpleMessage("請輸入密碼"),
        "passwordLengthError": MessageLookupByLibrary.simpleMessage("密碼長度唔正確"),
        "paymentCodeConfig": MessageLookupByLibrary.simpleMessage("付款碼支付配置"),
        "phoneError": MessageLookupByLibrary.simpleMessage("手機號碼錯誤"),
        "phoneForService":
            MessageLookupByLibrary.simpleMessage("為咗更好咁為您服務，請輸入您嘅電話號碼"),
        "phoneInput": MessageLookupByLibrary.simpleMessage("請輸入您嘅電話號碼"),
        "phoneOrCaptchaRequired":
            MessageLookupByLibrary.simpleMessage("手機號碼或者驗證碼唔可以為空"),
        "place": MessageLookupByLibrary.simpleMessage("請將"),
        "placeBookOnSensor":
            MessageLookupByLibrary.simpleMessage("請您將待還文獻放於桌面感應區"),
        "placeCardOnSensor":
            MessageLookupByLibrary.simpleMessage("請將讀者證放於桌面感應區"),
        "placeCardOnSensorForCard":
            MessageLookupByLibrary.simpleMessage("請將一卡通放於感應區"),
        "placeDocument": MessageLookupByLibrary.simpleMessage("請將文獻放喺桌面感應區"),
        "placeIdCardOnSensor":
            MessageLookupByLibrary.simpleMessage("請將身份證放於桌面感應區"),
        "prepaid_balance": MessageLookupByLibrary.simpleMessage("預存費餘額"),
        "printReceipt": MessageLookupByLibrary.simpleMessage("您是否打印憑條"),
        "processingCard":
            MessageLookupByLibrary.simpleMessage("正在為您辦理讀者證，\n請唔好離開..."),
        "qrCode": MessageLookupByLibrary.simpleMessage("二維碼"),
        "qrCodeLogin": MessageLookupByLibrary.simpleMessage("二維碼登錄"),
        "qrCodeRegistration": MessageLookupByLibrary.simpleMessage("展示二維碼註冊"),
        "query": MessageLookupByLibrary.simpleMessage("查詢"),
        "quit": MessageLookupByLibrary.simpleMessage("退出"),
        "readerCardAppliedSuccessfully":
            MessageLookupByLibrary.simpleMessage("您已經成功申請咗讀者證"),
        "readerCardID": MessageLookupByLibrary.simpleMessage("讀者證號"),
        "readerFailure": MessageLookupByLibrary.simpleMessage("打開閱讀器失敗"),
        "readerNotFound": MessageLookupByLibrary.simpleMessage("未配置閱讀器"),
        "readerTableFail": MessageLookupByLibrary.simpleMessage("處理讀者表失敗"),
        "reader_card": MessageLookupByLibrary.simpleMessage("讀者證"),
        "reader_closing": MessageLookupByLibrary.simpleMessage("正在關閉閱讀器，請稍後再試"),
        "regenerate": MessageLookupByLibrary.simpleMessage("重新生成"),
        "register": MessageLookupByLibrary.simpleMessage("登記"),
        "remainingTime": MessageLookupByLibrary.simpleMessage("剩餘\n時間"),
        "renew": MessageLookupByLibrary.simpleMessage("續借"),
        "renewBook": MessageLookupByLibrary.simpleMessage("續借"),
        "renewInProgress": MessageLookupByLibrary.simpleMessage("續借中..."),
        "renewOrQuery": MessageLookupByLibrary.simpleMessage("續借/查詢"),
        "renew_fail": MessageLookupByLibrary.simpleMessage("續借失敗"),
        "renew_success": MessageLookupByLibrary.simpleMessage("續借成功"),
        "requestingInfo": MessageLookupByLibrary.simpleMessage("正在請求讀者信息"),
        "resend": MessageLookupByLibrary.simpleMessage("秒之后再发一次"),
        "returnBook": MessageLookupByLibrary.simpleMessage("還書"),
        "returnDate": MessageLookupByLibrary.simpleMessage("歸還日期"),
        "returnFailure": MessageLookupByLibrary.simpleMessage("還書失敗，請求還書失敗"),
        "returnFailureRewrite":
            MessageLookupByLibrary.simpleMessage("還書失敗，改寫防盜位失敗"),
        "returnSuccess": MessageLookupByLibrary.simpleMessage("成功歸還文獻"),
        "return_date": MessageLookupByLibrary.simpleMessage("歸還日期"),
        "return_date_recorded": MessageLookupByLibrary.simpleMessage("還書日期"),
        "return_fail": MessageLookupByLibrary.simpleMessage("還書失敗"),
        "return_success": MessageLookupByLibrary.simpleMessage("還書成功"),
        "returningInProgress":
            MessageLookupByLibrary.simpleMessage("正在還書，請勿重複操作"),
        "returningStatus": MessageLookupByLibrary.simpleMessage("歸還中..."),
        "scanESCBarcode": MessageLookupByLibrary.simpleMessage("請掃描您嘅電子社保卡二維碼"),
        "scanPayRegister": MessageLookupByLibrary.simpleMessage("掃碼支付註冊"),
        "scanToListen": MessageLookupByLibrary.simpleMessage("掃碼收聽"),
        "scanYour": MessageLookupByLibrary.simpleMessage("請掃描您嘅"),
        "scan_e_social_card":
            MessageLookupByLibrary.simpleMessage("請掃描您嘅電子社保卡"),
        "scan_wechat_qr": MessageLookupByLibrary.simpleMessage("請掃描您嘅微信二維碼"),
        "secondGenIDCardRegistration":
            MessageLookupByLibrary.simpleMessage("二代身份證登記"),
        "selectCardType":
            MessageLookupByLibrary.simpleMessage("請選擇下面按鈕\n申辦對應類型嘅圖書證"),
        "selectOperationType": MessageLookupByLibrary.simpleMessage("請選擇操作類型"),
        "selectReaderCardType":
            MessageLookupByLibrary.simpleMessage("請選擇需要申辦嘅讀者證類型"),
        "selectRegistrationMethod":
            MessageLookupByLibrary.simpleMessage("請選擇註冊方式"),
        "select_all": MessageLookupByLibrary.simpleMessage("全選"),
        "selfServiceMachine": MessageLookupByLibrary.simpleMessage("自助借還書機"),
        "sendVerificationCode": MessageLookupByLibrary.simpleMessage("發送驗證碼"),
        "sensingArea": MessageLookupByLibrary.simpleMessage("感應區"),
        "sequenceNumber": MessageLookupByLibrary.simpleMessage("序號"),
        "serviceNotice": MessageLookupByLibrary.simpleMessage("自助辦證服務須知"),
        "shanghaiShenShenCodeRegister":
            MessageLookupByLibrary.simpleMessage("上海隨申碼註冊"),
        "socialSecurityCard": MessageLookupByLibrary.simpleMessage("社保卡"),
        "socialSecurityCardLogin":
            MessageLookupByLibrary.simpleMessage("社保卡登錄"),
        "spaceLogin": MessageLookupByLibrary.simpleMessage("登入"),
        "sscRegistration": MessageLookupByLibrary.simpleMessage("社保卡登記"),
        "status": MessageLookupByLibrary.simpleMessage("狀態"),
        "submittingData": MessageLookupByLibrary.simpleMessage("正在提交數據，請稍等…"),
        "success": MessageLookupByLibrary.simpleMessage("成功"),
        "takeCardAndBooks":
            MessageLookupByLibrary.simpleMessage("請取走您嘅讀者證同埋所借圖書,並請核對打印憑據！"),
        "take_away_card_books":
            MessageLookupByLibrary.simpleMessage("請取走您嘅讀者證同埋所借圖書"),
        "tencentEPassRegister": MessageLookupByLibrary.simpleMessage("騰訊E證通註冊"),
        "unsupportedCardType":
            MessageLookupByLibrary.simpleMessage("暫時唔支援此辦證類型"),
        "unsupportedCardTypeContact":
            MessageLookupByLibrary.simpleMessage("暫時唔支援此辦證類型，請聯繫圖書館工作人員"),
        "unsupportedType": MessageLookupByLibrary.simpleMessage("暫時唔支援此類型"),
        "validCaptcha": MessageLookupByLibrary.simpleMessage("請輸入正確嘅驗證碼"),
        "validPhoneInput": MessageLookupByLibrary.simpleMessage("請輸入正確嘅手機號碼"),
        "view_details": MessageLookupByLibrary.simpleMessage("點擊詳情"),
        "waitingResult": MessageLookupByLibrary.simpleMessage("等待結果"),
        "wechatLogin": MessageLookupByLibrary.simpleMessage("微信登陸"),
        "wechatScanAuth": MessageLookupByLibrary.simpleMessage("微信掃描QR碼認證"),
        "wechat_qr_code": MessageLookupByLibrary.simpleMessage("微信二維碼"),
        "wechat_scan": MessageLookupByLibrary.simpleMessage("微信掃一掃"),
        "yes": MessageLookupByLibrary.simpleMessage("是"),
        "yourPhoneNumber": MessageLookupByLibrary.simpleMessage("您嘅電話號碼")
      };
}
