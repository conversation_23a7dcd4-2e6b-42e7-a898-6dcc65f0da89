import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import '../models/auth_result.dart';

/// 认证方式优先级管理器
/// 负责根据配置动态管理认证方式的显示顺序和优先级
class AuthPriorityManager {
  // 单例实例
  static final AuthPriorityManager _instance = AuthPriorityManager._internal();
  static AuthPriorityManager get instance => _instance;
  AuthPriorityManager._internal();

  // 缓存的认证方式列表（按优先级排序）
  List<AuthMethod> _orderedAuthMethods = [];
  
  /// 获取按优先级排序的认证方式列表
  List<AuthMethod> get orderedAuthMethods => List.unmodifiable(_orderedAuthMethods);
  
  /// 获取最高优先级的认证方式（默认显示的认证方式）
  AuthMethod? get primaryAuthMethod => _orderedAuthMethods.isNotEmpty ? _orderedAuthMethods.first : null;

  /// 根据配置加载和排序认证方式
  /// [settingProvider] 设置提供器
  void loadAuthMethods(SettingProvider settingProvider) {
    _orderedAuthMethods.clear();
    
    final readerConfig = settingProvider.readerConfigData;
    if (readerConfig == null) {
      print('认证优先级管理器: 没有找到配置，使用默认顺序');
      _setDefaultOrder();
      return;
    }

    final authMap = readerConfig.authMap;
    final loginTypeOrders = readerConfig.loginTypeOrders ?? [];
    
    if (authMap == null || authMap.isEmpty) {
      print('认证优先级管理器: authMap为空，使用默认顺序');
      _setDefaultOrder();
      return;
    }

    print('认证优先级管理器: 开始加载认证方式');
    print('配置的排序: $loginTypeOrders');
    print('可用的认证方式: ${authMap.keys.toList()}');

    // 1. 先按照 loginTypeOrders 的顺序添加已配置的认证方式
    final addedMethods = <AuthMethod>{};
    
    for (String orderKey in loginTypeOrders) {
      if (authMap.containsKey(orderKey) && (authMap[orderKey]?.isNotEmpty ?? false)) {
        final authMethod = _mapConfigKeyToAuthMethod(orderKey);
        if (authMethod != null && !addedMethods.contains(authMethod)) {
          _orderedAuthMethods.add(authMethod);
          addedMethods.add(authMethod);
          print('认证优先级管理器: 按配置顺序添加 $orderKey -> ${_getAuthMethodDisplayName(authMethod)}');
        }
      }
    }

    // 2. 添加在 authMap 中配置但不在 loginTypeOrders 中的认证方式
    authMap.forEach((key, readers) {
      if (readers.isNotEmpty) {
        final authMethod = _mapConfigKeyToAuthMethod(key);
        if (authMethod != null && !addedMethods.contains(authMethod)) {
          _orderedAuthMethods.add(authMethod);
          addedMethods.add(authMethod);
          print('认证优先级管理器: 补充添加 $key -> ${_getAuthMethodDisplayName(authMethod)}');
        }
      }
    });

    if (_orderedAuthMethods.isEmpty) {
      print('认证优先级管理器: 没有配置任何有效的认证方式，使用默认顺序');
      _setDefaultOrder();
    } else {
      print('认证优先级管理器: 最终排序结果: ${_orderedAuthMethods.map((e) => _getAuthMethodDisplayName(e)).join(' -> ')}');
      print('认证优先级管理器: 主要认证方式: ${_getAuthMethodDisplayName(primaryAuthMethod!)}');
    }
  }

  /// 设置默认顺序（当没有配置时使用）
  void _setDefaultOrder() {
    _orderedAuthMethods = [
      AuthMethod.face,        // 人脸识别
      AuthMethod.readerCard,  // 读者证
      AuthMethod.idCard,      // 身份证
      AuthMethod.qrCode,      // 二维码
    ];
    print('认证优先级管理器: 使用默认顺序');
  }

  /// 将配置键映射到认证方式
  AuthMethod? _mapConfigKeyToAuthMethod(String configKey) {
    switch (configKey) {
      case '人脸识别认证':
        return AuthMethod.face;
      case '读者证认证':
        return AuthMethod.readerCard;
      case '身份证认证':
        return AuthMethod.idCard;
      case '社保卡认证':
        return AuthMethod.socialSecurityCard;
      case '市民卡认证':
        return AuthMethod.citizenCard;
      case '电子社保卡认证':
        return AuthMethod.eletricSocialSecurityCard;
      case '微信二维码认证':
        return AuthMethod.wechatQRCode;
      case '微信扫码认证':
        return AuthMethod.wechatScanQRCode;
      case '支付宝扫码认证':
        return AuthMethod.alipayQRCode;
      case '芝麻信用码认证':
        return AuthMethod.aliCreditQRCode;
      case '汇文二维码':
        return AuthMethod.huiwenQRCode;
      case '上海随申码认证':
        return AuthMethod.shangHaiQRCode;
      case '二维码读者认证':
        return AuthMethod.qrCode;
      case '手动输入认证':
        return AuthMethod.keyboardInput;
      case '腾讯E证通认证':
        return AuthMethod.tencentTCard;
      case 'IMI身份认证':
        return AuthMethod.imiAuth;
      case '拍照认证':
        return AuthMethod.takePhoto;
      case '微信/支付宝认证':
        return AuthMethod.wechatOrAlipay;
      case '支付宝信用认证':
      case '支付宝扫码认证（阿里信用）':
        return AuthMethod.alipayQRCodeCredit;
      case '借阅宝认证':
        return AuthMethod.jieYueBao;
      default:
        print('认证优先级管理器: 未知的配置键: $configKey');
        return null;
    }
  }

  /// 检查指定认证方式是否已启用
  bool isAuthMethodEnabled(AuthMethod method) {
    return _orderedAuthMethods.contains(method);
  }

  /// 获取指定认证方式的优先级（数字越小优先级越高）
  int getAuthMethodPriority(AuthMethod method) {
    final index = _orderedAuthMethods.indexOf(method);
    return index >= 0 ? index : 999; // 未配置的认证方式优先级最低
  }

  /// 根据当前使用的认证方式获取下一个认证方式（如果有）
  AuthMethod? getNextAuthMethod(AuthMethod currentMethod) {
    final currentIndex = _orderedAuthMethods.indexOf(currentMethod);
    if (currentIndex >= 0 && currentIndex < _orderedAuthMethods.length - 1) {
      return _orderedAuthMethods[currentIndex + 1];
    }
    return null;
  }

  /// 检查是否有多种认证方式可用
  bool get hasMultipleAuthMethods => _orderedAuthMethods.length > 1;

  /// 获取认证方式的显示名称
  String _getAuthMethodDisplayName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.idCard:
        return '身份证';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.socialSecurityCard:
        return '社保卡';
      case AuthMethod.citizenCard:
        return '市民卡';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡';
      case AuthMethod.wechatQRCode:
        return '微信二维码';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码';
      case AuthMethod.aliCreditQRCode:
        return '芝麻信用码';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码';
      case AuthMethod.shangHaiQRCode:
        return '上海随申码';
      case AuthMethod.keyboardInput:
        return '手动输入';
      case AuthMethod.tencentTCard:
        return '腾讯E证通';
      case AuthMethod.imiAuth:
        return 'IMI身份认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信/支付宝';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用认证';
      case AuthMethod.jieYueBao:
        return '借阅宝';
    }
  }

  /// 获取所有已启用认证方式的显示名称
  String getEnabledAuthMethodsDisplayText() {
    if (_orderedAuthMethods.isEmpty) {
      return '暂无可用认证方式';
    }
    
    return _orderedAuthMethods
        .map((method) => _getAuthMethodDisplayName(method))
        .join('/');
  }

  /// 清除缓存的认证方式列表
  void clear() {
    _orderedAuthMethods.clear();
  }
} 