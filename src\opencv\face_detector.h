#ifndef FACE_DETECTOR_H
#define FACE_DETECTOR_H

#include <stdint.h>

#if defined(_MSC_VER) || defined(WIN32) || defined(_WIN32) || defined(__WIN32__) || defined(__NT__)
#define DLL_EXPORT __declspec(dllexport)
#else
#define DLL_EXPORT __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 人脸矩形结构
typedef struct {
    int x;
    int y;
    int width;
    int height;
} FaceRect;

// 主要函数：检测人脸
DLL_EXPORT bool detect_faces(uint8_t* imageData, int width, int height, int channels, 
                        FaceRect* faces, int maxFaces, int* numFaces);

// 图像处理函数：在图像上标记出人脸
DLL_EXPORT uint8_t* process_image(uint8_t* imageData, int width, int height, int channels);

// 内存管理：释放图像内存
DLL_EXPORT void free_image(uint8_t* imageData);

// 摄像头控制函数
DLL_EXPORT bool start_camera();
DLL_EXPORT void stop_camera();

// 新增指定摄像头的API
DLL_EXPORT bool start_camera_with_id(int camera_id);

// 新增拍照功能
DLL_EXPORT bool take_photo(const char* save_path);

// 新增录像功能
DLL_EXPORT bool start_recording(const char* save_path);
DLL_EXPORT bool stop_recording();
DLL_EXPORT bool is_recording();

// 帧获取函数
DLL_EXPORT bool get_frame_info(int* width, int* height, int* channels);
DLL_EXPORT uint8_t* get_frame_data(int* width, int* height, int* channels);

// JPEG压缩函数
DLL_EXPORT bool get_jpeg_data(uint8_t** output, int* length);
DLL_EXPORT int get_jpeg_quality();
DLL_EXPORT void set_jpeg_quality(int quality);

// 获取原始帧尺寸
DLL_EXPORT void get_original_frame_size(int* width, int* height);

// Haar级联分类器控制
DLL_EXPORT void set_use_haar_detector(bool useHaar);
DLL_EXPORT bool get_use_haar_detector();

// 分离式人脸检测
DLL_EXPORT bool detect_faces_only(void* facesArray, int maxFaces, int* numFaces);

#ifdef __cplusplus
}
#endif

#endif // FACE_DETECTOR_H