<sip type="KeTu_ChanCheng" desc="KeTu_ChanCheng禅城科图SIP2协议">
	<message id="93" name="Login" type="Req" desc="读者登录">
		<field value = "UIDAlgorithm"	length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="读者ID加密算法"/>
		<field value = "PWDAlgorithm"	length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="读者密码加密算法"/>
		<field value = "UserId"			length = "-1"	must="Y"	flag="CN"	default=""		force = ""   brief ="用户名(操作员代码)"/>
		<field value = "Password"  		length = "-1" 	must="Y"  	flag="CO" 	default="" 		force = ""   brief ="密码"/>
		<field value = "LocationCode"  	length = "-1" 	must="Y"  	flag="CP" 	default=""		force = ""   brief ="馆藏地"/>
		<field value = "MsgSeqId"  		length = "-1" 	must="N"  	flag="AY" 	default=""		force = ""   brief ="消息序列标识"/>
	</message>
	<message id="94" name="Login" type="Ans" desc="读者登录">
		<field value = "OK"   			length = "1"  	must="Y"  	flag="" 	default="" 	force = ""   brief ="是否登录成功，1成功0失败"/>
		<field value = "MsgSeqId"  		length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="35" name="Logout" type="Req" desc="结束读者事务">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"   	length = "-1"	must="Y"	flag="AO" 	default="" 	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"   length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	default=""	force = ""   brief ="终端机密码"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"	default=""	force = ""   brief ="读者密码"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="36" name="Logout" type="Ans" desc="结束读者事务">
		<field value = "EndSession"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="结束标志，Y:成功结束 ; N:未成功结束"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"   	length = "-1"	must="Y"	flag="AO" 	default="" 	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"   length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "ScreenMessage"   	length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"   		length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义打印信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>		
	<message id="99" name="SCStatus" type="Req" desc="SC 状态">
		<field value = "StatusCode"			length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="状态码，0:SC正常，1:SC打印机缺纸,2:SC即将关机"/>
		<field value = "MaxprintWidth"		length = "3"	must="Y"	flag=""		default="052"	force = ""   brief ="最大打印宽度，默认052"/>
		<field value = "ProtocolVersion"	length = "4"	must="Y"	flag=""		default="2.00"	force = ""   brief ="协议版本，默认2.00"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 	default=""		force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="98" name="SCStatus" type="Ans" desc="SC 状态">
		<field value = "OnLineStatus"		length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="在线状态，Y:在线 ，N:即将下线"/>
		<field value = "CheckInOk"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC还书：Y:允许 or N:不允许"/>
		<field value = "CheckOutOk"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC借书：Y or N"/>
		<field value = "ACSRenewalPolicy"	length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC续借：Y or N"/>
		<field value = "StatusUpdateOk"		length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC修改读者状态：Y or N"/>
		<field value = "OfflineOk"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否支持离线业务：Y or N"/>
		<field value = "TimeoutPeriod"		length = "3"	must="Y"	flag=""		default=""	force = ""   brief ="应答超时时间"/>
		<field value = "RetriesAllowed"		length = "3"	must="Y"	flag=""		default=""	force = ""   brief ="应答重试次数"/>
		<field value = "DatetimeSync"		length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="同步时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>
		<field value = "LibraryName"		length = "-1"	must="N"	flag="AM"	default=""	force = ""   brief ="图书馆名"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义打印信息"/>
		<field value = "MsgSeqId"  	    	length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="63" name="PatronInformation" type="Req" desc="查询读者证信息">
		<field value = "Language"			length = "3"	must="Y"	flag=""		default="019"	force = ""   brief ="语言"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "Summary"			length = "10"	must="Y"	flag=""		default="  Y       "	force = ""   brief ="Summary"/>	
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "CardType"			length = "-1"	must="Y"	flag="TY"	default="0"	force = ""   brief ="卡类型0读者证,1身份证"/>
		<field value = "SubCard"			length = "-1"	must="N"	flag="PN"	default=""	force = ""   brief ="子卡号"/>
		<field value = "PatronLibcode"		length = "-1"	must="N"	flag="RC"	default=""	force = ""   brief ="如果是自助借还所在馆则为空，否则为实际值"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	default=""	force = ""   brief ="终端机密码"/>
		<field value = "PatronPassword"		length = "-1"	must="Y"	flag="AD"	default="123"	force = ""   brief ="读者证密码"/>
		<field value = "StartItem"			length = "-1"	must="N"	flag="BP"	default="1"	force = ""   brief ="返回序列起始值"/>
		<field value = "EndItem"			length = "-1"	must="N"	flag="BQ"	default="20"	force = ""   brief ="返回序列结束值"/>
		<field value = "MsgSeqId"           length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="64" name="PatronInformation" type="Ans" desc="查询读者证信息">
		<field value = "PatronStatus"			length = "14"	must="Y"	flag=""		default=""	force = ""   brief ="卡状态暂时为空，14位空格占位"/>
		<field value = "Language"				length = "3"	must="Y"	flag=""		default=""	force = ""   brief ="语言"/>
		<field value = "TransactionDate"		length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "HoldItemsCount"			length = "4"	must="Y"	flag=""		default=""	force = ""   brief ="当前借书数量"/>
		<field value = "OverdueItemCount"		length = "4"	must="Y"	flag=""		default=""	force = ""   brief ="超期书数量"/>
		<field value = "FineItemCount"			length = "4"	must="Y"	flag=""		default=""	force = ""   brief ="暂时无用"/>
		<field value = "RecallItemsCount"		length = "4"	must="Y"	flag=""		default=""	force = ""   brief ="暂时无用"/>
		<field value = "UnavailableItemsCount"	length = "4"	must="Y"	flag=""		default=""	force = ""   brief ="暂时无用"/>
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>	
		<field value = "PatronIdentifier"		length = "-1"	must="N"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"				length = "-1"	must="N"	flag="AE"	default=""	force = ""   brief ="读者姓名"/>
		<field value = "HoldItemsLimit"			length = "4"	must="N"	flag="BZ"	default=""	force = ""   brief ="可借图书数量"/>
		<field value = "OverdueItemsLimit"		length = "4"	must="N"	flag="CA"	default=""	force = ""   brief ="可超期图书数量"/>
		<field value = "ChargedItemsLimit"		length = "4"	must="N"	flag="CB"	default=""	force = ""   brief ="可预借图书数量"/>		
		<field value = "Fee"					length = "4"	must="N"	flag="BV"	default=""	force = ""   brief ="读者欠费金额"/>
		<field value = "FeeLimit"				length = "4"	must="N"	flag="CF"	default=""	force = ""   brief ="资金限额"/>
		<field value = "CurrencyType"			length = "4"	must="N"	flag="BH"	default=""	force = ""   brief ="流通货币类型"/>
		<field value = "ValidPatron"			length = "1"	must="N"	flag="BL"	default=""	force = ""   brief ="读者是否存在"/>
		<field value = "ValidPatronPassword"	length = "1"	must="N"	flag="CQ"	default=""	force = ""   brief ="读者密码是否正确"/>
		<field value = "FeeAmount"				length = "-1"	must="N"	flag="FE"	default=""	force = ""   brief ="读者押金"/>
		<field value = "ChargeAmount"			length = "-1"	must="N"	flag="JE"	default=""	force = ""   brief ="读者预付款"/>
		<field value = "PatronTotalFine"		length = "-1"	must="N"	flag="JF"	default=""	force = ""   brief ="读者欠款"/>	
		<field value = "Depositrate"			length = "-1"	must="N"	flag="XR"	default=""	force = ""   brief ="押金保证系数"/>
		<field value = "LoanedValue"			length = "-1"	must="N"	flag="XC"	default=""	force = ""   brief ="所借图书总额"/>
		<field value = "ReaderType"				length = "-1"	must="N"	flag="XT"	default=""	force = ""   brief ="读者类型"/>
		<field value = "EndDate"				length = "-1"	must="N"	flag="XD"	default=""	force = ""   brief ="证过期日期"/>
		<field value = "HoldItems"				length = "-1"	must="N"	flag="AU"	default=""	force = ""   brief ="已借出图书"/>		
		<field value = "OverdueItems"			length = "-1"	must="N"	flag="AT"	default=""	force = ""   brief ="超期图书"/>
		<field value = "FineItems"		    	length = "-1"	must="N"	flag="AV"	default=""	force = ""   brief ="欠款图书"/>
		<field value = "RecallItems"	    	length = "-1"	must="N"	flag="BU"	default=""	force = ""   brief ="撤销图书"/>
		<field value = "UnavailableItems"		length = "-1"	must="N"	flag="CD"	default=""	force = ""   brief ="不可用图书"/>
		<field value = "HomeAddress"	    	length = "-1"	must="N"	flag="BD"	default=""	force = ""   brief ="住址"/>
		<field value = "EmailAddress"	    	length = "-1"	must="N"	flag="BE"	default=""	force = ""   brief ="e-mail"/>
		<field value = "HomePhoneNumber"		length = "-1"	must="N"	flag="BF"	default=""	force = ""   brief ="联系电话"/>
		<field value = "Score"			    	length = "-1"	must="N"	flag="SR"	default=""	force = ""   brief ="积分"/>
		<field value = "ScreenMessage"			length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"				length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  				length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="23" name="PatronStatus" type="Req" desc="查询读者证状态">
		<field value = "Language"			length = "3"	must="Y"	flag=""		default="001"	force = ""   brief ="语言"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="N"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	default=""	force = ""   brief ="终端机密码"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"	default=""	force = ""   brief ="读者证密码"/>
		<field value = "MsgSeqId"           length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="24" name="PatronStatus" type="Ans" desc="查询读者证状态">
		<field value = "PatronStatus"			length = "14"	must="Y"	flag=""		default=""	force = ""   brief ="卡状态暂时为空，14位空格占位"/>
		<field value = "Language"				length = "3"	must="Y"	flag=""		default=""	force = ""   brief ="语言"/>
		<field value = "TransactionDate"		length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>	
		<field value = "PatronIdentifier"		length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"				length = "-1"	must="Y"	flag="AE"	default=""	force = ""   brief ="读者姓名"/>
		<field value = "ValidPatron"			length = "1"	must="N"	flag="BL"	default=""	force = ""   brief ="读者是否存在"/>
		<field value = "ValidPatronPassword"	length = "1"	must="N"	flag="CQ"	default=""	force = ""   brief ="读者密码是否正确"/>
        <field value = "CurrencyType"	    	length = "3"	must="N"	flag="BH"	default=""	force = ""   brief ="流通货币类型"/>
        <field value = "FeeAmount"	        	length = "-1"	must="N"	flag="BV"	default=""	force = ""   brief ="金额"/>
		<field value = "ScreenMessage"			length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"				length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  				length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="27" name="ModifyPasswd" type="Req" desc="读者密码修改">
		<field value = "InstitutionId"		length = "-1"	must="N"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"		length = "-1"	must="Y"	flag="AD"	default=""	force = ""   brief ="读者证密码"/>
		<field value = "NewPatronPassword"	length = "-1"	must="Y"	flag="ND"	default=""	force = ""   brief ="读者新密码"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="XK"	default=""	force = ""   brief ="操作验证方式:02"/>
		<field value = "MsgSeqId"           length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="28" name="ModifyPasswd" type="Ans" desc="读者密码修改">
		<field value = "OK"   					length = "1"  	must="Y"  	flag="" 	default="" 	force = ""   brief ="1成功0失败"/>
		<field value = "TransactionDate"		length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"			length = "-1"	must="N"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>	
		<field value = "PatronIdentifier"		length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "ScreenMessage"			length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"				length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  				length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="91" name="CheckRepeat" type="Req" desc="查重">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>	
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonalIdentifier"	length = "-1"	must="N"	flag="ID"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="XK"		default=""	force = ""   brief ="操作验证方式，0:图书证，1:读者证"/>
		<field value = "CardType"			length = "-1"	must="Y"	flag="TY"		default="1"	force = ""   brief ="卡类型0读者证,1身份证"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="92" name="CheckRepeat" type="Ans" desc="查重">
		<field value = "Result"				length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="验证结果：0:不存在记录，大于0,存在的读者证的数量"/>
	</message>
	<message id="81" name="CreateUser" type="Req" desc="创建读者">	
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"		length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"			length = "-1"	must="Y"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "PersonName"				length = "-1"	must="Y"	flag="AE"		default=""	force = ""   brief ="读者姓名"/>
		<field value = "LibraryName"			length = "-1"	must="Y"	flag="AM"		default=""	force = ""   brief ="图书馆名"/>
		<field value = "Telephone"				length = "-1"	must="N"	flag="BF"		default=""	force = ""   brief ="电话号码"/>
		<field value = "MobilePhone"			length = "-1"	must="N"	flag="MP"		default=""	force = ""   brief ="手机号码"/>
		<field value = "EmailAddress"			length = "-1"	must="N"	flag="BE"		default=""	force = ""   brief ="Email地址"/>
		<field value = "HomeAddress"			length = "-1"	must="N"	flag="BD"		default=""	force = ""   brief ="住址"/>
		<field value = "PersonalIdentifier"		length = "-1"	must="Y"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "PatronTypeCode"			length = "-1"	must="Y"	flag="XT"		default=""	force = ""   brief ="读者类型"/>
		<field value = "FeeAmount"		        length = "-1"	must="Y"	flag="BV"		default=""	force = ""   brief ="办证押金"/>
		<field value = "PatronRegisterDate"		length = "-1"	must="N"	flag="XB"		default=""	force = ""   brief ="读者证办证日期"/>
		<field value = "BirthDate"				length = "-1"	must="N"	flag="XH"		default=""	force = ""   brief ="出生日期"/>
		<field value = "Nation"					length = "-1"	must="N"	flag="XN"		default=""	force = ""   brief ="民族"/>
		<field value = "NativePlace"			length = "-1"	must="N"	flag="XP"		default=""	force = ""   brief ="籍贯"/>
		<field value = "Remark"					length = "-1"	must="N"	flag="XF"		default=""	force = ""   brief ="备注"/>
		<field value = "PatronValidDate"		length = "-1"	must="N"	flag="XD"		default=""	force = ""   brief ="读者证有效日期"/>
		<field value = "PatronStartDate"		length = "-1"	must="N"	flag="XE"		default=""	force = ""   brief ="读者证启用日期"/>		
		<field value = "PatronSex"				length = "-1"	must="N"	flag="XM"		default=""	force = ""   brief ="读者性别"/>		
		<field value = "NewPatronIdentifier"	length = "-1"	must=""		flag="XA"		default=""	force = ""   brief ="新读者证号"/>
		<field value = "CardType"				length = "-1"	must="Y"	flag="TY"		default="1"	force = ""   brief ="卡类型0读者证,1身份证"/>
		<field value = "CerType"				length = "-1"	must="Y"	flag="RT"		default="SHARERT020"	force = ""   brief ="卡类型0读者证,1身份证"/>
		<field value = "OperationType"			length = "-1"	must="Y"	flag="XK"		default=""	force = ""   brief ="操作方式"/>
		<field value = "MsgSeqId"  				length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="82" name="CreateUser" type="Ans" desc="创建读者">
		<field value = "OK"					length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "InstitutionId"		length = "-1"	must="N"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "OperationType"		length = "-1"	must="N"	flag="XK"		default=""	force = ""   brief ="操作方式"/>
		<field value = "FiscalIdentifier"	length = "-1"	must="N"	flag="JK"		default=""	force = ""   brief ="财经记录流水号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
	</message>
	<message id="77" name="PhotoData" type="" desc="上传头像">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPhotoLength"	length = "-1"	must="Y"	flag="LE"		default=""	force = ""   brief ="头像数据长度"/>
		<field value = "PhtronPhotoData"	length = "-1"	must="Y"	flag="DA"		default=""	force = ""   brief ="头像数据"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="78" name="PhotoData" type="" desc="上传头像">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "OK"					length = "1"	must="Y"	flag="OK"		default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>	
	</message>
	<message id="85" name="QueryUser" type="Req" desc="查询读者信息">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonalIdentifier"	length = "-1"	must="N"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "OperationType"		length = "-1"	must=""		flag="XK"		default=""	force = ""   brief ="操作方式"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="86" name="QueryUser" type="Ans" desc="查询读者信息">
		<field value = "TransactionDate"		length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>		
		<field value = "PatronIdentifier"		length = "-1"	must="N"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronIdentifierInfo"	length = "-1"	must="N"	flag="OX"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"			length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "LibraryName"			length = "-1"	must="N"	flag="AM"		default=""	force = ""   brief ="图书馆名(读者开户馆)"/>
		<field value = "Telephone"				length = "-1"	must="N"	flag="BP"		default=""	force = ""   brief ="电话号码"/>
		<field value = "MobilePhone"			length = "-1"	must="N"	flag="MP"		default=""	force = ""   brief ="手机号码"/>
		<field value = "EmailAddress"			length = "-1"	must="N"	flag="BE"		default=""	force = ""   brief ="Email地址"/>
		<field value = "HomeAddress"			length = "-1"	must="N"	flag="BD"		default=""	force = ""   brief ="住址"/>
		<field value = "FeeAmount"		        length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="办证押金"/>
		<field value = "ChargeAmount"			length = "-1"	must="N"	flag="JE"		default=""	force = ""   brief ="读者预付款"/>
		<field value = "PersonalIdentifier"		length = "-1"	must="N"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "PatronTypeCode"			length = "-1"	must="N"	flag="XT"		default=""	force = ""   brief ="读者类型"/>
		<field value = "PatronRegisterDate"		length = "-1"	must="N"	flag="XB"		default=""	force = ""   brief ="读者证办证日期"/>
		<field value = "BirthDate"				length = "-1"	must="N"	flag="XH"		default=""	force = ""   brief ="出生日期"/>
		<field value = "Nation"					length = "-1"	must="N"	flag="XN"		default=""	force = ""   brief ="民族"/>
		<field value = "NativePlace"			length = "-1"	must="N"	flag="XP"		default=""	force = ""   brief ="籍贯"/>
		<field value = "Remark"					length = "-1"	must="N"	flag="XF"		default=""	force = ""   brief ="备注"/>
		<field value = "PatronValidDate"		length = "-1"	must="N"	flag="XD"		default=""	force = ""   brief ="读者证有效日期"/>
		<field value = "PatronStartDate"		length = "-1"	must="N"	flag="XE"		default=""	force = ""   brief ="读者证启用日期"/>		
		<field value = "PatronSex"				length = "-1"	must="N"	flag="XM"		default=""	force = ""   brief ="读者性别"/>	
		<field value = "OK"						length = "1"	must="Y"	flag="OK"		default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "ScreenMessage"			length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>	
	</message>
	<message id="53" name="QueryImprestFine" type="Req" desc="查询预付款金额">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "OperationType"		length = "-1"	must=""		flag="XK"		default=""	force = ""   brief ="操作方式"/>	
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="54" name="QueryImprestFine" type="Ans" desc="查询预付款金额">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"			length = "-1"	must="Y"	flag="AE"		default=""	force = ""   brief ="读者姓名"/>
		<field value = "ChargeAmount"		length = "-1"	must="N"	flag="JE"		default=""	force = ""   brief ="读者预付款"/>
		<field value = "PatronTotalFine"	length = "-1"	must="N"	flag="JF"		default=""	force = ""   brief ="读者欠费总金额"/>
		<field value = "PatronFineRecord"	length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="读者欠费记录"/>
		<field value = "ValidActionFee"		length = "-1"	must="N"	flag="JA"		default=""	force = ""   brief ="读者证验证费用"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="51" name="ChargeImprest" type="Req" desc="充预付款">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ChargeAmount"		length = "-1"	must="Y"	flag="JE"		default=""	force = ""   brief ="读者预付款"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>	
	</message>
	<message id="52" name="ChargeImprest" type="Ans" desc="充预付款">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ChargeAmount"		length = "-1"	must="N"	flag="JE"		default=""	force = ""   brief ="读者预付款"/>
		<field value = "FiscalIdentifier"	length = "-1"	must="N"	flag="JK"		default=""	force = ""   brief ="财经记录流水号"/>		
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="19" name="ItemStatusUpdate" type="Req" desc="更新图书状态">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "OrgLocation"		length = "-1"	must="Y"	flag="AQ"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "CurrentLocation"	length = "-1"	must="N"	flag="AP"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "HoldingState"		length = "-1"	must="N"	flag="HS"		default=""	force = ""   brief ="馆藏状态(1.编目 2.在馆 3.借出 4.丢失)"/>
		<field value = "ShelfNo"			length = "-1"	must="N"	flag="KP"		default=""	force = ""   brief ="架位号"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="20" name="ItemStatusUpdate" type="Ans" desc="更新图书状态">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="11" name="CheckOut" type="Req" desc="借书">
		<field value = "SCRenewalPolicy"	length = "1"	must="Y"	flag=""		    default="Y"	force = ""   brief ="终端机是否可以处理续借操作"/>
		<field value = "NoBlock"			length = "1"	must="Y"	flag=""		    default="N"	force = ""   brief =""/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "NbDueDate"	        length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TerminalPwd"		length = "-1"	must="Y"	flag="AC"	    default=""	force = ""   brief ="终端机密码"/>
        <field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="图书附加信息"/>
        <field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"	    default=""	force = ""   brief ="读者证密码"/>
        <field value = "FeeAcknowledged"	length = "1"	must="N"	flag="BO"		default="Y"	force = ""   brief ="额外费用信息告知"/>
        <field value = "Cancel"				length = "-1"	must="N"	flag="BI"	    default="N"	force = ""   brief ="是否是取消命令"/>
		<field value = "JYBTransactionID"	length = "-1"	must="N"	flag="TD"	    default=""	force = ""   brief ="JYB交易ID"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="12" name="CheckOut" type="Ans" desc="借书">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="借书成功标志"/>
        <field value = "RenewalOK"	        length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否可进行续借"/>
        <field value = "MagneticMedia"		length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否有附件 Y：有附件；N：无附件；U：未知"/> 
        <field value = "Desensitize"	    length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否取消敏感处理"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="N"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "DueDate"			length = "-1"	must="N"	flag="AH"		default=""	force = ""   brief ="图书应还日期"/>
        <field value = "FeeType"	        length = "2"	must="N"	flag="BT"		default=""	force = ""   brief ="费用类型"/>
        <field value = "SecurityInhibit"	length = "1"	must="N"	flag="CI"		default=""	force = ""   brief ="图书安全状态"/>
        <field value = "CurrencyType"	    length = "3"	must="N"	flag="BH"		default=""	force = ""   brief ="流通货币类型"/>
        <field value = "FeeAmount"	        length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="金额"/>
        <field value = "MediaType"	        length = "3"	must="N"	flag="CK"		default=""	force = ""   brief ="媒体类型"/>
        <field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="图书的简单信息：AT（是否有附件：Y表示有，N表示无）PR（图书价格）"/>
        <field value = "TransactionId"	    length = "-1"	must="N"	flag="BK"		default=""	force = ""   brief ="事务ID"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="09" name="CheckIn" type="Req" desc="还书">
		<field value = "NoBlock"			length = "1"	must="Y"	flag=""		    default="Y"	force = ""   brief ="离线事务是否已处理"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "ReturnDate"     	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="图书实际还回时间"/>
		<field value = "CurrentLocation"	length = "-1"	must="Y"	flag="AP"		default=""	force = ""   brief ="当前图书所在位置,流通馆藏（理解为临时馆藏）"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TerminalPwd"		length = "-1"	must="Y"	flag="AC"	    default=""	force = ""   brief ="终端机密码"/>
		<field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="图书附加信息"/>
        <field value = "Cancel"				length = "-1"	must="N"	flag="BI"	    default="N"	force = ""   brief ="是否是取消命令"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="10" name="CheckIn" type="Ans" desc="还书">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否重做敏感处理"/> 
        <field value = "Resensitize"		length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="还书成功标志"/> 
        <field value = "MagneticMedia"		length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否有附件 Y：有附件；N：无附件；U：未知"/> 
        <field value = "Alert"		        length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否提示告警"/> 
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="N"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "PermanentLocation"	length = "-1"	must="N"	flag="AQ"		default=""	force = ""   brief ="所属馆的馆藏地代码,固定馆藏"/>   
		<field value = "MediaType"			length = "-1"	must="N"	flag="CK"		default=""	force = ""   brief ="图书类型（普通图书001或期刊）"/>
		<field value = "ItemCirtype"		length = "-1"	must="N"	flag="CT"		default=""	force = ""   brief ="图书流通类型"/>
		<field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="图书的简单信息：AT（是否有附件：Y表示有，N表示无）PR（图书价格）"/>
		<field value = "Fee"				length = "4"	must="N"	flag="CF"	    default=""	force = ""   brief ="读者欠费金额"/>
		<field value = "SortBin"			length = "-1"	must="N"	flag="CL"	    default=""	force = ""   brief ="排序规则号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="17" name="ItemInformation" type="Req" desc="图书查询">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"		default=""	force = ""   brief ="终端机密码"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="18" name="ItemInformation" type="Ans" desc="图书查询">
		<field value = "CirculationStatus"	length = "2"	must="Y"	flag=""			default=""	force = ""   brief ="图书流通状态"/>
		<field value = "SecurityMaker"		length = "2"	must="Y"	flag=""			default=""	force = ""   brief ="安全标记"/>
		<field value = "FeeType"			length = "2"	must="Y"	flag=""			default=""	force = ""   brief ="费用类型"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="N"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="N"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="N"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="N"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "ItemAuthor"			length = "-1"	must="N"	flag="AW"		default=""	force = ""   brief ="著者"/>
		<field value = "ISBN"				length = "-1"	must="N"	flag="AK"		default=""	force = ""   brief ="ISBN号"/>
		<field value = "Reservation"		length = "-1"	must="N"	flag="RE"		default=""	force = ""   brief ="是否有预约"/>
		<field value = "MediaType"			length = "-1"	must="N"	flag="CK"		default=""	force = ""   brief ="图书类型（普通图书001或期刊）"/>
		<field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="图书的简单信息：AT（是否有附件：Y表示有，N表示无）PR（图书价格）"/>
		<field value = "CallNo"				length = "-1"	must="N"	flag="KC"	    default=""	force = ""   brief ="索书号"/>
        <field value = "DueDate"			length = "-1"	must="N"	flag="AH"	    default=""	force = ""   brief ="截止日期"/>
		<field value = "ItemCirtype"		length = "-1"	must="N"	flag="CT"		default=""	force = ""   brief ="图书流通类型"/>
		<field value = "OrgLocation"		length = "-1"	must="N"	flag="AQ"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "CurrentLocation"	length = "-1"	must="N"	flag="AP"		default=""	force = ""   brief ="所属馆的馆藏地代码"/>
		<field value = "ReservationRfid"	length = "-1"	must="N"	flag="BG"		default=""	force = ""   brief ="预约分配了该条码图书的读者证号"/>
		<field value = "Publisher"		    length = "-1"	must="N"	flag="PB"		default=""	force = ""   brief ="出版社"/>
		<field value = "Subject"			length = "-1"	must="N"	flag="SJ"		default=""	force = ""   brief ="主题词"/>
		<field value = "PageNum"			length = "-1"	must="N"	flag="PG"		default=""	force = ""   brief ="总页数"/>
		<field value = "ShelfNo"			length = "-1"	must="N"	flag="KP"		default=""	force = ""   brief ="架位号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
    <message id="29" name="Renew" type="Req" desc="图书续借">
		<field value = "ThirdPartyAllowed"	length = "1"	must="Y"	flag=""			default="N"	force = ""   brief ="是否允许第三方续借"/>
		<field value = "NoBlock"		    length = " 1"	must="Y"	flag=""		    default="N"	force = ""   brief ="离线事务是否已处理"/>
        <field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>    
		<field value = "NbDueDate"		    length = "18"	must="Y"	flag=""	    	default=""	force = ""   brief ="离线事务发生时间"/>
        <field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
        <field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证代码"/>
        <field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者密码"/>
		<field value = "OperationCode"		length = "-1"	must="Y"	flag="CN"		default=""	force = ""   brief ="操作员代码/本设备流通地点代码"/>				
        <field value = "ItemIdentifier"		length = "-1"	must="N"	flag="AB"		default=""	force = ""   brief ="图书代码"/>
        <field value = "TitleIdentifier"	length = "-1"	must="N"	flag="AJ"		default=""	force = ""   brief ="主题标识"/>
        <field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"		default=""	force = ""   brief ="终端机密码"/>
        <field value = "ItemProperties"		length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="图书附加信息"/>
        <field value = "FeeAcknowledged"	length = "1"	must="N"	flag="BO"		default="N"	force = ""   brief ="额外费用信息告知"/>
		<field value = "JYBTransactionID"	length = "-1"	must="N"	flag="TD"	    default=""	force = ""   brief ="JYB交易ID"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="30" name="Renew" type="Ans" desc="图书续借">
		<field value = "OK"	                length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="操作是否成功"/>
		<field value = "RenewalOK"	        length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否可进行续借"/>
        <field value = "MagneticMedia"	    length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否有附件"/>
        <field value = "Desensitize"	    length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否取消敏感处理"/>
        <field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
        <field value = "InstitutionId"	    length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
        <field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证代码"/>
        <field value = "ItemIdentifier"	    length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书代码"/>
        <field value = "TitleIdentifier"	length = "-1"	must="N"	flag="AJ"		default=""	force = ""   brief ="主题标识"/>
        <field value = "DueDate"	        length = "-1"	must="N"	flag="AH"		default=""	force = ""   brief ="应还日期"/>
        <field value = "FeeType"	        length = "2"	must="N"	flag="BT"		default=""	force = ""   brief ="费用类型"/>
        <field value = "SecurityInhibit"	length = "1"	must="N"	flag="CI"		default=""	force = ""   brief ="图书安全状态"/>
        <field value = "CurrencyType"	    length = "3"	must="N"	flag="BH"		default=""	force = ""   brief ="流通货币类型"/>
        <field value = "FeeAmount"	        length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="金额"/>
        <field value = "MediaType"	        length = "3"	must="N"	flag="CK"		default=""	force = ""   brief ="媒体类型"/>
        <field value = "ItemProperties"	    length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="图书附加信息"/>
        <field value = "TransactionId"	    length = "-1"	must="N"	flag="BK"		default=""	force = ""   brief ="事务ID"/>
        <field value = "ScreenMessage"	    length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
        <field value = "PrintLine"	        length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义打印信息"/>
        <field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
</sip>