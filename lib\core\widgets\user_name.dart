import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/user_provider.dart';
import '../utils/window_util.dart';

class UserName extends StatelessWidget {
  const UserName({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:  EdgeInsets.symmetric(horizontal: 148.p),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
        Row(
          children: [
            Text(
              '姓名:',
              style: TextStyle(fontSize: 34.p,color: const Color(0xFF222222),fontWeight: FontWeight.bold,height: 1),
            ),
            const SizedBox(width: 10),
            Text(
              context.read<UserProvider>().currentUser?.PersonName??'',
              style: TextStyle(fontSize: 34.p,color: const Color(0xFF222222),fontWeight: FontWeight.bold,height: 1),
            ),
          ],
        ),
        Row(
          children: [
            Text(
              '读者证号:',
              style: TextStyle(fontSize: 34.p,color: const Color(0xFF222222),fontWeight: FontWeight.bold,height: 1),
            ),
            const SizedBox(width: 10),
            Text(
              context.read<UserProvider>().currentUser?.PatronIdentifier??'',
              style: TextStyle(fontSize: 34.p,color: const Color(0xFF222222),fontWeight: FontWeight.bold,height: 1),
            ),
          ],
        )
      ]),
    );
  }
}
