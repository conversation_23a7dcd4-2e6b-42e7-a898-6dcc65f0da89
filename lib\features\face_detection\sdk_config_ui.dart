import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'sdk_config_manager.dart';
import 'baidu_face_recognition.dart';

/// SDK配置UI界面
class SdkConfigScreen extends StatefulWidget {
  const SdkConfigScreen({Key? key}) : super(key: key);

  @override
  State<SdkConfigScreen> createState() => _SdkConfigScreenState();
}

class _SdkConfigScreenState extends State<SdkConfigScreen> {
  final SdkConfigManager _configManager = SdkConfigManager.instance;
  final TextEditingController _pathController = TextEditingController();
  
  bool _isLoading = false;
  bool _isValidating = false;
  SdkValidationResult? _validationResult;
  List<String> _recentPaths = [];
  Map<String, dynamic>? _sdkStatus;
  
  @override
  void initState() {
    super.initState();
    _loadCurrentConfig();
  }

  @override
  void dispose() {
    _pathController.dispose();
    super.dispose();
  }

  /// 加载当前配置
  Future<void> _loadCurrentConfig() async {
    setState(() => _isLoading = true);
    
    try {
      final currentPath = await _configManager.getSdkPath();
      _pathController.text = currentPath;
      
      final recentPaths = await _configManager.getRecentSdkPaths();
      setState(() {
        _recentPaths = recentPaths;
      });
      
      // 验证当前路径
      await _validateCurrentPath();
      
      // 获取SDK状态
      await _updateSdkStatus();
      
    } catch (e) {
      _showSnackBar('加载配置失败: $e', isError: true);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 验证当前路径
  Future<void> _validateCurrentPath() async {
    if (_pathController.text.isNotEmpty) {
      setState(() => _isValidating = true);
      
      try {
        final result = await _configManager.validateSdkPath(_pathController.text);
        setState(() {
          _validationResult = result;
        });
      } catch (e) {
        print('验证路径失败: $e');
      } finally {
        setState(() => _isValidating = false);
      }
    }
  }

  /// 更新SDK状态
  Future<void> _updateSdkStatus() async {
    try {
      if (BaiduFaceRecognition.isInitialized()) {
        final status = BaiduFaceRecognition.getDetailedSdkStatus();
        setState(() {
          _sdkStatus = status;
        });
      }
    } catch (e) {
      print('获取SDK状态失败: $e');
    }
  }

  /// 选择文件夹
  Future<void> _selectFolder() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      
      if (selectedDirectory != null) {
        setState(() {
          _pathController.text = selectedDirectory;
        });
        await _validateCurrentPath();
      }
    } catch (e) {
      _showSnackBar('选择文件夹失败: $e', isError: true);
    }
  }

  /// 应用SDK路径
  Future<void> _applySdkPath() async {
    final path = _pathController.text.trim();
    if (path.isEmpty) {
      _showSnackBar('请输入SDK路径', isError: true);
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      // 验证路径
      final validation = await _configManager.validateSdkPath(path);
      if (!validation.isValid) {
        _showSnackBar('路径验证失败: ${validation.error}', isError: true);
        return;
      }

      // 保存配置
      final saveSuccess = await _configManager.setSdkPath(path);
      if (!saveSuccess) {
        _showSnackBar('保存配置失败', isError: true);
        return;
      }

      // 添加到最近使用
      await _configManager.addToRecentPaths(path);

      // 如果SDK已初始化，尝试设置路径
      if (BaiduFaceRecognition.isInitialized()) {
        final result = BaiduFaceRecognition.setSdkPath(path);
        if (result == 0) {
          _showSnackBar('SDK路径应用成功！');
        } else {
          _showSnackBar('SDK路径设置失败，错误码: $result', isError: true);
        }
      } else {
        _showSnackBar('配置保存成功，重启应用后生效');
      }

      // 刷新数据
      await _loadCurrentConfig();
      
    } catch (e) {
      _showSnackBar('应用配置失败: $e', isError: true);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 选择最近路径
  void _selectRecentPath(String path) {
    setState(() {
      _pathController.text = path;
    });
    _validateCurrentPath();
  }

  /// 重置为默认路径
  Future<void> _resetToDefault() async {
    setState(() => _isLoading = true);
    
    try {
      final success = await _configManager.resetToDefault();
      if (success) {
        final defaultPath = await _configManager.getSdkPath();
        _pathController.text = defaultPath;
        await _validateCurrentPath();
        _showSnackBar('已重置为默认路径');
      } else {
        _showSnackBar('重置失败', isError: true);
      }
    } catch (e) {
      _showSnackBar('重置失败: $e', isError: true);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 显示提示信息
  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red[600] : Colors.green[600],
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }

  /// 复制路径到剪贴板
  Future<void> _copyPath() async {
    if (_pathController.text.isNotEmpty) {
      await Clipboard.setData(ClipboardData(text: _pathController.text));
      _showSnackBar('路径已复制到剪贴板');
    }
  }

  /// 打开文件夹
  Future<void> _openFolder() async {
    final path = _pathController.text.trim();
    if (path.isNotEmpty && Directory(path).existsSync()) {
      try {
        await Process.run('explorer', [path], runInShell: true);
      } catch (e) {
        _showSnackBar('打开文件夹失败: $e', isError: true);
      }
    } else {
      _showSnackBar('文件夹不存在', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SDK配置'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadCurrentConfig,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPathInputSection(),
                  const SizedBox(height: 24),
                  _buildValidationSection(),
                  const SizedBox(height: 24),
                  _buildSdkStatusSection(),
                  const SizedBox(height: 24),
                  _buildRecentPathsSection(),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  /// 路径输入部分
  Widget _buildPathInputSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.folder, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'SDK路径配置',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _pathController,
                    decoration: const InputDecoration(
                      labelText: 'SDK根目录路径',
                      hintText: 'D:\\gdwork\\文档\\FaceOfflineSdk',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.folder_open),
                    ),
                    onChanged: (_) => _validateCurrentPath(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.folder_open),
                  onPressed: _selectFolder,
                  tooltip: '选择文件夹',
                ),
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: _copyPath,
                  tooltip: '复制路径',
                ),
                IconButton(
                  icon: const Icon(Icons.open_in_new),
                  onPressed: _openFolder,
                  tooltip: '打开文件夹',
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              '请选择百度人脸识别SDK的根目录，目录下应包含x64、models等文件夹',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// 验证结果部分
  Widget _buildValidationSection() {
    if (_isValidating) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text('正在验证路径...'),
            ],
          ),
        ),
      );
    }

    if (_validationResult == null) {
      return const SizedBox.shrink();
    }

    final result = _validationResult!;
    final isValid = result.isValid;

    return Card(
      color: isValid ? Colors.green[50] : Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isValid ? Icons.check_circle : Icons.error,
                  color: isValid ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  isValid ? '路径验证成功' : '路径验证失败',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isValid ? Colors.green[800] : Colors.red[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              isValid ? (result.message ?? '路径有效') : (result.error ?? '未知错误'),
              style: TextStyle(
                color: isValid ? Colors.green[700] : Colors.red[700],
              ),
            ),
            if (result.detectedVersion != null) ...[
              const SizedBox(height: 4),
              Text(
                '检测到SDK版本: ${result.detectedVersion}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// SDK状态部分
  Widget _buildSdkStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'SDK运行状态',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_sdkStatus != null) ...[
              _buildStatusItem('初始化状态', _sdkStatus!['initialized'] == true ? '已初始化' : '未初始化'),
              _buildStatusItem('DLL路径设置', _sdkStatus!['dll_path_set'] == true ? '已设置' : '未设置'),
              _buildStatusItem('当前SDK路径', _sdkStatus!['current_sdk_path'] ?? '未设置'),
              _buildStatusItem('API实例', _sdkStatus!['api_instance'] == true ? '已创建' : '未创建'),
              if (_sdkStatus!['authorized'] != null)
                _buildStatusItem('授权状态', _sdkStatus!['authorized'] == true ? '已授权' : '未授权'),
              if (_sdkStatus!['face_count'] != null)
                _buildStatusItem('人脸数量', '${_sdkStatus!['face_count']} 个'),
            ] else ...[
              const Text('无法获取SDK状态信息'),
            ],
          ],
        ),
      ),
    );
  }

  /// 状态项
  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  /// 最近路径部分
  Widget _buildRecentPathsSection() {
    if (_recentPaths.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.history, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  '最近使用的路径',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ..._recentPaths.map((path) => ListTile(
              dense: true,
              leading: const Icon(Icons.folder_outlined, size: 20),
              title: Text(
                path,
                style: const TextStyle(fontSize: 14),
                overflow: TextOverflow.ellipsis,
              ),
              trailing: IconButton(
                icon: const Icon(Icons.arrow_forward, size: 16),
                onPressed: () => _selectRecentPath(path),
                tooltip: '使用此路径',
              ),
              onTap: () => _selectRecentPath(path),
            )),
          ],
        ),
      ),
    );
  }

  /// 操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoading || (_validationResult?.isValid != true) 
                    ? null 
                    : _applySdkPath,
                icon: const Icon(Icons.save),
                label: const Text('应用配置'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : _resetToDefault,
                icon: const Icon(Icons.restore),
                label: const Text('重置默认'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '使用说明',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                '1. 选择百度人脸识别SDK的根目录\n'
                '2. 系统会自动验证目录结构和必要文件\n'
                '3. 应用配置后，SDK将从指定路径加载\n'
                '4. 这样可以大幅减少应用安装包体积',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }
} 