// ignore_for_file: must_be_immutable

import 'dart:convert';
import 'package:base_package/base_package.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:seaface/seaface.dart';
import 'package:seasetting/seasetting.dart';
import '../../../generated/l10n.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';
import 'gradientView.dart';

class FaceLoginView extends StatelessWidget {
  FaceLoginView(this.loginType, {Key? key}) : super(key: key);
  AuthLoginType loginType;

  @override
  Widget build(BuildContext context) {
    return Consumer2<CurrentLayoutProvider, SettingProvider>(
        builder: (context, layoutProvider, settingProvider, child) {
      if (settingProvider.getTheme() == ThemeType.ximalaya) {
        return FaceLoginXmlyView(loginType);
      } else if (settingProvider.getTheme() == ThemeType.child) {
        return FaceLoginChildView(loginType);
      } else {
        return FaceLoginBlueView(loginType);
      }
    });
  }
}

class FaceLoginChildView extends StatelessWidget {
  FaceLoginChildView(this.loginType, {Key? key}) : super(key: key);
  AuthLoginType loginType;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.faceAuth == AuthLoginTypeMap[element.type]).firstOrNull;

    return ListView(
      shrinkWrap: true,
      padding: const EdgeInsets.only(),
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title: S.current.face_recognition_login,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
                child: Container(
              margin: EdgeInsets.only(
                top: 85.p,
                bottom: 85.p,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 40.p,
                vertical: 7.p,
              ),
              decoration: BoxDecoration(
                color: BPUtils.c_FFCCEFFF,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.p),
                  topRight: Radius.circular(16.p),
                  bottomRight: Radius.circular(16.p),
                  bottomLeft: Radius.circular(4.p),
                ),
              ),
              child: Text(
                (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:S.current.face_to_camera,
                style: TextStyle(
                  color: BPUtils.c_FF1481D7,
                  fontWeight: FontWeight.normal,
                  fontSize: 28.p,
                ),
              ),
            ))
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                  color: BPUtils.c_FFFEC72D,
                  borderRadius:
                      BorderRadius.circular(283.p)),
              padding: EdgeInsets.all(10.p),
              child:
                  Consumer<SFCameraProvider>(builder: (ctx, provider, child) {
                String? imageData =
                    provider.imageData?.devices?.firstOrNull?.recv_data?.video;
                if (imageData != null && imageData.isNotEmpty) {
                  return Container(
                    width: 546.p,
                    height: 546.p,
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                        273.p,
                      ),
                    ),
                    child: Image.memory(
                      base64Decode(imageData),
                      gaplessPlayback: true,
                      fit: BoxFit.cover,
                    ),
                  );
                } else {
                  return Container(
                    width: 546.p,
                    height: 546.p,
                    decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(273.p),
                        color: Colors.white),
                    child: CupertinoActivityIndicator(
                      radius: 50.p,
                    ),
                  );
                }
              }),
            ),
          ],
        ),
      ],
    );
  }
}

class FaceLoginBlueView extends StatelessWidget {
  FaceLoginBlueView(this.loginType, {Key? key}) : super(key: key);
  AuthLoginType loginType;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.faceAuth == AuthLoginTypeMap[element.type]).firstOrNull;

    return ListView(
      shrinkWrap: true,
      padding: const EdgeInsets.only(),
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title:S.current.face_recognition_login,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: BPUtils.c_ff12215F,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
                child: Container(
              margin: EdgeInsets.only(
                top: 134.p,
                bottom: 71.p,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 40.p,
                vertical: 7.p,
              ),
              decoration: BoxDecoration(
                color: BPUtils.c_FFB7CDFF,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.p),
                  topRight: Radius.circular(16.p),
                  bottomRight: Radius.circular(16.p),
                  bottomLeft: Radius.circular(4.p),
                ),
              ),
              child: Text(
                (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:S.current.face_to_camera,
                style: TextStyle(
                  color: BPUtils.c_FF1D62FD,
                  fontWeight: FontWeight.normal,
                  fontSize: 28.p,
                ),
              ),
            ))
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  AssetUtil.fullPath('face_bg'),
                  width: 600.p,
                  height: 600.p,
                  key: const Key('face_bg'),
                ),
                Consumer<SFCameraProvider>(builder: (ctx, provider, child) {
                  String? imageData = provider
                      .imageData?.devices?.firstOrNull?.recv_data?.video;
                  if (imageData != null && imageData.isNotEmpty) {
                    return Container(
                      width: 568.p,
                      height: 568.p,
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                          284.p,
                        ),
                      ),
                      child: Image.memory(
                        base64Decode(imageData),
                        gaplessPlayback: true,
                        fit: BoxFit.cover,
                      ),
                    );
                  } else {
                    return Container(
                      width: 568.p,
                      height: 568.p,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            284.p,
                          ),
                          color: Colors.white),
                      child: CupertinoActivityIndicator(
                        radius: 50.p,
                      ),
                    );
                  }
                }),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

class FaceLoginXmlyView extends StatelessWidget {
  FaceLoginXmlyView(this.loginType, {Key? key}) : super(key: key);
  AuthLoginType loginType;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.faceAuth == AuthLoginTypeMap[element.type]).firstOrNull;

    return ListView(
      shrinkWrap: true,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title: S.current.face_recognition_login,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: BPUtils.c_ff12215F,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 154.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
                child: GradientView(
              radius: BorderRadius.only(
                topLeft: Radius.circular(20.p),
                topRight: Radius.circular(20.p),
                bottomLeft: Radius.circular(4.p),
                bottomRight: Radius.circular(20.p),
              ),
              shadowColor: Colors.transparent,
              stops: const [0, 0.5, 1],
              colors: const [
                BPUtils.c_FFBBFBFF,
                BPUtils.c_FFAFDAFF,
                BPUtils.c_FFC3CAFF,
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              child: Padding(
                padding: EdgeInsets.symmetric(
                    vertical: 5.p,
                    horizontal: 35.p),
                child: Text(
                  (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:S.current.face_to_camera,
                  style: TextStyle(
                    color: BPUtils.c_FF1D62FD,
                    fontWeight: FontWeight.normal,
                    fontSize: 28.p,
                  ),
                ),
              ),
            )),
          ],
        ),
        SizedBox(height: 70.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  AssetUtil.fullPath('face_bg'),
                  width: 600.p,
                  height: 600.p,
                  key:const Key('face_bg')
                ),
                Consumer<SFCameraProvider>(builder: (ctx, provider, child) {
                  String? imageData = provider
                      .imageData?.devices?.firstOrNull?.recv_data?.video;
                  if (imageData != null && imageData.isNotEmpty) {
                    return Container(
                      width: 568.p,
                      height: 568.p,
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                          284.p,
                        ),
                      ),
                      child: Image.memory(
                        base64Decode(imageData),
                        gaplessPlayback: true,
                        fit: BoxFit.cover,
                      ),
                    );
                  } else {
                    return Container(
                      width: 568.p,
                      height: 568.p,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            284.p,
                          ),
                          color: Colors.white),
                      child: CupertinoActivityIndicator(
                        radius: 50.p,
                      ),
                    );
                  }
                }),
              ],
            ),
          ],
        ),
      ],
    );
  }
} 