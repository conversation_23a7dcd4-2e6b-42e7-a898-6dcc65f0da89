import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';

/// 通用认证容器组件 - 支持多种认证方式的统一显示
class AuthContainerWidget extends StatelessWidget {
  final AuthLoginType authLoginType;
  final String? customTitle;
  final String? customSubtitle;
  final String? customIcon;
  final Widget? extraWidget;
  final VoidCallback? onTap;

  const AuthContainerWidget({
    super.key,
    required this.authLoginType,
    this.customTitle,
    this.customSubtitle,
    this.customIcon,
    this.extraWidget,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingProvider>(
      builder: (context, settingProvider, child) {
        final String title = _getDynamicTitle(settingProvider) ?? _getDefaultTitle();
        final String subtitle = customSubtitle ?? _getDefaultSubtitle();
        final String iconPath = customIcon ?? _getDefaultIcon();

        return Center(
          child: GestureDetector(
            onTap: onTap,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 图标显示
                Container(
                  width: 120.p,
                  height: 120.p,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.p),
                    color: Colors.white.withOpacity(0.1),
                  ),
                  child: Image.asset(
                    iconPath,
                    width: 120.p,
                    height: 120.p,
                    errorBuilder: (context, error, stackTrace) {
                      print('无法加载图标: $iconPath, 错误: $error');
                      return Container(
                        width: 120.p,
                        height: 120.p,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12.p),
                          color: const Color(0xFF4F83FC).withOpacity(0.2),
                        ),
                        child: Icon(
                          _getDefaultIconData(),
                          size: 60.p,
                          color: const Color(0xFF4F83FC),
                        ),
                      );
                    },
                  ),
                ),
                SizedBox(height: 40.p),
                
                // 主标题
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 48.p,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF222222),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.p),
                
                // 副标题
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 32.p,
                    color: const Color(0xFF999999),
                  ),
                  textAlign: TextAlign.center,
                ),
                
                // 额外组件
                if (extraWidget != null) ...[
                  SizedBox(height: 40.p),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 80.p),
                    child: extraWidget!,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// 获取动态标题配置
  String? _getDynamicTitle(SettingProvider settingProvider) {
    try {
      final readerConfig = settingProvider.readerConfigData;
      final authTitleConfig = readerConfig?.authTitleConfig;
      
      if (authTitleConfig != null && authTitleConfig.isNotEmpty) {
        ReaderAuthTitleConfig? titleData;
        try {
          titleData = authTitleConfig.firstWhere(
            (element) => authLoginType == AuthLoginTypeMap[element.type],
          );
        } catch (e) {
          titleData = null;
        }
        
        if (titleData != null && titleData.title?.isNotEmpty == true) {
          return titleData.title!;
        }
      }
    } catch (e) {
      print('获取认证页面动态标题失败: $e');
    }
    
    return null;
  }

  /// 获取默认标题
  String _getDefaultTitle() {
    switch (authLoginType) {
      case AuthLoginType.eletricSocialSecurityCard:
        return '请出示电子社保卡';
      case AuthLoginType.wechatScanQRCode:
        return '请出示微信二维码';
      case AuthLoginType.huiwenQRCode:
        return '请出示汇文码';
      case AuthLoginType.wechatQRCode:
        return '请出示微信二维码';
      case AuthLoginType.aliCreditQRCode:
        return '请出示芝麻信用码';
      case AuthLoginType.shangHaiQRCode:
        return '请出示随申码';
      case AuthLoginType.readerQRCode:
        return '请出示读者二维码';
      case AuthLoginType.citizenCard:
        return '请将市民卡放在感应区';
      case AuthLoginType.socailSecurityCard:
        return '请将社保卡放在感应区';
      case AuthLoginType.tencentTCard:
        return '请打开腾讯E证通';
      case AuthLoginType.IMIAuth:
        return '请进行IMI身份认证';
      case AuthLoginType.takePhoto:
        return '请对准摄像头';
      case AuthLoginType.wecharOrAlipay:
        return '请出示付款码';
      case AuthLoginType.alipayQRCode_credit:
        return '请出示支付宝付款码';
      case AuthLoginType.jieYueBao:
        return '请使用借阅宝';
      case AuthLoginType.keyboardInput:
        return '请输入证件号码';
      default:
        return '请按提示进行认证';
    }
  }

  /// 获取默认副标题
  String _getDefaultSubtitle() {
    switch (authLoginType) {
      case AuthLoginType.eletricSocialSecurityCard:
        return '电子社保卡认证';
      case AuthLoginType.wechatScanQRCode:
        return '微信扫码认证';
      case AuthLoginType.huiwenQRCode:
        return '汇文码认证';
      case AuthLoginType.wechatQRCode:
        return '微信二维码认证';
      case AuthLoginType.aliCreditQRCode:
        return '芝麻信用认证';
      case AuthLoginType.shangHaiQRCode:
        return '随申码认证';
      case AuthLoginType.readerQRCode:
        return '二维码读者认证';
      case AuthLoginType.citizenCard:
        return '市民卡认证';
      case AuthLoginType.socailSecurityCard:
        return '社保卡认证';
      case AuthLoginType.tencentTCard:
        return '腾讯E证通认证';
      case AuthLoginType.IMIAuth:
        return 'IMI身份认证';
      case AuthLoginType.takePhoto:
        return '拍照认证';
      case AuthLoginType.wecharOrAlipay:
        return '微信/支付宝认证';
      case AuthLoginType.alipayQRCode_credit:
        return '支付宝信用认证';
      case AuthLoginType.jieYueBao:
        return '借阅宝认证';
      case AuthLoginType.keyboardInput:
        return '手动输入认证';
      default:
        return '身份认证';
    }
  }

  /// 获取默认图标路径
  String _getDefaultIcon() {
    switch (authLoginType) {
      case AuthLoginType.eletricSocialSecurityCard:
        return AssetUtil.fullPath('e_social_security_auth.png');
      case AuthLoginType.wechatScanQRCode:
      case AuthLoginType.wechatQRCode:
        return AssetUtil.fullPath('wechat_auth.png');
      case AuthLoginType.huiwenQRCode:
        return AssetUtil.fullPath('reader_qr_auth.png');
      case AuthLoginType.aliCreditQRCode:
        return AssetUtil.fullPath('zhima_credit_auth.png');
      case AuthLoginType.shangHaiQRCode:
        return AssetUtil.fullPath('reader_qr_auth.png');
      case AuthLoginType.readerQRCode:
        return AssetUtil.fullPath('reader_qr_auth.png');
      case AuthLoginType.citizenCard:
        return AssetUtil.fullPath('citizen_card_auth.png');
      case AuthLoginType.socailSecurityCard:
        return AssetUtil.fullPath('social_security_auth.png');
      case AuthLoginType.tencentTCard:
        return AssetUtil.fullPath('tencent_card_auth.png');
      case AuthLoginType.IMIAuth:
        return AssetUtil.fullPath('id_card_auth.png');
      case AuthLoginType.takePhoto:
        return AssetUtil.fullPath('face_auth.png');
      case AuthLoginType.wecharOrAlipay:
        return AssetUtil.fullPath('wechat_auth.png');
      case AuthLoginType.alipayQRCode_credit:
        return AssetUtil.fullPath('alipay_auth.png');
      case AuthLoginType.jieYueBao:
        return AssetUtil.fullPath('borrow_treasure_auth.png');
      case AuthLoginType.keyboardInput:
        return AssetUtil.fullPath('keyboard_auth.png');
      default:
        return AssetUtil.fullPath('reader_card_auth.png');
    }
  }

  /// 获取默认图标数据
  IconData _getDefaultIconData() {
    switch (authLoginType) {
      case AuthLoginType.eletricSocialSecurityCard:
      case AuthLoginType.socailSecurityCard:
        return Icons.credit_card;
      case AuthLoginType.wechatScanQRCode:
      case AuthLoginType.wechatQRCode:
      case AuthLoginType.huiwenQRCode:
      case AuthLoginType.aliCreditQRCode:
      case AuthLoginType.shangHaiQRCode:
      case AuthLoginType.readerQRCode:
      case AuthLoginType.alipayQRCode_credit:
        return Icons.qr_code;
      case AuthLoginType.citizenCard:
        return Icons.badge;
      case AuthLoginType.tencentTCard:
        return Icons.phone_android;
      case AuthLoginType.IMIAuth:
        return Icons.fingerprint;
      case AuthLoginType.takePhoto:
        return Icons.camera_alt;
      case AuthLoginType.wecharOrAlipay:
        return Icons.payment;
      case AuthLoginType.jieYueBao:
        return Icons.book;
      case AuthLoginType.keyboardInput:
        return Icons.keyboard;
      default:
        return Icons.credit_card;
    }
  }
} 