// ignore_for_file: must_be_immutable

import 'dart:convert';
import 'dart:async';

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hardware/hardware.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:a3g/core/widgets/gradient_border_container.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:seasetting/seasetting.dart';
import '../../../generated/l10n.dart';
import '../../../core/utils/window_util.dart';
import '../view_models/auth_view_model.dart';
import 'SeaBlueKeyboard.dart';
import 'SeaBlueTextInput.dart';

class AccountLoginView extends StatelessWidget {
  AccountLoginView({this.callback, this.showPsw = true, Key? key})
      : super(key: key);
  bool showPsw;
  Function(String account)? callback;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:  EdgeInsets.symmetric(horizontal: 40.p),
      child: Consumer2<CurrentLayoutProvider, SettingProvider>(
          builder: (context, layoutProvider, settingProvider, child) {
        if (settingProvider.getTheme() == ThemeType.ximalaya) {
          return AccountLoginXmlyView(callback: callback);
        } else if (settingProvider.getTheme() == ThemeType.child) {
          return AccountLoginChildView(callback: callback);
        } else {
          return AccountAuthBluePage();
        }
      }),
    );
  }
}

class AccountLoginChildView extends StatelessWidget {
  AccountLoginChildView({this.callback, Key? key}) : super(key: key);
  TextEditingController ctr1 = TextEditingController();

  // TextEditingController ctr2 = TextEditingController();
  FocusNode node1 = FocusNode();

  // FocusNode node2 = FocusNode();
  // bool showPsw;

  Function(String account)? callback;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.keyboardInput == AuthLoginTypeMap[element.type]).firstOrNull;

    return Column(
      children: [
        Text(
          (titleData?.title.isNotEmpty??false) ? titleData!.title:S.current.accountLogin,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 48.p,
          ),
        ),
        SizedBox(height: 108.p),
        SeaBlueTextInput(
          icon: '账号图标',
          hintText: S.current.accountInput,
          controller: ctr1,
          focusNode: node1,
          needObscure: false,
        ),
        SizedBox(height: 50.p),
        SizedBox(
          width: 930.p,
          height: 630.p,
          child: SeaBlueKeyboard(
            SeaBlueKeyboardType.number,
            controller: ctr1,
            focus: node1,
            onSubmitted: (text) => callback?.call(ctr1.text),
          ),
        ),
      ],
    );
  }
}

// class AccountLoginBlueView extends StatelessWidget {
//   AccountLoginBlueView({this.callback, Key? key}) : super(key: key);
//   TextEditingController ctr1 = TextEditingController();
//   TextEditingController ctr2 = TextEditingController();
//   FocusNode node1 = FocusNode();
//   FocusNode node2 = FocusNode();
//   late bool showPsw;
//   TextEditingController? editingController;
//   FocusNode? editingNode;
//
//   Function(String account)? callback;
//
//   @override
//   Widget build(BuildContext context) {
//     ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.keyboardInput == AuthLoginTypeMap[element.type]).firstOrNull;
//
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         SizedBox(height: 100.p),
//         Text(
//           S.current.accountLogin,
//           style: TextStyle(
//             color: BPUtils.c_FF212121,
//             fontWeight: FontWeight.w600,
//             fontSize: 48.p,
//           ),
//         ),
//         SizedBox(height: 108.p),
//         Padding(
//           padding: EdgeInsets.symmetric(
//               horizontal: 120.p),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               SeaBlueTextInput(
//                 icon: '账号图标',
//                 hintText: S.current.accountInput,
//                 controller: ctr1,
//                 focusNode: node1,
//                 needObscure: false,
//                 onTap: onTapAccount,
//               ), //
//               Visibility(
//                   visible: showPsw,
//                   child: SizedBox(
//                       height: 44.p)),
//               Visibility(
//                   visible: showPsw,
//                   child: SeaBlueTextInput(
//                     icon: '密码图标',
//                     hintText: S.current.passwordInput,
//                     controller: ctr2,
//                     focusNode: node2,
//                     needObscure: true,
//                     onTap: onTapPsw,
//                   )), //
//               Visibility(
//                   visible: showPsw,
//                   child: Row(
//                     children: [
//                       Expanded(child: Container(
//                         // color: Colors.red,
//                         padding: EdgeInsets.symmetric(
//                             horizontal: 120.p,
//                             vertical: 10.p),
//                         child: Text(
//                           pswHintText,
//                           textAlign: TextAlign.start,
//                           style: TextStyle(
//                             color: BPUtils.c_99,
//                             fontSize: 28.p,
//                           ),
//                         ),
//                       )),
//                     ],
//                   )),
//               SizedBox(height: 144.p),
//             ],
//           ),
//         ),
//         Padding(
//           padding: EdgeInsets.symmetric(),
//           child: SeaBlueKeyboard(
//             SeaBlueKeyboardType.number,
//             controller: editingController,
//             focus: editingNode,
//             onSubmitted: (text) {
//               if (editingController == ctr1) {
//                 onTapPsw();
//               } else if (editingController == ctr2) {
//                 widget.controller.post('onSubmitted',
//                     {'account': ctr1.text, 'psw': ctr2.text});
//               }
//             },
//           ),
//         )
//       ],
//     );
//   }
//
//   onTapAccount() {
//     node2.unfocus();
//     node1.requestFocus();
//     setState(() {
//       editingController = ctr1;
//       editingNode = node1;
//     });
//   }
//
//   onTapPsw() {
//     node1.unfocus();
//     node2.requestFocus();
//     setState(() {
//       editingController = ctr2;
//       editingNode = node2;
//     });
//   }
// }


class AccountAuthBluePage extends StatefulWidget {
  const AccountAuthBluePage(
      {Key? key})
      : super(key: key);


  @override
  State<AccountAuthBluePage> createState() => _AccountAuthBluePageState();
}

class _AccountAuthBluePageState extends SEContentState<AccountAuthBluePage> {
  TextEditingController ctr1 = TextEditingController();
  TextEditingController ctr2 = TextEditingController();
  FocusNode node1 = FocusNode();
  FocusNode node2 = FocusNode();
  late bool showPsw;
  TextEditingController? editingController;
  FocusNode? editingNode;

  @override
  void initState() {
    super.initState();
    final authConfig = context.read<AuthViewModel>().authConfig;
    showPsw = authConfig?.isNeedPsw ?? false;
    onTapAccount();
  }

  @override
  Widget build(BuildContext context) {
    String pswHintText = context
            .read<SettingProvider>()
            .pageControlConfig
            ?.pswInputData
            .hintText ??
        "";

    return GradientBGContainer(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 100.p),
          Text(
            S.current.accountLogin,
            style: TextStyle(
              color: BPUtils.c_FF212121,
              fontWeight: FontWeight.w600,
              fontSize: 48.p,
            ),
          ),
          SizedBox(height: 108.p),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 120.p),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SeaBlueTextInput(
                  icon: '账号图标',
                  hintText: S.current.accountInput,
                  controller: ctr1,
                  focusNode: node1,
                  needObscure: false,
                  onTap: onTapAccount,
                ), //
                Visibility(
                  visible: showPsw,
                  child: SizedBox(height: 44.p),
                ),
                Visibility(
                  visible: showPsw,
                  child: SeaBlueTextInput(
                    icon: '密码图标',
                    hintText: S.current.passwordInput,
                    controller: ctr2,
                    focusNode: node2,
                    needObscure: true,
                    onTap: onTapPsw,
                  ),
                ),
                Visibility(
                  visible: showPsw,
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 120.p, vertical: 10.p),
                          child: Text(
                            pswHintText,
                            textAlign: TextAlign.start,
                            style: TextStyle(
                              color: BPUtils.c_99,
                              fontSize: 28.p,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 144.p),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(),
            child: SeaBlueKeyboard(
              SeaBlueKeyboardType.number,
              controller: editingController,
              focus: editingNode,
              onSubmitted: (text) {
                if (editingController == ctr1) {
                  if (showPsw) {
                    onTapPsw();
                  } else {
                    // Only account is needed, proceed if not empty
                    if (ctr1.text.isNotEmpty) {
                      context
                          .read<AuthViewModel>()
                          .requestReaderInfo(ctr1.text, '');
                    } else {
                      // Optionally handle empty account error
                      showToast('请输入账号');
                    }
                  }
                } else if (editingController == ctr2) {
                   // Password field is active (showPsw must be true here)
                  if (ctr2.text.isNotEmpty) { // Check if password is not empty
                    // Proceed only if account is also not empty
                     if (ctr1.text.isNotEmpty) {
                       context
                          .read<AuthViewModel>()
                          .requestReaderInfo(ctr1.text, '', psw: ctr2.text);
                     } else {
                       showToast('请输入账号');
                       onTapAccount(); // Switch focus back to account
                     }
                  } else {
                    // Handle empty password error
                    // You might want to show a message or visually indicate the error
                     showToast('请输入密码');
                  }
                }
              },
            ),
          )
        ],
      ),
    );
  }

  onTapAccount() {
    if (mounted) {
      node2.unfocus();
      node1.requestFocus();
      setState(() {
        editingController = ctr1;
        editingNode = node1;
      });
    }
  }

  onTapPsw() {
    if (mounted) {
      node1.unfocus();
      node2.requestFocus();
      setState(() {
        editingController = ctr2;
        editingNode = node2;
      });
    }
  }

  @override
  void dispose() {
    ctr1.dispose();
    ctr2.dispose();
    node1.dispose();
    node2.dispose();
    super.dispose();
  }
}

class AccountLoginXmlyView extends StatelessWidget {
  AccountLoginXmlyView({this.callback, Key? key}) : super(key: key);
  TextEditingController ctr1 = TextEditingController();

  // TextEditingController ctr2 = TextEditingController();
  FocusNode node1 = FocusNode();

  // FocusNode node2 = FocusNode();
  // bool showPsw;

  Function(String account)? callback;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.keyboardInput == AuthLoginTypeMap[element.type]).firstOrNull;

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 150.p),
          child: Text(
            (titleData?.title.isNotEmpty??false) ? titleData!.title:S.current.accountLogin,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: BPUtils.c_ff12215F,
              fontWeight: FontWeight.w600,
              fontSize: 48.p,
            ),
          ),
        ),
        SizedBox(height: 108.p),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 150.p),
          child: SeaBlueTextInput(
            icon: '账号图标',
            hintText: S.current.accountInput,
            controller: ctr1,
            focusNode: node1,
            needObscure: false,
          ),
        ),
        SizedBox(height: 50.p),
        SizedBox(
          width: 930.p,
          height: 630.p,
          child: SeaBlueKeyboard(
            SeaBlueKeyboardType.number,
            controller: ctr1,
            focus: node1,
            onSubmitted: (text) => callback?.call(ctr1.text),
          ),
        ),
      ],
    );
  }
} 