import 'package:hardware/hardware.dart';
import 'package:sea_socket/sea_socket.dart';

import 'login_item_data.dart';
import 'operation_type.dart';

class LoginState {
  final bool isLoading;
  final String? errorMessage;
  final LoginItemData selectedItem;
  final List<HWReaderSettingData>? openedReaders;
  final OperationType operationType;
  final Sip2PatronInfoData? readerInfo;

  const LoginState({
    this.isLoading = false,
    this.errorMessage,
    required this.selectedItem,
    this.openedReaders,
    required this.operationType,
    this.readerInfo,
  });

  LoginState copyWith({
    bool? isLoading,
    String? errorMessage,
    LoginItemData? selectedItem,
    List<HWReaderSettingData>? openedReaders,
    OperationType? operationType,
    Sip2PatronInfoData? readerInfo,
  }) {
    return LoginState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      selectedItem: selectedItem ?? this.selectedItem,
      openedReaders: openedReaders ?? this.openedReaders,
      operationType: operationType ?? this.operationType,
      readerInfo: readerInfo ?? this.readerInfo,
    );
  }
}