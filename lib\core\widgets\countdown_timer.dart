import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../providers/countdown_provider.dart';
import '../router/app_router.dart';
import '../router/route_extension.dart';
import '../utils/window_util.dart';

class AutoCountdown extends StatefulWidget {
  final Widget? child;
  final VoidCallback? onComplete;
  final bool showTimer;
  final String? returnRoute;

  const AutoCountdown({
    Key? key,
    this.child,
    this.onComplete,
    this.showTimer = true,
    this.returnRoute = '/home',
  }) : super(key: key);

  @override
  State<AutoCountdown> createState() => _AutoCountdownState();
}

class _AutoCountdownState extends State<AutoCountdown> {
  void _handleComplete() {
    if (widget.onComplete != null) {
      widget.onComplete!();
    } else if (widget.returnRoute != null && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppNavigator.untilHome();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showTimer)
          Consumer<CountdownProvider>(
            builder: (_, provider, __) {
              // 当倒计时为0时执行回调
              if (provider.seconds == 0) {
                _handleComplete();
              }
              return CountdownTimer(
                startSeconds: provider.seconds,
              );
            },
          ),
        if (widget.child != null) widget.child!,
      ],
    );
  }
}




class CountdownTimer extends StatelessWidget {  // 改为 StatelessWidget
  final int startSeconds;

  const CountdownTimer({
    super.key,
    required this.startSeconds,
  });

  @override
  Widget build(BuildContext context) {
    // 计算每一位数字
    final List<int> digits = [];
    int remaining = startSeconds;

    // 转换成单个数字数组
    do {
      digits.insert(0, remaining % 10);
      remaining = remaining ~/ 10;
    } while (remaining > 0);

    // 补齐前导零，确保至少显示两位数
    while (digits.length < 2) {
      digits.insert(0, 0);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (int i = 0; i < digits.length; i++) ...[
          if (i > 0) SizedBox(width: 6.p),
          NumContent(number: digits[i]),
        ],
        Padding(
          padding: EdgeInsets.only(left: 14.p),
          child: SizedBox(
            height: 106.p,
            child: Align(
              alignment: Alignment.bottomLeft,
              child: Text(
                'S',
                style: TextStyle(
                    fontSize: 40.p,
                    color: const Color(0xFF222222),
                    fontWeight: FontWeight.w400,
                    height: 40/32
                ),
              ),
            ),
          ),
        )
      ],
    );
  }
}


class NumContent extends StatefulWidget {
  final int number;  // 只接收0-9的数字

  const NumContent({
    super.key,
    required this.number,
  }) : assert(number >= 0 && number <= 9, 'Number must be between 0 and 9');

  @override
  State<NumContent> createState() => _NumContentState();
}

class _NumContentState extends State<NumContent> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late int _currentNumber;
  late int _nextNumber;

  @override
  void initState() {
    super.initState();
    _currentNumber = widget.number;
    _nextNumber = widget.number;

    _controller = AnimationController(
      duration: const Duration(milliseconds: 450),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(NumContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.number != widget.number) {
      _currentNumber = oldWidget.number;
      _nextNumber = widget.number;
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 66.p,
          height: 94.p,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.p),
          ),
          child: Row(
            children: [
              Container(
                width: 6.p,
                height: 18.p,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFDDE0E1),
                      Color(0xFFD2D7DD),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  height: 4.p,
                  color: const Color(0xFFD6DCE3),
                ),
              ),
              Container(
                width: 6.p,
                height: 18.p,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFDDE0E1),
                      Color(0xFFD2D7DD),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          child: Center(
            child: ClipRect(
              child: AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  final value = _animation.value;
                  final isFirstHalf = value < 0.5;

                  final displayNumber = isFirstHalf ? _currentNumber : _nextNumber;
                  final rotationValue = isFirstHalf ? value : value - 1;

                  return Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.identity()
                      ..setEntry(3, 2, 0.001)
                      ..rotateX(rotationValue * pi),
                    child: Text(
                      displayNumber.toString(),
                      style: TextStyle(
                        fontSize: 60.p,
                        color: const Color(0xFF222222),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}

