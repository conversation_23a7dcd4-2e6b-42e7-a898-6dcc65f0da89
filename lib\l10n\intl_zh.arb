{"noCardTip": "卡已用完，暂时不能办理读者证，请联系工作人员！", "cardLefts": "卡剩余", "resend": "秒后重发", "Sending": "正在发送...", "AccountPassword": "账号密码", "ReaderCardVerification": "读者证认证", "ScanAuth": "二维码认证", "wechatScanAuth": "微信扫码认证", "IncorrectPassword": "读者密码错误", "accountLogin": "账号登录", "accountInput": "请输入读者证号/身份证号", "passwordInput": "请输入密码", "loginButton": "登录", "spaceLogin": "登录", "accountRequired": "账号不能为空", "passwordLengthError": "密码长度不正确", "passwordFormatError": "密码格式不正确", "requestingInfo": "正在请求读者信息", "invalidCredentials": "读者证不存在或密码错误", "readerFailure": "打开阅读器失败", "barcodeError": "条码解析失败", "cardCreationFail": "办证失败", "waitingResult": "等待结果", "processingCard": "正在为您办理读者证，\n请别走开...", "cardIdFail": "读者证号获取失败", "cardIdFailContact": "读者证号获取失败，请联系图书馆工作人员", "cardIssuanceError": "发卡错误", "cardMachineError": "发卡机错误，请联系图书馆工作人员", "cardIdExhausted": "当前读者证号已使用完毕", "cardIdExhaustedContact": "当前读者证号已使用完毕，请联系图书馆工作人员", "unsupportedCardType": "暂不支持此办证类型", "unsupportedCardTypeContact": "暂不支持此办证类型，请联系图书馆工作人员", "depositNotSupported": "暂不支持收取押金", "cardCreationSuccess": "办证成功", "readerTableFail": "处理读者表失败", "businessTableFail": "处理业务系统表失败", "cardRequestFail": "请求办证失败", "nameInput": "姓名", "genderInput": "性别", "ethnicityInput": "民族", "birthDateInput": "出生日期", "issuingAuthInput": "签发机关", "idNumberInput": "身份证号", "expirationInput": "有效期限", "addressInput": "住址", "captchaRequired": "验证码不能为空", "phoneError": "手机号错误", "depositInput": "押金", "currencyUnit": "元", "phoneForService": "为了更好的为您服务，请输入您的电话号码", "phoneInput": "请输入您的电话号码", "validCaptcha": "请输入正确验证码", "captchaInput": "请输入验证码", "phoneOrCaptchaRequired": "手机号码或者验证码不能为空", "validPhoneInput": "请输入正确手机号码", "confirmButton": "确定", "serviceNotice": "自助办证服务须知", "agreement": "我已阅读并同意以上内容", "continueButton": "继续", "readerCardAppliedSuccessfully": "您已成功申请了读者证", "collectReaderCard": "请从出卡口取走您的读者证", "confirm": "确认", "socialSecurityCard": "社保卡", "identityCard": "身份证", "register": "注册", "scanESCBarcode": "请扫描您的电子社保卡二维码", "place": "请将", "at": "放于", "sensingArea": "感应区", "electronicSocialSecurityCard": "电子社保卡", "scanYour": "请扫描您的", "qrCode": "二维码", "selectRegistrationMethod": "请选择注册方式", "readerNotFound": "未配置阅读器", "sscRegistration": "社保卡注册", "secondGenIDCardRegistration": "二代身份证注册", "duplicateRegistration": "您已经办理过读者证，请勿重复办证", "eSSCRegistration": "电子社保卡注册", "unsupportedType": "暂不支持此类型", "noDeposit": "免押金", "selectReaderCardType": "请选择需要申办的读者证类型", "ineligibleAge": "对不起，您不符合此读者证办理年龄条件", "ineligibleArea": "对不起，您不符合此读者证办理区域条件", "placeDocument": "请将文献错开平放于桌面感应区", "success": "成功", "copy": "本", "failure": "失败", "sequenceNumber": "序号", "barcode": "条码", "bookTitle": "书名", "dueDate": "应还时间", "note": "备注", "booksToReturn": "待还书", "availableInLibrary": "在馆", "borrowSuccess": "借书成功", "bookTitleShort": "题名", "callNumber": "索书号", "borrowFailure": "借书失败", "continueBorrowing": "继续借书", "confirmBorrow": "确认借书", "readerCardID": "读者证号", "borrowableCopies": "可借阅(本)", "borrowedCopies": "已借阅(本)", "defaultPassword": "默认密码", "Predeposit": "预存(元)", "returningInProgress": "正在还书，请勿重复操作", "noBooksAvailable": "无可借书籍", "borrowingInProgress": "借书中...", "renewInProgress": "续借中...", "borrowRequestFailed": "请求借书失败", "borrow_fail_request": "借书失败，请求借书失败", "borrow_fail_rewrite_security": "借书失败，改写防盗位失败", "cert_number": "证号", "borrow_success": "成功借阅文献", "status": "状态", "return_date": "归还日期", "take_away_card_books": "请取走您的读者证和所借图书", "free_listening": "免费收听啦", "wechat_scan": "微信扫一扫", "reader_card": "读者证", "scan_e_social_card": "请扫描您的电子社保卡", "wechat_qr_code": "微信二维码", "huiwen_qr_code": "汇文二维码", "scan_wechat_qr": "请扫描您的微信二维码", "scan_huiwen_qr": "请扫描您的汇文二维码", "alipay_login": "支付宝登录", "alipay": "支付宝", "alipay_scan_guide": "打开手机端支付宝应用 点击左上角扫一扫图标", "borrow_with_alipay_credit": "芝麻分只要550分以上，打开支付宝扫一扫屏幕中的二维码即可开启借书", "auth_fail_advice": "若授权失败，请查看页面失败原因或咨询芝麻信用客服", "regenerate": "重新生成", "face_recognition_login": "人脸识别登录", "face_recognition": "人脸识别", "face_to_camera": "请您正视前方摄像头", "login_method": "登录方式", "card_not_exist": "读者证不存在", "reader_closing": "正在关闭阅读器，请稍后再试", "detail_info": "详情信息", "borrowing": "在借", "overdue": "超期", "renew_success": "续借成功", "renew_fail": "续借失败", "barcode_number": "条码号", "renew": "续借", "debt_amount": "欠款(元)", "details": "详情", "current_borrow_count": "现借总数", "available_borrow_count": "可借总数", "borrowed": "已借阅", "overdue_count": "超期文献数", "deposit": "押金", "debt": "欠款", "prepaid_balance": "预存费余额", "select_all": "全选", "view_details": "点击详情", "available_for_borrow": "可借阅", "return_success": "还书成功", "return_date_recorded": "还书日期", "return_fail": "还书失败", "continue_returning": "继续还书", "confirm_return": "确认还书", "book_overdue": "该单册已逾期", "no_books_to_return": "无待归还书籍", "returningStatus": "归还中...", "returnFailure": "还书失败，请求还书失败", "returnFailureRewrite": "还书失败，改写防盗位失败", "placeBookOnSensor": "请您将待还文献放于桌面感应区", "returnSuccess": "成功归还文献", "takeCardAndBooks": "请取走您的读者证和所借图书,并请核对打印凭据！", "returnDate": "归还日期", "bookRecommendations": "猜你喜欢的书籍", "selfServiceMachine": "自助借还书机", "sendVerificationCode": "发送验证码", "printReceipt": "您是否打印凭条", "no": "否", "yes": "是", "clear": "清空", "characters": "字符", "quit": "退出", "bookRecommendationsTitle": "好书推荐", "scanToListen": "扫码收听", "homePage": "首页", "borrowBook": "借书", "returnBook": "还书", "renewBook": "续借", "applyCard": "办证", "renewOrQuery": "续借/查询", "query": "查询", "remainingTime": "剩余\n时间", "selectOperationType": "请选择操作类型", "qrCodeLogin": "二维码登录", "wechatLogin": "微信登陆", "cardLogin": "读者证登录", "manualInputLogin": "手动输入登录", "manualInput": "手动输入", "idCardLogin": "身份证登录", "socialSecurityCardLogin": "社保卡登录", "eSocialSecurityCardLogin": "电子社保卡登录", "citizenCardLogin": "市民卡登录", "exit": "退出", "placeCardOnSensor": "请将读者证放于桌面感应区", "placeIdCardOnSensor": "请将身份证放于桌面感应区", "tencentEPassRegister": "腾讯E证通注册", "qrCodeRegistration": "展示二维码注册", "alipayCreditRegister": "阿里信用分注册", "faceMatchRegister": "人脸匹配注册", "scanPayRegister": "扫码支付注册", "externalCardRegistration": "外部刷卡（办证）", "paymentCodeConfig": "付款码支付配置", "manualInputRegistration": "手动输入注册", "shanghaiShenShenCodeRegister": "上海随申码注册", "placeCardOnSensorForCard": "请将一卡通放于感应区", "enterPhoneNumber": "为了更好的为您服务，请输入您的手机号码", "inputPhoneNumber": "请输入手机号", "yourPhoneNumber": "您的电话号码", "selectCardType": "请选择下面按钮\n申办对应类型的图书证", "submittingData": "正在提交数据，请稍等…", "insertMoney": "请您将纸币放入收钞口", "moneyAcceptanceInfo": "本机仅接收10元、20元、50元、100元人民币，不设找零，如有余额将存入预付款账户", "accountBalance": "账户余额", "currentDeposit": "本次存入", "cardApplicationSuccess": "您已经成功申请了读者证"}