import '../post_auth_service.dart';
import 'welcome_handler.dart';
import 'door_lock_handler.dart';

/// 后认证处理器注册器
class PostAuthHandlersRegistry {
  /// 注册所有默认处理器
  static void registerDefaultHandlers() {
    final service = PostAuthService.instance;

    // 注册门锁开门处理器（高优先级）
    service.registerHandler(DoorLockHandler());

    // 注册欢迎处理器
    service.registerHandler(WelcomeHandler());

  }
  
  /// 根据配置注册处理器
  static void registerFromConfig(Map<String, dynamic> config) {
    final service = PostAuthService.instance;
    
    // 从配置中读取并注册自定义处理器
    // 这里可以根据实际需要实现更复杂的逻辑
  }
} 