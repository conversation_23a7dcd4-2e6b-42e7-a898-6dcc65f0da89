import 'dart:async';
import 'dart:io';

import 'network_utils.dart';

/// 错误恢复管理器
/// 统一处理各种认证服务的错误恢复逻辑
class ErrorRecoveryManager {
  static final ErrorRecoveryManager _instance = ErrorRecoveryManager._internal();
  static ErrorRecoveryManager get instance => _instance;
  ErrorRecoveryManager._internal();

  // 错误恢复状态
  final Map<String, DateTime> _lastErrorTime = {};
  final Map<String, int> _errorCount = {};
  final Map<String, Timer> _recoveryTimers = {};

  /// 处理认证服务错误
  /// [serviceId] 服务标识
  /// [error] 错误信息
  /// [onRecovery] 恢复回调函数
  Future<bool> handleServiceError({
    required String serviceId,
    required dynamic error,
    Future<void> Function()? onRecovery,
  }) async {
    final errorString = error.toString();
    
    // 记录错误
    _recordError(serviceId, errorString);
    
    // 根据错误类型决定处理策略
    if (NetworkUtils.isNetworkError(error)) {
      return await _handleNetworkError(serviceId, errorString, onRecovery);
    } else if (NetworkUtils.isLibraryError(error)) {
      return await _handleLibraryError(serviceId, errorString, onRecovery);
    } else if (_isReaderError(error)) {
      return await _handleReaderError(serviceId, errorString, onRecovery);
    } else {
      return await _handleGenericError(serviceId, errorString, onRecovery);
    }
  }

  /// 记录错误
  void _recordError(String serviceId, String error) {
    _lastErrorTime[serviceId] = DateTime.now();
    _errorCount[serviceId] = (_errorCount[serviceId] ?? 0) + 1;
    
    print('错误恢复管理器: [$serviceId] 错误次数=${_errorCount[serviceId]}, 错误=$error');
  }

  /// 处理网络错误
  Future<bool> _handleNetworkError(
    String serviceId, 
    String error, 
    Future<void> Function()? onRecovery
  ) async {
    print('处理网络错误: [$serviceId] $error');
    
    // 检查是否为172.16.1.103服务器连接问题
    if (error.contains('172.16.1.103')) {
      return await _handle172ServerError(serviceId, error, onRecovery);
    }
    
    // 通用网络错误处理
    return await _scheduleRecovery(
      serviceId: serviceId,
      delay: const Duration(seconds: 30),
      maxRetries: 3,
      onRecovery: onRecovery,
      errorType: 'network',
    );
  }

  /// 处理172.16.1.103服务器错误
  Future<bool> _handle172ServerError(
    String serviceId,
    String error,
    Future<void> Function()? onRecovery,
  ) async {
    print('检测到172.16.1.103服务器连接问题，检查服务器状态...');
    
    // 检查服务器端口状态
    final portStatus = await NetworkUtils.instance.check172ServerPorts();
    
    bool anyPortAvailable = portStatus.values.any((status) => status);
    
    if (anyPortAvailable) {
      print('172.16.1.103服务器部分端口可用，尝试立即恢复');
      return await _scheduleRecovery(
        serviceId: serviceId,
        delay: const Duration(seconds: 5),
        maxRetries: 2,
        onRecovery: onRecovery,
        errorType: '172_server',
      );
    } else {
      print('172.16.1.103服务器完全不可用，延长重试间隔');
      return await _scheduleRecovery(
        serviceId: serviceId,
        delay: const Duration(minutes: 2),
        maxRetries: 5,
        onRecovery: onRecovery,
        errorType: '172_server_down',
      );
    }
  }

  /// 处理动态库错误
  Future<bool> _handleLibraryError(
    String serviceId,
    String error,
    Future<void> Function()? onRecovery,
  ) async {
    print('处理动态库错误: [$serviceId] $error');
    
    // 动态库错误通常不需要重试，直接标记为已处理
    if (error.contains('iCloseCard') || 
        error.contains('Failed to lookup symbol')) {
      print('动态库符号查找失败，这是已知问题，忽略错误');
      return true; // 返回true表示错误已被处理
    }
    
    // 其他动态库错误，尝试重新初始化
    return await _scheduleRecovery(
      serviceId: serviceId,
      delay: const Duration(seconds: 10),
      maxRetries: 1,
      onRecovery: onRecovery,
      errorType: 'library',
    );
  }

  /// 处理读卡器错误
  Future<bool> _handleReaderError(
    String serviceId,
    String error,
    Future<void> Function()? onRecovery,
  ) async {
    print('处理读卡器错误: [$serviceId] $error');
    
    return await _scheduleRecovery(
      serviceId: serviceId,
      delay: const Duration(seconds: 15),
      maxRetries: 3,
      onRecovery: onRecovery,
      errorType: 'reader',
    );
  }

  /// 处理通用错误
  Future<bool> _handleGenericError(
    String serviceId,
    String error,
    Future<void> Function()? onRecovery,
  ) async {
    print('处理通用错误: [$serviceId] $error');
    
    return await _scheduleRecovery(
      serviceId: serviceId,
      delay: const Duration(seconds: 20),
      maxRetries: 2,
      onRecovery: onRecovery,
      errorType: 'generic',
    );
  }

  /// 安排错误恢复
  Future<bool> _scheduleRecovery({
    required String serviceId,
    required Duration delay,
    required int maxRetries,
    required Future<void> Function()? onRecovery,
    required String errorType,
  }) async {
    final errorCount = _errorCount[serviceId] ?? 0;
    
    if (errorCount > maxRetries) {
      print('[$serviceId] 错误次数超过最大重试次数($maxRetries)，停止恢复尝试');
      return false;
    }
    
    // 取消之前的恢复定时器
    _recoveryTimers[serviceId]?.cancel();
    
    print('[$serviceId] 安排${errorType}错误恢复，延迟${delay.inSeconds}秒');
    
    _recoveryTimers[serviceId] = Timer(delay, () async {
      try {
        print('[$serviceId] 开始执行错误恢复');
        
        if (onRecovery != null) {
          await onRecovery();
          print('[$serviceId] 错误恢复完成');
          
          // 恢复成功，重置错误计数
          _errorCount[serviceId] = 0;
        }
      } catch (e) {
        print('[$serviceId] 错误恢复失败: $e');
        // 递归尝试恢复
        await _scheduleRecovery(
          serviceId: serviceId,
          delay: Duration(seconds: delay.inSeconds * 2), // 指数退避
          maxRetries: maxRetries,
          onRecovery: onRecovery,
          errorType: errorType,
        );
      } finally {
        _recoveryTimers.remove(serviceId);
      }
    });
    
    return true;
  }

  /// 检查是否为读卡器错误
  bool _isReaderError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('reader') ||
           errorString.contains('读卡器') ||
           errorString.contains('card') ||
           errorString.contains('卡片') ||
           errorString.contains('inventory') ||
           errorString.contains('盘存');
  }

  /// 清除服务的错误记录
  void clearServiceErrors(String serviceId) {
    _errorCount.remove(serviceId);
    _lastErrorTime.remove(serviceId);
    _recoveryTimers[serviceId]?.cancel();
    _recoveryTimers.remove(serviceId);
    
    print('已清除服务[$serviceId]的错误记录');
  }

  /// 获取服务的错误统计
  Map<String, dynamic> getServiceErrorStats(String serviceId) {
    return {
      'errorCount': _errorCount[serviceId] ?? 0,
      'lastErrorTime': _lastErrorTime[serviceId]?.toIso8601String(),
      'hasRecoveryTimer': _recoveryTimers.containsKey(serviceId),
    };
  }

  /// 获取所有服务的错误统计
  Map<String, Map<String, dynamic>> getAllErrorStats() {
    final stats = <String, Map<String, dynamic>>{};
    
    final allServiceIds = <String>{
      ..._errorCount.keys,
      ..._lastErrorTime.keys,
      ..._recoveryTimers.keys,
    };
    
    for (final serviceId in allServiceIds) {
      stats[serviceId] = getServiceErrorStats(serviceId);
    }
    
    return stats;
  }

  /// 释放资源
  void dispose() {
    for (final timer in _recoveryTimers.values) {
      timer.cancel();
    }
    _recoveryTimers.clear();
    _errorCount.clear();
    _lastErrorTime.clear();
  }
} 