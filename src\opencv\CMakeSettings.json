﻿{
    "configurations": [
        {
            "name": "x64-Debug",
            "generator": "Ninja",
            "configurationType": "Debug",
            "inheritEnvironments": [ "msvc_x64_x64" ],
            "buildRoot": "${projectDir}\\out\\build\\${name}",
            "installRoot": "${projectDir}\\out\\install\\${name}",
            "cmakeCommandArgs": "",
            "buildCommandArgs": "",
            "ctestCommandArgs": "",
            "variables": [
              {
                "name": "OpenCV_DIR",
                "value": "D:\\Users\\Administrator\\Downloads\\opencv\\build",
                "type": "PATH"
              }
            ]
        }
    ]
}