import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';

class GradientView extends StatelessWidget {
  GradientView({
    this.child,
    this.borderRadius,
    this.color = Colors.white,
    this.begin = Alignment.topCenter,
    this.end = Alignment.bottomCenter,
    this.colors = const [
      BPUtils.c_FFC6D7FF,
      Colors.white,
    ],
    this.stops = const [0,0.2],
    this.spreadRadius,
    this.shadowColor = BPUtils.c_4D1D62FD,
    this.shadowOffset,
    this.shadowBlurRadius,
    this.radius,
    Key? key,
  }) : super(key: key);
  Widget? child;
  double? borderRadius;
  BorderRadiusGeometry? radius;
  Color color;
  Alignment begin = Alignment.topCenter;
  Alignment end = Alignment.bottomCenter;
  List<Color> colors;
  List<double>? stops;
  double? spreadRadius;
  Color shadowColor;
  Offset? shadowOffset;
  double? shadowBlurRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: colors.isNotEmpty ? null : color,
        borderRadius: radius ?? BorderRadius.circular(borderRadius ?? 20.p),
        gradient: colors.isNotEmpty ? LinearGradient(
          colors: colors,
          stops: stops,
          begin: begin,
          end: end,
        ) : null,
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            offset: shadowOffset ?? Offset(0,0),
            spreadRadius: spreadRadius ?? 10.p,
            blurRadius: shadowBlurRadius ?? 15.p,
          ),
        ],
      ),
      child: child,
    );
  }
}
