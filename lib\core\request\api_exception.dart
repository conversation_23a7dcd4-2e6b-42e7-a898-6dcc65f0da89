import 'package:dio/dio.dart';

/// API异常类
class ApiException implements Exception {
  /// 错误消息
  final String message;
  
  /// 错误码
  final int? code;
  
  /// Dio错误类型
  final DioExceptionType? dioErrorType;

  ApiException({
    required this.message,
    this.code,
    this.dioErrorType,
  });

  /// 从Dio错误创建ApiException
  factory ApiException.fromDioError(DioException error) {
    String message = 'Unknown error occurred';
    
    switch (error.type) {
      case DioExceptionType.cancel:
        message = '请求取消';
        break;
      case DioExceptionType.connectionTimeout:
        message = '连接超时';
        break;
      case DioExceptionType.receiveTimeout:
        message = '接收超时';
        break;
      case DioExceptionType.sendTimeout:
        message = '发送超时';
        break;
      case DioExceptionType.badResponse:
        message = _handleError(
          error.response?.statusCode,
          error.response?.data,
        );
        break;
      case DioExceptionType.connectionError:
        message = '网络连接错误';
        break;
      case DioExceptionType.badCertificate:
        message = '证书验证失败';
        break;
      case DioExceptionType.unknown:
      default:
        if (error.message != null && error.message!.contains('SocketException')) {
          message = '网络连接失败';
        } else {
          message = '发生未知错误';
        }
        break;
    }

    return ApiException(
      message: message,
      code: error.response?.statusCode,
      dioErrorType: error.type,
    );
  }

  /// 处理HTTP状态码错误
  static String _handleError(int? statusCode, dynamic error) {
    switch (statusCode) {
      case 400:
        return '请求参数错误';
      case 401:
        return '未授权，请重新登录';
      case 403:
        return '拒绝访问';
      case 404:
        return '请求路径不存在';
      case 405:
        return '请求方法不允许';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务不可用';
      case 505:
        return 'HTTP版本不支持';
      default:
        return '发生错误: $statusCode';
    }
  }

  @override
  String toString() => message;
} 