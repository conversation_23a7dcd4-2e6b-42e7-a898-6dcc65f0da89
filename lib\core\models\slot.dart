import 'dart:convert';

import 'package:seasetting/setting/mini_smart_library/models/cabinet.dart';

import 'book.dart';

class Slot {
  final String? cabinetNo; // 书柜编号
  final String? shelfNo; // 书架编号
  final String? slotNo; // 格口编号
  final String? barcode; // 书籍条形码
  final String? status; // 格口状态
  final String? slotType; // 格口类型
  final String? pickupCode; // 取书码
  final List<LockConfig>? lockConfigs; // 门锁配置
  final List<LightConfig>? lightConfigs; // 灯配置
  final List<ReaderConfig>? readerConfigs; // 阅读器配置
  final String? lastChecked; // 最后检查时间
  final String? lastOperator; // 最后操作人员
  final String? createdAt; // 创建时间
  final String? updatedAt; // 更新时间

  Slot({
    this.cabinetNo,
    this.shelfNo,
    this.slotNo,
    this.barcode,
    this.status,
    this.slotType,
    this.pickupCode,
    this.lockConfigs,
    this.lightConfigs,
    this.readerConfigs,
    this.lastChecked,
    this.lastOperator,
    this.createdAt,
    this.updatedAt,
  });

  // 从 JSON 创建对象
  factory Slot.fromJson(Map<String, dynamic> json) {
    return Slot(
      cabinetNo: json['cabinet_no'] as String?,
      shelfNo: json['shelf_no'] as String?,
      slotNo: json['slot_no'] as String?,
      barcode: json['barcode'] as String?,
      status: json['status'] as String?,
      slotType: json['slot_type'] as String?,
      pickupCode: json['pickupCode'] as String?,
      lockConfigs: json['lock_configs'] != null
          ? (json['lock_configs'] is String
          ? List<LockConfig>.from(jsonDecode(json['lock_configs']).map((item) => LockConfig.fromJson(item)))
          : List<LockConfig>.from(json['lock_configs'].map((item) => LockConfig.fromJson(item))))
          : null,
      lightConfigs: json['light_configs'] != null
          ? (json['light_configs'] is String
          ? List<LightConfig>.from(jsonDecode(json['light_configs']).map((item) => LightConfig.fromJson(item)))
          : List<LightConfig>.from(json['light_configs'].map((item) => LightConfig.fromJson(item))))
          : null,
      readerConfigs: json['reader_configs'] != null
          ? (json['reader_configs'] is String
          ? List<ReaderConfig>.from(jsonDecode(json['reader_configs']).map((item) => ReaderConfig.fromJson(item)))
          : List<ReaderConfig>.from(json['reader_configs'].map((item) => ReaderConfig.fromJson(item))))
          : null,
      lastChecked: json['lastChecked'] as String?,
      lastOperator: json['lastOperator'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );
  }

  // 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'cabinet_no': cabinetNo,
      'shelf_no': shelfNo,
      'slot_no': slotNo,
      'barcode': barcode,
      'status': status,
      'slot_type': slotType,
      'pickupCode': pickupCode,
      'lock_configs': lockConfigs != null ? jsonEncode(lockConfigs) : null,
      'light_configs': lightConfigs != null ? jsonEncode(lightConfigs) : null,
      'reader_configs': readerConfigs != null ? jsonEncode(readerConfigs) : null,
      'lastChecked': lastChecked,
      'lastOperator': lastOperator,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // 复制对象并修改部分属性
  Slot copyWith({
    String? cabinetNo,
    String? shelfNo,
    String? slotNo,
    String? barcode,
    String? status,
    String? slotType,
    String? pickupCode,
    List<LockConfig>? lockConfigs,
    List<LightConfig>? lightConfigs,
    List<ReaderConfig>? readerConfigs,
    String? lastChecked,
    String? lastOperator,
    String? createdAt,
    String? updatedAt,
  }) {
    return Slot(
      cabinetNo: cabinetNo ?? this.cabinetNo,
      shelfNo: shelfNo ?? this.shelfNo,
      slotNo: slotNo ?? this.slotNo,
      barcode: barcode ?? this.barcode,
      status: status ?? this.status,
      slotType: slotType ?? this.slotType,
      pickupCode: pickupCode ?? this.pickupCode,
      lockConfigs: lockConfigs ?? this.lockConfigs,
      lightConfigs: lightConfigs ?? this.lightConfigs,
      readerConfigs: readerConfigs ?? this.readerConfigs,
      lastChecked: lastChecked ?? this.lastChecked,
      lastOperator: lastOperator ?? this.lastOperator,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // 状态常量
  static const String statusEmpty = '0'; // 空闲
  static const String statusOccupied = '1'; // 占用
  static const String statusDisabled = '2'; // 禁用

  // 格口类型常量
  static const String typeNormal = '0'; // 普通区
  static const String typeReserve = '1'; // 预约区

  // 判断格口状态
  bool get isEmpty => status == statusEmpty;

  bool get isOccupied => status == statusOccupied;

  bool get isDisabled => status == statusDisabled;

  // 判断格口类型
  bool get isNormalType => slotType == typeNormal;

  bool get isReserveType => slotType == typeReserve;

  @override
  String toString() {
    return 'Slot(cabinetNo: $cabinetNo, slotNo: $slotNo, status: $status, type: $slotType)';
  }
}

class SlotOperate {
  Slot slot;
  int operate; // 1 上架  2 下架
  int status; // 0:成功 其他失败
  String msg;

  SlotOperate({
    required this.slot,
    required this.operate,
    required this.status,
    required this.msg,
  });
}

class SlotBookOperate {
  Slot slot;
  Book? book;
  OperateType operate;
  SlotOperationStatus status;
  String msg;
  String? date;

  SlotBookOperate({
    required this.slot,
    this.book,
    required this.operate,
    required this.status,
    required this.msg,
    this.date,
  });
  factory SlotBookOperate.fromJson(Map<String, dynamic> json) {
    return SlotBookOperate(
      slot: Slot.fromJson(json),
        operate: OperateType.borrow,
      book: json['book'] != null ? Book.fromJson(json['book']) : null,
      status: SlotOperationStatus.pending,
      msg: '',
      date: json['date'],
    );
  }
}

enum OperateType {
  shelf, // 上架
  unshelf, // 下架
  borrow,
  returnBook,
  renew,
  open,
  other
}

enum SlotOperationStatus {
  pending(0), // 待处理
  opening(1), // 正在打开
  opened(2), // 已打开
  scanning(3), // 扫描中
  closing(4), // 正在关闭
  completed(5), // 已完成
  error(-1); // 错误

  final int value;

  const SlotOperationStatus(this.value);
}




