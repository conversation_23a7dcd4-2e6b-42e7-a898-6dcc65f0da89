import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../../../core/router/app_router.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/widgets/base_page2.dart';
import '../../../core/widgets/countdown_timer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/footer.dart';
import '../../../shared/widgets/logo_banner.dart';
import '../../home/<USER>/home_view.dart';
import '../view_models/admin_login_view_model.dart';
import 'widgets/admin_login_v_content.dart';
import '../widgets/sys_timeout_button.dart';

class AdminLoginView extends StatelessWidget {
  const AdminLoginView({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AdminLoginViewModel(),
      child: const AdminLoginContent(),
    );
  }
}

class AdminLoginContent extends StatefulWidget {
  const AdminLoginContent({super.key});

  @override
  State<AdminLoginContent> createState() => _AdminLoginContentState();
}

class _AdminLoginContentState extends State<AdminLoginContent> {
  @override
  Widget build(BuildContext context) {
    return BasePage2(
      topWrapper: _buildHeader(),
      mainWrapper: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _buildMainContent(),
      ),
      // bottomWrapper: _buildFooter(),
    );
  }
    Widget _buildHeader() {
    return const HeaderWidget();
  }


  Widget _buildMainContent() {
    return Consumer<AdminLoginViewModel>(
      builder: (context, viewModel, child) {
        return AdminLoginVContent(
          items: viewModel.loginOptions,
          loginType: viewModel.loginType,
          onTypeSwitch: viewModel.switchLoginType,
          onLogin: viewModel.verifyAdmin,
        );
      },
    );
  }
}

