// ignore_for_file: must_be_immutable

import 'package:a3g/core/utils/window_util.dart';
import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';

import '../../../../generated/l10n.dart';

class ReaderCardAuthWidget extends StatelessWidget {
  final AuthLoginType loginType;
  
  const ReaderCardAuthWidget(this.loginType, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String icon = 'images/common/';
    if (loginType == AuthLoginType.IDCard) {
      icon += '刷身份证.gif';
    } else if (loginType == AuthLoginType.socailSecurityCard) {
      icon += '刷社保卡.gif';
    } else {
      icon += '刷读者证.gif';
    }
    
    return Consumer2<CurrentLayoutProvider, SettingProvider>(
      builder: (context, layoutProvider, settingProvider, child) {
        return ReaderCardBlueLogin(loginType, icon);
      }
    );
  }
}

class ReaderCardBlueLogin extends StatelessWidget {
  final AuthLoginType loginType;
  final String icon;

  const ReaderCardBlueLogin(this.loginType, this.icon, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取认证标题配置
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>()
        .readerConfigData?.authTitleConfig
        .where((element) => loginType == AuthLoginTypeMap[element.type])
        .firstOrNull;

    // 获取卡类型名称
    String name = '';
    if (loginType == AuthLoginType.socailSecurityCard) {
      name = S.current.socialSecurityCard;
    } else if (loginType == AuthLoginType.readerCard) {
      name = S.current.reader_card;
    } else if (loginType == AuthLoginType.IDCard) {
      name = S.current.identityCard;
    }

    // 标题文本
    final String titleText = (titleData?.title.isNotEmpty ?? false) 
        ? titleData!.title 
        : '$name${S.current.spaceLogin}';
    
    // 副标题文本
    final String subtitleText = (titleData?.subTitle.isNotEmpty ?? false) 
        ? titleData!.subTitle 
        : '${S.current.place}$name${S.current.at}$name${S.current.sensingArea}';

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        // color: Color(0xFFFF4D4F),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 标题
          Padding(
            padding: EdgeInsets.symmetric(vertical: 30.p),
            child: Text(
              titleText,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: BPUtils.c_ff12215F,
                fontWeight: FontWeight.w600,
                fontSize: 48.p,
              ),
            ),
          ),
          
          // 提示文本
          Container(
            margin: EdgeInsets.only(top: 20.p, bottom: 40.p),
            padding: EdgeInsets.symmetric(
              horizontal: 40.p,
              vertical: 12.p,
            ),
            decoration: BoxDecoration(
              color: BPUtils.c_FFB7CDFF,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.p),
                topRight: Radius.circular(16.p),
                bottomRight: Radius.circular(16.p),
                bottomLeft: Radius.circular(4.p),
              ),
            ),
            child: Text(
              subtitleText,
              style: TextStyle(
                color: BPUtils.c_FF1D62FD,
                fontWeight: FontWeight.normal,
                fontSize: 28.p,
              ),
            ),
          ),
          
          // 卡片图片
          Expanded(
            child: Center(
              child: Image.asset(
                icon,
                width: 560.p,
                height: 400.p,
                key: Key(icon),
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
 