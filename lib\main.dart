import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hardware/hardware.dart';
// import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:sealog/sealog.dart';
import 'package:seasetting/setting/navigatorKey.dart';

import 'app.dart';
import 'core/initializer/app_initializer.dart';
import 'core/providers/app_providers.dart';
import 'features/face_detection/baidu_face_recognition.dart';
import 'features/face_detection/face_detector.dart';
import 'features/face_detection/face_detector_app.dart';
import 'features/face_detection/face_recognition_service.dart';
import 'features/face_detection/sdk_config_manager.dart';

void main(){

  FlutterError.onError = (FlutterErrorDetails details) {
    SLLogCache.instance().writeFile(
      "catch exception:${details.exception.toString()},\n  stack:${details.stack?.toString()}",
      fullyWrite: true
    );
    if (details.stack != null) {
      Zone.current.handleUncaughtError(details.exception, details.stack!);
    }
  };
  // var navigatorKey = GlobalKey<NavigatorState>();

  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();
      
      // 初始化应用
      await AppInitializer().initialize();



      runApp(
        MultiProvider(
          providers: AppProviders.providers,
          child:  const App(),
        ),
      );
    },
    (error, stack) {
      SLLogCache.instance().writeFile(
        "Error:${error.toString()}\nStack：${stack.toString()}",
        fullyWrite: true
      );
    },
    zoneSpecification: ZoneSpecification(
      print: kDebugMode
        ? null
        : (Zone self, ZoneDelegate parent, Zone zone, String line) {
            SLLogCache.instance().writeFile(line);
          },
    ),
  );
}
