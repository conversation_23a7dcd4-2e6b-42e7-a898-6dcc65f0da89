import 'package:flutter/material.dart';

import 'dart:math' as math;

class FaceScannerEffect extends StatefulWidget {
  final double size;
  final Color startColor; // 渐变起始色
  final Color endColor; // 渐变结束色
  final double startOpacity; // 起始透明度
  final double endOpacity; // 结束透明度
  final Color borderColor;

  const FaceScannerEffect({
    Key? key,
    this.size = 300.0,
    this.startColor = const Color(0xFF4F83FC),
    this.endColor = const Color(0xFF4F83FC),
    this.startOpacity = 1.0, // 完全不透明
    this.endOpacity = 0.1, // 10%不透明
    this.borderColor = const Color(0xFF4F83FC),
  }) : super(key: key);

  @override
  _FaceScannerEffectState createState() => _FaceScannerEffectState();
}

class _FaceScannerEffectState extends State<FaceScannerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: 3),
    );
    // 临时禁用动画，不启动repeat
    // )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 临时禁用扫描器动画效果，返回空容器
    return Container(
      width: widget.size,
      height: widget.size,
      // 可以添加一个简单的静态边框替代动画
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: widget.borderColor.withOpacity(0.3),
          width: 2.0,
        ),
      ),
    );

    // 原始动画代码已注释
    // return Container(
    //   width: widget.size,
    //   height: widget.size,
    //   child: AnimatedBuilder(
    //     animation: _animationController,
    //     builder: (context, child) {
    //       return CustomPaint(
    //         painter: FaceScanPainter(
    //           progress: _animationController.value,
    //           startColor: widget.startColor,
    //           endColor: widget.endColor,
    //           startOpacity: widget.startOpacity,
    //           endOpacity: widget.endOpacity,
    //           borderColor: widget.borderColor,
    //         ),
    //         size: Size(widget.size, widget.size),
    //       );
    //     },
    //   ),
    // );
  }
}

class FaceScanPainter extends CustomPainter {
  final double progress;
  final Color startColor;
  final Color endColor;
  final double startOpacity;
  final double endOpacity;
  final Color borderColor;

  FaceScanPainter({
    required this.progress,
    required this.startColor,
    required this.endColor,
    required this.startOpacity,
    required this.endOpacity,
    required this.borderColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // 计算扫描线的Y位置 (从下往上扫描)
    final scanY = size.height - size.height * progress;

    // 保存当前画布状态
    canvas.save();

    // 裁剪为圆形区域
    Path clipPath = Path()
      ..addOval(Rect.fromCircle(center: center, radius: radius));
    canvas.clipPath(clipPath);

    // 绘制扫描过的区域
    final gradientRect = Rect.fromLTWH(
      0,
      scanY,
      size.width,
      size.height - scanY,
    );

    // 使用可调整透明度的渐变色
    final gradientPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          startColor.withOpacity(startOpacity),
          endColor.withOpacity(endOpacity),
        ],
      ).createShader(gradientRect);

    canvas.drawRect(gradientRect, gradientPaint);

    // 绘制新样式的扫描线
    if (scanY >= 0 && scanY <= size.height) {
      // 计算扫描线宽度（适应圆形区域）
      final lineWidth = radius * 2;

      // 扫描线矩形
      final scanLineRect = Rect.fromLTWH(
        center.dx - radius, // 从圆左边缘开始
        scanY - 1.5, // 居中放置3px高的线
        lineWidth, // 宽度等于圆直径
        3, // 高度为3px
      );

      // 使用指定的渐变颜色
      final scanLinePaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: const [
            Color(0x004F83FC), // rgba(79,131,252,0) - 透明
            Color(0xFFA9C3FF), // #A9C3FF - 中间浅蓝色
            Color(0x004F83FC), // rgba(79,131,252,0) - 透明
          ],
          stops: const [0.0, 0.49, 1.0],
        ).createShader(scanLineRect);

      // 绘制扫描线矩形
      canvas.drawRect(scanLineRect, scanLinePaint);
    }

    canvas.restore();

    // 绘制圆形边框
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawCircle(center, radius, borderPaint);
  }

  @override
  bool shouldRepaint(FaceScanPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.startOpacity != startOpacity ||
        oldDelegate.endOpacity != endOpacity;
  }
}