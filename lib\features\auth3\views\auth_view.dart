import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';

import '../../../core/providers/countdown_provider.dart';
import '../../../core/providers/user_provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/router/route_extension.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/widgets/countdown_timer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/footer.dart';
import '../../../shared/widgets/logo_banner.dart';
import '../../login/models/operation_type.dart';
import '../view_models/auth_view_model.dart';
import '../widget/AccountLoginView.dart';
import '../widget/AlipayLoginView.dart';
import '../widget/FaceLoginView.dart';
import '../widget/ReaderCardLoginView.dart';
import '../widget/ReaderEletricSSCardView.dart';

/// 认证视图 - 处理用户认证界面的显示和交互
class AuthView extends StatefulWidget {
  final AuthLoginType authLoginType;
  final OperationType operationType;

  const AuthView({
    super.key, 
    required this.authLoginType, 
    required this.operationType
  });

  @override
  State<AuthView> createState() => _AuthViewState();
}

class _AuthViewState extends State<AuthView> {
  AuthViewModel? _authViewModel; // Make nullable initially
  bool _viewModelReady = false; // Flag to track readiness

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Only initialize once
    if (_authViewModel == null) {
      _authViewModel = Provider.of<AuthViewModel>(context, listen: false);
      // Schedule the init call after the current frame
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) { // Check if the widget is still in the tree
          _authViewModel!.init(); // Call init on the non-null view model
          setState(() {
             _viewModelReady = true; // Mark as ready to trigger rebuild
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator until view model is initialized and ready
    if (!_viewModelReady || _authViewModel == null) {
      return const BasePage( // Maintain overall page structure if needed
        mainWrapper: Center(child: CircularProgressIndicator()),
      );
    }

    // Once ready, build the actual UI
    return Consumer<UserProvider>(
      builder: (context, userProvider, _) {
        return BasePage(
          topWrapper: const LogoBanner(tailWidget: AutoCountdown()),
          mainWrapper: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: userProvider.isAuthenticated == true
                ? _buildMainContent(context, widget.operationType)
                // Pass the now initialized _authViewModel!
                : ReaderCardAuth(vm: _authViewModel!, authLoginType: widget.authLoginType),
          ),
          bottomWrapper: userProvider.isAuthenticated == true
              ? const SizedBox.shrink()
              : _buildFooter(),
        );
      },
    );
  }

  Widget _buildFooter() {
    return Footer(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 80.p, vertical: 25.p),
        child: CustomButton.outline(
          text: '退出',
          onTap: AppNavigator.back,
        ),
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, OperationType operationType) {
    switch (operationType) {
      default:
        return const UnknownAuthType(); // Or a more specific error widget
    }
  }
}

/// 读者卡认证组件 - 根据不同的认证类型展示对应的认证界面
class ReaderCardAuth extends StatelessWidget {
  final AuthViewModel vm;
  final AuthLoginType authLoginType;

  const ReaderCardAuth({super.key, required this.authLoginType, required this.vm});

  @override
  Widget build(BuildContext context) {
    switch (authLoginType) {
      case AuthLoginType.readerCard:
        return ReaderCardLoginView(authLoginType);
        
      case AuthLoginType.faceAuth:
        return FaceLoginView(authLoginType);
        
      case AuthLoginType.eletricSocialSecurityCard:
        return ReaderEletricSSCardView(authLoginType,onSubmitted: vm.onScanContent2);
        
      case AuthLoginType.wechatScanQRCode:
        return const AuthContainer(
          icon: 'assets/icons/wechat_qr.png',
          title: '请出示微信二维码',
          subtitle: '微信扫码认证',
        );
        
      case AuthLoginType.huiwenQRCode:
        return const AuthContainer(
          icon: 'assets/icons/huiwen_qr.png',
          title: '请出示汇文码',
          subtitle: '汇文码认证',
        );
        
      case AuthLoginType.IDCard:
        return ReaderCardLoginView(authLoginType);

      case AuthLoginType.socailSecurityCard:
        return ReaderCardLoginView(authLoginType);

      case AuthLoginType.citizenCard:
        return const AuthContainer(
          icon: 'assets/icons/citizen_card.png',
          title: '请将市民卡放在感应区',
          subtitle: '市民卡认证',
        );

      case AuthLoginType.wechatQRCode:
        return ReaderEletricSSCardView(authLoginType,onSubmitted: vm.onScanContent2);

      case AuthLoginType.alipayQRCode:
        return AlipayLoginView('safdsa');

      case AuthLoginType.aliCreditQRCode:
        return const AuthContainer(
          icon: 'assets/icons/alipay_credit.png',
          title: '请出示芝麻信用码',
          subtitle: '芝麻信用认证',
        );

      case AuthLoginType.readerQRCode:
        return const AuthContainer(
          icon: 'assets/icons/qr_code.png',
          title: '请出示读者二维码',
          subtitle: '二维码读者认证',
        );

      case AuthLoginType.shangHaiQRCode:
        return const AuthContainer(
          icon: 'assets/icons/sh_qr.png',
          title: '请出示随申码',
          subtitle: '随申码认证',
        );

      case AuthLoginType.keyboardInput:
        return  AccountLoginView(
            callback: (account) {
              vm.requestReaderInfo(account, '');
            });

      case AuthLoginType.tencentTCard:
        return const AuthContainer(
          icon: 'assets/icons/tencent.png',
          title: '请打开腾讯E证通',
          subtitle: '腾讯E证通认证',
        );

      case AuthLoginType.IMIAuth:
        return const AuthContainer(
          icon: 'assets/icons/imi.png',
          title: '请进行IMI身份认证',
          subtitle: 'IMI身份认证',
        );

      case AuthLoginType.takePhoto:
        return const AuthContainer(
          icon: 'assets/icons/camera.png',
          title: '请对准摄像头',
          subtitle: '拍照认证',
        );

      case AuthLoginType.wecharOrAlipay:
        return const AuthContainer(
          icon: 'assets/icons/pay.png',
          title: '请出示付款码',
          subtitle: '微信/支付宝认证',
        );

      case AuthLoginType.alipayQRCode_credit:
        return const AuthContainer(
          icon: 'assets/icons/alipay_credit.png',
          title: '请出示支付宝付款码',
          subtitle: '支付宝信用认证',
        );

      case AuthLoginType.jieYueBao:
        return const AuthContainer(
          icon: 'assets/icons/jieyuebao.png',
          title: '请使用借阅宝',
          subtitle: '借阅宝认证',
        );

      case AuthLoginType.unknow:
      default:
        return const UnknownAuthType();
    }
  }
}

/// 未知认证类型组件
class UnknownAuthType extends StatelessWidget {
  const UnknownAuthType({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '未知的认证类型',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w500,
          color: Color(0xFF222222),
        ),
      ),
    );
  }
}

/// 认证容器组件 - 统一的认证界面容器
class AuthContainer extends StatelessWidget {
  final String icon;
  final String title;
  final String subtitle;
  final Widget? extraWidget;

  const AuthContainer({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.extraWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            icon, 
            width: 120.p, 
            height: 120.p,
            errorBuilder: (context, error, stackTrace) {
              debugPrint('无法加载图标: $error');
              return SizedBox(
                width: 120.p,
                height: 120.p,
                child: Icon(
                  Icons.credit_card,
                  size: 80.p,
                  color: const Color(0xFF999999),
                ),
              );
            },
          ),
          SizedBox(height: 40.p),
          Text(
            title,
            style: TextStyle(
              fontSize: 48.p,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF222222),
            ),
          ),
          SizedBox(height: 16.p),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 32.p,
              color: const Color(0xFF999999),
            ),
          ),
          if (extraWidget != null) ...[
            SizedBox(height: 40.p),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 80.p),
              child: extraWidget!,
            ),
          ],
        ],
      ),
    );
  }
}
