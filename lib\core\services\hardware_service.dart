import 'package:logging/logging.dart';
import 'package:seasetting/seasetting.dart';
import 'package:hardware/hardware.dart';

// Import the specific services
import 'lock_service.dart';
import 'reader_service.dart';

// Removed unnecessary imports like dart:convert, foundation, Get, hardware package

class HardwareService {
  static final HardwareService _instance = HardwareService._internal();
  factory HardwareService() => _instance;
  HardwareService._internal();

  // Get instances of the services to delegate initialization
  final LockService _lockService = LockService();
  final ReaderService _readerService = ReaderService();
  final _logger = Logger('HardwareService');

  /// Initializes all hardware components by delegating to specific services.
  /// This should typically be called once during application startup with all settings.
  Future<void> initializeAllHardware(List<HWReaderSettingData> allSettings) async {
    _logger.info('开始初始化所有硬件服务...');
    try {
      // 1. Initialize Locks
      final lockSettings = allSettings.where((s) => s.info != null && s.info is HWLockInfoData).toList();
      if (lockSettings.isNotEmpty) {
        await _lockService.initLocks(lockSettings);
         _logger.info('门锁初始化委托完成。');
      } else {
        _logger.info('没有找到门锁配置。');
      }

      // 2. Initialize Readers
      final readerSettings = allSettings.where((s) => s.info != null && 
          (s.info is HWRR9299InfoData ||
           s.info is HWUHFInfoData))
          .toList();
      if (readerSettings.isNotEmpty) {
        // Assuming ReaderService now has an initReaders method
        await _readerService.initReaders(readerSettings);
        _logger.info('阅读器初始化委托完成。');
      } else {
         _logger.info('没有找到阅读器配置用于初始化。');
      }

       _logger.info('硬件服务初始化流程完成。');

    } catch (e, stack) {
      _logger.severe('硬件服务初始化失败', e, stack);
      // Decide if initialization failure should prevent app start or be handled gracefully
      rethrow; // Rethrow to signal failure upstream
    }
  }

  // Removed all specific hardware operation methods (openDoor, startInventory, etc.)
  // These are now handled by LockService and ReaderService directly.

  /// Disposes resources managed by the underlying hardware services.
  void dispose() {
    _logger.info('开始释放所有硬件服务资源...');
    try {
      _lockService.dispose();
       _logger.info('LockService 资源已释放。');
    } catch (e, stack) {
      _logger.severe('释放 LockService 资源失败', e, stack);
    }
    try {
      _readerService.dispose();
      _logger.info('ReaderService 资源已释放。');
    } catch (e, stack) {
       _logger.severe('释放 ReaderService 资源失败', e, stack);
    }
     _logger.info('所有硬件服务资源释放流程完成。');
  }
}

// Removed the old initHardware method that mixed concerns.
// Removed direct instances of Lock/Reader services if they were meant to be used elsewhere.
// Keeping them here only for the initializeAllHardware delegation.
