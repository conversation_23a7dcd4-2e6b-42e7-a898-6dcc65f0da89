import 'package:dio/dio.dart';
import 'api_exception.dart';
import 'api_response.dart';
import 'dio_client.dart';
import 'interceptors/header_interceptor.dart';
export 'interceptors/header_interceptor.dart'; // 导出HeaderInterceptor类型，以便使用时不需要额外导入

/// API服务类
/// 封装所有的API请求，提供统一的接口
class ApiService {
  /// 单例模式
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;

  /// Dio客户端
  final DioClient _dioClient = DioClient();

  ApiService._internal();

  /// 初始化API服务
  void init({
    required String baseUrl,
    int? connectTimeout,
    int? receiveTimeout,
    bool? showLog,
    Map<String, dynamic>? headers,
  }) {
    _dioClient.init(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
      showLog: showLog,
      headers: headers,
    );
  }

  /// 设置认证令牌
  void setAuthToken(String token) {
    final headerInterceptor = _dioClient.dio.interceptors
        .whereType<HeaderInterceptor>().first;
    headerInterceptor.setAuthToken(token);
  }

  /// 清除认证令牌
  void clearAuthToken() {
    final headerInterceptor = _dioClient.dio.interceptors
        .whereType<HeaderInterceptor>().first;
    headerInterceptor.clearAuthToken();
  }

  /// 创建包裹（示例）
  /// [packageData] 包裹数据
  Future<ApiResponse<Map<String, dynamic>>> createPackage(
      Map<String, dynamic> packageData) async {
    try {
      return await _dioClient.post<Map<String, dynamic>>(
        '/Package/CreateO2OPackage',
        data: packageData,
      );
    } on ApiException {
      rethrow;
    }
  }

  /// 查询包裹详情（示例）
  /// [packageNo] 包裹编号
  Future<ApiResponse<Map<String, dynamic>>> queryPackage(String packageNo) async {
    try {
      return await _dioClient.post<Map<String, dynamic>>(
        '/Package/queryO2OPackageByNo',
        data: {
          'data': {
            'packageNo': packageNo,
          }
        },
      );
    } on ApiException {
      rethrow;
    }
  }

  /// 确认取件（示例）
  /// [packageNos] 包裹编号列表
  Future<ApiResponse<dynamic>> confirmPickUp(List<String> packageNos) async {
    try {
      return await _dioClient.post<dynamic>(
        '/Package/ConfirmPickUp',
        data: {
          'data': {
            'packageNo': packageNos,
          }
        },
      );
    } on ApiException {
      rethrow;
    }
  }

  /// 包裹投递上架（示例）
  /// [mac] 设备MAC地址
  /// [cell] 格子号
  /// [servicePointCode] 服务点编码
  /// [packageNo] 包裹号
  Future<ApiResponse<bool>> uploadPackage({
    required String mac,
    required String cell,
    required String servicePointCode,
    required String packageNo,
  }) async {
    try {
      return await _dioClient.post<bool>(
        '/equipment/upload/package',
        data: {
          'data': {
            'mac': mac,
            'cell': cell,
            'serveciePointCode': servicePointCode,
            'o2OPackageNo': packageNo,
          }
        },
      );
    } on ApiException {
      rethrow;
    }
  }

  /// 查询逾期包裹（示例）
  /// [servicePointCode] 服务点编码
  Future<ApiResponse<List<dynamic>>> queryOverduePackages(
      String servicePointCode) async {
    try {
      return await _dioClient.post<List<dynamic>>(
        '/Package/QueryO2OOverduePackage',
        data: {
          'data': {
            'servicePointCode': servicePointCode,
          }
        },
      );
    } on ApiException {
      rethrow;
    }
  }
} 