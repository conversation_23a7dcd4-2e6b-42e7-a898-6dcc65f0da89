﻿#include <opencv2/opencv.hpp>
#include <opencv2/objdetect.hpp>
#include <opencv2/dnn.hpp>
#include <vector>
#include <iostream>
#include <fstream>
#include <cstdlib>
#include <string>
#include <mutex>
#include <thread>
#include <chrono>
#include <queue>
#include <condition_variable>
#include <atomic>
#include <cmath>
#include <limits>
#include <algorithm>
#include <iomanip>
#include <ctime> // 添加时间戳支持

// Function pointer for face detection callback  
typedef void (*FaceDetectionCallback)(const unsigned char* imageData, int imageSize, float confidence);
static FaceDetectionCallback g_face_detection_callback = nullptr;

// 人脸缓存系统 - 轮询机制（按需编码优化版本）
struct FaceCapture {
    cv::Mat rawImageMat;  // 存储原始Mat数据，按需编码
    std::vector<unsigned char> imageData;  // 编码后的JPEG数据（按需生成）
    float confidence;
    std::chrono::steady_clock::time_point timestamp;
    bool isValid;
    bool isEncoded;  // 标记是否已编码

    FaceCapture() : confidence(0.0f), isValid(false), isEncoded(false) {}

    // 清理方法
    void clear() {
        rawImageMat.release();
        imageData.clear();
        confidence = 0.0f;
        isValid = false;
        isEncoded = false;
    }
};

// 避免使用mutex锁防止崩溃
// static std::mutex g_face_capture_mutex;
static std::shared_ptr<FaceCapture> g_latest_face_capture_ptr;
static std::atomic<bool> g_has_new_face_capture(false);

// 捕获间隔控制（考勤认证场景需要合理间隔）
static std::chrono::steady_clock::time_point g_last_capture_time;
static const int CAPTURE_INTERVAL_MILLISECONDS = 1000; // 1000毫秒间隔，考勤认证

#ifdef _WIN32
#include <windows.h>
#include <direct.h> // 用于_getcwd
#endif

// 添加日志系统 - 避免使用mutex锁防止崩溃
// std::mutex g_log_mutex;
std::string g_log_file = "face_camera_debug.log";

// 日志函数，记录详细运行日志 - 移除mutex锁
void log_message(const std::string& message) {
    // 避免使用mutex锁防止崩溃
    // std::lock_guard<std::mutex> lock(g_log_mutex);
    std::ofstream log_file(g_log_file, std::ios::app);
    if (log_file.is_open()) {
        // 添加时间戳
        auto now = std::chrono::system_clock::now();
        auto now_time = std::chrono::system_clock::to_time_t(now);
        struct tm timeinfo;
#ifdef _WIN32
        localtime_s(&timeinfo, &now_time);
#else
        localtime_r(&now_time, &timeinfo);
#endif
        char time_str[30];
        std::strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", &timeinfo);
        
        // 写入日志
        log_file << "[" << time_str << "] " << message << std::endl;
    }
}

// 带错误级别的日志
void log_error(const std::string& message) {
    log_message("[ERROR] " + message);
}

void log_info(const std::string& message) {
    log_message("[INFO] " + message);
}

void log_warning(const std::string& message) {
    log_message("[WARNING] " + message);
}

// 日志系统初始化
void init_logging() {
    // 创建或打开日志文件，写入开始记录标记
    std::ofstream log_file(g_log_file, std::ios::app);
    if (log_file.is_open()) {
        auto now = std::chrono::system_clock::now();
        time_t now_time = std::chrono::system_clock::to_time_t(now);
        log_file << "\n\n===== 日志记录开始: " << std::put_time(std::localtime(&now_time), "%Y-%m-%d %H:%M:%S") << " =====\n" << std::endl;
    }
    
    // 记录系统信息
#ifdef _WIN32
    char windows_version[256] = {0};
    OSVERSIONINFOA osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOA));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOA);
    
    // 由于GetVersionEx已被Windows弃用，使用其他方式获取版本信息
    NTSTATUS(WINAPI *RtlGetVersion)(LPOSVERSIONINFOA) = NULL;
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (hNtdll) {
        *(FARPROC*)&RtlGetVersion = GetProcAddress(hNtdll, "RtlGetVersion");
        if (RtlGetVersion) {
            RtlGetVersion(&osvi);
            sprintf_s(windows_version, "Windows Version: %d.%d.%d", 
                    osvi.dwMajorVersion, osvi.dwMinorVersion, osvi.dwBuildNumber);
        }
    }
    
    // 记录系统信息
    log_info(std::string("操作系统: ") + windows_version);
    
    // 记录当前目录
    char current_dir[MAX_PATH] = {0};
    if (_getcwd(current_dir, MAX_PATH)) {
        log_info(std::string("当前工作目录: ") + current_dir);
    }
    
    // 记录可执行文件路径
    char exe_path[MAX_PATH] = {0};
    if (GetModuleFileNameA(NULL, exe_path, MAX_PATH)) {
        log_info(std::string("可执行文件路径: ") + exe_path);
    }
#else
    // Linux/macOS系统信息
    log_info("操作系统: 非Windows系统");
    
    // 获取当前目录
    char current_dir[PATH_MAX];
    if (getcwd(current_dir, PATH_MAX)) {
        log_info(std::string("当前工作目录: ") + current_dir);
    }
#endif
}

// Windows平台导出宏
#ifdef _WIN32
    #ifdef FACE_DETECTOR_EXPORTS
        #define EXPORT extern "C" __declspec(dllexport)
    #else
        #define EXPORT extern "C" __declspec(dllimport)
    #endif
#endif

// 全局变量
std::string g_models_dir;
std::string g_yunet_model_path;

// 人脸检测器 - 使用YuNet
cv::Ptr<cv::FaceDetectorYN> g_face_detector_yunet;
bool g_detector_initialized = false;

// YuNet 参数 (可以根据需要调整)
const float g_yunet_confThreshold = 0.85f;  // 置信度阈值调整为0.7f，提高准确性
const float g_yunet_nmsThreshold = 0.3f;
const int g_yunet_topK = 5000;

// 显示帧率和调试信息
bool g_show_fps = false; // 改为默认不显示
int g_detection_fps = 0; // 保留变量以保持API兼容，但不再更新
std::chrono::steady_clock::time_point g_detection_fps_last_time = std::chrono::steady_clock::now();
int g_detection_fps_counter = 0;
int g_last_detection_time = 0;  // 上次检测耗时（毫秒）
int g_avg_detection_time = 0;   // 平均检测耗时（毫秒）
const int g_avg_time_samples = 10; // 平均耗时的采样数
std::vector<int> g_time_samples; // 存储最近的检测时间

// 控制使用哪种检测器 - 只用YuNet
bool g_use_haar_detector = false; // 始终为false，只用于API兼容

// 控制是否显示人脸框
bool g_show_face_boxes = true; // 默认显示人脸框

// 限制处理速率
int g_last_process_time = 0;
int g_processing_interval = 30; // 毫秒，从100降低到30以提高检测频率
int g_system_load = 0; // 系统负载指标(0-100)

// 全局帧缓冲区 - 避免使用mutex锁防止崩溃
std::shared_ptr<cv::Mat> g_current_frame_ptr;      // 用于检测的帧
std::shared_ptr<cv::Mat> g_display_frame_ptr;      // 用于显示的帧
std::atomic<bool> g_frame_ready{false};
// 移除mutex锁以避免死锁问题
// std::mutex g_frame_mutex;
// std::mutex g_display_mutex;

// 摄像头变量 - 改进版本，参考camera项目
std::shared_ptr<cv::VideoCapture> g_capture_ptr;
std::shared_ptr<cv::Mat> g_camera_frame_ptr;
// 避免使用mutex锁防止崩溃
// std::mutex g_camera_mutex;
std::thread g_camera_thread;
std::atomic<bool> g_is_camera_running{false};
std::string g_last_error_message;
std::string g_current_backend;
int g_frame_width = 0;
int g_frame_height = 0;
int g_frame_channels = 3;
int g_original_width = 0;
int g_original_height = 0;
int g_jpeg_quality = 60;
int g_fps_counter = 0;
int g_current_fps = 0;
std::chrono::steady_clock::time_point g_fps_last_time = std::chrono::steady_clock::now();

// JPEG编码变量 - 按需编码模式，简化变量
// 移除了后台编码线程，改为按需编码提高效率
// std::thread g_jpeg_thread;  // 已废弃
// std::queue<cv::Mat> g_jpeg_queue;  // 已废弃
// std::atomic<bool> g_is_jpeg_thread_running{false};  // 已废弃
// std::shared_ptr<std::vector<uchar>> g_current_jpeg_data_ptr;  // 已废弃
// std::mutex g_jpeg_data_mutex;

// 上次检测的人脸结果 - 用于平滑
std::vector<cv::Rect> g_last_faces;
std::vector<cv::Rect> g_prev_last_faces; // 前一帧的人脸位置，用于运动预测
double g_smoothing_factor = 0.2; // 平滑因子调整为0.2，减少平滑强度以提高准确性

// 用于检测线程的变量 - 避免使用mutex锁防止崩溃
std::thread g_detection_thread;
std::atomic<bool> g_is_detection_thread_running{false};
// 避免使用mutex锁防止崩溃
// std::mutex g_detection_mutex;
// std::condition_variable g_detection_condition;
std::queue<cv::Mat> g_detection_queue;
// std::mutex g_detection_result_mutex;
std::shared_ptr<std::vector<cv::Rect>> g_detection_result_ptr;
std::atomic<bool> g_detection_in_progress{false};
std::atomic<int> g_detection_interval{50}; // 毫秒，从80降低到30提高响应性

// 录像相关变量 - 避免使用mutex锁防止崩溃
std::shared_ptr<cv::VideoWriter> g_video_writer_ptr;
std::atomic<bool> g_is_recording{false};
std::string g_recording_path;
// 避免使用mutex锁防止崩溃
// std::mutex g_recording_mutex;

// 人脸信息结构
struct FaceInfo {
    cv::Rect bbox;                       // 人脸边界框
    float score;                         // 置信度分数
    std::vector<cv::Point2f> landmarks;  // YuNet输出关键点是Point2f
    cv::Point2f velocity;                // 运动速度（像素/帧）
    
    FaceInfo() : score(0), velocity(0, 0) {}
};

// 添加中文路径支持的辅助函数
#ifdef _WIN32
// UTF-8转宽字符
std::wstring utf8_to_wstring(const std::string& str) {
    if (str.empty()) return std::wstring();
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstr(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstr[0], size_needed);
    return wstr;
}

// Windows平台需要特殊处理文件路径
bool saveImageToPath(const cv::Mat& image, const std::string& path) {
    // 打印原始路径的十六进制值，方便调试
    std::cout << "保存图像到路径 (原始): " << path << std::endl;
    std::cout << "路径十六进制表示: ";
    for (unsigned char c : path) {
        std::cout << std::hex << std::setw(2) << std::setfill('0') << (int)c << " ";
    }
    std::cout << std::dec << std::endl;
    
    // 标准化路径 - 使用正斜杠(/)
    std::string normalized_path = path;
    for (char& c : normalized_path) {
        if (c == '\\') c = '/';
    }
    std::cout << "标准化后的路径: " << normalized_path << std::endl;
    log_info("准备保存图像到标准化路径: " + normalized_path);
    
    // 确保目录存在
    std::string dir_path = normalized_path.substr(0, normalized_path.find_last_of('/'));
    std::wstring wdir_path = utf8_to_wstring(dir_path);
    
    if (!dir_path.empty()) {
        DWORD dir_attr = GetFileAttributesW(wdir_path.c_str());
        if (dir_attr == INVALID_FILE_ATTRIBUTES || !(dir_attr & FILE_ATTRIBUTE_DIRECTORY)) {
            log_info("目录不存在，尝试创建: " + dir_path);
            // 递归创建目录
            std::wstring current_path;
            for (size_t i = 0; i < wdir_path.length(); ++i) {
                if (wdir_path[i] == L'/' || wdir_path[i] == L'\\' || i == wdir_path.length() - 1) {
                    if (!current_path.empty()) {
                        CreateDirectoryW(current_path.c_str(), NULL);
                    }
                }
                current_path += wdir_path[i];
            }
            CreateDirectoryW(wdir_path.c_str(), NULL);
            
            // 再次检查目录是否创建成功
            dir_attr = GetFileAttributesW(wdir_path.c_str());
            if (dir_attr == INVALID_FILE_ATTRIBUTES || !(dir_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                log_error("无法创建目录: " + dir_path);
                std::cerr << "无法创建目录: " << dir_path << std::endl;
            } else {
                log_info("成功创建目录: " + dir_path);
            }
        }
    }
    
    std::wstring wpath = utf8_to_wstring(normalized_path);
    std::cout << "UTF-8转换为宽字符路径字符数: " << wpath.length() << std::endl;
    
    // 检查目标路径不是目录
    DWORD file_attr = GetFileAttributesW(wpath.c_str());
    if (file_attr != INVALID_FILE_ATTRIBUTES && (file_attr & FILE_ATTRIBUTE_DIRECTORY)) {
        log_error("错误: 目标路径是一个目录，无法保存图像: " + normalized_path);
        std::cerr << "错误: 目标路径是一个目录，无法保存图像: " << normalized_path << std::endl;
        return false;
    }
    
    // 先尝试使用OpenCV的标准方式
    std::vector<int> params;
    params.push_back(cv::IMWRITE_JPEG_QUALITY);
    params.push_back(95); // 高质量JPEG
    
    try {
        log_info("尝试使用OpenCV imwrite保存图像");
        bool result = cv::imwrite(normalized_path, image, params);
        if (result) {
            std::cout << "OpenCV imwrite成功保存图像" << std::endl;
            log_info("OpenCV imwrite成功保存图像");
            
            // 验证文件是否正确保存
            DWORD check_attr = GetFileAttributesW(wpath.c_str());
            if (check_attr != INVALID_FILE_ATTRIBUTES && !(check_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                log_info("已验证文件成功保存: " + normalized_path);
                return true;
            } else {
                log_warning("文件属性检查失败，尝试备用方法");
            }
        }
        std::cerr << "OpenCV imwrite保存失败，尝试备用方法" << std::endl;
        log_warning("OpenCV imwrite保存失败，尝试备用方法");
    } catch(const cv::Exception& ex) {
        std::cerr << "OpenCV保存异常: " << ex.what() << std::endl;
        log_error("OpenCV保存异常: " + std::string(ex.what()));
    }
    
    // 如果标准方式失败，尝试使用Windows API
    try {
        log_info("尝试使用Windows API保存图像");
        // 编码到内存中
        std::vector<uchar> buf;
        cv::imencode(".jpg", image, buf, params);
        std::cout << "图像已编码为JPEG，大小: " << buf.size() << " 字节" << std::endl;
        log_info("图像已编码为JPEG，大小: " + std::to_string(buf.size()) + " 字节");
        
        // 使用Windows API直接写入文件
        HANDLE hFile = CreateFileW(
            wpath.c_str(),
            GENERIC_WRITE,
            0,
            NULL,
            CREATE_ALWAYS,
            FILE_ATTRIBUTE_NORMAL,
            NULL
        );
        
        if (hFile == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            std::cerr << "CreateFileW错误码: " << error << std::endl;
            log_error("CreateFileW错误码: " + std::to_string(error));
            
            // 如果出错，尝试使用系统临时文件路径
            wchar_t temp_path[MAX_PATH];
            GetTempPathW(MAX_PATH, temp_path);
            std::wstring temp_file = std::wstring(temp_path) + L"face_photo_temp.jpg";
            std::cout << "尝试保存到临时文件" << std::endl;
            log_info("尝试保存到临时文件");
            
            hFile = CreateFileW(
                temp_file.c_str(),
                GENERIC_WRITE,
                0,
                NULL,
                CREATE_ALWAYS,
                FILE_ATTRIBUTE_NORMAL,
                NULL
            );
            
            if (hFile == INVALID_HANDLE_VALUE) {
                std::cerr << "临时文件创建也失败，错误码: " << GetLastError() << std::endl;
                log_error("临时文件创建也失败，错误码: " + std::to_string(GetLastError()));
                return false;
            } else {
                log_info("已创建临时文件");
            }
        }
        
        DWORD bytesWritten;
        BOOL result = WriteFile(
            hFile,
            buf.data(),
            static_cast<DWORD>(buf.size()),
            &bytesWritten,
            NULL
        );
        
        CloseHandle(hFile);
        
        if (result && bytesWritten == buf.size()) {
            std::cout << "Windows API成功写入文件，已写入: " << bytesWritten << " 字节" << std::endl;
            log_info("Windows API成功写入文件，已写入: " + std::to_string(bytesWritten) + " 字节");
            return true;
        } else {
            std::cerr << "WriteFile失败，错误码: " << GetLastError() 
                    << "，已写入: " << bytesWritten << "/" << buf.size() << " 字节" << std::endl;
            log_error("WriteFile失败，错误码: " + std::to_string(GetLastError()) + 
                    "，已写入: " + std::to_string(bytesWritten) + "/" + std::to_string(buf.size()) + " 字节");
        }
        return (result != 0);
    } catch (const std::exception& e) {
        std::cerr << "保存图像失败: " << e.what() << std::endl;
        log_error("保存图像一般异常: " + std::string(e.what()));
        return false;
    }
}

// 创建支持中文路径的VideoWriter
bool createVideoWriter(cv::VideoWriter& writer, const std::string& path, int fourcc, double fps, const cv::Size& frameSize) {
    std::wstring wpath = utf8_to_wstring(path);
    log_info("创建VideoWriter，路径: " + path);
    log_info("帧大小: " + std::to_string(frameSize.width) + "x" + std::to_string(frameSize.height) + ", FPS: " + std::to_string(fps));
    
    // 将fourcc转为可读形式
    char fourcc_str[5] = {0};
    fourcc_str[0] = static_cast<char>((fourcc & 0XFF));
    fourcc_str[1] = static_cast<char>((fourcc & 0XFF00) >> 8);
    fourcc_str[2] = static_cast<char>((fourcc & 0XFF0000) >> 16);
    fourcc_str[3] = static_cast<char>((fourcc & 0XFF000000) >> 24);
    log_info("尝试使用编解码器: '" + std::string(fourcc_str) + "' (值: " + std::to_string(fourcc) + ")");
    
    try {
        // 先尝试常规方法
        log_info("尝试使用标准方法打开VideoWriter");
        if (writer.open(path, fourcc, fps, frameSize)) {
            log_info("使用标准方法成功创建VideoWriter");
            return true;
        }
        
        // 获取OpenCV构建信息，检查支持的编解码器
        log_info("OpenCV构建信息: " + cv::getBuildInformation());
        
        // 如果失败，可能是路径问题，输出错误信息
        log_error("标准方法创建视频失败，尝试其他方式");
        std::cerr << "常规方法创建视频失败，尝试其他方式: " << path << std::endl;
        
        // 可以尝试创建临时文件然后复制或使用不同编码器
        // 这里我们尝试不同编码器
        int codecs[] = {
            cv::VideoWriter::fourcc('X', '2', '6', '4'),  // H.264
            cv::VideoWriter::fourcc('H', '2', '6', '4'),  // 另一种H.264表示
            cv::VideoWriter::fourcc('a', 'v', 'c', '1'),  // AVC1 (H.264的另一种表示)
            cv::VideoWriter::fourcc('X', 'V', 'I', 'D'),  // XVID
            cv::VideoWriter::fourcc('M', 'J', 'P', 'G'),  // MJPEG
            cv::VideoWriter::fourcc('M', 'P', '4', 'V'),  // MP4V
            cv::VideoWriter::fourcc('D', 'I', 'V', 'X'),  // DIVX
            cv::VideoWriter::fourcc('W', 'M', 'V', '2'),  // WMV2
            cv::VideoWriter::fourcc('W', 'M', 'V', '1'),  // WMV1
            cv::VideoWriter::fourcc('F', 'L', 'V', '1'),  // FLV1
            cv::VideoWriter::fourcc('V', 'P', '8', '0'),  // VP80
            cv::VideoWriter::fourcc('T', 'H', 'E', 'O'),  // THEO
            cv::VideoWriter::fourcc('I', '4', '2', '0'),  // I420 (未压缩YUV)
            0                                             // 未压缩AVI
        };
        
        for (int codec : codecs) {
            if (codec == fourcc) continue; // 跳过已经尝试过的
            
            // 将当前尝试的codec转为可读形式
            char codec_str[5] = {0};
            if (codec != 0) {
                codec_str[0] = static_cast<char>((codec & 0XFF));
                codec_str[1] = static_cast<char>((codec & 0XFF00) >> 8);
                codec_str[2] = static_cast<char>((codec & 0XFF0000) >> 16);
                codec_str[3] = static_cast<char>((codec & 0XFF000000) >> 24);
            } else {
                strcpy(codec_str, "RAW");
            }
            
            log_info("尝试备用编解码器: '" + std::string(codec_str) + "' (值: " + std::to_string(codec) + ")");
            
            if (writer.open(path, codec, fps, frameSize)) {
                log_info("使用备用编解码器 '" + std::string(codec_str) + "' 创建视频成功");
                std::cout << "使用备用编码器创建视频成功" << std::endl;
                return true;
            } else {
                log_warning("编解码器 '" + std::string(codec_str) + "' 创建失败");
            }
        }
        
        // 不再尝试在临时目录创建视频，而是返回失败
        log_info("所有编解码器在用户指定路径都失败，返回失败状态");
        
        log_error("所有编解码器都尝试失败，无法创建视频文件");
        return false;
    } catch (const cv::Exception& e) {
        log_error("创建视频writer异常: " + std::string(e.what()));
        std::cerr << "创建视频writer异常: " << e.what() << std::endl;
        return false;
    } catch (const std::exception& e) {
        log_error("创建视频一般异常: " + std::string(e.what()));
        std::cerr << "创建视频一般异常: " << e.what() << std::endl;
        return false;
    }
}
#else
// 非Windows平台直接使用OpenCV的imwrite
bool saveImageToPath(const cv::Mat& image, const std::string& path) {
    std::vector<int> params;
    params.push_back(cv::IMWRITE_JPEG_QUALITY);
    params.push_back(95); // 高质量JPEG
    return cv::imwrite(path, image, params);
}

// 非Windows平台直接使用OpenCV的VideoWriter
bool createVideoWriter(cv::VideoWriter& writer, const std::string& path, int fourcc, double fps, const cv::Size& frameSize) {
    return writer.open(path, fourcc, fps, frameSize);
}
#endif

// 检查文件是否存在
bool fileExists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

// 前向声明
bool initialize_detector();
std::vector<FaceInfo> smoothFaceResults(const std::vector<FaceInfo>& current_faces_info);
void faceDetectionThread();
bool detectFaces_YuNet(const cv::Mat& frame, std::vector<FaceInfo>& face_info);
void drawFPSandBoxes(cv::Mat& frame, const std::vector<FaceInfo>& faces_info, double fps, int avg_fps, int system_fps, bool is_hd, int recording_state, bool add_landmarks);
// 新增前向声明，防止循环依赖
EXPORT void stop_camera();
EXPORT bool stop_recording();

// 辅助函数：设置错误信息
void setLastError(const std::string& error) {
    g_last_error_message = error;
    log_error("OpenCV Error: " + error);
}

// 辅助函数：多后端摄像头打开 - 参考camera项目
bool openCameraWithMultipleBackends(int camera_id, std::shared_ptr<cv::VideoCapture>& cap_ptr) {
    log_info("开始多后端摄像头打开尝试，ID=" + std::to_string(camera_id));

    bool opened = false;
    std::string successful_backend = "";

    // 确保智能指针已初始化
    if (!cap_ptr) {
        cap_ptr = std::make_shared<cv::VideoCapture>();
    }

    // 方案1：尝试DirectShow后端（Windows推荐）
#ifdef _WIN32
    log_info("尝试方案1: DirectShow后端");
    try {
        if (cap_ptr->open(camera_id, cv::CAP_DSHOW)) {
            opened = true;
            successful_backend = "DirectShow";
            log_info("成功使用DirectShow后端打开摄像头");
        } else {
            log_warning("DirectShow后端打开失败");
        }
    } catch (const cv::Exception& e) {
        log_error("DirectShow后端异常: " + std::string(e.what()));
    } catch (...) {
        log_error("DirectShow后端未知异常");
    }

    // 方案2：如果DirectShow失败，尝试MediaFoundation后端
    if (!opened) {
        log_info("尝试方案2: MediaFoundation后端");
        try {
            if (cap_ptr->open(camera_id, cv::CAP_MSMF)) {
                opened = true;
                successful_backend = "MediaFoundation";
                log_info("成功使用MediaFoundation后端打开摄像头");
            } else {
                log_warning("MediaFoundation后端打开失败");
            }
        } catch (const cv::Exception& e) {
            log_error("MediaFoundation后端异常: " + std::string(e.what()));
        } catch (...) {
            log_error("MediaFoundation后端未知异常");
        }
    }
#endif

    // 方案3：如果前面都失败，尝试默认后端
    if (!opened) {
        log_info("尝试方案3: 默认后端");
        try {
            if (cap_ptr->open(camera_id)) {
                opened = true;
                successful_backend = "Default";
                log_info("成功使用默认后端打开摄像头");
            } else {
                log_warning("默认后端打开失败");
            }
        } catch (const cv::Exception& e) {
            log_error("默认后端异常: " + std::string(e.what()));
        } catch (...) {
            log_error("默认后端未知异常");
        }
    }

    if (opened) {
        g_current_backend = successful_backend;
        log_info("摄像头打开成功，使用后端: " + successful_backend);
        return true;
    } else {
        setLastError("所有后端都无法打开摄像头，ID=" + std::to_string(camera_id));
        return false;
    }
}

// 添加 initialize_detector 函数的实现
bool initialize_detector() {
    log_info("=== 开始初始化人脸检测器 ===");
    // 写一些初始化日志到文件，便于排查问题
    std::ofstream debug_log("face_detector_debug.log", std::ios::app);
    debug_log << "=== 开始初始化人脸检测器 ===" << std::endl;
    
    if (g_detector_initialized) {
        debug_log << "检测器已经初始化，直接返回" << std::endl;
        return true;
    }

#ifdef _WIN32
    // 获取可执行文件路径
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    std::string exe_path = path;
    debug_log << "可执行文件路径: " << exe_path << std::endl;
    
    std::string exe_dir = exe_path.substr(0, exe_path.find_last_of("\\/"));
    debug_log << "可执行文件目录: " << exe_dir << std::endl;
    
    // models 目录应该在 exe 文件同级的 data 目录下
    g_models_dir = exe_dir + "\\data\\models";
#else
    // Linux/macOS: 假设 models 目录在可执行文件同级的 data 目录下
     char path[PATH_MAX];
     ssize_t count = readlink("/proc/self/exe", path, PATH_MAX);
     std::string exe_path = std::string(path, (count > 0) ? count : 0);
     std::string exe_dir = exe_path.substr(0, exe_path.find_last_of("/"));
     g_models_dir = exe_dir + "/data/models";
#endif

    debug_log << "模型目录设置为: " << g_models_dir << std::endl;
    std::cout << "模型目录: " << g_models_dir << std::endl;

    // YuNet模型文件路径 (假设模型文件名为 face_detection_yunet_2023mar.onnx)
    g_yunet_model_path = g_models_dir + "/face_detection_yunet_2023mar.onnx"; // Update model filename
    debug_log << "尝试模型路径: " << g_yunet_model_path << std::endl;

    if (!fileExists(g_yunet_model_path)) {
        debug_log << "错误: YuNet模型文件未找到: " << g_yunet_model_path << std::endl;
        std::cerr << "错误: YuNet模型文件未找到: " << g_yunet_model_path << std::endl;
        
        // 如果在exe/data/models下找不到，尝试在项目根目录的models下查找 (用于开发环境)
        g_yunet_model_path = "models/face_detection_yunet_2023mar.onnx";
        debug_log << "尝试备用模型路径: " << g_yunet_model_path << std::endl;
        
        if (!fileExists(g_yunet_model_path)) {
            debug_log << "错误: 在项目根目录 models/ 下也未找到 YuNet 模型文件: " << g_yunet_model_path << std::endl;
            std::cerr << "错误: 在项目根目录 models/ 下也未找到 YuNet 模型文件: " << g_yunet_model_path << std::endl;
            debug_log << "=== 初始化失败 ===" << std::endl;
            return false;
        } else {
            debug_log << "在项目根目录 models/ 下找到模型文件: " << g_yunet_model_path << std::endl;
            std::cout << "在项目根目录 models/ 下找到模型文件: " << g_yunet_model_path << std::endl;
        }
    }

    debug_log << "正在加载 YuNet 模型: " << g_yunet_model_path << std::endl;
    std::cout << "正在加载 YuNet 模型: " << g_yunet_model_path << std::endl;

    try {
        debug_log << "创建 YuNet 检测器实例，参数：" << std::endl;
        debug_log << "- 模型文件: " << g_yunet_model_path << std::endl;
        debug_log << "- 输入尺寸: 320x240" << std::endl;  // 恢复为320×240，这是原本正常工作的尺寸
        debug_log << "- 置信度阈值: " << g_yunet_confThreshold << std::endl;
        debug_log << "- NMS阈值: " << g_yunet_nmsThreshold << std::endl;
        debug_log << "- topK: " << g_yunet_topK << std::endl;
         
        // 创建 YuNet 检测器实例 - 增加输入尺寸以提高精度
        g_face_detector_yunet = cv::FaceDetectorYN::create(
            g_yunet_model_path,
            "", // config file (usually not needed for ONNX)
            cv::Size(320, 240), // 恢复为320×240，这是原本正常工作的尺寸
            g_yunet_confThreshold,
            g_yunet_nmsThreshold,
            g_yunet_topK
        );

        if (!g_face_detector_yunet) {
            debug_log << "错误: 创建YuNet检测器失败，返回值为null" << std::endl;
            std::cerr << "错误: 创建YuNet检测器失败." << std::endl;
            debug_log << "=== 初始化失败 ===" << std::endl;
            return false;
        }

        debug_log << "YuNet 检测器创建成功，尝试进行虚拟检测..." << std::endl;
        
        // 尝试进行一次虚拟检测以确保模型加载成功
        cv::Mat dummy_frame(240, 320, CV_8UC3, cv::Scalar(0, 0, 0));  // 恢复为匹配320×240的尺寸
        cv::Mat faces;
        
        
        debug_log << "调用 detect() 方法..." << std::endl;
        g_face_detector_yunet->detect(dummy_frame, faces);
        debug_log << "虚拟检测成功完成" << std::endl;

    } catch (const cv::Exception& e) {
        debug_log << "加载或初始化YuNet模型时发生OpenCV错误: " << e.what() << std::endl;
        std::cerr << "加载或初始化YuNet模型时发生OpenCV错误: " << e.what() << std::endl;
        debug_log << "=== 初始化失败 ===" << std::endl;
        return false;
    } catch (const std::exception& e) {
        debug_log << "加载或初始化YuNet模型时发生错误: " << e.what() << std::endl;
        std::cerr << "加载或初始化YuNet模型时发生错误: " << e.what() << std::endl;
        debug_log << "=== 初始化失败 ===" << std::endl;
        return false;
    }

    debug_log << "YuNet 模型加载并初始化成功" << std::endl;
    debug_log << "=== 初始化完成 ===" << std::endl;
    std::cout << "YuNet 模型加载并初始化成功。" << std::endl;
    g_detector_initialized = true;
    return true;
}

// 动态调整检测频率
void adjustDetectionInterval(int processingTime) {
    // 根据处理时间动态调整检测间隔
    
    // 计算系统负载指标(0-100)
    g_system_load = std::min<int>(100, processingTime * 2);
    
    if (processingTime > 70) {
        // 处理时间过长，增加间隔减轻负担
        g_processing_interval = std::min<int>(200, g_processing_interval + 10);
    } 
    else if (processingTime < 30 && g_processing_interval > 30) {
        // 处理时间短，可以适当减少间隔提高响应性
        g_processing_interval = std::max<int>(30, g_processing_interval - 5);
    }
}

// JPEG编码线程函数 - 已废弃，改为按需编码
// 按需编码模式：Flutter请求时才进行JPEG编码，避免不必要的CPU消耗
/*
void jpegEncodingThread() {
    // 此函数已废弃，现在使用按需编码
    // 在get_jpeg_data()中直接编码，提高效率和实时性
}
*/

// 启动人脸检测线程
void startDetectionThread() {
    log_info("启动检测线程 - startDetectionThread()");
    
    if (g_is_detection_thread_running) {
        log_info("检测线程已在运行中，直接返回");
        return;
    }
    
    // 确保先初始化模型再启动线程
    if (!g_detector_initialized) {
        log_info("检测线程开始前初始化模型");
        std::cout << "Starting detection thread before initializing model..." << std::endl;
        // 删除使用filesystem的代码
        // std::cout << "正在搜索模型文件，当前路径: " << std::filesystem::current_path().string() << std::endl;
        std::cout << "Searching for model file..." << std::endl;
        log_info("搜索模型文件");
        initialize_detector();
        
        // 如果初始化失败，尝试扫描目录寻找模型文件
        if (!g_detector_initialized) {
            std::cout << "Model initialization failed, searching directory for model file..." << std::endl;
            // 尝试在常见位置搜索
            std::vector<std::string> search_dirs = {
                ".", "./models", "../models", "../../models", 
                "./windows/runner/Debug/data/models", 
                "../../../models", "./data/models",
                "models"
            };
            
            for (const auto& dir : search_dirs) {
                // 直接搜索YuNet模型文件
                std::string yunet_model_path = dir + "/face_detection_yunet_2023mar.onnx";
                
                std::cout << "Checking YuNet model path: " << yunet_model_path << std::endl;
                
                if (fileExists(yunet_model_path)) {
                    std::cout << "Found YuNet model file!" << std::endl;
                    g_yunet_model_path = yunet_model_path;
                    
                    try {
                        g_face_detector_yunet = cv::FaceDetectorYN::create(
                            g_yunet_model_path,
                            "", // config file (usually not needed for ONNX)
                            cv::Size(320, 240), // 恢复为320×240，这是原本正常工作的尺寸
                            g_yunet_confThreshold,
                            g_yunet_nmsThreshold,
                            g_yunet_topK
                        );
                        if (g_face_detector_yunet) {
                            g_detector_initialized = true;
                            std::cout << "YuNet model loaded successfully!" << std::endl;
                            break;
                        }
                    } catch (const cv::Exception& e) {
                        std::cerr << "Failed to load YuNet model: " << e.what() << std::endl;
                    }
                }
            }
        }
    }
    
    // 最后检查模型是否已加载
    if (!g_face_detector_yunet) {
        std::cerr << "Warning: YuNet model is not loaded even after trying to initialize. Detection may not work properly." << std::endl;
    } else {
        std::cout << "YuNet model loaded successfully, ready to detect." << std::endl;
    }
    
    g_is_detection_thread_running = true;
    g_detection_thread = std::thread(faceDetectionThread);
}

// 检测线程函数 - 完全独立于视频线程
void faceDetectionThread() {
    log_info("人脸检测线程开始运行");
    std::cout << "Starting face detection thread" << std::endl;
    
    // 确保检测线程拥有自己的正确初始化的模型副本
    if (!g_detector_initialized) {
        log_info("检测线程中初始化人脸检测器");
        std::cout << "Initializing face detector..." << std::endl;
        initialize_detector();
    }
    
    // 再次检查模型是否已正确加载
    if (!g_face_detector_yunet) {
        std::cerr << "Error: YuNet model is not loaded in detection thread, trying to reload" << std::endl;
        // 重新加载模型
        try {
            if (!g_yunet_model_path.empty()) {
                std::cout << "Reloading model file: " << g_yunet_model_path << std::endl;
                g_face_detector_yunet = cv::FaceDetectorYN::create(
                    g_yunet_model_path,
                    "", // config file (usually not needed for ONNX)
                    cv::Size(320, 240), // Input size, adjust if necessary
                    g_yunet_confThreshold,
                    g_yunet_nmsThreshold,
                    g_yunet_topK
                );
                if (!g_face_detector_yunet) {
                    std::cerr << "Reload failed, YuNet model is still not loaded" << std::endl;
                } else {
                    std::cout << "YuNet model reloaded successfully" << std::endl;
                }
            } else {
                std::cerr << "Model file path is empty, cannot reload" << std::endl;
            }
        } catch (const cv::Exception& e) {
            std::cerr << "Failed to reload YuNet model: " << e.what() << std::endl;
        }
    }
    
    // FPS计算变量
    int frame_counter = 0;
    auto fps_start_time = std::chrono::steady_clock::now();
    
    while (g_is_detection_thread_running.load()) {
        cv::Mat frame_to_process;

        // 避免使用mutex锁，简化队列处理
        if (!g_detection_queue.empty()) {
            frame_to_process = g_detection_queue.front();
            g_detection_queue.pop();
            g_detection_in_progress.store(true);
        } else {
            // 如果队列为空，短暂休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }
        
        // 如果有帧可处理
        if (!frame_to_process.empty()) {
            // Log periodically to show thread activity
            static int process_counter = 0;
            process_counter++;
            if (process_counter % 200 == 0) {
                log_info("Detection thread active - processing frame " + std::to_string(process_counter));
            }
            
            auto start_time = std::chrono::high_resolution_clock::now();
            
            // 进行人脸检测
            double processed_fps = 0;
            std::vector<FaceInfo> face_info;
            
            try {
                // 检查YuNet模型是否有效，无效则直接返回空结果
                if (!g_face_detector_yunet) {
                    std::cerr << "Error: YuNet model is not loaded, cannot execute detection" << std::endl;
                } else {
                    // 使用YuNet模型检测
                    bool success = detectFaces_YuNet(frame_to_process, face_info);
                    
                    if (success) {
                        // Log detection results for debugging
                        log_info("Frame processing completed. Detected " + std::to_string(face_info.size()) + " raw faces");
                        
                        // 将平滑后的结果写入全局变量
                        std::vector<FaceInfo> smoothed_faces = smoothFaceResults(face_info);
                        
                        log_info("After smoothing: " + std::to_string(smoothed_faces.size()) + " smoothed faces");
                        
                        // 转换为Rect用于兼容旧接口
                        std::vector<cv::Rect> faces;
                        for (const auto& face : smoothed_faces) {
                            faces.push_back(face.bbox);
                        }
                        
                        // 避免使用mutex锁，直接更新共享指针
                        if (!g_detection_result_ptr) {
                            g_detection_result_ptr = std::make_shared<std::vector<cv::Rect>>();
                        }
                        *g_detection_result_ptr = faces;
                        
                        // 轮询机制 - 移除高频日志输出
                        
                        // Check for high confidence faces and store to cache (轮询机制)
                        for (const auto& face : smoothed_faces) {
                            if (face.score > 0.8f) {
                                // 检查捕获间隔（考勤认证场景）
                                auto current_time = std::chrono::steady_clock::now();
                                auto time_since_last_capture = std::chrono::duration_cast<std::chrono::milliseconds>(
                                    current_time - g_last_capture_time).count();
                                
                                if (time_since_last_capture < CAPTURE_INTERVAL_MILLISECONDS) {
                                    // 间隔不够，跳过此次捕获
                                    continue;
                                }
                                
                                // 高置信度人脸检测到，存储到缓存
                                
                                // Extract face region from frame
                                cv::Rect face_roi = face.bbox;
                                
                                // Ensure ROI is within frame bounds
                                face_roi.x = (face_roi.x > 0) ? face_roi.x : 0;
                                face_roi.y = (face_roi.y > 0) ? face_roi.y : 0;
                                int max_width = frame_to_process.cols - face_roi.x;
                                int max_height = frame_to_process.rows - face_roi.y;
                                face_roi.width = (face_roi.width < max_width) ? face_roi.width : max_width;
                                face_roi.height = (face_roi.height < max_height) ? face_roi.height : max_height;
                                
                                if (face_roi.width > 0 && face_roi.height > 0) {
                                    cv::Mat face_image = frame_to_process(face_roi);

                                    // 按需编码优化：只保存原始Mat数据，Flutter请求时才编码为JPEG
                                    // 这样可以减少不必要的编码操作，提高CPU效率

                                    // 避免使用mutex锁，直接更新共享指针
                                    if (!g_latest_face_capture_ptr) {
                                        g_latest_face_capture_ptr = std::make_shared<FaceCapture>();
                                    }

                                    // 清理之前的数据
                                    g_latest_face_capture_ptr->clear();

                                    // 保存原始Mat数据（克隆以避免数据竞争）
                                    g_latest_face_capture_ptr->rawImageMat = face_image.clone();
                                    g_latest_face_capture_ptr->confidence = face.score;
                                    g_latest_face_capture_ptr->timestamp = std::chrono::steady_clock::now();
                                    g_latest_face_capture_ptr->isValid = true;
                                    g_latest_face_capture_ptr->isEncoded = false;  // 标记未编码

                                    // 设置新数据标志并更新最后捕获时间
                                    g_has_new_face_capture.store(true);
                                    g_last_capture_time = current_time;

                                    std::cout << "高阈值人脸已保存（原始Mat），置信度: " << face.score
                                             << "，等待Flutter请求时编码" << std::endl;

                                    // 只存储第一个高置信度人脸，避免过度更新
                                    break;
                                } else {
                                    std::cout << "❌ 无效的人脸区域" << std::endl;
                                }
                            }
                        }
                        
                        // 更新FPS计数
                        frame_counter++;
                        auto now = std::chrono::steady_clock::now();
                        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - fps_start_time).count();
                        
                        if (elapsed >= 1) {
                            g_detection_fps = frame_counter / elapsed;
                            frame_counter = 0;
                            fps_start_time = now;
                            
                            // 输出到控制台
                            std::cout << "Face detection FPS: " << g_detection_fps << " FPS" << std::endl;
                        }
                    }
                }
            } catch (const std::exception& e) {
                std::cerr << "Exception in detection thread: " << e.what() << std::endl;
            }
            
            g_detection_in_progress = false;
            
            // 计算处理时间
            auto end_time = std::chrono::high_resolution_clock::now();
            auto processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
            
            // 更新全局变量
            g_last_detection_time = processing_time;
            
            // 保存最近的时间样本用于计算平均值
            g_time_samples.push_back(processing_time);
            if (g_time_samples.size() > 30) { // 保留最近30个样本
                g_time_samples.erase(g_time_samples.begin());
            }
            
            // 计算平均时间
            double sum = 0;
            for (double sample : g_time_samples) {
                sum += sample;
            }
            g_avg_detection_time = sum / g_time_samples.size();
            
            // 计算系统负载百分比（基于平均检测时间与帧间隔的比率）
            double frame_interval = 1000.0 / 30.0; // 假设目标是30FPS
            g_system_load = (g_avg_detection_time / frame_interval) * 100.0;
            
            // 将系统负载限制在0-100范围内
            g_system_load = std::min<double>(100.0, std::max<double>(0.0, g_system_load));
            
            // 输出到控制台
            // std::cout << "Face Detection Time haha: " << processing_time << " ms, Average Time: " 
            //           << g_avg_detection_time << " ms, System Load: " << g_system_load << "%" << std::endl;
            
            // 限制频率，避免过度消耗CPU - 动态调整休眠时间
            // 如果处理非常快，休眠时间短，保证高响应性
            // 如果处理慢，跳过休眠，直接处理下一帧
            int sleep_time = g_detection_interval.load();
            if (processing_time < sleep_time) {
                std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time - processing_time));
            }
        } else {
            // 没有帧或帧无效，短暂休眠避免空转
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
        }
    }
}

// 停止人脸检测线程
void stopDetectionThread() {
    g_is_detection_thread_running = false;
    // g_detection_condition.notify_all(); // 已移除条件变量
    
    if (g_detection_thread.joinable()) {
        g_detection_thread.join();
    }
}

// 提交帧进行人脸检测 - 非阻塞
void submitFrameForDetection(const cv::Mat& frame) {
    // 如果检测线程未运行，直接返回
    if (!g_is_detection_thread_running) {
        return;
    }
    
    // 创建帧副本并提交到队列
    cv::Mat frame_copy = frame.clone();
    
    // 避免使用mutex锁防止崩溃
    // 只保留最多2帧，避免队列过长但仍保持一定的缓冲
    while (g_detection_queue.size() > 2) {
        g_detection_queue.pop();
    }

    g_detection_queue.push(frame_copy);
}

// 使用YuNet模型检测人脸 - 已重构为非阻塞异步方式
bool detectFaces_YuNet(const cv::Mat& frame, std::vector<FaceInfo>& face_info) {
    try {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // 确保检测器已初始化
        if (!g_detector_initialized) {
            if (!initialize_detector()) {
                std::cerr << "无法初始化YuNet检测器" << std::endl;
                return false;
            }
        }
        
        // 确保输入图像和检测器尺寸匹配
        if (g_face_detector_yunet->getInputSize().width != frame.cols ||
            g_face_detector_yunet->getInputSize().height != frame.rows) {
            g_face_detector_yunet->setInputSize(cv::Size(frame.cols, frame.rows));
        }
        
        // 执行检测
        cv::Mat faces;
        g_face_detector_yunet->detect(frame, faces);
        
        // 解析检测结果
        if (!faces.empty()) {
            face_info.clear();
            for (int i = 0; i < faces.rows; i++) {
                FaceInfo info;
                
                // 使用备份版本的索引位置：置信度在索引4
                float x = faces.at<float>(i, 0);
                float y = faces.at<float>(i, 1);
                float w = faces.at<float>(i, 2);
                float h = faces.at<float>(i, 3);
                info.score = faces.at<float>(i, 4);
                
                // 确保坐标在图像范围内
                x = std::max<float>(0.0f, x);
                y = std::max<float>(0.0f, y);
                
                // 使用原始坐标不进行任何缩放处理
                info.bbox = cv::Rect(static_cast<int>(x), static_cast<int>(y), 
                                    static_cast<int>(w), static_cast<int>(h));
                
                // 读取5个关键点 (YuNet提供5组xy坐标，共10个值)
                // 根据备份版本，关键点是从索引5开始
                for (int j = 0; j < 5; j++) {
                    float landmark_x = faces.at<float>(i, 5 + j * 2);
                    float landmark_y = faces.at<float>(i, 5 + j * 2 + 1);
                    info.landmarks.push_back(cv::Point2f(landmark_x, landmark_y));
                }
                
                // 检测到大于阈值的人脸时输出日志
                if (info.score > g_yunet_confThreshold) {
                    std::cout << "High confidence face detected: " << info.score << std::endl;
                }
                
                // 添加到结果列表
                face_info.push_back(info);
            }
        }
        
        // 计算检测耗时
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
        g_last_detection_time = static_cast<int>(duration);
        
        // 记录检测时间样本以计算平均值
        g_time_samples.push_back(g_last_detection_time);
        if (g_time_samples.size() > g_avg_time_samples) {
            g_time_samples.erase(g_time_samples.begin());
        }
        
        // 更新平均检测时间
        int sum = 0;
        for (int time : g_time_samples) {
            sum += time;
        }
        g_avg_detection_time = sum / g_time_samples.size();
        
        return true;
    }
    catch (const cv::Exception& e) {
        std::cerr << "OpenCV 异常: " << e.what() << std::endl;
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "检测异常: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "未知检测异常" << std::endl;
        return false;
    }
}

// 平滑人脸检测结果，使用更简单的平滑算法
std::vector<FaceInfo> smoothFaceResults(const std::vector<FaceInfo>& current_faces_info) {
    std::vector<FaceInfo> smoothed_faces_info;
    
    // 如果上一帧没有检测到人脸，直接返回当前结果
    if (g_last_faces.empty()) {
        // 更新last_faces用于下一次平滑
        g_last_faces.clear();
        for (const auto& info : current_faces_info) {
            g_last_faces.push_back(info.bbox);
        }
        return current_faces_info;
    }

    // 提取当前帧的矩形
    std::vector<cv::Rect> current_faces;
    for(const auto& info : current_faces_info) {
        current_faces.push_back(info.bbox);
    }

    // 进行基于IoU的匹配和平滑处理
    std::vector<bool> matched(current_faces.size(), false);

    for (size_t i = 0; i < g_last_faces.size(); ++i) {
        const auto& last_face = g_last_faces[i];
        float best_iou = 0.0f;
        int best_match_idx = -1;

        // 查找最佳匹配
        for (size_t j = 0; j < current_faces.size(); ++j) {
            if (!matched[j]) {
                cv::Rect intersection = last_face & current_faces[j];
                float intersection_area = intersection.area();
                
                // 避免除以零
                if (intersection_area > 0) {
                    float union_area = last_face.area() + current_faces[j].area() - intersection_area;
                    float iou = intersection_area / union_area;
                    
                    if (iou > best_iou && iou > 0.3f) { // IoU阈值
                        best_iou = iou;
                        best_match_idx = j;
                    }
                }
            }
        }

        // 应用平滑
        if (best_match_idx != -1) {
            matched[best_match_idx] = true;
            FaceInfo smoothed_info = current_faces_info[best_match_idx];
            
            // 使用完整的平滑因子
            cv::Rect& current_bbox = current_faces[best_match_idx];
            
            // 计算平滑后的边界框
            cv::Rect smoothed_box;
            smoothed_box.x = static_cast<int>(last_face.x * g_smoothing_factor + current_bbox.x * (1.0 - g_smoothing_factor));
            smoothed_box.y = static_cast<int>(last_face.y * g_smoothing_factor + current_bbox.y * (1.0 - g_smoothing_factor));
            smoothed_box.width = static_cast<int>(last_face.width * g_smoothing_factor + current_bbox.width * (1.0 - g_smoothing_factor));
            smoothed_box.height = static_cast<int>(last_face.height * g_smoothing_factor + current_bbox.height * (1.0 - g_smoothing_factor));
            
            // 确保边界框有效
            smoothed_box.width = std::max<int>(1, smoothed_box.width);
            smoothed_box.height = std::max<int>(1, smoothed_box.height);
            
            // 更新平滑后的信息
            smoothed_info.bbox = smoothed_box;
            smoothed_faces_info.push_back(smoothed_info);
        }
    }

    // 添加未匹配的人脸
    for (size_t i = 0; i < current_faces.size(); ++i) {
        if (!matched[i]) {
            smoothed_faces_info.push_back(current_faces_info[i]);
        }
    }

    // 更新last_faces用于下一次平滑
    g_last_faces.clear();
    for (const auto& info : smoothed_faces_info) {
        g_last_faces.push_back(info.bbox);
    }

    return smoothed_faces_info;
}

// 在图像上绘制帧率和人脸框
void drawFPSandBoxes(cv::Mat& frame, const std::vector<FaceInfo>& faces_info, double fps, int avg_fps, int system_fps, bool is_hd, int recording_state, bool add_landmarks) {
    // 绘制人脸矩形
    cv::Scalar color(0, 255, 0); // Green
    cv::Scalar landmark_color(255, 0, 255); // Purple for landmarks
    int thickness = is_hd ? 2 : 1;
    
    // 绘制人脸框和特征点
    for (const auto& face : faces_info) {
        // 绘制人脸矩形
        cv::rectangle(frame, face.bbox, color, thickness);
        
        // 绘制置信度分数
        std::string score_str = cv::format("%.2f", face.score);
        cv::putText(frame, score_str, cv::Point(face.bbox.x, face.bbox.y - 5),
                    cv::FONT_HERSHEY_SIMPLEX, 0.4, color, 1);
        
        // 如果需要，绘制特征点
        if (add_landmarks) {
            for (const auto& point : face.landmarks) {
                cv::circle(frame, point, 2, landmark_color, -1);
            }
        }
    }
    
    // 绘制录制状态指示器
    if (recording_state == 1) { // 录像中
        cv::circle(frame, cv::Point(frame.cols - 20, 20), 10, cv::Scalar(0, 0, 255), -1);
    } else if (recording_state == 2) { // 拍照
        cv::rectangle(frame, cv::Rect(frame.cols - 30, 10, 20, 20), cv::Scalar(255, 0, 0), -1);
    }
}

// 导出函数封装
extern "C" {
    // 设置是否使用Haar级联分类器 - 不需要但为了API兼容性保留
    EXPORT void set_use_haar_detector(bool use_haar) {
        // 忽略参数，始终为false，只使用YuNet
        g_use_haar_detector = false;
    }

    // 获取当前检测器类型
    EXPORT bool get_use_haar_detector() {
        return false; // 始终返回false，表示不使用Haar检测器
    }

    // 设置平滑因子
    EXPORT void set_smoothing_factor(double factor) {
        // 确保因子在有效范围内
        g_smoothing_factor = std::max<double>(0.0, std::min<double>(1.0, factor));
    }

    // 获取平滑因子
    EXPORT double get_smoothing_factor() {
        return g_smoothing_factor;
    }

    // 获取当前系统负载
    EXPORT int get_system_load() {
        return g_system_load;
    }

    // 获取人脸检测帧率
    EXPORT int get_detection_fps() {
        return g_detection_fps; // 保持API兼容性
    }

    // 获取最后一次检测耗时（毫秒）
    EXPORT int get_last_detection_time() {
        return g_last_detection_time;
    }

    // 获取平均检测耗时（毫秒）
    EXPORT int get_avg_detection_time() {
        return g_avg_detection_time;
    }

    // 设置是否显示帧率
    EXPORT void set_show_fps(bool show) {
        g_show_fps = false; // 忽略输入参数，总是设为false
    }

    // 获取是否显示帧率
    EXPORT bool get_show_fps() {
        return false; // 总是返回false
    }

    // 设置检测间隔
    EXPORT void set_processing_interval(int interval_ms) {
        g_detection_interval.store(std::max<int>(10, std::min<int>(500, interval_ms)));
    }

    // 获取当前处理间隔
    EXPORT int get_processing_interval() {
        return g_detection_interval.load();
    }

    // 仅检测人脸，不处理图像，直接返回坐标 - 已优化为无阻塞
    EXPORT bool detect_faces_only(
        void* facesArray,
        int maxFaces,
        int* numFaces
    ) {
        if (!g_detector_initialized) {
            if (!initialize_detector()) {
                *numFaces = 0;
                return false;
            }
        }

        std::vector<cv::Rect> current_faces;
        // 避免使用mutex锁，直接访问共享指针
        if (g_detection_result_ptr) {
            current_faces = *g_detection_result_ptr;
        }

        struct FaceRect {
            int x, y, width, height;
        };
        FaceRect* outputFaces = static_cast<FaceRect*>(facesArray);

        int count = 0;
        for (const auto& face : current_faces) {
            if (count >= maxFaces) break;
            outputFaces[count].x = face.x;
            outputFaces[count].y = face.y;
            outputFaces[count].width = face.width;
            outputFaces[count].height = face.height;
            count++;
        }
        *numFaces = count;

        // 不在这个函数中绘制人脸框，只返回坐标
        return true;
    }

    // 修改后的原始detect_faces函数，更新全局帧缓冲区
    EXPORT bool detect_faces(
        unsigned char* imageData,
        int width,
        int height,
        int channels,
        void* facesArray,
        int maxFaces,
        int* numFaces
    ) {
        if (!g_detector_initialized) {
            if (!initialize_detector()) {
                *numFaces = 0;
                return false;
            }
        }

        // Get the latest detection results (already smoothed)
        std::vector<cv::Rect> current_smoothed_faces;
        // 避免使用mutex锁，直接访问共享指针
        if (g_detection_result_ptr) {
            current_smoothed_faces = *g_detection_result_ptr;
        }

        // Prepare the output structure
        struct FaceRect {
            int x, y, width, height;
        };
        FaceRect* outputFaces = static_cast<FaceRect*>(facesArray);
        int count = 0;
        for (const auto& face : current_smoothed_faces) {
            if (count >= maxFaces) break;
            outputFaces[count].x = face.x;
            outputFaces[count].y = face.y;
            outputFaces[count].width = face.width;
            outputFaces[count].height = face.height;
            count++;
        }
        *numFaces = count;


        // Draw results onto the display frame (g_display_frame)
        // Note: imageData might be outdated if Flutter sends its display buffer.
        // It's better to draw on the internally managed g_display_frame.
        // 避免使用mutex锁，直接访问共享指针
        if (g_display_frame_ptr && !g_display_frame_ptr->empty()) {
            // 只有当g_show_face_boxes为true时才绘制人脸框
            if (g_show_face_boxes && !current_smoothed_faces.empty()) {
                // Create a temporary vector of FaceInfo with only bboxes for drawing
                std::vector<FaceInfo> faces_to_draw;
                for (const auto& rect : current_smoothed_faces) {
                    FaceInfo info;
                    info.bbox = rect;
                    faces_to_draw.push_back(info);
                }

                // Draw FPS and boxes onto the display frame
                drawFPSandBoxes(*g_display_frame_ptr, faces_to_draw, g_current_fps, g_avg_detection_time, g_system_load, (g_original_width > 640), 0, false); // Don't draw landmarks here unless specifically needed
            }

            // Copy the modified display frame data back to imageData if that's the intended behavior.
            // This assumes imageData is a buffer Flutter expects to be updated.
            // Be careful with formats (BGR vs BGRA).
            if (imageData && g_display_frame_ptr->total() * g_display_frame_ptr->elemSize() == static_cast<size_t>(width * height * channels)) {
                if (g_display_frame_ptr->channels() == 3 && channels == 4) {
                    cv::Mat bgra_frame;
                    cv::cvtColor(*g_display_frame_ptr, bgra_frame, cv::COLOR_BGR2BGRA);
                    memcpy(imageData, bgra_frame.data, bgra_frame.total() * bgra_frame.elemSize());
                }
                else if (g_display_frame_ptr->channels() == channels) {
                    memcpy(imageData, g_display_frame_ptr->data, g_display_frame_ptr->total() * g_display_frame_ptr->elemSize());
                }
                else {
                    std::cerr << "Warning: Channel mismatch between internal frame and output buffer in detect_faces." << std::endl;
                }
            }
            else if (imageData) {
                std::cerr << "Warning: Size mismatch between internal frame and output buffer in detect_faces." << std::endl;
            }

        }
        else {
            // Handle case where display frame is empty
            *numFaces = 0; // Indicate no result if no frame to process/draw on
        }

        return true;
    }
    
    
    // 获取JPEG图像数据 - 用于摄像头预览显示
    EXPORT bool get_jpeg_data(unsigned char** output, int* length) {
        if (!g_is_camera_running || g_frame_width <= 0 || g_frame_height <= 0) {
            return false;
        }

        try {
            // 从显示帧队列中获取当前帧 - 避免使用mutex锁
            cv::Mat frame_to_encode;
            if (!g_display_frame_ptr || g_display_frame_ptr->empty()) {
                return false;
            }
            g_display_frame_ptr->copyTo(frame_to_encode);

            // 按需编码：Flutter请求时立即编码，确保获取最新帧
            std::vector<uchar> jpeg_buffer;
            std::vector<int> params = {cv::IMWRITE_JPEG_QUALITY, g_jpeg_quality};

            if (!cv::imencode(".jpg", frame_to_encode, jpeg_buffer, params)) {
                std::cerr << "Failed to encode frame to JPEG" << std::endl;
                return false;
            }

            // 分配内存并复制数据
            *length = static_cast<int>(jpeg_buffer.size());
            *output = static_cast<unsigned char*>(malloc(*length));
            if (*output) {
                memcpy(*output, jpeg_buffer.data(), *length);

                // 注意：这里不清理帧缓存，因为这是预览功能，需要持续显示
                // 人脸识别使用的是get_latest_face_capture()，有独立的清理机制

                return true;
            }
        } catch (const cv::Exception& e) {
            std::cerr << "Failed to get JPEG image data: " << e.what() << std::endl;
        }

        return false;
    }
    
    // 旧的兼容函数名称
    EXPORT bool get_jpeg_image_data(unsigned char** output, int* length) {
        return get_jpeg_data(output, length); // 调用主实现
    }
    
    // 设置JPEG质量
    EXPORT void set_jpeg_quality(int quality) {
        g_jpeg_quality = std::max<int>(1, std::min<int>(100, quality));
    }
    
    // 获取JPEG质量
    EXPORT int get_jpeg_quality() {
        return g_jpeg_quality;
    }
    
    // 启动摄像头 - 确保同时启动检测线程
    EXPORT bool start_camera() {
        log_info("调用 start_camera()");
        if (g_is_camera_running) {
            log_info("摄像头已经在运行中，直接返回");
            return true;
        }
        
        // 初始化日志系统
        init_logging();
        
        log_info("尝试初始化摄像头...");
        
        try {
            // 初始化智能指针
            if (!g_capture_ptr) {
                g_capture_ptr = std::make_shared<cv::VideoCapture>();
            }

            // 使用多后端打开摄像头
            if (!openCameraWithMultipleBackends(0, g_capture_ptr)) {
                log_error("摄像头打开失败，ID=0");
                
                // 获取当前工作目录
                char cwd[1024] = {0};
#ifdef _WIN32
                if (_getcwd(cwd, sizeof(cwd))) {
                    log_info(std::string("当前工作目录: ") + cwd);
                }
                
                // 获取可执行文件路径
                char exe_path[MAX_PATH] = {0};
                if (GetModuleFileNameA(NULL, exe_path, MAX_PATH)) {
                    log_info(std::string("可执行文件路径: ") + exe_path);
                }
                
                // 尝试输出摄像头设备信息
                log_info("尝试枚举可用摄像头设备");
                for (int i = 0; i < 10; i++) {
                    cv::VideoCapture temp_cap(i);
                    if (temp_cap.isOpened()) {
                        log_info("发现可用摄像头，ID=" + std::to_string(i));
                        double width = temp_cap.get(cv::CAP_PROP_FRAME_WIDTH);
                        double height = temp_cap.get(cv::CAP_PROP_FRAME_HEIGHT);
                        log_info("摄像头 " + std::to_string(i) + " 分辨率: " + 
                                 std::to_string((int)width) + "x" + std::to_string((int)height));
                        temp_cap.release();
                    }
                }
#endif
                std::cerr << "Failed to open camera" << std::endl;
                return false;
            }
            
            log_info("摄像头打开成功，ID=0，使用后端: " + g_current_backend);

            // 设置分辨率以减少处理负载
            log_info("设置摄像头分辨率为 640x480");
            if (g_capture_ptr) {
                g_capture_ptr->set(cv::CAP_PROP_FRAME_WIDTH, 640);
                g_capture_ptr->set(cv::CAP_PROP_FRAME_HEIGHT, 480);
            }
            
            g_is_camera_running.store(true);
            
            // JPEG编码已改为按需编码，不再需要后台线程
            
            // 启动人脸检测线程
            startDetectionThread();
            
            // 创建摄像头捕获线程
            g_camera_thread = std::thread([]() {
                cv::Mat frame;
                
                log_info("摄像头捕获线程已启动");
                
                while (g_is_camera_running.load()) {
                    bool success = false;
                    if (g_capture_ptr) {
                        success = g_capture_ptr->read(frame);
                    }
                    if (success && !frame.empty()) {
                        // 记录原始大小
                        g_original_width = frame.cols;
                        g_original_height = frame.rows;
                        
                        // 调整图像大小以提高处理速度
                        cv::Mat resized;
                        cv::resize(frame, resized, cv::Size(320, 240));
                        
                        // 避免使用mutex锁防止崩溃
                        if (!g_camera_frame_ptr) {
                            g_camera_frame_ptr = std::make_shared<cv::Mat>();
                        }
                        resized.copyTo(*g_camera_frame_ptr);
                        g_frame_width = resized.cols;
                        g_frame_height = resized.rows;
                        g_frame_channels = resized.channels();
                        g_fps_counter++;
                        
                        // 更新检测帧 - 避免使用mutex锁
                        if (!g_current_frame_ptr) {
                            g_current_frame_ptr = std::make_shared<cv::Mat>();
                        }
                        resized.copyTo(*g_current_frame_ptr);
                        g_frame_ready.store(true);
                        
                        // 更新显示帧 - 避免使用mutex锁
                        if (!g_display_frame_ptr) {
                            g_display_frame_ptr = std::make_shared<cv::Mat>();
                        }
                        resized.copyTo(*g_display_frame_ptr);
                        
                        // 录像功能 - 避免使用mutex锁防止崩溃
                        if (g_is_recording.load() && g_video_writer_ptr && g_video_writer_ptr->isOpened()) {
                            // 使用原始帧进行录制以保持质量
                            g_video_writer_ptr->write(frame);
                        }
                        
                        // 非阻塞方式提交帧到检测线程
                        submitFrameForDetection(resized);
                        
                        auto now = std::chrono::steady_clock::now();
                        auto diff = std::chrono::duration_cast<std::chrono::milliseconds>(
                            now - g_fps_last_time).count();
                            
                        if (diff >= 1000) {
                            g_current_fps = g_fps_counter;
                            g_fps_counter = 0;
                            g_fps_last_time = now;
                        }
                    } else if (!success) {
                        log_error("从摄像头获取帧失败");
                        std::cerr << "Failed to grab frame from camera" << std::endl;
                    }
                    
                    // 短暂休眠以减少CPU使用率
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
                
                log_info("摄像头捕获线程已停止");
            });
            
            log_info("摄像头成功启动");
            return true;
        } catch (const cv::Exception& e) {
            log_error("启动摄像头时发生OpenCV异常: " + std::string(e.what()));
            std::cerr << "Failed to start camera: " << e.what() << std::endl;
            return false;
        }
    }
    
    // 指定摄像头ID的启动函数
    EXPORT bool start_camera_with_id(int camera_id) {
        log_info("调用 start_camera_with_id(), camera_id=" + std::to_string(camera_id));
        
        if (g_is_camera_running) {
            // 如果摄像头已经开启，先关闭
            log_info("摄像头已经在运行中，先停止当前摄像头");
            stop_camera();
        }
        
        // 初始化日志系统
        init_logging();
        
        try {
            // 初始化智能指针
            if (!g_capture_ptr) {
                g_capture_ptr = std::make_shared<cv::VideoCapture>();
            }

            // 使用多后端打开摄像头
            if (!openCameraWithMultipleBackends(camera_id, g_capture_ptr)) {
                
                // 获取当前工作目录
                char cwd[1024] = {0};
#ifdef _WIN32
                if (_getcwd(cwd, sizeof(cwd))) {
                    log_info(std::string("当前工作目录: ") + cwd);
                }
                
                // 获取可执行文件路径
                char exe_path[MAX_PATH] = {0};
                if (GetModuleFileNameA(NULL, exe_path, MAX_PATH)) {
                    log_info(std::string("可执行文件路径: ") + exe_path);
                }
                
                // 尝试输出摄像头设备信息
                log_info("尝试枚举可用摄像头设备");
                for (int i = 0; i < 10; i++) {
                    cv::VideoCapture temp_cap(i);
                    if (temp_cap.isOpened()) {
                        log_info("发现可用摄像头，ID=" + std::to_string(i));
                        double width = temp_cap.get(cv::CAP_PROP_FRAME_WIDTH);
                        double height = temp_cap.get(cv::CAP_PROP_FRAME_HEIGHT);
                        log_info("摄像头 " + std::to_string(i) + " 分辨率: " + 
                                 std::to_string((int)width) + "x" + std::to_string((int)height));
                        temp_cap.release();
                    }
                }
#endif
                std::cerr << "Failed to open camera with ID: " << camera_id << std::endl;
                return false;
            }

            log_info("摄像头打开成功，ID=" + std::to_string(camera_id) + "，使用后端: " + g_current_backend);

            // 验证摄像头是否真的可用
            cv::Mat test_frame;
            bool can_read = false;
            for (int retry = 0; retry < 3; retry++) {
                if (g_capture_ptr && g_capture_ptr->read(test_frame) && !test_frame.empty()) {
                    can_read = true;
                    log_info("摄像头读取测试成功，尝试次数: " + std::to_string(retry + 1));
                    break;
                } else {
                    log_warning("摄像头读取测试失败，尝试次数: " + std::to_string(retry + 1));
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }

            if (!can_read) {
                log_error("摄像头虽然打开但无法读取帧数据");
                if (g_capture_ptr) {
                    g_capture_ptr->release();
                    g_capture_ptr.reset();
                }
                return false;
            }

            // 设置分辨率以减少处理负载
            log_info("设置摄像头分辨率为 640x480");
            if (g_capture_ptr) {
                g_capture_ptr->set(cv::CAP_PROP_FRAME_WIDTH, 640);
                g_capture_ptr->set(cv::CAP_PROP_FRAME_HEIGHT, 480);
            }

            g_is_camera_running.store(true);
            log_info("设置g_is_camera_running=true");
            
            // JPEG编码已改为按需编码，不再需要后台线程
            log_info("使用按需JPEG编码，无需后台线程");
            
            // 启动人脸检测线程
            log_info("启动人脸检测线程");
            startDetectionThread();
            
            // 创建摄像头捕获线程
            log_info("创建摄像头捕获线程");
            g_camera_thread = std::thread([camera_id]() {
                cv::Mat frame;
                
                log_info("摄像头捕获线程已启动，ID=" + std::to_string(camera_id));
                
                while (g_is_camera_running.load()) {
                    bool success = false;
                    if (g_capture_ptr) {
                        success = g_capture_ptr->read(frame);
                    }
                    if (success && !frame.empty()) {
                        // 记录原始大小
                        g_original_width = frame.cols;
                        g_original_height = frame.rows;
                        
                        // 调整图像大小以提高处理速度
                        cv::Mat resized;
                        cv::resize(frame, resized, cv::Size(320, 240));
                        
                        // 避免使用mutex锁防止崩溃
                        if (!g_camera_frame_ptr) {
                            g_camera_frame_ptr = std::make_shared<cv::Mat>();
                        }
                        resized.copyTo(*g_camera_frame_ptr);
                        g_frame_width = resized.cols;
                        g_frame_height = resized.rows;
                        g_frame_channels = resized.channels();
                        g_fps_counter++;
                        
                        // 更新检测帧 - 避免使用mutex锁
                        if (!g_current_frame_ptr) {
                            g_current_frame_ptr = std::make_shared<cv::Mat>();
                        }
                        resized.copyTo(*g_current_frame_ptr);
                        g_frame_ready.store(true);
                        
                        // 更新显示帧 - 避免使用mutex锁
                        if (!g_display_frame_ptr) {
                            g_display_frame_ptr = std::make_shared<cv::Mat>();
                        }
                        resized.copyTo(*g_display_frame_ptr);
                        
                        // 录像功能 - 避免使用mutex锁防止崩溃
                        if (g_is_recording.load() && g_video_writer_ptr && g_video_writer_ptr->isOpened()) {
                            // 使用原始帧进行录制以保持质量
                            g_video_writer_ptr->write(frame);
                        }
                        
                        // 非阻塞方式提交帧到检测线程
                        submitFrameForDetection(resized);
                        
                        auto now = std::chrono::steady_clock::now();
                        auto diff = std::chrono::duration_cast<std::chrono::milliseconds>(
                            now - g_fps_last_time).count();
                            
                        if (diff >= 1000) {
                            g_current_fps = g_fps_counter;
                            g_fps_counter = 0;
                            g_fps_last_time = now;
                        }
                    } else if (!success) {
                        log_error("从摄像头获取帧失败，ID=" + std::to_string(camera_id));
                        std::cerr << "Failed to grab frame from camera" << std::endl;
                    }
                    
                    // 短暂休眠以减少CPU使用率
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
                
                log_info("摄像头捕获线程已停止，ID=" + std::to_string(camera_id));
            });
            
            log_info("摄像头成功启动，ID=" + std::to_string(camera_id));
            return true;
        } catch (const cv::Exception& e) {
            log_error("启动摄像头时发生OpenCV异常: " + std::string(e.what()));
            std::cerr << "Failed to start camera with ID " << camera_id << ": " << e.what() << std::endl;
            return false;
        }
    }
    
    // 停止摄像头 - 确保同时停止检测线程
    EXPORT void stop_camera() {
        log_info("调用 stop_camera()");
        
        if (!g_is_camera_running.load()) {
            log_info("摄像头未运行，无需停止");
            return;
        }
        
        log_info("设置g_is_camera_running=false");
        g_is_camera_running.store(false);
        
        // 按需编码模式，无需停止JPEG编码线程
        log_info("按需编码模式，无JPEG后台线程需要停止");
        
        // 停止人脸检测线程
        log_info("停止人脸检测线程");
        stopDetectionThread();
        log_info("人脸检测线程已成功停止");
        
        if (g_camera_thread.joinable()) {
            log_info("等待摄像头捕获线程结束");
            g_camera_thread.join();
            log_info("摄像头捕获线程已成功停止");
        }
        
        log_info("释放摄像头资源");
        if (g_capture_ptr) {
            g_capture_ptr->release();
            g_capture_ptr.reset();
        }
        log_info("摄像头已完全停止");
    }
    
    // 获取最后的错误信息
    EXPORT const char* get_last_error() {
        return g_last_error_message.c_str();
    }

    // 获取当前使用的摄像头后端
    EXPORT const char* get_current_backend() {
        return g_current_backend.c_str();
    }

    // 检测可用的摄像头设备
    EXPORT int detect_available_cameras(int* camera_ids, int max_count) {
        if (!camera_ids || max_count <= 0) {
            return 0;
        }

        int found_count = 0;
        log_info("开始检测可用的摄像头设备...");

        for (int i = 0; i < 10 && found_count < max_count; i++) {
            auto test_cap = std::make_shared<cv::VideoCapture>();
            bool opened = false;

            try {
#ifdef _WIN32
                if (test_cap->open(i, cv::CAP_DSHOW) ||
                    test_cap->open(i, cv::CAP_MSMF) ||
                    test_cap->open(i)) {
#else
                if (test_cap->open(i)) {
#endif
                    opened = true;
                    camera_ids[found_count] = i;
                    found_count++;
                    log_info("检测到摄像头设备 ID=" + std::to_string(i));
                }
                test_cap->release();
            } catch (...) {
                // 忽略检测过程中的异常
            }
        }

        log_info("检测完成，找到 " + std::to_string(found_count) + " 个摄像头设备");
        return found_count;
    }

    // 拍照功能实现 - 接收目录路径并在内部生成文件名
    EXPORT bool take_photo(const char* directory_path) {
        log_info("调用 take_photo(), 保存目录: " + std::string(directory_path));
        
        if (!g_is_camera_running) {
            log_error("摄像头未运行，无法拍照");
            return false;
        }
        
        try {
            // 生成带时间戳的文件名
            time_t now = time(0);
            struct tm timeinfo;
            char timestamp[30];
#ifdef _WIN32
            localtime_s(&timeinfo, &now);
#else
            localtime_r(&now, &timeinfo);
#endif
            strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &timeinfo);
            
            // 标准化目录路径 - 使用正斜杠
            std::string dir_path = std::string(directory_path);
            log_info("原始目录路径: " + dir_path);
            
            // 替换所有反斜杠为正斜杠
            for (char& c : dir_path) {
                if (c == '\\') c = '/';
            }
            log_info("标准化后目录路径: " + dir_path);
            
            // 确保路径以分隔符结尾
            if (!dir_path.empty() && dir_path.back() != '/') {
                dir_path += '/';
                log_info("添加分隔符后目录路径: " + dir_path);
            }
            
            // 确保目录存在
            std::cout << "确保目录存在: " << dir_path << std::endl;
            bool dir_exists = false;
            
#ifdef _WIN32
            // 使用Windows API检查并创建目录
            std::wstring wdir_path = utf8_to_wstring(dir_path);
            DWORD dir_attr = GetFileAttributesW(wdir_path.c_str());
            if (dir_attr != INVALID_FILE_ATTRIBUTES && (dir_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                dir_exists = true;
                log_info("目录已存在: " + dir_path);
            } else {
                log_info("目录不存在，尝试创建: " + dir_path);
                // 递归创建目录
                std::string temp_dir = dir_path;
                // 移除末尾的斜杠
                if (temp_dir.back() == '/') {
                    temp_dir.pop_back();
                }
                
                std::wstring current_path;
                std::wstring full_wdir_path = utf8_to_wstring(temp_dir);
                for (wchar_t& c : full_wdir_path) {
                    if (c == L'/') c = L'\\';  // Windows API使用反斜杠
                }
                
                // 创建所有层级的目录
                for (size_t i = 0; i < full_wdir_path.length(); ++i) {
                    current_path += full_wdir_path[i];
                    if (full_wdir_path[i] == L'\\' || i == full_wdir_path.length() - 1) {
                        DWORD attrib = GetFileAttributesW(current_path.c_str());
                        if (attrib == INVALID_FILE_ATTRIBUTES) {
                            // 目录不存在，尝试创建
                            if (!CreateDirectoryW(current_path.c_str(), NULL) && GetLastError() != ERROR_ALREADY_EXISTS) {
                                DWORD error = GetLastError();
                                log_error("创建目录失败: " + std::string(current_path.begin(), current_path.end()) + 
                                        ", 错误码: " + std::to_string(error));
                            } else {
                                log_info("成功创建目录: " + std::string(current_path.begin(), current_path.end()));
                            }
                        }
                    }
                }
                
                // 再次检查目录是否创建成功
                dir_attr = GetFileAttributesW(wdir_path.c_str());
                if (dir_attr != INVALID_FILE_ATTRIBUTES && (dir_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                    dir_exists = true;
                    log_info("成功创建目录: " + dir_path);
                } else {
                    log_error("无法创建目录: " + dir_path);
                }
            }
#else
            // 非Windows平台使用mkdir
            DIR* dir = opendir(dir_path.c_str());
            if (dir) {
                closedir(dir);
                dir_exists = true;
            } else {
                // 目录不存在，尝试创建
                if (mkdir(dir_path.c_str(), 0755) == 0) {
                    dir_exists = true;
                }
            }
#endif
            
            if (!dir_exists) {
                log_error("无法创建或访问目录: " + dir_path);
                return false;
            }
            
            // 组合目录和文件名 - 显式创建完整的文件路径
            std::string filename = "photo_" + std::string(timestamp) + ".jpg";
            std::string full_path = dir_path + filename;
            
            std::cout << "拍照保存完整路径: " << full_path << std::endl;
            log_info("生成的完整文件路径: " + full_path);
            
            // 获取高分辨率图像进行保存
            log_info("正在从摄像头获取高分辨率图像");
            cv::Mat frame;
            bool success = false;
            if (g_capture_ptr) {
                success = g_capture_ptr->read(frame);
            }
            
            if (!success || frame.empty()) {
                log_error("获取帧失败，无法拍照");
                std::cerr << "Failed to capture frame for photo" << std::endl;
                return false;
            }
            
            log_info("图像获取成功，尺寸: " + std::to_string(frame.cols) + "x" + std::to_string(frame.rows));
            
            // 使用我们的辅助函数保存图像，支持中文路径
            log_info("开始保存图像到路径: " + full_path);
            bool result = saveImageToPath(frame, full_path);
            if (result) {
                log_info("图像保存成功: " + full_path);
                
                // 验证文件确实被创建
#ifdef _WIN32
                std::wstring wfull_path = utf8_to_wstring(full_path);
                DWORD file_attr = GetFileAttributesW(wfull_path.c_str());
                if (file_attr != INVALID_FILE_ATTRIBUTES && !(file_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                    log_info("文件验证成功，已被正确创建: " + full_path);
                } else {
                    log_warning("保存可能成功，但文件验证失败: " + full_path);
                }
#endif
            } else {
                log_error("图像保存失败: " + full_path);
            }
            return result;
        } catch (const cv::Exception& e) {
            log_error("拍照时发生OpenCV异常: " + std::string(e.what()));
            std::cerr << "Failed to take photo: " << e.what() << std::endl;
            return false;
        } catch (const std::exception& e) {
            log_error("拍照时发生一般异常: " + std::string(e.what()));
            std::cerr << "General error taking photo: " << e.what() << std::endl;
            return false;
        }
    }
    
    // 录像功能 - 开始录制 - 接收目录路径并在内部生成文件名
    EXPORT bool start_recording(const char* directory_path) {
        log_info("调用 start_recording(), 保存目录: " + std::string(directory_path));
        
        if (!g_is_camera_running) {
            log_error("摄像头未运行，无法开始录像");
            return false;
        }
        
        // 如果已经在录制，先停止
        if (g_is_recording) {
            log_info("已有录像进行中，先停止当前录像");
            stop_recording();
        }
        
        try {
            // 避免使用mutex锁防止崩溃

            // 生成带时间戳的文件名
            time_t now = time(0);
            struct tm timeinfo;
            char timestamp[30];
#ifdef _WIN32
            localtime_s(&timeinfo, &now);
#else
            localtime_r(&now, &timeinfo);
#endif
            strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &timeinfo);
            
            // 标准化目录路径 - 使用正斜杠
            std::string dir_path = std::string(directory_path);
            log_info("原始目录路径: " + dir_path);
            
            // 替换所有反斜杠为正斜杠
            for (char& c : dir_path) {
                if (c == '\\') c = '/';
            }
            log_info("标准化后目录路径: " + dir_path);
            
            // 确保路径以分隔符结尾
            if (!dir_path.empty() && dir_path.back() != '/') {
                dir_path += '/';
                log_info("添加分隔符后目录路径: " + dir_path);
            }
            
            // 确保目录存在
            std::cout << "确保视频保存目录存在: " << dir_path << std::endl;
            bool dir_exists = false;
            
#ifdef _WIN32
            // 使用Windows API检查并创建目录
            std::wstring wdir_path = utf8_to_wstring(dir_path);
            DWORD dir_attr = GetFileAttributesW(wdir_path.c_str());
            if (dir_attr != INVALID_FILE_ATTRIBUTES && (dir_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                dir_exists = true;
                log_info("视频保存目录已存在: " + dir_path);
            } else {
                log_info("视频保存目录不存在，尝试创建: " + dir_path);
                // 递归创建目录
                std::string temp_dir = dir_path;
                // 移除末尾的斜杠
                if (temp_dir.back() == '/') {
                    temp_dir.pop_back();
                }
                
                std::wstring current_path;
                std::wstring full_wdir_path = utf8_to_wstring(temp_dir);
                for (wchar_t& c : full_wdir_path) {
                    if (c == L'/') c = L'\\';  // Windows API使用反斜杠
                }
                
                // 创建所有层级的目录
                for (size_t i = 0; i < full_wdir_path.length(); ++i) {
                    current_path += full_wdir_path[i];
                    if (full_wdir_path[i] == L'\\' || i == full_wdir_path.length() - 1) {
                        DWORD attrib = GetFileAttributesW(current_path.c_str());
                        if (attrib == INVALID_FILE_ATTRIBUTES) {
                            // 目录不存在，尝试创建
                            if (!CreateDirectoryW(current_path.c_str(), NULL) && GetLastError() != ERROR_ALREADY_EXISTS) {
                                DWORD error = GetLastError();
                                log_error("创建视频目录失败: " + std::string(current_path.begin(), current_path.end()) + 
                                        ", 错误码: " + std::to_string(error));
                            } else {
                                log_info("成功创建视频目录: " + std::string(current_path.begin(), current_path.end()));
                            }
                        }
                    }
                }
                
                // 再次检查目录是否创建成功
                dir_attr = GetFileAttributesW(wdir_path.c_str());
                if (dir_attr != INVALID_FILE_ATTRIBUTES && (dir_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                    dir_exists = true;
                    log_info("成功创建视频保存目录: " + dir_path);
                } else {
                    log_error("无法创建视频保存目录: " + dir_path);
                }
            }
#else
            // 非Windows平台使用mkdir
            DIR* dir = opendir(dir_path.c_str());
            if (dir) {
                closedir(dir);
                dir_exists = true;
            } else {
                // 目录不存在，尝试创建
                if (mkdir(dir_path.c_str(), 0755) == 0) {
                    dir_exists = true;
                }
            }
#endif
            
            if (!dir_exists) {
                log_error("无法创建或访问视频保存目录: " + dir_path);
                return false;
            }
            
            // 组合目录和文件名
            std::string filename = "video_" + std::string(timestamp) + ".avi";
            std::string full_path = dir_path + filename;
            
            log_info("录像保存完整路径: " + full_path);
            std::cout << "录像保存完整路径: " << full_path << std::endl;
            
            // 获取摄像头原始分辨率
            int width = g_original_width;
            int height = g_original_height;
            log_info("摄像头原始分辨率: " + std::to_string(width) + "x" + std::to_string(height));
            
            if (width <= 0 || height <= 0) {
                width = 640;
                height = 480;
                log_warning("使用默认分辨率: 640x480");
            }
            
            // 尝试几种常见的编解码器，从兼容性最好的开始
            std::vector<int> codecs = {
                cv::VideoWriter::fourcc('M', 'J', 'P', 'G'), // MJPEG通常支持最广泛
                cv::VideoWriter::fourcc('X', 'V', 'I', 'D'), // XVID常见支持
                cv::VideoWriter::fourcc('X', '2', '6', '4'), // H.264
                0 // 未压缩，作为最后选择
            };
            
            bool success = false;
            for (int codec : codecs) {
                // 将fourcc转为可读形式
                char fourcc_str[5] = {0};
                if (codec != 0) {
                    fourcc_str[0] = static_cast<char>((codec & 0XFF));
                    fourcc_str[1] = static_cast<char>((codec & 0XFF00) >> 8);
                    fourcc_str[2] = static_cast<char>((codec & 0XFF0000) >> 16);
                    fourcc_str[3] = static_cast<char>((codec & 0XFF000000) >> 24);
                } else {
                    strcpy(fourcc_str, "RAW");
                }
                
                log_info("尝试使用编解码器: '" + std::string(fourcc_str) + "' 创建视频，FPS: 30.0");
                double fps = 30.0;
                
                if (!g_video_writer_ptr) {
                    g_video_writer_ptr = std::make_shared<cv::VideoWriter>();
                }
                if (createVideoWriter(*g_video_writer_ptr, full_path, codec, fps, cv::Size(width, height))) {
                    log_info("使用编解码器 '" + std::string(fourcc_str) + "' 成功创建视频");
                    success = true;
                    break;
                }
                
                log_warning("使用编解码器 '" + std::string(fourcc_str) + "' 创建视频失败，尝试下一个");
            }
            
            if (!success) {
                log_error("所有编解码器都无法创建视频，请检查路径权限或编解码器支持: " + full_path);
                return false;
            }
            
            // 检查视频文件是否真的成功创建
#ifdef _WIN32
            std::wstring wfull_path = utf8_to_wstring(full_path);
            DWORD file_attr = GetFileAttributesW(wfull_path.c_str());
            if (file_attr != INVALID_FILE_ATTRIBUTES && !(file_attr & FILE_ATTRIBUTE_DIRECTORY)) {
                log_info("视频文件初始化成功: " + full_path);
            } else {
                log_warning("视频文件可能未正确初始化: " + full_path);
            }
#endif
            
            g_recording_path = full_path;
            g_is_recording = true;
            log_info("录像成功开始: " + full_path);
            return true;
        } catch (const std::exception& e) {
            log_error("开始录像失败，异常: " + std::string(e.what()));
            std::cerr << "Failed to start recording: " << e.what() << std::endl;
            return false;
        }
    }
    
    // 停止录像
    EXPORT bool stop_recording() {
        log_info("调用 stop_recording()");
        
        try {
            // 避免使用mutex锁防止崩溃
            if (g_is_recording.load()) {
                log_info("停止录像，文件路径: " + g_recording_path);
                g_is_recording.store(false);
                if (g_video_writer_ptr && g_video_writer_ptr->isOpened()) {
                    log_info("关闭VideoWriter");
                    g_video_writer_ptr->release();
                    g_video_writer_ptr.reset();
                    log_info("VideoWriter成功关闭");
                } else {
                    log_warning("VideoWriter已经关闭");
                }
                return true;
            } else {
                log_info("没有正在进行的录像");
            }
            return false;
        } catch (const std::exception& e) {
            log_error("停止录像失败，异常: " + std::string(e.what()));
            std::cerr << "Failed to stop recording: " << e.what() << std::endl;
            return false;
        }
    }
    
    // 查询是否在录像
    EXPORT bool is_recording() {
        // 避免使用mutex锁防止崩溃
        return g_is_recording.load();
    }
    
    // 获取帧宽度
    EXPORT int get_frame_width() {
        return g_frame_width;
    }
    
    // 获取帧高度
    EXPORT int get_frame_height() {
        return g_frame_height;
    }
    
    // 获取帧通道数
    EXPORT int get_frame_channels() {
        return g_frame_channels;
    }
    
    // 获取原始宽度
    EXPORT int get_original_width() {
        return g_original_width;
    }
    
    // 获取原始高度
    EXPORT int get_original_height() {
        return g_original_height;
    }
    
    // 获取原始帧大小
    EXPORT void get_original_frame_size(int* width, int* height) {
        *width = g_original_width;
        *height = g_original_height;
    }
    
    // 获取当前帧信息 - 仅大小信息
    EXPORT bool get_frame_info(int* width, int* height, int* channels) {
        if (!g_is_camera_running || g_frame_width <= 0 || g_frame_height <= 0) {
            return false;
        }
        
        try {
            // 避免使用mutex锁防止崩溃
            if (!g_camera_frame_ptr || g_camera_frame_ptr->empty()) {
                return false;
            }

            *width = g_frame_width;
            *height = g_frame_height;
            *channels = g_frame_channels;
            
            return true;
        } catch (const cv::Exception& e) {
            std::cerr << "Failed to get frame info: " << e.what() << std::endl;
        }
        
        return false;
    }
    
    // 获取当前帧数据 - 返回实际数据
    EXPORT unsigned char* get_frame_data(int* width, int* height, int* channels) {
        if (!g_is_camera_running || g_frame_width <= 0 || g_frame_height <= 0) {
            return nullptr;
        }
        
        try {
            // 避免使用mutex锁防止崩溃
            if (!g_display_frame_ptr || g_display_frame_ptr->empty()) {
                return nullptr;
            }

            *width = g_frame_width;
            *height = g_frame_height;
            *channels = g_frame_channels;

            int data_size = static_cast<int>(g_display_frame_ptr->total() * g_display_frame_ptr->elemSize());
            unsigned char* data = (unsigned char*)malloc(data_size);
            if (data) {
                memcpy(data, g_display_frame_ptr->data, data_size);
                return data;
            }
        } catch (const cv::Exception& e) {
            std::cerr << "Failed to get frame data: " << e.what() << std::endl;
        }
        
        return nullptr;
    }
    
    // 简化的图像处理函数
    EXPORT unsigned char* process_image(
        unsigned char* imageData,
        int width,
        int height,
        int channels
    ) {
        return nullptr; // 未使用，以减少内存消耗
    }
    
    // 释放图像内存
    EXPORT void free_image(unsigned char* imageData) {
        if (imageData) {
            free(imageData);
        }
    }

    // 添加一个新的导出函数来设置这个变量
    EXPORT void set_show_face_boxes(bool show) {
        g_show_face_boxes = show;
    }
    
    EXPORT bool get_show_face_boxes() {
        return g_show_face_boxes;
    }

    // Set face detection callback function
    EXPORT void set_face_detection_callback(FaceDetectionCallback callback) {
        g_face_detection_callback = callback;
        if (callback) {
            log_info("Face detection callback function set successfully (not NULL)");
        } else {
            log_info("Face detection callback function set to NULL");
        }
    }

    // ============= 轮询机制接口函数 =============
    
    // 检查是否有新的人脸捕获数据
    EXPORT bool has_new_face_capture() {
        bool hasNew = g_has_new_face_capture.load();
        std::cout << "has_new_face_capture called: " << (hasNew ? "有新数据" : "无新数据") << std::endl;
        return hasNew;
    }
    
    // 获取人脸捕获信息（不包含图像数据）
    EXPORT bool get_face_capture_info(float* confidence, int* dataSize) {
        // 避免使用mutex锁防止崩溃
        if (!g_latest_face_capture_ptr || !g_latest_face_capture_ptr->isValid) {
            return false;
        }

        if (confidence) *confidence = g_latest_face_capture_ptr->confidence;
        if (dataSize) *dataSize = static_cast<int>(g_latest_face_capture_ptr->imageData.size());

        std::cout << "get_face_capture_info: 置信度=" << g_latest_face_capture_ptr->confidence
                 << ", 数据大小=" << g_latest_face_capture_ptr->imageData.size() << std::endl;

        return true;
    }
    
    // 获取最新的人脸捕获数据（按需编码优化版本）
    EXPORT bool get_latest_face_capture(unsigned char** imageData, int* imageSize, float* confidence) {
        // 避免使用mutex锁防止崩溃
        if (!g_latest_face_capture_ptr || !g_latest_face_capture_ptr->isValid) {
            std::cout << "get_latest_face_capture: 无有效数据可获取" << std::endl;
            return false;
        }

        // 按需编码：如果还没有编码，现在才编码为JPEG
        if (!g_latest_face_capture_ptr->isEncoded) {
            if (g_latest_face_capture_ptr->rawImageMat.empty()) {
                std::cout << "get_latest_face_capture: 原始图像数据为空" << std::endl;
                return false;
            }

            std::cout << "Flutter请求人脸数据，开始按需编码为JPEG..." << std::endl;

            // 编码为JPEG
            std::vector<uchar> jpeg_buffer;
            std::vector<int> encode_params;
            encode_params.push_back(cv::IMWRITE_JPEG_QUALITY);
            encode_params.push_back(90);  // 高质量JPEG

            if (!cv::imencode(".jpg", g_latest_face_capture_ptr->rawImageMat, jpeg_buffer, encode_params)) {
                std::cout << "get_latest_face_capture: JPEG编码失败" << std::endl;
                return false;
            }

            // 保存编码后的数据
            g_latest_face_capture_ptr->imageData = std::move(jpeg_buffer);
            g_latest_face_capture_ptr->isEncoded = true;

            std::cout << "按需编码完成，JPEG大小: " << g_latest_face_capture_ptr->imageData.size() << " 字节" << std::endl;
        }

        // 分配内存并复制JPEG数据
        size_t dataSize = g_latest_face_capture_ptr->imageData.size();
        unsigned char* buffer = (unsigned char*)malloc(dataSize);
        if (!buffer) {
            std::cout << "get_latest_face_capture: 内存分配失败" << std::endl;
            return false;
        }

        memcpy(buffer, g_latest_face_capture_ptr->imageData.data(), dataSize);

        *imageData = buffer;
        *imageSize = static_cast<int>(dataSize);
        *confidence = g_latest_face_capture_ptr->confidence;

        std::cout << "人脸数据获取成功，置信度: " << *confidence << "，数据大小: " << *imageSize << std::endl;

        // 获取后自动清理缓存（一次性消费模式）
        g_latest_face_capture_ptr->clear();
        g_has_new_face_capture.store(false);

        std::cout << "人脸缓存已自动清理" << std::endl;

        return true;
    }
    
    // 清理已获取的人脸数据（按需编码优化版本）
    EXPORT void clear_face_capture() {
        // 避免使用mutex锁防止崩溃
        if (g_latest_face_capture_ptr) {
            g_latest_face_capture_ptr->clear();  // 使用新的clear方法，清理所有数据
        }

        g_has_new_face_capture.store(false);

        std::cout << "clear_face_capture: 人脸缓存已清理（包括原始Mat数据）" << std::endl;
    }
}