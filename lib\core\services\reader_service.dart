import 'package:flutter/foundation.dart'; // For VoidCallback
import 'package:hardware/hardware.dart';
import 'package:logging/logging.dart';
import 'package:seasetting/seasetting.dart'; // Needed for HWReaderSettingData

// TODO: Verify this import path or remove if HardwareStateManager is not used here
// import 'package:your_app/core/state/hardware_state_manager.dart';

class ReaderService {
  static final ReaderService _instance = ReaderService._internal();
  factory ReaderService() => _instance;
  ReaderService._internal();

  final _readerManager = ReaderManager.instance;
  final _logger = Logger('ReaderService');

  // Store initial settings if needed for reference, e.g., default power
  List<HWReaderSettingData> _initialReaderSettings = [];

  /// Initializes the reader manager with the base configuration.
  /// Specific reader/antenna activation happens via changeReaders.
  Future<void> initReaders(List<HWReaderSettingData> readerSettings) async {
    _logger.info('初始化阅读器服务，配置 ${readerSettings.length} 个阅读器设置...');
    // Store initial settings for potential future use (e.g., restoring defaults)
    _initialReaderSettings = readerSettings;
    // Potentially perform any one-time setup with the reader manager here if needed.
    // Example: Setting a default communication protocol or global property.
    // _readerManager.setGlobalProperty(...) // Hypothetical example
    _logger.info('阅读器服务初始化完成。');
    // Note: Actual hardware port opening and configuration is deferred to changeReaders/open
  }

  /// Configures the reader hardware based on the provided settings string.
  /// This typically activates specific antennas/readers for an operation.
  Future<void> changeReaders(String readerSettingsJson) async {
    try {
      _logger.info('动态配置阅读器: $readerSettingsJson');
      // Pass the JSON settings string directly to the manager
      await _readerManager.changeReaders(readerSettingsJson);
      _logger.info('阅读器动态配置完成。');
      // State update should happen in the calling code (e.g., OperationMixin)
    } catch (e, stack) {
      _logger.severe('动态配置阅读器失败', e, stack);
      rethrow; // Propagate error
    }
  }

  /// Opens the communication channel(s) to the configured reader(s).
  int open() {
    try {
      _logger.info('打开阅读器通信...');
      final result = _readerManager.open();
      _logger.info('阅读器打开操作结果: $result');
      // State update in calling code
      return result;
    } catch (e, stack) {
      _logger.severe('打开阅读器通信失败', e, stack);
      // State update in calling code
      return -1; // Indicate error
    }
  }

  /// Closes the communication channel(s).
  int close() {
    try {
      _logger.info('关闭阅读器通信...');
      final result = _readerManager.close();
       _logger.info('阅读器关闭操作结果: $result');
      // State update in calling code
      return result;
    } catch (e, stack) {
      _logger.severe('关闭阅读器通信失败', e, stack);
      // State update in calling code
      return -1; // Indicate error
    }
  }

  /// Starts the inventory (scanning) process.
  int startInventory() {
    try {
      _logger.info('启动扫描 (Inventory)...');
      final result = _readerManager.startInventory();
       _logger.info('启动扫描操作结果: $result');
      return result;
    } catch (e, stack) {
      _logger.severe('启动扫描失败', e, stack);
      return -1; // Indicate error
    }
  }

   /// Stops the inventory process.
  int stopInventory() {
    try {
      _logger.info('停止扫描 (Inventory)...');
      final result = _readerManager.stopInventory();
      _logger.info('停止扫描操作结果: $result');
      return result;
    } catch (e, stack) {
      _logger.severe('停止扫描失败', e, stack);
      return -1; // Indicate error
    }
  }

  /// Pauses the inventory process.
  int pauseInventory() {
    try {
      _logger.info('暂停扫描 (Inventory)...');
      final result = _readerManager.pauseInventory();
       _logger.info('暂停扫描操作结果: $result');
      return result;
    } catch (e, stack) {
      _logger.severe('暂停扫描失败', e, stack);
      return -1; // Indicate error
    }
  }

  /// Resumes the inventory process.
  int resumeInventory() {
    try {
      _logger.info('恢复扫描 (Inventory)...');
      final result = _readerManager.resumeInventory();
       _logger.info('恢复扫描操作结果: $result');
      return result;
    } catch (e, stack) {
      _logger.severe('恢复扫描失败', e, stack);
      return -1; // Indicate error
    }
  }

  /// Adds a listener for tag data updates.
  void addReaderListener(VoidCallback listener) {
    _readerManager.controller.addListener(listener);
  }

  /// Removes a listener for tag data updates.
  void removeReaderListener(VoidCallback listener) {
    _readerManager.controller.removeListener(listener);
  }

  /// Gets the current error/state type from the reader controller.
  ReaderErrorType getReaderState() {
    // This provides the low-level state. Application logic might manage a higher-level state.
    return _readerManager.controller.type;
  }

  /// Disposes resources, ensuring the reader is closed.
  void dispose() {
    _logger.info('释放 ReaderService 资源...');
    try {
      close(); // Ensure reader communication is closed
    } catch (e) {
      _logger.warning('关闭阅读器时出错 (dispose): $e');
    }
    // Listeners should be managed by the controller itself or removed by the calling code when necessary.
     _logger.info('ReaderService 资源释放完成。');
  }
}
