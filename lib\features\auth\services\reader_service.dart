import 'dart:convert';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hardware/hardware.dart';

/// 读卡器错误类型枚举
enum ReaderErrorType2 {
  connection,   // 连接错误
  hardware,     // 硬件错误
  timeout,      // 超时错误
  protocol,     // 协议错误
  unknown       // 未知错误
}

/// 读卡器服务异常类
class ReaderServiceException implements Exception {
  final String message;
  final ReaderErrorType2 type;
  final dynamic originalError;

  const ReaderServiceException(this.message, this.type, [this.originalError]);

  /// 创建连接错误
  static ReaderServiceException connection(String message, [dynamic error]) => 
      ReaderServiceException(message, ReaderErrorType2.connection, error);
      
  /// 创建硬件错误
  static ReaderServiceException hardware(String message, [dynamic error]) => 
      ReaderServiceException(message, ReaderErrorType2.hardware, error);
      
  /// 创建超时错误
  static ReaderServiceException timeout(String message, [dynamic error]) => 
      ReaderServiceException(message, ReaderErrorType2.timeout, error);

  @override
  String toString() => 'ReaderServiceException: $message${originalError != null ? '\nOriginal error: $originalError' : ''}';
}

/// 读卡器服务类 - 负责管理读卡器的连接、操作和状态
class ReaderService {
  // 单例实例
  static final ReaderService instance = ReaderService._();
  
  // 私有构造函数
  ReaderService._();

  // 常量配置
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(milliseconds: 500);
  static const Duration _closeTimeout = Duration(seconds: 3);
  static const int _maxCloseAttempts = 30;
  
  // 内部状态
  bool _isClosing = false;
  bool _isOpening = false;
  Timer? _closeTimeoutTimer;
  final List<HWReaderSettingData> _activeReaders = [];

  // 状态访问器
  bool get isClosing => _isClosing;
  bool get isOpening => _isOpening;
  bool get hasActiveReaders => _activeReaders.isNotEmpty;
  List<HWReaderSettingData> get activeReaders => List.unmodifiable(_activeReaders);

  /// 打开读卡器
  /// 
  /// [readers] 要打开的读卡器设置列表
  /// 如果列表为空，则直接返回
  /// 抛出 [ReaderServiceException] 当发生错误时
  Future<void> openReader(List<HWReaderSettingData> readers) async {
    if (readers.isEmpty) {
      debugPrint('没有要打开的读卡器');
      return;
    }

    // 防止重复调用
    if (_isOpening) {
      debugPrint('已有打开读卡器的操作正在进行');
      return;
    }
    _isOpening = true;

    try {
      // 过滤出需要打开的读卡器
      final list = readers.where((e) => e.isNeedOpenReader).toList();
      if (list.isEmpty) {
        debugPrint('没有需要打开的读卡器');
        return;
      }
      
      // 如果有活跃的读卡器，先关闭
      if (_activeReaders.isNotEmpty) {
        debugPrint('关闭已有的读卡器');
        await closeReader(showToast: false);
      }

      // 尝试打开读卡器
      await _retryOpenReader(list);
      
      // 更新活跃的读卡器列表
      _activeReaders.clear();
      _activeReaders.addAll(list);
      
      debugPrint('成功打开${list.length}个读卡器');
    } catch (e) {
      debugPrint('打开读卡器失败: $e');
      // 转换为统一的异常格式
      if (e is ReaderServiceException) {
        rethrow;
      } else {
        throw ReaderServiceException(
          '打开读卡器失败', 
          ReaderErrorType2.hardware,
          e
        );
      }
    } finally {
      _isOpening = false;
    }
  }

  /// 使用重试逻辑打开读卡器
  Future<void> _retryOpenReader(List<HWReaderSettingData> readers) async {
    int retryCount = 0;
    Exception? lastError;

    while (retryCount < _maxRetries) {
      try {
        await _tryOpenReader(readers);
        return; // 成功打开
      } catch (e) {
        retryCount++;
        lastError = e is Exception ? e : Exception(e.toString());
        debugPrint('打开读卡器失败(第$retryCount次尝试): $e');
        
        if (retryCount < _maxRetries) {
          // 延迟一段时间后重试
          await Future.delayed(_retryDelay);
        }
      }
    }

    // 所有重试都失败
    throw ReaderServiceException.hardware(
      '多次尝试后无法打开读卡器', 
      lastError
    );
  }

  /// 尝试打开读卡器的内部方法
  Future<void> _tryOpenReader(List<HWReaderSettingData> readers) async {
    try {
      // 转换读卡器配置为 JSON
      final readersJson = jsonEncode(readers);
      debugPrint('正在配置读卡器: ${readers.length}个设备');
      
      // 设置读卡器
      await ReaderManager.instance.changeReaders(readersJson);
      
      // 打开读卡器
      await ReaderManager.instance.open();
      
      // 等待读卡器初始化完成
      await ReaderManager.instance.untilDeteted();
      
      debugPrint('读卡器配置完成');
    } catch (e) {
      debugPrint('读卡器操作失败: $e');
      
      // 转换为更具体的错误类型
      if (e.toString().contains('timeout') || e.toString().contains('超时')) {
        throw ReaderServiceException.timeout('读卡器操作超时', e);
      } else if (e.toString().contains('connect') || e.toString().contains('连接')) {
        throw ReaderServiceException.connection('无法连接到读卡器', e);
      } else {
        rethrow;
      }
    }
  }

  /// 关闭读卡器
  /// 
  /// [showToast] 是否显示加载提示
  /// 返回一个 [Future] 表示操作是否成功完成
  Future<void> closeReader({bool showToast = true}) async {
    // 防止重复调用
    if (_isClosing) {
      debugPrint('已有关闭读卡器的操作正在进行');
      return;
    }
    
    // 如果没有活跃的读卡器，直接返回
    if (_activeReaders.isEmpty && !ReaderManager.instance.isConnectReader) {
      debugPrint('没有活跃的读卡器需要关闭');
      return;
    }
    
    _isClosing = true;

    try {
      if (showToast) EasyLoading.show();
      
      // 设置超时定时器
      _closeTimeoutTimer = Timer(_closeTimeout, () {
        debugPrint('关闭读卡器超时');
        _isClosing = false;
        _activeReaders.clear();
        if (showToast) EasyLoading.dismiss();
      });

      // 尝试停止正在进行的操作
      try {
        debugPrint('停止读卡器操作');
        await ReaderManager.instance.stopInventory();
      } catch (e) {
        // 忽略停止操作的错误，继续关闭连接
        debugPrint('停止读卡器操作失败: $e');
      }

      // 关闭读卡器连接
      debugPrint('关闭读卡器连接');
      await ReaderManager.instance.close();
      
      // 等待读卡器完全关闭
      await _waitForClose();
      
      // 清空活跃读卡器列表
      _activeReaders.clear();
      debugPrint('读卡器已成功关闭');
    } catch (e) {
      debugPrint('关闭读卡器失败: $e');
      throw ReaderServiceException('关闭读卡器失败', ReaderErrorType2.hardware, e);
    } finally {
      _closeTimeoutTimer?.cancel();
      _closeTimeoutTimer = null;
      _isClosing = false;
      if (showToast) EasyLoading.dismiss();
    }
  }

  /// 获取编码器
  /// 
  /// [tagFrequency] 标签频率
  /// [setting] 读卡器设置
  /// 返回对应的编码器接口实现
  CoderCommonInterface? getCoder(int? tagFrequency, HWReaderSettingData setting) {
    try {
      if (setting.readerType == 6) {
        return _getDoubleFrequencyCoder(tagFrequency, setting);
      }
      
      final decoderType = setting.info?.valueForKey('decoderType');
      if (decoderType == null) {
        debugPrint('警告: 读卡器未设置解码器类型');
        return null;
      }
      
      return SECoderMap[decoderType];
    } catch (e) {
      debugPrint('获取编码器失败: $e');
      return null;
    }
  }

  /// 获取双频编码器
  CoderCommonInterface? _getDoubleFrequencyCoder(
    int? tagFrequency,
    HWReaderSettingData setting,
  ) {
    final extraData = setting.extras?.firstOrNull;
    if (setting.selectedCardType == '高频&超高频' &&
        extraData != null &&
        extraData is HWDFExtraData) {
      // 根据标签频率选择不同的解码器
      final decoderKey = tagFrequency == 0
          ? extraData.valueForKey('hfDecoderType')
          : extraData.valueForKey('uhfDecoderType');
          
      if (decoderKey == null) {
        debugPrint('警告: ${tagFrequency == 0 ? '高频' : '超高频'}解码器类型未设置');
        return null;
      }
      
      return SECoderMap[decoderKey];
    }
    
    // 处理普通情况
    final decoderType = extraData?.valueForKey('decoderType');
    if (decoderType == null) {
      debugPrint('警告: 读卡器未设置解码器类型');
      return null;
    }
    
    return SECoderMap[decoderType];
  }

  /// 等待读卡器关闭
  /// 
  /// 使用计数器确保不会无限等待
  Future<void> _waitForClose() async {
    int attempts = 0;
    bool isStillConnected = false;
    
    do {
      isStillConnected = ReaderManager.instance.isConnectReader;
      if (!isStillConnected) break;
      
      await Future.delayed(const Duration(milliseconds: 100));
      attempts++;
      
      if (attempts % 10 == 0) {
        debugPrint('等待读卡器关闭，已尝试$attempts次');
      }
    } while (attempts < _maxCloseAttempts);

    if (isStillConnected) {
      debugPrint('读卡器关闭超时，强制清理');
      throw ReaderServiceException.timeout('读卡器关闭超时', null);
    } else {
      debugPrint('读卡器已完全关闭');
    }
  }
  
  /// 重置读卡器
  /// 尝试彻底清理读卡器状态，适用于读卡器陷入异常状态时
  Future<void> resetReader() async {
    try {
      // 确保所有操作停止
      try {
        await ReaderManager.instance.stopInventory();
      } catch (e) {
        debugPrint('停止读卡器操作失败: $e');
      }
      
      // 尝试关闭连接
      if (ReaderManager.instance.isConnectReader) {
        await ReaderManager.instance.close();
      }
      
      // 等待一段时间让硬件复位
      await Future.delayed(const Duration(seconds: 1));
      
      // 清理内部状态
      _isClosing = false;
      _isOpening = false;
      _activeReaders.clear();
      _closeTimeoutTimer?.cancel();
      _closeTimeoutTimer = null;
      
      debugPrint('读卡器已重置');
    } catch (e) {
      debugPrint('重置读卡器失败: $e');
      throw ReaderServiceException('重置读卡器失败', ReaderErrorType2.hardware, e);
    }
  }
}