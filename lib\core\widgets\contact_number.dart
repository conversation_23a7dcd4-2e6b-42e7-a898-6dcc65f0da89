import 'package:flutter/material.dart';

import '../utils/window_util.dart';
import 'select_language.dart';

class ContactNumber extends StatelessWidget {
  const ContactNumber({super.key});
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 170.p,
      padding:  EdgeInsets.only(left: 40.p),
        decoration:  const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color.fromRGBO(123, 173, 242, 0.2),
              Color.fromRGBO(165, 219, 255, 0.2),
            ],
          ),

        ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('客服电话：0755-88376811', style: TextStyle(color: Color(0xFF222222), fontSize: 22.p, fontWeight: FontWeight.w400,height: 22/26)),
              SizedBox(height: 10.p),
              Text('技术支持：深圳市海恒智能股份有限公司',style: TextStyle(color: Color(0xFF222222), fontSize: 22.p, fontWeight: FontWeight.w400,height: 22/26))
            ],
          ),
          SelectLanguage()
        ],
      ),
    );
  }
}




