import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;

/// SDK配置管理器 - 负责管理百度SDK的路径配置
class SdkConfigManager {
  static const String _configFileName = 'sdk_config.json';
  static const String _defaultSdkPath = 'D:\\FaceOfflineSdk';
  
  static SdkConfigManager? _instance;
  static SdkConfigManager get instance => _instance ??= SdkConfigManager._();
  
  SdkConfigManager._();
  
  String? _currentSdkPath;
  String? _configFilePath;
  
  /// 获取配置文件路径
  Future<String> _getConfigFilePath() async {
    if (_configFilePath != null) return _configFilePath!;
    
    // 在exe同目录下创建配置文件
    final exeDir = File(Platform.resolvedExecutable).parent.path;
    _configFilePath = path.join(exeDir, _configFileName);
    return _configFilePath!;
  }
  
  /// 加载配置
  Future<Map<String, dynamic>> _loadConfig() async {
    try {
      final configPath = await _getConfigFilePath();
      final configFile = File(configPath);
      
      if (!configFile.existsSync()) {
        print('配置文件不存在，使用默认配置: $configPath');
        return {
          'sdk_path': _defaultSdkPath,
          'auto_load': true,
          'last_updated': DateTime.now().toIso8601String(),
        };
      }
      
      final content = await configFile.readAsString();
      final config = json.decode(content) as Map<String, dynamic>;
      print('成功加载配置: $config');
      return config;
    } catch (e) {
      print('加载配置失败: $e，使用默认配置');
      return {
        'sdk_path': _defaultSdkPath,
        'auto_load': true,
        'last_updated': DateTime.now().toIso8601String(),
      };
    }
  }
  
  /// 保存配置
  Future<bool> _saveConfig(Map<String, dynamic> config) async {
    try {
      final configPath = await _getConfigFilePath();
      final configFile = File(configPath);
      
      config['last_updated'] = DateTime.now().toIso8601String();
      final content = const JsonEncoder.withIndent('  ').convert(config);
      
      await configFile.writeAsString(content);
      print('配置保存成功: $configPath');
      return true;
    } catch (e) {
      print('保存配置失败: $e');
      return false;
    }
  }
  
  /// 获取SDK路径
  Future<String> getSdkPath() async {
    if (_currentSdkPath != null) {
      return _currentSdkPath!;
    }
    
    final config = await _loadConfig();
    _currentSdkPath = config['sdk_path'] as String? ?? _defaultSdkPath;
    return _currentSdkPath!;
  }
  
  /// 设置SDK路径
  Future<bool> setSdkPath(String newPath) async {
    try {
      // 验证路径格式
      if (newPath.isEmpty) {
        throw ArgumentError('SDK路径不能为空');
      }
      
      // 规范化路径
      final normalizedPath = path.normalize(newPath);
      
      // 加载现有配置
      final config = await _loadConfig();
      config['sdk_path'] = normalizedPath;
      
      // 保存配置
      final success = await _saveConfig(config);
      if (success) {
        _currentSdkPath = normalizedPath;
        print('SDK路径已更新: $normalizedPath');
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('设置SDK路径失败: $e');
      return false;
    }
  }
  
  /// 验证SDK路径是否有效
  Future<SdkValidationResult> validateSdkPath([String? testPath]) async {
    final pathToValidate = testPath ?? await getSdkPath();
    
    try {
      final sdkDir = Directory(pathToValidate);
      if (!sdkDir.existsSync()) {
        return SdkValidationResult(
          isValid: false,
          error: 'SDK目录不存在: $pathToValidate',
        );
      }
      
      // 检查关键子目录
      final x64Dir = Directory(path.join(pathToValidate, 'x64'));
      if (!x64Dir.existsSync()) {
        return SdkValidationResult(
          isValid: false,
          error: 'x64目录不存在: ${x64Dir.path}',
        );
      }
      
      final modelsDir = Directory(path.join(pathToValidate, 'models'));
      if (!modelsDir.existsSync()) {
        return SdkValidationResult(
          isValid: false,
          error: 'models目录不存在: ${modelsDir.path}',
        );
      }
      
      // 检查关键DLL文件
      final requiredDlls = [
        'BaiduFaceApi.dll',
        'face_sdk.dll',
        'opencv_world320.dll',
        'paddle_inference.dll',
      ];
      
      final List<String> missingDlls = [];
      for (final dll in requiredDlls) {
        final dllFile = File(path.join(pathToValidate, 'x64', dll));
        if (!dllFile.existsSync()) {
          missingDlls.add(dll);
        }
      }
      
      if (missingDlls.isNotEmpty) {
        return SdkValidationResult(
          isValid: false,
          error: '缺少关键DLL文件: ${missingDlls.join(', ')}',
        );
      }
      
      // 验证通过
      return SdkValidationResult(
        isValid: true,
        message: 'SDK路径验证成功',
        detectedVersion: await _detectSdkVersion(pathToValidate),
      );
      
    } catch (e) {
      return SdkValidationResult(
        isValid: false,
        error: '验证SDK路径时发生错误: $e',
      );
    }
  }
  
  /// 检测SDK版本（可选功能）
  Future<String?> _detectSdkVersion(String sdkPath) async {
    try {
      // 可以通过读取某个版本文件或者DLL属性来获取版本信息
      // 这里简单返回一个默认值
      return '8.4.0'; // 从之前文档中看到的版本号
    } catch (e) {
      return null;
    }
  }
  
  /// 获取所有配置信息
  Future<Map<String, dynamic>> getAllConfig() async {
    return await _loadConfig();
  }
  
  /// 重置为默认配置
  Future<bool> resetToDefault() async {
    _currentSdkPath = null;
    return await setSdkPath(_defaultSdkPath);
  }
  
  /// 获取最近使用的SDK路径列表（可扩展功能）
  Future<List<String>> getRecentSdkPaths() async {
    final config = await _loadConfig();
    final List<dynamic>? recent = config['recent_paths'] as List<dynamic>?;
    return recent?.cast<String>() ?? [];
  }
  
  /// 添加到最近使用列表
  Future<void> addToRecentPaths(String sdkPath) async {
    try {
      final config = await _loadConfig();
      List<String> recentPaths = (config['recent_paths'] as List<dynamic>?)?.cast<String>() ?? [];
      
      // 移除重复项
      recentPaths.remove(sdkPath);
      // 添加到开头
      recentPaths.insert(0, sdkPath);
      // 限制数量
      if (recentPaths.length > 5) {
        recentPaths = recentPaths.take(5).toList();
      }
      
      config['recent_paths'] = recentPaths;
      await _saveConfig(config);
    } catch (e) {
      print('添加到最近路径失败: $e');
    }
  }
}

/// SDK验证结果
class SdkValidationResult {
  final bool isValid;
  final String? error;
  final String? message;
  final String? detectedVersion;
  
  SdkValidationResult({
    required this.isValid,
    this.error,
    this.message,
    this.detectedVersion,
  });
  
  @override
  String toString() {
    if (isValid) {
      return 'SDK验证成功${detectedVersion != null ? ' (版本: $detectedVersion)' : ''}';
    } else {
      return 'SDK验证失败: $error';
    }
  }
} 