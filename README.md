# A3G 智能门禁认证系统

[![Flutter](https://img.shields.io/badge/Flutter-3.0+-blue.svg)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-2.17+-blue.svg)](https://dart.dev/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 📋 项目概述

A3G是一个基于Flutter开发的智能门禁认证系统，专为图书馆、办公场所、学校等场景设计。系统支持多达15种不同的认证方式，采用模块化架构设计，具有高度的可配置性和扩展性。

### 🎯 核心特性

- **多重认证支持**：同时支持多种认证方式并行运行
- **实时响应**：毫秒级认证响应，无感知用户体验
- **智能切换**：根据配置自动切换单一/多重认证模式
- **完整记录**：详细的认证日志和统计分析
- **模块化设计**：高度解耦，易于扩展和维护
- **跨平台支持**：支持Windows、Android、iOS等多平台

## 🏗️ 技术架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                      A3G 门禁认证系统                        │
├─────────────────────────────────────────────────────────────┤
│  UI Layer (Views & Widgets)                                │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ Auth Views  │ Admin Views │ Home Views  │ Query Views │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer (ViewModels)                         │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ AuthViewModel│AdminViewModel│UserProvider │QueryViewModel│  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │PostAuthSvc  │MultiAuthMgr │RadarService │HardwareSvc  │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ SQLite DB   │ Settings    │ Hardware    │ Network     │   │
│  │ Manager     │ Provider    │ Manager     │ Service     │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Hardware Interface Layer                                   │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ Card Reader │ Face Camera │ QR Scanner  │ Lock Device │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 📁 目录结构详解

```
lib/
├── core/                           # 核心基础架构
│   ├── auth/                       # 认证核心
│   ├── config/                     # 配置管理
│   │   └── app_config.dart        # 应用配置
│   ├── database/                   # 数据库层
│   │   ├── db_manager.dart        # 数据库管理器
│   │   ├── implementations/        # 数据库实现
│   │   │   ├── sqlite_db.dart     # SQLite实现
│   │   │   └── mysql_db.dart      # MySQL实现
│   │   └── interfaces/            # 数据库接口
│   ├── initializer/               # 初始化器
│   │   └── app_initializer.dart   # 应用初始化
│   ├── providers/                 # 状态管理
│   │   ├── app_providers.dart     # 全局Provider
│   │   ├── countdown_provider.dart # 倒计时Provider
│   │   └── user_provider.dart     # 用户状态Provider
│   ├── router/                    # 路由管理
│   │   ├── app_router.dart        # 路由配置
│   │   └── route_params.dart      # 路由参数
│   ├── services/                  # 核心服务
│   │   ├── post_auth_service.dart # 认证后处理服务
│   │   ├── hardware_service.dart  # 硬件服务
│   │   └── lock_service.dart      # 门锁服务
│   ├── utils/                     # 工具类
│   │   ├── dialog_util.dart       # 对话框工具
│   │   ├── error_handler.dart     # 错误处理
│   │   └── window_util.dart       # 窗口工具
│   └── widgets/                   # 通用组件
│       ├── base_page.dart         # 基础页面
│       └── auth_item.dart         # 认证项组件
├── features/                      # 功能模块
│   ├── auth/                      # 认证模块
│   │   ├── models/                # 认证数据模型
│   │   │   └── auth_result.dart   # 认证结果模型
│   │   ├── repositories/          # 认证仓库
│   │   │   └── auth_repository.dart
│   │   ├── services/              # 认证服务
│   │   │   ├── multi_auth_manager.dart    # 多认证管理器
│   │   │   ├── auth_priority_manager.dart # 认证优先级管理
│   │   │   ├── card_reader_service.dart   # 读卡器服务
│   │   │   └── face_auth_service.dart     # 人脸认证服务
│   │   ├── view_models/           # 认证视图模型
│   │   │   └── auth_view_model.dart
│   │   ├── views/                 # 认证视图
│   │   │   ├── auth_page.dart     # 认证页面
│   │   │   ├── auth_view.dart     # 认证视图
│   │   │   └── widgets/           # 认证组件
│   │   └── widgets/               # 认证UI组件
│   ├── admin/                     # 管理模块
│   │   ├── view_models/
│   │   │   ├── admin_view_model.dart
│   │   │   └── error_records_view_model.dart
│   │   └── views/
│   │       ├── admin_page.dart    # 管理员页面
│   │       └── error_records_page.dart
│   ├── home/                      # 主页模块
│   │   └── views/
│   │       ├── home_page.dart     # 首页
│   │       └── index_page.dart    # 入口页面
│   ├── login/                     # 登录模块
│   │   ├── models/
│   │   │   └── operation_type.dart # 操作类型
│   │   └── views/
│   │       └── login_type_page.dart
│   └── query/                     # 查询模块
│       ├── models/
│       │   └── record_model.dart  # 记录模型
│       ├── view_models/
│       │   └── query_view_model.dart
│       └── views/
│           └── query_page.dart    # 查询页面
├── generated/                     # 生成文件
│   ├── intl/                     # 国际化文件
│   └── l10n.dart                 # 本地化配置
├── l10n/                         # 本地化资源
│   ├── intl_en.arb              # 英文资源
│   └── intl_zh.arb              # 中文资源
├── shared/                       # 共享资源
│   ├── constants/                # 常量定义
│   ├── themes/                   # 主题配置
│   ├── utils/                    # 共享工具
│   └── widgets/                  # 共享组件
└── main.dart                     # 应用入口
```

## 🔐 认证系统详解

### 支持的认证方式

系统支持以下15种认证方式，可根据实际需求灵活配置：

| 认证方式 | 类型标识 | 硬件要求 | 说明 |
|---------|---------|---------|------|
| 人脸识别 | `AuthMethod.face` | 摄像头 | 基于AI的人脸识别技术 |
| 读者证 | `AuthMethod.readerCard` | 读卡器 | 标准IC卡/ID卡读取 |
| 身份证 | `AuthMethod.idCard` | 身份证读卡器 | 二代身份证读取 |
| 社保卡 | `AuthMethod.socialSecurityCard` | 社保卡读卡器 | 社会保障卡读取 |
| 市民卡 | `AuthMethod.citizenCard` | 读卡器 | 市民服务卡读取 |
| 电子社保卡 | `AuthMethod.eletricSocialSecurityCard` | 二维码扫描 | 电子社保卡二维码 |
| 二维码 | `AuthMethod.qrCode` | 摄像头/扫描器 | 通用二维码认证 |
| 微信二维码 | `AuthMethod.wechatQRCode` | 摄像头/扫描器 | 微信专用二维码 |
| 微信扫码 | `AuthMethod.wechatScanQRCode` | 微信小程序 | 微信扫描系统二维码 |
| 支付宝二维码 | `AuthMethod.alipayQRCode` | 摄像头/扫描器 | 支付宝付款码 |
| 支付宝信用 | `AuthMethod.aliCreditQRCode` | 摄像头/扫描器 | 支付宝信用付款码 |
| 汇文二维码 | `AuthMethod.huiwenQRCode` | 摄像头/扫描器 | 汇文图书管理系统 |
| 上海随申码 | `AuthMethod.shangHaiQRCode` | 摄像头/扫描器 | 上海地区专用码 |
| 键盘输入 | `AuthMethod.keyboardInput` | 键盘 | 手动输入账号密码 |
| 腾讯T卡 | `AuthMethod.tencentTCard` | NFC读卡器 | 腾讯公交卡 |
| 借阅宝 | `AuthMethod.jieYueBao` | 专用读卡器 | 借阅宝设备 |

### 认证模式

#### 单一认证模式
```dart
// 当只配置一种认证方式时自动启用
if (enabledMethods.length == 1) {
  _initializeSingleAuth();
}
```

#### 多重认证模式
```dart
// 当配置多种认证方式时自动启用
if (enabledMethods.length > 1) {
  await _multiAuthManager.startListening();
  // 同时监听所有配置的认证方式
}
```

### 认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 认证界面
    participant AM as 认证管理器
    participant HW as 硬件设备
    participant DB as 数据库
    participant PS as 后处理服务
    
    U->>UI: 触发认证
    UI->>AM: 初始化认证
    AM->>HW: 启动设备监听
    HW->>AM: 检测到认证数据
    AM->>DB: 验证用户信息
    DB->>AM: 返回验证结果
    AM->>PS: 执行后处理服务
    PS->>UI: 更新界面状态
    UI->>U: 显示认证结果
```

## ⚙️ 配置系统

### 认证配置

认证配置通过`SettingProvider`管理，支持以下配置项：

```dart
// 认证方式配置
class AuthConfig {
  List<AuthMethod> enabledMethods;    // 启用的认证方式
  Map<AuthMethod, int> priority;      // 认证优先级
  Map<AuthMethod, String> customTitles; // 自定义标题
  Duration timeout;                   // 认证超时时间
  bool allowMultiAuth;               // 是否允许多重认证
}
```

### 硬件配置

```dart
// 硬件设备配置
class HardwareConfig {
  Map<AuthMethod, List<HWReaderSettingData>> readerMapping; // 读卡器映射
  FaceAuthConfig faceConfig;         // 人脸识别配置
  QRCodeConfig qrConfig;            // 二维码配置
}
```

### 数据库配置

```dart
// 数据库配置
class DatabaseConfig {
  DatabaseType type;                 // SQLite/MySQL
  String connectionString;           // 连接字符串
  bool enableLogging;               // 是否记录日志
}
```

## 🚀 部署指南

### 环境要求

- **Flutter SDK**: 3.0+
- **Dart SDK**: 2.17+
- **操作系统**: Windows 10+, Android 7.0+, iOS 11.0+
- **内存**: 最低4GB RAM，推荐8GB+
- **存储**: 最低2GB可用空间

### 依赖安装

```bash
# 1. 克隆项目
git clone https://github.com/yourusername/a3g.git
cd a3g

# 2. 安装Flutter依赖
flutter pub get

# 3. 配置本地化
flutter gen-l10n

# 4. 构建项目
flutter build windows  # Windows平台
flutter build apk      # Android平台
flutter build ios      # iOS平台
```

### 硬件设备配置

#### 读卡器配置
```yaml
# config/hardware.yaml
card_readers:
  - name: "IC卡读卡器"
    type: "IC_CARD"
    port: "COM1"
    baudrate: 9600
    supported_methods: ["readerCard", "socialSecurityCard"]
  
  - name: "身份证读卡器"
    type: "ID_CARD"
    port: "COM2"
    baudrate: 115200
    supported_methods: ["idCard"]
```

#### 摄像头配置
```yaml
cameras:
  - name: "人脸识别摄像头"
    device_id: 0
    resolution: "1920x1080"
    fps: 30
    supported_methods: ["face", "qrCode"]
```

### 网络配置

```yaml
# config/network.yaml
sip2_server:
  host: "your-library-server.com"
  port: 6001
  institution_id: "your_institution"
  timeout: 30

database:
  type: "sqlite"  # 或 "mysql"
  path: "data/a3g.db"  # SQLite路径
  # host: "localhost"  # MySQL配置
  # port: 3306
  # username: "a3g_user"
  # password: "your_password"
```

## 🔧 开发指南

### 添加新的认证方式

1. **定义认证方法枚举**
```dart
// lib/features/auth/models/auth_result.dart
enum AuthMethod {
  // 现有方法...
  newAuthMethod,  // 新增认证方法
}
```

2. **实现认证服务**
```dart
// lib/features/auth/services/new_auth_service.dart
class NewAuthService implements AuthServiceInterface {
  @override
  Future<AuthResult> authenticate() async {
    // 实现具体认证逻辑
    return AuthResult(
      method: AuthMethod.newAuthMethod,
      status: AuthStatus.success,
      userName: "用户名",
      userId: "用户ID",
    );
  }
}
```

3. **注册到认证管理器**
```dart
// lib/features/auth/view_models/auth_view_model.dart
case AuthMethod.newAuthMethod:
  result = await NewAuthService().authenticate();
  break;
```

### 添加后处理服务

1. **实现处理器接口**
```dart
// lib/core/services/post_auth_handlers/custom_handler.dart
class CustomHandler implements PostAuthHandler {
  @override
  int get priority => 50;  // 设置优先级
  
  @override
  Future<bool> handle(Sip2PatronInfoData readerInfo, String authMethod) async {
    // 实现自定义逻辑
    debugPrint('执行自定义处理: ${readerInfo.PersonName}');
    
    // 返回true继续执行链，false中断
    return true;
  }
}
```

2. **注册处理器**
```dart
// lib/core/services/post_auth_handlers/handlers_registry.dart
static void registerCustomHandlers() {
  PostAuthService.instance.registerHandler(CustomHandler());
}
```

### 自定义UI主题

```dart
// lib/shared/themes/custom_theme.dart
class CustomTheme {
  static ThemeData get theme => ThemeData(
    primaryColor: Color(0xFF4F83FC),
    backgroundColor: Color(0xFF1E2238),
    cardColor: Color(0xFF2A2F4A),
    textTheme: TextTheme(
      headline1: TextStyle(color: Colors.white, fontSize: 32),
      bodyText1: TextStyle(color: Colors.white70, fontSize: 16),
    ),
  );
}
```

## 📊 数据库结构

### 主要数据表

#### access_logs 表（认证日志）
```sql
CREATE TABLE access_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                 -- 用户姓名
  reader_card_no TEXT,               -- 读者证号
  open_time DATETIME NOT NULL,       -- 认证时间
  auth_method TEXT NOT NULL,         -- 认证方式
  status TEXT NOT NULL,              -- 认证状态
  remark TEXT                        -- 备注信息
);
```

#### settings 表（系统配置）
```sql
CREATE TABLE settings (
  key TEXT PRIMARY KEY,              -- 配置键
  value TEXT NOT NULL,               -- 配置值
  type TEXT NOT NULL,                -- 数据类型
  description TEXT,                  -- 描述
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### error_logs 表（错误日志）
```sql
CREATE TABLE error_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  error_type TEXT NOT NULL,          -- 错误类型
  error_message TEXT NOT NULL,       -- 错误信息
  stack_trace TEXT,                  -- 堆栈跟踪
  occurred_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  resolved BOOLEAN DEFAULT FALSE     -- 是否已解决
);
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 认证失败问题

**问题**: 显示"读者未找到,请联系管理员寻求帮助"
```
flutter: SIP2认证失败: 读者未找到,请联系管理员寻求帮助
```

**可能原因**:
- 用户信息未在图书馆系统中注册
- SIP2服务器配置错误
- 网络连接问题

**解决方案**:
```dart
// 1. 检查SIP2服务器配置
final config = SettingProvider.sip2Config;
debugPrint('SIP2服务器: ${config.host}:${config.port}');

// 2. 验证网络连接
try {
  final result = await NewSip2Request.instance.getReaderInfo(
    barcode, uid, psw: psw
  ).timeout(Duration(seconds: 10));
} catch (e) {
  debugPrint('网络请求失败: $e');
}
```

#### 2. 硬件设备无响应

**问题**: 读卡器或摄像头无法正常工作

**解决方案**:
```dart
// 检查设备状态
final isConnected = ReaderManager.instance.isConnectReader;
if (!isConnected) {
  await ReaderManager.instance.open();
}

// 重新初始化设备
await _cleanup();
await _initializeReader();
```

#### 3. 多认证模式冲突

**问题**: 多种认证方式同时触发时产生冲突

**解决方案**:
```dart
// 在MultiAuthManager中添加防冲突逻辑
class MultiAuthManager {
  bool _isProcessing = false;
  
  void _handleAuthResult(AuthResult result) {
    if (_isProcessing) return;  // 防止重复处理
    
    _isProcessing = true;
    // 处理认证结果...
    _isProcessing = false;
  }
}
```

### 日志调试

启用详细日志：
```dart
// main.dart
void main() {
  // 启用调试模式
  if (kDebugMode) {
    debugPrint('A3G系统启动，调试模式已开启');
  }
  
  runApp(MyApp());
}
```

查看关键日志：
```bash
# 过滤认证相关日志
flutter logs | grep "认证"

# 过滤错误日志
flutter logs | grep "ERROR"

# 过滤硬件相关日志
flutter logs | grep "硬件\|设备"
```

## 📈 性能优化

### 认证响应时间优化

1. **硬件预热**
```dart
// 在应用启动时预热硬件设备
class AppInitializer {
  static Future<void> preloadHardware() async {
    await ReaderManager.instance.initialize();
    await CameraManager.instance.initialize();
  }
}
```

2. **认证缓存**
```dart
// 短期内相同用户认证结果缓存
class AuthCache {
  static final _cache = <String, AuthResult>{};
  static const Duration _cacheTime = Duration(minutes: 5);
  
  static AuthResult? getCachedResult(String userId) {
    final cached = _cache[userId];
    if (cached != null && 
        DateTime.now().difference(cached.timestamp!) < _cacheTime) {
      return cached;
    }
    return null;
  }
}
```

### 内存优化

```dart
// 定期清理无用资源
class ResourceManager {
  static Timer? _cleanupTimer;
  
  static void startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(Duration(minutes: 10), (_) {
      // 清理图片缓存
      PaintingBinding.instance.imageCache.clear();
      
      // 清理认证缓存
      AuthCache.clear();
      
      // 垃圾回收
      gc();
    });
  }
}
```

## 🔒 安全性说明

### 数据安全

1. **敏感信息加密**
```dart
// 敏感配置信息加密存储
class SecureStorage {
  static Future<void> storeSecurely(String key, String value) async {
    final encrypted = await EncryptionService.encrypt(value);
    await storage.write(key: key, value: encrypted);
  }
}
```

2. **通信加密**
```dart
// SIP2通信使用TLS加密
class Sip2Request {
  static final dio = Dio()
    ..interceptors.add(TlsInterceptor());
}
```

### 权限管理

```dart
// 基于角色的权限控制
enum UserRole { admin, operator, viewer }

class PermissionManager {
  static bool hasPermission(UserRole role, String action) {
    switch (role) {
      case UserRole.admin:
        return true;  // 管理员拥有所有权限
      case UserRole.operator:
        return !['delete_logs', 'system_config'].contains(action);
      case UserRole.viewer:
        return ['view_logs', 'export_data'].contains(action);
    }
  }
}
```

## 📱 多平台支持

### Windows 平台特性

```dart
// Windows平台特定功能
if (Platform.isWindows) {
  // 全屏模式
  await windowManager.setFullScreen(true);
  
  // 任务栏隐藏
  await windowManager.setSkipTaskbar(true);
  
  // 开机自启动
  await AutoStart.enable();
}
```

### Android 平台特性

```dart
// Android平台特定功能
if (Platform.isAndroid) {
  // 保持屏幕常亮
  await WakelockPlus.enable();
  
  // 隐藏状态栏和导航栏
  await SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.immersiveSticky
  );
}
```

## 📚 API参考

### 核心API

#### PostAuthService

```dart
class PostAuthService {
  // 注册认证后处理器
  void registerHandler(PostAuthHandler handler);
  
  // 移除处理器
  void removeHandler(PostAuthHandler handler);
  
  // 执行处理器链
  Future<void> executeHandlers(
    Sip2PatronInfoData readerInfo, 
    String authMethod
  );
}
```

#### AuthViewModel

```dart
class AuthViewModel extends ChangeNotifier {
  // 设置认证方法
  void setAuthMethod(AuthMethod method);
  
  // 执行认证
  Future<AuthResult> authenticate();
  
  // 重置认证状态
  void resetAuth();
  
  // 清除用户信息
  void clearUserInfo();
}
```

#### MultiAuthManager

```dart
class MultiAuthManager {
  // 初始化多认证系统
  Future<void> initialize(BuildContext context);
  
  // 开始监听
  Future<void> startListening();
  
  // 停止监听
  Future<void> stopListening();
  
  // 认证结果流
  Stream<AuthResult> get authResultStream;
}
```

## 🤝 贡献指南

### 开发规范

1. **代码风格**
   - 遵循Dart官方代码风格
   - 使用驼峰命名法
   - 添加适当的注释和文档

2. **提交规范**
   ```bash
   # 提交格式
   git commit -m "feat: 添加新的认证方式"
   git commit -m "fix: 修复认证失败问题"
   git commit -m "docs: 更新API文档"
   ```

3. **测试要求**
   ```dart
   // 单元测试示例
   testWidgets('认证流程测试', (WidgetTester tester) async {
     // 构建测试环境
     await tester.pumpWidget(MyApp());
     
     // 执行测试步骤
     await tester.tap(find.text('开始认证'));
     await tester.pump();
     
     // 验证结果
     expect(find.text('认证成功'), findsOneWidget);
   });
   ```

### 发布流程

1. **版本号规范**
   - 主版本号：重大架构变更
   - 次版本号：新功能添加
   - 修订号：bug修复

2. **发布检查清单**
   - [ ] 所有测试通过
   - [ ] 文档已更新
   - [ ] 变更日志已记录
   - [ ] 版本号已更新

## 📞 技术支持

### 联系方式

- **技术文档**: [Wiki页面](https://github.com/yourusername/a3g/wiki)
- **问题反馈**: [Issue页面](https://github.com/yourusername/a3g/issues)
- **技术讨论**: [Discussions页面](https://github.com/yourusername/a3g/discussions)

### 社区资源

- **官方QQ群**: 123456789
- **技术博客**: https://blog.a3g.com
- **视频教程**: https://video.a3g.com

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**版本**: v2.1.0  
**最后更新**: 2025年1月15日  
**维护团队**: A3G开发团队

> 💡 如果这个项目对您有帮助，请给我们一个⭐️！
