import 'dart:async';
import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:base_package/base_package.dart';

import '../models/gate_state.dart';
import '../models/gate_event.dart';
import '../services/gate_coordinator.dart';
import '../widgets/gate_status_widget.dart';
import '../widgets/gate_auth_overlay.dart';
import '../widgets/gate_scanning_widget.dart';
import '../widgets/gate_book_check_widget.dart';

/// 安全闸机主页面
class GateHomePage extends StatefulWidget {
  const GateHomePage({Key? key}) : super(key: key);

  @override
  State<GateHomePage> createState() => _GateHomePageState();
}

class _GateHomePageState extends State<GateHomePage> 
    with TickerProviderStateMixin {
  
  // 闸机协调器
  late GateCoordinator _coordinator;
  
  // 事件订阅
  StreamSubscription? _gateEventSubscription;
  
  // 当前状态
  GateState _currentState = GateState.idle;
  String _statusMessage = '系统待机中...';
  
  // 动画控制器
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  
  // 覆盖层控制
  OverlayEntry? _authOverlay;
  OverlayEntry? _scanningOverlay;
  OverlayEntry? _bookCheckOverlay;
  
  // 管理员入口点击计数
  int _adminClickCount = 0;
  Timer? _adminClickTimer;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeGateSystem();
  }
  
  /// 初始化动画
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
    _fadeController.forward();
  }
  
  /// 初始化闸机系统
  Future<void> _initializeGateSystem() async {
    try {
      _coordinator = GateCoordinator.instance;
      
      // 监听闸机事件
      _gateEventSubscription = _coordinator.eventStream.listen(
        _handleGateEvent,
        onError: (error) {
          debugPrint('闸机事件流错误: $error');
          _showErrorMessage('系统错误: $error');
        },
      );
      
      // 监听状态变化
      _coordinator.addListener(_onStateChanged);
      
      // 更新初始状态
      _onStateChanged();
      
      debugPrint('闸机主页面初始化完成');
    } catch (e) {
      debugPrint('闸机主页面初始化失败: $e');
      _showErrorMessage('系统初始化失败: $e');
    }
  }
  
  /// 状态变化处理
  void _onStateChanged() {
    if (mounted) {
      setState(() {
        _currentState = _coordinator.currentState;
        _statusMessage = _currentState.displayName;
      });
    }
  }
  
  /// 处理闸机事件
  void _handleGateEvent(GateEvent event) {
    debugPrint('处理闸机事件: ${event.type} - ${event.message}');
    
    switch (event.type) {
      case GateEvent.enterStart:
        _showAuthOverlay();
        break;
      case GateEvent.exitStart:
        _showScanningOverlay();
        break;
      case GateEvent.showBooks:
        _hideAllOverlays();
        _showBookCheckOverlay(event.books ?? []);
        break;
      case GateEvent.authSuccess:
        _hideAuthOverlay();
        _showSuccessMessage(event.userName ?? '用户');
        break;
      case GateEvent.authFailed:
        _hideAuthOverlay();
        _showErrorMessage(event.message);
        break;
      case GateEvent.exitAllowed:
        _hideAllOverlays();
        _showSuccessMessage(event.message);
        break;
      case GateEvent.exitBlocked:
        _hideAllOverlays();
        _showErrorMessage(event.message);
        break;
      case GateEvent.enterEnd:
      case GateEvent.exitEnd:
        _hideAllOverlays();
        break;
      case GateEvent.tailgating:
        _showWarningMessage(event.message);
        break;
      case GateEvent.doorBlocked:
        _showWarningMessage(event.message);
        break;
      case GateEvent.error:
        _hideAllOverlays();
        _showErrorMessage(event.message);
        break;
    }
  }
  
  /// 显示认证覆盖层
  void _showAuthOverlay() {
    _hideAllOverlays();
    
    _authOverlay = OverlayEntry(
      builder: (context) => GateAuthOverlay(
        onClose: _hideAuthOverlay,
      ),
    );
    
    Overlay.of(context).insert(_authOverlay!);
  }
  
  /// 隐藏认证覆盖层
  void _hideAuthOverlay() {
    _authOverlay?.remove();
    _authOverlay = null;
  }
  
  /// 显示扫描覆盖层
  void _showScanningOverlay() {
    _hideAllOverlays();
    
    _scanningOverlay = OverlayEntry(
      builder: (context) => GateScanningWidget(
        onClose: _hideScanningOverlay,
      ),
    );
    
    Overlay.of(context).insert(_scanningOverlay!);
  }
  
  /// 隐藏扫描覆盖层
  void _hideScanningOverlay() {
    _scanningOverlay?.remove();
    _scanningOverlay = null;
  }
  
  /// 显示书籍检查覆盖层
  void _showBookCheckOverlay(List<Map<String, dynamic>> books) {
    _hideAllOverlays();
    
    _bookCheckOverlay = OverlayEntry(
      builder: (context) => GateBookCheckWidget(
        books: books,
        onClose: _hideBookCheckOverlay,
      ),
    );
    
    Overlay.of(context).insert(_bookCheckOverlay!);
  }
  
  /// 隐藏书籍检查覆盖层
  void _hideBookCheckOverlay() {
    _bookCheckOverlay?.remove();
    _bookCheckOverlay = null;
  }
  
  /// 隐藏所有覆盖层
  void _hideAllOverlays() {
    _hideAuthOverlay();
    _hideScanningOverlay();
    _hideBookCheckOverlay();
  }
  
  /// 显示成功消息
  void _showSuccessMessage(String message) {
    _showTemporaryMessage(message, Colors.green);
  }
  
  /// 显示错误消息
  void _showErrorMessage(String message) {
    _showTemporaryMessage(message, Colors.red);
  }
  
  /// 显示警告消息
  void _showWarningMessage(String message) {
    _showTemporaryMessage(message, Colors.orange);
  }
  
  /// 显示临时消息
  void _showTemporaryMessage(String message, Color color) {
    if (!mounted) return;
    
    setState(() {
      _statusMessage = message;
    });
    
    // 3秒后恢复状态消息
    Timer(Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _statusMessage = _currentState.displayName;
        });
      }
    });
  }
  
  /// 处理管理员入口点击
  void _handleAdminClick() {
    _adminClickCount++;
    
    // 重置计时器
    _adminClickTimer?.cancel();
    _adminClickTimer = Timer(Duration(seconds: 2), () {
      _adminClickCount = 0;
    });
    
    // 连续点击5次进入管理界面
    if (_adminClickCount >= 5) {
      _adminClickCount = 0;
      _adminClickTimer?.cancel();
      _showAdminLogin();
    }
  }
  
  /// 显示管理员登录
  void _showAdminLogin() {
    Get.toNamed('/adminLogin');
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFF1E3C72),
              Color(0xFF2A5298),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            // 主要内容区域
            Center(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo区域
                    Container(
                      margin: EdgeInsets.only(bottom: 80.p),
                      child: Column(
                        children: [
                          Icon(
                            Icons.security,
                            size: 120.p,
                            color: Colors.white.withOpacity(0.9),
                          ),
                          SizedBox(height: 20.p),
                          Text(
                            '安全闸机系统',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 48.p,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 2.0,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // 状态显示区域
                    GateStatusWidget(
                      state: _currentState,
                      message: _statusMessage,
                      pulseAnimation: _pulseAnimation,
                    ),
                  ],
                ),
              ),
            ),
            
            // 系统信息显示（左上角）
            Positioned(
              top: 30.p,
              left: 30.p,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(20.p),
                ),
                child: Text(
                  '版本 1.0.0',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16.p,
                  ),
                ),
              ),
            ),
            
            // 时间显示（右上角）
            Positioned(
              top: 30.p,
              right: 30.p,
              child: StreamBuilder<DateTime>(
                stream: Stream.periodic(Duration(seconds: 1), (_) => DateTime.now()),
                builder: (context, snapshot) {
                  final now = snapshot.data ?? DateTime.now();
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(20.p),
                    ),
                    child: Text(
                      '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 24.p,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // 管理员入口（隐藏按钮，右下角）
            Positioned(
              bottom: 30.p,
              right: 30.p,
              child: GestureDetector(
                onTap: _handleAdminClick,
                child: Container(
                  width: 60.p,
                  height: 60.p,
                  color: Colors.transparent,
                  child: _adminClickCount > 0
                      ? Icon(
                          Icons.admin_panel_settings,
                          color: Colors.white.withOpacity(0.3),
                          size: 30.p,
                        )
                      : null,
                ),
              ),
            ),
            
            // 系统状态指示器（左下角）
            Positioned(
              bottom: 30.p,
              left: 30.p,
              child: Container(
                padding: EdgeInsets.all(15.p),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(25.p),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 12.p,
                      height: 12.p,
                      decoration: BoxDecoration(
                        color: _currentState.indicatorColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: 10.p),
                    Text(
                      '系统正常',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 16.p,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  @override
  void dispose() {
    _gateEventSubscription?.cancel();
    _coordinator.removeListener(_onStateChanged);
    
    _hideAllOverlays();
    
    _pulseController.dispose();
    _fadeController.dispose();
    
    _adminClickTimer?.cancel();
    
    super.dispose();
  }
}
