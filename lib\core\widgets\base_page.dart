import 'package:flutter/material.dart';
// import 'package:a3g/gen/assets.gen.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';
class BasePage extends StatelessWidget {

  final Widget? topWrapper;
  final Widget? mainWrapper;
  final Widget? bottomWrapper;

  const BasePage({super.key,  this.topWrapper,  this.mainWrapper, this.bottomWrapper});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          width: WindowUtil.width,
          height: WindowUtil.height,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AssetUtil.fullPath('base_bg')),
            ),
          ),
          child: Padding(
            padding:  EdgeInsets.only(top: 60.p),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                topWrapper?? const SizedBox(),
                SizedBox(height: 40.p),
                if(mainWrapper != null) Expanded(
                  child: mainWrapper!,
                ),
                bottomWrapper?? const SizedBox(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
