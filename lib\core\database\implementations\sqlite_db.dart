import 'dart:convert';
import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:base_package/base_package.dart';
import 'package:seasetting/seasetting.dart';
import 'package:seasetting/setting/mini_smart_library/models/cabinet.dart';
import 'package:sqlite3/sqlite3.dart';
import 'package:path/path.dart' as PP;
import '../../../features/admin/models/error.dart';
import '../../models/book.dart';
import '../../models/slot.dart';
import '../interfaces/db_interface.dart';

class SqliteDB implements DBInterface {
  Database? _db;
  static final SqliteDB _instance = SqliteDB._();

  SqliteDB._();

  static Future<SqliteDB> instance() async {
    if (_instance._db == null) {
      await _instance._init();
    }
    return _instance;
  }

  String books_sql = """
  CREATE TABLE IF NOT EXISTS books (
    barcode TEXT PRIMARY KEY,             -- 书籍条形码（主键）
    uid TEXT,                             -- 书籍唯一标识符
    title TEXT,                           -- 书籍标题
    author TEXT,                          -- 作者
    isbn TEXT,                            -- 国际标准书号（ISBN）
    epc TEXT,                             -- RFID 标签 EPC
    tid TEXT,                             -- 标签 ID
    call_number TEXT,                     -- 索书号
    image_url TEXT,                       -- 封面图片 URL
    price TEXT,                           -- 书籍价格
    circulation_type TEXT,                -- 流通类型
    permanent_location TEXT,              -- 永久存放位置
    current_location TEXT,                -- 当前存放位置
    publisher TEXT,                       -- 出版社
    pages TEXT,                           -- 页数
    subject TEXT,                         -- 主题或分类
    extra TEXT,                           -- 额外信息
    slot_no TEXT,                         -- 格口编号（如：A1-1-101）
    status TEXT,                          -- 状态（在馆/借出/损坏等）
    upload TEXT,                          -- 是否已上传到系统
    created_at TEXT,                      -- 创建时间（字符串格式）
    updated_at TEXT,                       -- 更新时间（字符串格式）
    reservation TEXT                       -- 是否预约（Y N）
);
  """;

  String slots_sql = """
CREATE TABLE IF NOT EXISTS slots (
  cabinet_no TEXT,                          -- 书柜编号（如 A1）
  shelf_no TEXT,                            -- 书架编号（如 A1-1）
  slot_no TEXT,                             -- 格口编号（1~120）
  barcode TEXT,                             -- 书籍条形码（外键，指向 books 表）
  status TEXT,                              -- 格口状态（0:空、1:占用、2:禁用）
  slot_type TEXT,                           -- 格口类型（0:普通区、1:预约区）
  pickupCode TEXT,                          -- 格口的取书码(默认空串)
  lock_configs TEXT,                        -- 门锁配置(JSON数组) [{cardType, serialPort}, ...]
  light_configs TEXT,                       -- 灯配置(JSON数组) [{cardType, serialPort}, ...]
  reader_configs TEXT,                      -- 阅读器配置(JSON数组) [{cardType, serialPort, decoder, antennas}, ...]
  lastChecked TEXT,                         -- 最后检查时间
  lastOperator TEXT,                        -- 最后操作人员
  created_at TEXT,                          -- 创建时间
  updated_at TEXT,                          -- 更新时间
  PRIMARY KEY (cabinet_no, slot_no),        -- 复合主键
  FOREIGN KEY (barcode) REFERENCES books(barcode)  -- 外键约束
);
""";

  /// 创建书柜管理表
  String cabinets_sql = """
CREATE TABLE IF NOT EXISTS cabinets (
  cabinet_no TEXT PRIMARY KEY,        -- 书柜编号（如 A, B, C）
  cabinet_name TEXT,                  -- 书柜名称
  status TEXT,                        -- 书柜状态（0:离线 1:在线 2:维护中）
  sort_order INTEGER,                 -- 排序顺序
  created_at TEXT,                    -- 创建时间
  updated_at TEXT                     -- 更新时间
);
""";

  String settings_sql = """
CREATE TABLE IF NOT EXISTS app_settings (
  key VARCHAR(255) PRIMARY KEY,           -- 配置键名
  value TEXT,                             -- 配置值
  type VARCHAR(50),                       -- 值类型
  description TEXT                        -- 配置说明
);
""";

  String error_logs_sql = """
  CREATE TABLE IF NOT EXISTS error_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    slot_no TEXT,
    book_title TEXT,
    error_reason TEXT,
    remark TEXT,
    status TEXT,
    operator TEXT,                -- 添加操作者字段
    created_at TEXT,
    updated_at TEXT
  );
""";

  String access_logs_sql = """
  CREATE TABLE IF NOT EXISTS access_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,     -- 记录ID
    name TEXT NOT NULL,                       -- 用户姓名
    reader_card_no TEXT NOT NULL,             -- 读者证号
    open_time TEXT NOT NULL,                  -- 开门时间(格式化字符串)
    auth_method TEXT,                         -- 认证方式(face, id_card, qr_code等)
    status TEXT NOT NULL DEFAULT 'success',   -- 认证状态
    remark TEXT,                              -- 备注
    created_at TEXT DEFAULT CURRENT_TIMESTAMP -- 记录创建时间
  );
  """;

  Future<void> _init() async {
    try {
      _db = await _getDB();
      await _createTables(books_sql);
      await _createTables(slots_sql);
      await _createTables(cabinets_sql);
      await _createTables(settings_sql);
      await _createTables(error_logs_sql);
      await _createTables(access_logs_sql);
      await _createIndices();
      await checkAndSetDefaultBookCount();
    } catch (e) {
      throw Exception('Failed to initialize SQLite: $e');
    }
  }

  Future<void> _createTables(String sql) async {
    _db?.execute(sql);
  }

  Future<void> _createIndices() async {
    _db?.execute('CREATE INDEX IF NOT EXISTS idx_barcode ON books(barcode)');
    _db?.execute('CREATE INDEX IF NOT EXISTS idx_slot_no ON books(slot_no)');
    _db?.execute('CREATE INDEX IF NOT EXISTS idx_barcode ON slots(barcode)');
    _db?.execute('CREATE INDEX IF NOT EXISTS idx_status ON slots(status)');
    _db?.execute('CREATE INDEX IF NOT EXISTS idx_access_logs_open_time ON access_logs(open_time)');
  }



  @override
  Future<void> close() async {
    _db?.dispose();
    _instance._db = null;
  }

  Future<Database> _getDB() async {
    String path = await _getDBPath();
    return sqlite3.open(path);
  }

  Future<String> _getDBPath() async {
    Directory path = await getApplicationSupportDirectory();
    String fileName = "sea_a3g.db";
    return PP.join(path.path, fileName);
  }

  @override
  Future<DBResult<List<Book>>> queryBooksByShelfNo(String shelfNo) async {
    try {
      final stmt = _db?.prepare('''
        SELECT DISTINCT
          b.*,
          s.slot_no,
          s.status as slot_status,
          s.pickupCode,
          s.slot_type
        FROM slots s
        LEFT JOIN books b ON s.slot_no = b.slot_no
        WHERE s.shelf_no = ?
          AND s.status = ?
          AND b.barcode IS NOT NULL
        ORDER BY s.slot_no ASC
      ''');

      final results = stmt?.select([
        shelfNo,
        CabinetConfig.slotOccupied,
      ]);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final books = results.map((row) {
        final Map<String, dynamic> bookData = {
          'barcode': row['barcode'],
          'title': row['title'],
          'author': row['author'],
          'isbn': row['isbn'],
          'call_number': row['call_number'],
          'image_url': row['image_url'],
          'circulation_type': row['circulation_type'],
          'permanent_location': row['permanent_location'],
          'current_location': row['current_location'],
          'status': row['status'],
          'slot_no': row['slot_no'],
          'slot_status': row['slot_status'],
          'slot_type': row['slot_type'],
          'pickupCode': row['pickupCode'],
          'reservation': row['reservation'] ?? 'N',
        };
        return Book.fromJson(bookData);
      }).toList();

      return DBResult.success(books);
    } catch (e) {
      print('查询书架书籍失败: $e');
      return DBResult.error('查询书架书籍失败: $e');
    }
  }

  /// 添加新书柜（包括初始化格口）
  @override
  Future<DBResult<void>> addNewCabinet({
    required String cabinetNo,
    String? cabinetName,
    required int sortOrder,
  }) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      _db?.execute('BEGIN TRANSACTION');

      // 1. 添加书柜信息
      final cabinetStmt = _db?.prepare('''
      INSERT INTO cabinets (
        cabinet_no, cabinet_name, status, sort_order, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?)
    ''');

      cabinetStmt?.execute([
        cabinetNo,
        cabinetName ?? '',
        CabinetConfig.cabinetOnline,
        sortOrder,
        now,
        now,
      ]);
      cabinetStmt?.dispose();

      // 2. 初始化该书柜的所有格口
      await initializeCabinetSlots(cabinetNo);

      _db?.execute('COMMIT');

      // 3. 验证插入
      final check = _db?.select(
          'SELECT * FROM cabinets WHERE cabinet_no = ?',
          [cabinetNo]
      );
      print('Added cabinet: ${check?.firstOrNull}');

      return DBResult.success();
    } catch (e) {
      _db?.execute('ROLLBACK');
      return DBResult.error('添加书柜失败: $e');
    }
  }

  /// 获取所有书柜信息
  @override
  Future<DBResult<List<Map<String, dynamic>>>> getAllCabinets() async {
    try {
      final stmt = _db?.prepare('''
      SELECT 
        c.*,
        (SELECT COUNT(*) FROM slots s 
         WHERE s.cabinet_no = c.cabinet_no 
         AND s.status = '1') as occupied_slots,
        (SELECT COUNT(*) FROM slots s 
         WHERE s.cabinet_no = c.cabinet_no) as total_slots
      FROM cabinets c
    ''');

      final results = stmt?.select([]);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        print('No cabinets found in database');
        return DBResult.success([]);
      }

      final cabinets = results.map((row) {
        final map = Map<String, dynamic>.from(row);  // 直接转换 Row 为 Map
        print('Cabinet row: $map');
        return map;
      }).toList();

      return DBResult.success(cabinets);
    } catch (e) {
      print('Error getting cabinets: $e'); // 调试输出
      return DBResult.error('获取书柜列表失败: $e');
    }
  }

  /// 更新书柜状态
  Future<DBResult<void>> updateCabinetStatus(
      String cabinetNo, String status) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      final stmt = _db?.prepare('''
        UPDATE cabinets 
        SET status = ?,
            updated_at = ?
        WHERE cabinet_no = ?
      ''');

      stmt?.execute([status, now, cabinetNo]);
      stmt?.dispose();

      return DBResult.success();
    } catch (e) {
      return DBResult.error('更新书柜状态失败: $e');
    }
  }

  /// 移除书柜（包括其所有格口）
  Future<DBResult<void>> removeCabinet(String cabinetNo) async {
    try {
      final deleteSlots =
          _db?.prepare('DELETE FROM slots WHERE cabinet_no = ?');
      deleteSlots?.execute([cabinetNo]);
      deleteSlots?.dispose();

      // 4. 删除书柜记录
      final deleteCabinet =
          _db?.prepare('DELETE FROM cabinets WHERE cabinet_no = ?');
      deleteCabinet?.execute([cabinetNo]);
      deleteCabinet?.dispose();

      _db?.execute('COMMIT');
      return DBResult.success();
    } catch (e) {
      _db?.execute('ROLLBACK');
      return DBResult.error('移除书柜失败: $e');
    }
  }

  /// 初始化指定书柜的所有格口
  @override
  Future<DBResult<void>> initializeCabinetSlots(String cabinetNo) async {
    final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

    final stmt = _db?.prepare('''
    INSERT OR IGNORE INTO slots (
      cabinet_no, shelf_no, slot_no, barcode, status, 
      slot_type, pickupCode, lock_configs, light_configs, reader_configs,
      lastChecked, lastOperator, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  ''');

    try {
      // 遍历每层书架 (1-5层)
      for (int shelf = 1; shelf <= CabinetConfig.shelfPerCabinet; shelf++) {
        // 当前层的书架编号 (如: A1-1)
        final shelfNo = '$cabinetNo-$shelf';

        // 遍历每层的格口 (24个格口)
        for (int slot = 1; slot <= CabinetConfig.slotsPerShelf; slot++) {
          // 格口编号 (如: A1-1-101)
          final slotNoStr = '$cabinetNo-$shelf-${(shelf * 100 + slot).toString().padLeft(3, '0')}';

          // 初始化默认配置
          final defaultConfigs = {
            'lock_configs': jsonEncode([]),
            'light_configs': jsonEncode([]),
            'reader_configs': jsonEncode([]),
          };

          stmt?.execute([
            cabinetNo,                    // 书柜编号 (A1)
            shelfNo,                      // 书架编号 (A1-1)
            slotNoStr,                    // 格口编号 (A1-1-101)
            '',                           // 书籍条码（初始为空）
            CabinetConfig.slotEmpty,      // 格口状态（初始为空）
            '0',                          // 格口类型（0:普通区）
            '',                           // 取书码（初始为空）
            defaultConfigs['lock_configs'],    
            defaultConfigs['light_configs'],   
            defaultConfigs['reader_configs'],  
            now,                          
            'system',                     
            now,                          
            now,                          
          ]);
        }
      }
      return DBResult.success();
    } catch (e) {
      print('初始化格口失败: $e');
      return DBResult.error('初始化格口失败: $e');
    } finally {
      stmt?.dispose();
    }
  }

  /// 验证格口初始化是否成功
  @override
  Future<DBResult<bool>> verifyCabinetSlots(String cabinetNo) async {
    try {
      // 检查格口数量是否正确
      final stmt = _db?.prepare('''
      SELECT COUNT(*) as count 
      FROM slots 
      WHERE cabinet_no = ?
    ''');

      final result = stmt?.select([cabinetNo]);
      stmt?.dispose();

      final count = result?.first['count'] as int;
      final expectedCount =
          CabinetConfig.shelfPerCabinet * CabinetConfig.slotsPerShelf;

      if (count != expectedCount) {
        return DBResult.error('格口数量不正确: 预期 $expectedCount, 实际 $count');
      }

      return DBResult.success(true);
    } catch (e) {
      return DBResult.error('验证格口失败: $e');
    }
  }

  @override
  Future<DBResult<List<Map<String, dynamic>>>> getCabinetSlots(String cabinetNo) async {
    try {
      final stmt = _db?.prepare('''
      SELECT 
        s.cabinet_no,
        s.shelf_no,
        s.slot_no,
        s.status as slot_status,
        s.slot_type,
        s.pickupCode,
        s.lock_configs,
        s.light_configs,
        s.reader_configs,
        s.lastChecked,
        s.lastOperator,
        s.created_at,
        s.updated_at,
        b.barcode,
        b.title as book_title,
        b.author as book_author,
        b.isbn as book_isbn,
        b.call_number,
        b.image_url,
        b.permanent_location,
        b.current_location,
        b.status as book_status,
        b.reservation as book_reservation
      FROM slots s
      LEFT JOIN books b ON s.barcode = b.barcode
      WHERE s.cabinet_no = ?
      ORDER BY CAST(s.slot_no AS INTEGER) ASC
    ''');

      final results = stmt?.select([cabinetNo]);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        print('No slots found for cabinet: $cabinetNo');
        return DBResult.success([]);
      }

      final slots = results.map((row) {
        final map = Map<String, dynamic>.from(row);

        try {
          if (map['lock_configs'] != null) {
            map['lock_configs'] = jsonDecode(map['lock_configs']);
          }
          if (map['light_configs'] != null) {
            map['light_configs'] = jsonDecode(map['light_configs']);
          }
          if (map['reader_configs'] != null) {
            map['reader_configs'] = jsonDecode(map['reader_configs']);
          }
        } catch (e) {
          print('JSON parsing error: $e');
        }

        map['is_empty'] = map['slot_status'] == CabinetConfig.slotEmpty;
        map['is_occupied'] = map['slot_status'] == CabinetConfig.slotOccupied;
        map['is_disabled'] = map['slot_status'] == CabinetConfig.slotDisabled;

        if (map['barcode'] != null) {
          map['book'] = {
            'barcode': map['barcode'],
            'title': map['book_title'],
            'author': map['book_author'],
            'isbn': map['book_isbn'],
            'call_number': map['call_number'],
            'image_url': map['image_url'],
            'permanent_location': map['permanent_location'],
            'current_location': map['current_location'],
            'status': map['book_status'],
            'reservation': map['book_reservation'] ?? 'N'
          };
        }

        // 清理重复字段
        ['barcode', 'book_title', 'book_author', 'book_isbn', 'call_number', 
         'image_url', 'permanent_location', 'current_location', 'book_status', 
         'book_reservation'].forEach(map.remove);

        return map;
      }).toList();

      return DBResult.success(slots);
    } catch (e) {
      print('Error getting slots: $e');
      return DBResult.error('获取格口信息失败: $e');
    }
  }

  /// 更新格口配置
  @override
  Future<DBResult<void>> updateSlotConfigs({
    required String cabinetNo,
    required String slotNo,
    List<LockConfig>? lockConfigs,
    List<LightConfig>? lightConfigs,
    List<ReaderConfig>? readerConfigs,
    String? type,
    String? status,
  }) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      // 1. 检查格口是否存在
      final checkStmt = _db?.prepare(
          'SELECT 1 FROM slots WHERE cabinet_no = ? AND slot_no = ?'
      );
      final exists = checkStmt?.select([cabinetNo, slotNo]);
      checkStmt?.dispose();

      if (exists == null || exists.isEmpty) {
        return DBResult.error('格口不存在');
      }

      // 2. 转换配置为 JSON 字符串
      String? lockConfigsJson;
      if (lockConfigs != null) {
        final List<Map<String, dynamic>> configs = lockConfigs.map((c) => c.toJson()).toList();
        lockConfigsJson = jsonEncode(configs);
      }

      String? lightConfigsJson;
      if (lightConfigs != null) {
        final List<Map<String, dynamic>> configs = lightConfigs.map((c) => c.toJson()).toList();
        lightConfigsJson = jsonEncode(configs);
      }

      String? readerConfigsJson;
      if (readerConfigs != null) {
        final List<Map<String, dynamic>> configs = readerConfigs.map((c) => c.toJson()).toList();
        readerConfigsJson = jsonEncode(configs);
      }

      // 3. 开始事务
      _db?.execute('BEGIN TRANSACTION');

      // 4. 更新数据库
      final updateStmt = _db?.prepare('''
      UPDATE slots 
      SET 
        lock_configs = COALESCE(?, lock_configs),
        light_configs = COALESCE(?, light_configs),
        reader_configs = COALESCE(?, reader_configs),
        slot_type = COALESCE(?, slot_type),
        updated_at = ?,
        lastOperator = ?
      WHERE cabinet_no = ? AND slot_no = ?
    ''');

      updateStmt?.execute([
        lockConfigsJson,
        lightConfigsJson,
        readerConfigsJson,
        type,
        now,
        'system',  // 操作人员
        cabinetNo,
        slotNo,
      ]);

      updateStmt?.dispose();

      // 5. 提交事务
      _db?.execute('COMMIT');

      print('格口配置更新成功: $cabinetNo-$slotNo');
      return DBResult.success();
    } catch (e) {
      // 6. 回滚事务
      _db?.execute('ROLLBACK');
      print('更新格口配置失败: $e');
      return DBResult.error('更新格口配置失败: $e');
    }
  }

  /// 获取格口配置
  @override
  Future<DBResult<SlotConfig>> getSlotConfigs(String cabinetNo, String slotNo) async {
    try {
      // 1. 查询格口配置
      final stmt = _db?.prepare('''
      SELECT 
        lock_configs,
        light_configs,
        reader_configs,
        status,
        slot_type,
        lastChecked,
        lastOperator,
        updated_at
      FROM slots 
      WHERE cabinet_no = ? AND slot_no = ?
    ''');

      final results = stmt?.select([cabinetNo, slotNo]);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.error('格口不存在');
      }

      final row = Map<String, dynamic>.from(results.first);

      // 2. 解析锁配置
      List<LockConfig>? lockConfigs;
      if (row['lock_configs'] != null) {
        final List<dynamic> json = jsonDecode(row['lock_configs']);
        lockConfigs = json.map((j) => LockConfig.fromJson(j)).toList();
      }

      // 3. 解析灯配置
      List<LightConfig>? lightConfigs;
      if (row['light_configs'] != null) {
        final List<dynamic> json = jsonDecode(row['light_configs']);
        lightConfigs = json.map((j) => LightConfig.fromJson(j)).toList();
      }

      // 4. 解析读写器配置
      List<ReaderConfig>? readerConfigs;
      if (row['reader_configs'] != null) {
        final List<dynamic> json = jsonDecode(row['reader_configs']);
        readerConfigs = json.map((j) => ReaderConfig.fromJson(j)).toList();
      }

      // 5. 创建配置对象
      final config = SlotConfig(
        lockConfigs: lockConfigs,
        lightConfigs: lightConfigs,
        readerConfigs: readerConfigs,
        type: row['slot_type'] as String?,
      );

      print('获取格口配置成功: $cabinetNo-$slotNo');
      print('Lock configs: ${lockConfigs?.length ?? 0}');
      print('Light configs: ${lightConfigs?.length ?? 0}');
      print('Reader configs: ${readerConfigs?.length ?? 0}');
      print('Slot type: ${row['slot_type']}');

      return DBResult.success(config);
    } catch (e) {
      print('获取格口配置失败: $e');
      return DBResult.error('获取格口配置失败: $e');
    }
  }

  @override
  Future<DBResult<void>> deleteDatabase() async {
    try {
      // 1. 关闭现有连接
      await close();
      _db?.dispose();  // 确保释放资源
      _instance._db = null;

      // 2. 等待一小段时间确保连接完全关闭
      await Future.delayed(Duration(milliseconds: 100));

      // 3. 删除文件
      String path = await _getDBPath();
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        print('SQLite 数据库文件已删除: $path');
      }
      // 4. 重新初始化数据库
      await _instance._init();
      print('数据库已重新创建');

      return DBResult.success();
    } catch (e) {
      return DBResult.error('删除 SQLite 数据库失败: $e');
    }
  }

  /// 检查并设置默认书箱数量
  Future<DBResult<int>> checkAndSetDefaultBookCount() async {
    try {
      // 1. 检查是否已存在配置
      final stmt = _db?.prepare(
        'SELECT value FROM app_settings WHERE key = ?'
      );
      final result = stmt?.select(['cabinet_book_count']);
      stmt?.dispose();

      if (result == null || result.isEmpty) {
        // 2. 不存在则插入默认值
        const defaultCount = 0;  // 默认书箱容量
        final insertStmt = _db?.prepare('''
          INSERT INTO app_settings (key, value, type, description)
          VALUES (?, ?, ?, ?)
        ''');
        
        insertStmt?.execute([
          'cabinet_book_count',
          defaultCount.toString(),
          'number',
          '书箱最大可存放书籍数量'
        ]);
        insertStmt?.dispose();

        print('已设置默认书箱数量: $defaultCount');
        return DBResult.success(defaultCount);
      }

      // 3. 已存在则返回当前值
      final count = int.parse(result.first['value']);
      print('当前书箱数量设置: $count');
      return DBResult.success(count);
    } catch (e) {
      print('检查书箱数量设置失败: $e');
      return DBResult.error('检查书箱数量设置失败: $e');
    }
  }

  /// 重置书箱书籍数量
  @override
  Future<DBResult<void>> setBookBoxCount(int newCount) async {
    try {
      final stmt = _db?.prepare('''
        UPDATE app_settings 
        SET value = ? 
        WHERE key = 'cabinet_book_count'
      ''');
      
      stmt?.execute([newCount.toString()]);
      stmt?.dispose();

      print('已重置书箱数量为: $newCount');
      return DBResult.success();
    } catch (e) {
      print('重置书箱数量失败: $e');
      return DBResult.error('重置书箱数量失败: $e');
    }
  }

  /// 查询书箱书籍数量
  @override
  Future<DBResult<int>> getBookBoxCount() async {
    try {
      final stmt = _db?.prepare(
        'SELECT value FROM app_settings WHERE key = ?'
      );
      final result = stmt?.select(['cabinet_book_count']);
      stmt?.dispose();

      if (result == null || result.isEmpty) {
        return DBResult.error('未找到书箱数量配置');
      }

      final count = int.parse(result.first['value']);
      return DBResult.success(count);
    } catch (e) {
      print('获取书箱数量失败: $e');
      return DBResult.error('获取书箱数量失败: $e');
    }
  }

  /// 检查是否超过最大数量
  Future<DBResult<bool>> isExceedMaxCount(int currentCount) async {
    try {
      final stmt = _db?.prepare(
        'SELECT value FROM app_settings WHERE key = ?'
      );
      final result = stmt?.select(['cabinet_book_count']);
      stmt?.dispose();

      if (result == null || result.isEmpty) {
        return DBResult.error('未找到书箱数量配置');
      }

      final maxCount = int.parse(result.first['value']);
      return DBResult.success(currentCount >= maxCount);
    } catch (e) {
      print('检查书箱数量失败: $e');
      return DBResult.error('检查书箱数量失败: $e');
    }
  }

  /// 当前数量自增1
  Future<DBResult<int>> incrementBookCount() async {
    try {
      // 1. 获取当前值
      final getStmt = _db?.prepare(
        'SELECT value FROM app_settings WHERE key = ?'
      );
      final result = getStmt?.select(['current_book_count']);
      getStmt?.dispose();

      int currentCount = 0;
      if (result != null && result.isNotEmpty) {
        currentCount = int.parse(result.first['value']);
      }

      // 2. 自增并更新
      currentCount++;
      
      final updateStmt = _db?.prepare('''
        INSERT OR REPLACE INTO app_settings (key, value, type, description)
        VALUES (?, ?, ?, ?)
      ''');
      
      updateStmt?.execute([
        'current_book_count',
        currentCount.toString(),
        'number',
        '当前书箱中的书籍数量'
      ]);
      updateStmt?.dispose();

      print('当前书籍数量: $currentCount');
      return DBResult.success(currentCount);
    } catch (e) {
      print('更新书籍数量失败: $e');
      return DBResult.error('更新书籍数量失败: $e');
    }
  }

  /// 获取正常状态的所有书柜
  @override
  Future<DBResult<List<Cabinet>>> getActiveCabinets() async {
    try {
      final stmt = _db?.prepare('''
        SELECT 
          c.*,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no 
           AND s.status = '1') as occupied_slots,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no) as total_slots
        FROM cabinets c
        WHERE c.status = '1'
        ORDER BY c.cabinet_no ASC
      ''');

      final results = stmt?.select();
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final cabinets = results.map((row) => Cabinet.fromJson(Map<String, dynamic>.from(row))).toList();
      return DBResult.success(cabinets);
    } catch (e) {
      return DBResult.error('获取正常书柜失败: $e');
    }
  }

  @override
  Future<DBResult<void>> addErrorRecord(ErrorRecord record) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      final stmt = _db?.prepare('''
        INSERT INTO error_logs (
          slot_no, book_title, error_reason, remark, status, operator, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      ''');

      stmt?.execute([
        record.slotNo ?? '',
        record.bookTitle ?? '',
        record.errorReason ?? '',
        record.remark ?? '',
        record.status ?? '0',  // 默认未处理
        record.operator ?? 'system',
        now,
        now
      ]);
      stmt?.dispose();

      return DBResult.success();
    } catch (e) {
      return DBResult.error('添加错误日志失败: $e');
    }
  }

  /// 更新错误记录状态
  @override
  Future<DBResult<void>> updateErrorRecordStatus({
    required int id,
    required String status,
    String? operator,

  }) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());


      final stmt = _db?.prepare('''
        UPDATE error_logs 
        SET status = ?,
            operator = ?,
            updated_at = ?
        WHERE id = ?
      ''');

      stmt?.execute([
        status,
        operator ?? 'system',
        now,
        id,
      ]);
      stmt?.dispose();

      return DBResult.success();
    } catch (e) {
      return DBResult.error('更新错误记录状态失败: $e');
    }
  }


  /// 获取所有错误记录（可选状态筛选）
  @override
  Future<DBResult<List<Map<String, dynamic>>>> getErrorRecords({String? status}) async {
    try {
      String sql = '''
        SELECT * FROM error_logs 
        ${status != null ? 'WHERE status = ?' : ''}
        ORDER BY created_at DESC
      ''';

      final stmt = _db?.prepare(sql);
      final results = stmt?.select(status != null ? [status] : []);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      return DBResult.success(
        results.map((row) => Map<String, dynamic>.from(row)).toList(),
      );
    } catch (e) {
      return DBResult.error('获取错误记录失败: $e');
    }
  }

  /// 根据状态和类型获取格口
  @override
  Future<DBResult<List<Slot>>> getSlotsByStatusAndType({
    required String cabinetNo,
    String? status,
    String? slotType,
  }) async {
    try {
      List<String> conditions = ['s.cabinet_no = ?'];
      List<String> params = [cabinetNo];
      
      if (status != null) {
        conditions.add('s.status = ?');
        params.add(status);
      }
      
      if (slotType != null) {
        conditions.add('s.slot_type = ?');
        params.add(slotType);
      }

      String whereClause = 'WHERE ${conditions.join(' AND ')}';

      final stmt = _db?.prepare('''
        SELECT 
          s.*,
          b.title as book_title,
          b.author as book_author,
          b.isbn as book_isbn,
          b.call_number as book_call_number,
          b.image_url as book_image_url,
          b.circulation_type as book_circulation_type,
          b.permanent_location as book_permanent_location,
          b.current_location as book_current_location,
          b.status as book_status,
          b.reservation as book_reservation
        FROM slots s
        LEFT JOIN books b ON s.barcode = b.barcode
        $whereClause
        ORDER BY s.slot_no ASC
      ''');

      final results = stmt?.select(params);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final slots = results.map((row) {
        final Map<String, dynamic> data = Map<String, dynamic>.from(row);
        
        // 处理配置的 JSON 解析
        try {
          if (data['lock_configs'] != null) {
            data['lock_configs'] = jsonDecode(data['lock_configs']);
          }
          if (data['light_configs'] != null) {
            data['light_configs'] = jsonDecode(data['light_configs']);
          }
          if (data['reader_configs'] != null) {
            data['reader_configs'] = jsonDecode(data['reader_configs']);
          }
        } catch (e) {
          print('JSON parsing error: $e');
          data['lock_configs'] = [];
          data['light_configs'] = [];
          data['reader_configs'] = [];
        }

        // 添加书籍信息
        if (data['barcode'] != null && data['barcode'].toString().isNotEmpty) {
          data['book'] = {
            'barcode': data['barcode'],
            'title': data['book_title'],
            'author': data['book_author'],
            'isbn': data['book_isbn'],
            'call_number': data['book_call_number'],
            'image_url': data['book_image_url'],
            'circulation_type': data['book_circulation_type'],
            'permanent_location': data['book_permanent_location'],
            'current_location': data['book_current_location'],
            'status': data['book_status'],
            'reservation': data['book_reservation'] ?? 'N'
          };
        }

        return Slot.fromJson(data);
      }).toList();

      return DBResult.success(slots);
    } catch (e) {
      print('获取格口失败: $e');
      return DBResult.error('获取格口失败: $e');
    }
  }

  /// 保存书籍信息到 books 表，并更新格口的 barcode
  @override
  Future<DBResult<void>> saveBookAndUpdateSlot({
    required String barcode,
    required String slotNo,
    required String status,
    required Book bookInfo,
  }) async {
    try {
      // 开始事务
      _db?.execute('BEGIN TRANSACTION');

      // 检查书籍是否已存在
      final existingBook = await getBookByBarcode(barcode);
      if (existingBook != null) {
        // 如果书籍已存在，更新书籍信息
        throw Exception('该条码已存在，请处理');
      } else {
        // 如果书籍不存在，插入新书籍信息
        await insertBookItem(barcode, bookInfo);
      }

      // 更新格口的 barcode
      await updateSlotBarcode(slotNo, barcode,status);

      // 提交事务
      _db?.execute('COMMIT');
      print('保存成功');
      return DBResult.success();
    } catch (e) {
      // 回滚事务
      _db?.execute('ROLLBACK');
      print('保存书籍信息并更新格口失败: $e');
      return DBResult.error('保存书籍信息并更新格口失败: $e');
    }
  }

  /// 获取书籍信息
  Future<Map<String, dynamic>?> getBookByBarcode(String barcode) async {
    final stmt = _db?.prepare('SELECT * FROM books WHERE barcode = ?');
    final result = stmt?.select([barcode]);
    stmt?.dispose();
    return result?.isNotEmpty == true ? result?.first : null;
  }

  /// 更新书籍信息
  Future<void> updateBookItem(String barcode, Book bookInfo) async {
    final stmt = _db?.prepare('''
      UPDATE books SET 
        title = ?, 
        author = ?, 
        isbn = ?, 
        call_number = ?, 
        image_url = ?, 
        circulation_type = ?, 
        permanent_location = ?, 
        current_location = ?, 
        status = ?,
        reservation = ?,
        updated_at = ?
      WHERE barcode = ?
    ''');
     stmt?.execute([
      bookInfo.title,
      bookInfo.author,
      bookInfo.isbn,
      bookInfo.callno,
      bookInfo.media_type,
      bookInfo.circulation_type,
      bookInfo.permanent_location,
      bookInfo.current_location,
      bookInfo.status,
      bookInfo.reservation ?? 'N',
      DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
      barcode,
    ]);
    stmt?.dispose();
  }

  /// 插入新书籍信息
  Future<void> insertBookItem(String barcode, Book bookInfo) async {
    bookInfo.barcode = barcode;
    final stmt = _db?.prepare('''
      INSERT INTO books (
        barcode, title, author, isbn, call_number, 
        image_url, circulation_type, permanent_location, 
        current_location, status, reservation, created_at, updated_at
      ) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''');
     stmt?.execute([
      bookInfo.barcode,
      bookInfo.title,
      bookInfo.author,
      bookInfo.isbn,
      bookInfo.callno,
      bookInfo.media_type,
      bookInfo.circulation_type,
      bookInfo.permanent_location,
      bookInfo.current_location,
      bookInfo.status,
      bookInfo.reservation ?? 'N',
      DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
      DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
    ]);
    stmt?.dispose();
  }


  /// 更新格口的 barcode
  Future<void> updateSlotBarcode(String slotNo, String barcode, String status) async {
    // 解析 slotNo 获取 cabinetNo 和 shelfNo
    final parts = slotNo.split('-');
    if (parts.length != 3) {
      throw Exception('Invalid slotNo format. Expected format: cabinetNo-shelfNo-slotNo');
    }
    final cabinetNo = parts[0];

     final result = _db?.execute('''
      UPDATE slots SET barcode = ?, status = ? WHERE cabinet_no = ?  AND slot_no = ?
    ''', [barcode, status, cabinetNo, slotNo]);
     print('object');
  }

  @override
  Future<DBResult<List<Map<String, dynamic>>>> querySlotsByCabinetAndStatus(
      String cabinetNo, String status, String? slotType) async {
    try {
      List<String> conditions = ['s.cabinet_no = ?', 's.status = ?'];
      List<Object> params = [cabinetNo, status];
      
      if (slotType != null) {
        conditions.add('s.slot_type = ?');
        params.add(slotType);
      }

      String whereClause = 'WHERE ${conditions.join(' AND ')}';

      final stmt = _db?.prepare('''
      SELECT 
        s.slot_no AS slot_no, 
        s.barcode, 
        s.cabinet_no AS cabinet_no, 
        s.status AS slot_status, 
        s.slot_type AS slot_type,
        s.pickupCode AS pickup_code,
        s.lock_configs AS lock_configs,
        s.reader_configs AS reader_configs,
        s.light_configs AS light_configs,
        b.barcode AS book_barcode, 
        b.title AS book_title, 
        b.author AS book_author,
        b.isbn AS book_isbn,
        b.image_url AS book_image_url,
        b.circulation_type AS book_circulation_type,
        b.permanent_location AS book_permanent_location,
        b.current_location AS book_current_location,
        b.status AS book_status,
        b.reservation AS book_reservation
      FROM slots s
      LEFT JOIN books b ON s.barcode = b.barcode
      $whereClause
    ''');

      final results = stmt?.select(params);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      List<Map<String, dynamic>> slotsWithBooks = [];

      for (var row in results) {
        dynamic lockConfigs;
        dynamic readerConfigs;
        dynamic lightConfigs;
        
        try {
          if (row['lock_configs'] != null) {
            lockConfigs = jsonDecode(row['lock_configs']);
          }
          if (row['reader_configs'] != null) {
            readerConfigs = jsonDecode(row['reader_configs']);
          }
          if (row['light_configs'] != null) {
            lightConfigs = jsonDecode(row['light_configs']);
          }
        } catch (e) {
          print('JSON parsing error: $e');
        }
        
        Map<String, dynamic> slotInfo = {
          'slot_no': row['slot_no'],
          'barcode': row['barcode'],
          'cabinet_no': row['cabinet_no'],
          'slot_status': row['slot_status'],
          'slot_type': row['slot_type'],
          'pickup_code': row['pickup_code'],
          'lock_configs': lockConfigs,
          'reader_configs': readerConfigs,
          'light_configs': lightConfigs,
        };

        if (row['book_barcode'] != null) {
          slotInfo['book'] = {
            'barcode': row['book_barcode'],
            'title': row['book_title'],
            'author': row['book_author'],
            'isbn': row['book_isbn'],
            'image_url': row['book_image_url'],
            'circulation_type': row['book_circulation_type'],
            'permanent_location': row['book_permanent_location'],
            'current_location': row['book_current_location'],
            'status': row['book_status'],
            'reservation': row['book_reservation'] ?? 'N'
          };
        }

        slotsWithBooks.add(slotInfo);
      }

      return DBResult.success(slotsWithBooks);
    } catch (e) {
      print('查询格口信息失败: $e');
      return DBResult.error('查询格口信息失败: $e');
    }
  }

  /// 清除指定条码的书籍信息并更新格口状态为空闲
  @override
  Future<DBResult<void>> delBookAndUpdateSlot({
    required String slotNo,
    required String barcode,
  }) async {
    try {
      // 开始事务
      _db?.execute('BEGIN TRANSACTION');

      // 解析 slotNo 获取 cabinetNo
      final parts = slotNo.split('-');
      if (parts.length != 3) {
        throw Exception('Invalid slotNo format. Expected format: cabinetNo-shelfNo-slotNo');
      }
      final cabinetNo = parts[0];

      // 验证格口和书籍信息是否匹配
      final slotStmt = _db?.prepare('''
        SELECT barcode 
        FROM slots 
        WHERE cabinet_no = ? AND slot_no = ? AND barcode = ?
      ''');
      final slotResult = slotStmt?.select([cabinetNo, slotNo, barcode]);
      slotStmt?.dispose();

      if (slotResult == null || slotResult.isEmpty) {
        throw Exception('格口中没有找到对应的书籍');
      }

      // 从 books 表中删除书籍信息
      final deleteBookStmt = _db?.prepare('DELETE FROM books WHERE barcode = ?');
      deleteBookStmt?.execute([barcode]);
      deleteBookStmt?.dispose();

      // 更新格口状态为空闲
      final updateSlotStmt = _db?.prepare('''
        UPDATE slots 
        SET barcode = '',
            status = ?,
            pickupCode = '',
            updated_at = ?
        WHERE cabinet_no = ? AND slot_no = ?
      ''');
      
      updateSlotStmt?.execute([
        CabinetConfig.slotEmpty, // 设置格口状态为空闲
        DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
        cabinetNo,
        slotNo,
      ]);
      updateSlotStmt?.dispose();

      // 提交事务
      _db?.execute('COMMIT');
      print('删除书籍信息成功');
      return DBResult.success();
    } catch (e) {
      // 回滚事务
      _db?.execute('ROLLBACK');
      print('删除书籍信息失败: $e');
      return DBResult.error('删除书籍信息失败: $e');
    }
  }

  @override
  Future<DBResult<Map<String, dynamic>>> querySlot(String slotNo) async {
    try {
      final parts = slotNo.split('-');
      if (parts.length != 3) {
        return DBResult.error('格口号格式错误，应为: cabinetNo-shelfNo-slotNo');
      }
      final cabinetNo = parts[0];

      final stmt = _db?.prepare('''
        SELECT 
          s.slot_no,
          s.barcode,
          s.cabinet_no,
          s.shelf_no,
          s.status as slot_status,
          s.slot_type,
          s.pickupCode,
          s.lock_configs,
          s.reader_configs,
          s.light_configs,
          s.lastChecked,
          s.lastOperator,
          b.barcode as book_barcode,
          b.title as book_title,
          b.author as book_author,
          b.isbn as book_isbn,
          b.call_number as book_call_number,
          b.image_url as book_image_url,
          b.circulation_type as book_circulation_type,
          b.permanent_location as book_permanent_location,
          b.current_location as book_current_location,
          b.status as book_status,
          b.reservation as book_reservation
        FROM slots s
        LEFT JOIN books b ON s.barcode = b.barcode
        WHERE s.cabinet_no = ? AND s.slot_no = ?
      ''');

      final results = stmt?.select([cabinetNo, slotNo]);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.error('未找到该格口');
      }

      final row = results.first;
      
      Map<String, dynamic> slotInfo = {
        'slot_no': row['slot_no'],
        'barcode': row['barcode'],
        'cabinet_no': row['cabinet_no'],
        'shelf_no': row['shelf_no'],
        'slot_status': row['slot_status'],
        'slot_type': row['slot_type'],
        'pickup_code': row['pickupCode'],
        'lock_configs': row['lock_configs'] != null ? jsonDecode(row['lock_configs']) : [],
        'reader_configs': row['reader_configs'] != null ? jsonDecode(row['reader_configs']) : [],
        'light_configs': row['light_configs'] != null ? jsonDecode(row['light_configs']) : [],
        'last_checked': row['lastChecked'],
        'last_operator': row['lastOperator'],
      };

      if (row['book_barcode'] != null) {
        slotInfo['book'] = {
          'barcode': row['book_barcode'],
          'title': row['book_title'],
          'author': row['book_author'],
          'isbn': row['book_isbn'],
          'call_number': row['book_call_number'],
          'image_url': row['book_image_url'],
          'circulation_type': row['book_circulation_type'],
          'permanent_location': row['book_permanent_location'],
          'current_location': row['book_current_location'],
          'status': row['book_status'],
          'reservation': row['book_reservation'] ?? 'N',
        };
      }

      return DBResult.success(slotInfo);
    } catch (e) {
      print('查询格口信息失败: $e');
      return DBResult.error('查询格口信息失败: $e');
    }
  }

  @override
  Future<DBResult<List<Map<String, dynamic>>>> querySlotsByBarcode(String barcode) async {
    try {
      final stmt = _db?.prepare('''
      SELECT 
        s.slot_no AS slot_no, 
        s.cabinet_no AS cabinet_no, 
        s.status AS slot_status, 
        s.slot_type AS slot_type,
        s.pickupCode AS pickup_code,
        s.lock_configs AS lock_configs,
        s.reader_configs AS reader_configs,
        s.light_configs AS light_configs,
        b.barcode AS book_barcode, 
        b.title AS book_title, 
        b.author AS book_author,
        b.isbn AS book_isbn,
        b.image_url AS book_image_url,
        b.circulation_type AS book_circulation_type,
        b.permanent_location AS book_permanent_location,
        b.current_location AS book_current_location,
        b.status AS book_status,
        b.reservation AS book_reservation
      FROM slots s
      LEFT JOIN books b ON s.barcode = b.barcode
      WHERE s.barcode = ? 
    ''');

      final results = stmt?.select([barcode]);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      List<Map<String, dynamic>> slotsWithBooks = [];

      for (var row in results) {
        dynamic lockConfigs;
        dynamic readerConfigs;
        dynamic lightConfigs;

        try {
          if (row['lock_configs'] != null) {
            lockConfigs = jsonDecode(row['lock_configs']);
          }
          if (row['reader_configs'] != null) {
            readerConfigs = jsonDecode(row['reader_configs']);
          }
          if (row['light_configs'] != null) {
            lightConfigs = jsonDecode(row['light_configs']);
          }
        } catch (e) {
          print('JSON parsing error: $e');
        }

        Map<String, dynamic> slotInfo = {
          'slot_no': row['slot_no'],
          'cabinet_no': row['cabinet_no'],
          'slot_status': row['slot_status'],
          'slot_type': row['slot_type'],
          'pickup_code': row['pickup_code'],
          'lock_configs': lockConfigs,
          'reader_configs': readerConfigs,
          'light_configs': lightConfigs,
          'book': {
            'barcode': row['book_barcode'],
            'title': row['book_title'],
            'author': row['book_author'],
            'isbn': row['book_isbn'],
            'image_url': row['book_image_url'],
            'circulation_type': row['book_circulation_type'],
            'permanent_location': row['book_permanent_location'],
            'current_location': row['book_current_location'],
            'status': row['book_status'],
            'reservation': row['book_reservation'] ?? 'N',
          }
        };

        if (row['book_barcode'] == null) {
          slotInfo.remove('book');
        }

        slotsWithBooks.add(slotInfo);
      }

      return DBResult.success(slotsWithBooks);
    } catch (e) {
      print('查询格口信息失败: $e');
      return DBResult.error('查询格口信息失败: $e');
    }
  }

  /// 查询空格口，尝试返回普通区(type=0)和预约区(type=1)各一个。
  /// 返回一个包含两个元素的列表，第一个为普通空格口，第二个为预约空格口。
  /// 如果找不到对应类型的空格口，则该位置为 null。
  @override
  Future<DBResult<List<Map<String, dynamic>?>>> queryEmptySlot({String type = 'rfid'}) async {
    try {
      List<Map<String, dynamic>?> resultSlots = [null, null];
      final baseConditions = '''
        (s.barcode IS NULL OR s.barcode = '') 
          AND s.status = ? 
          AND s.lock_configs IS NOT NULL AND s.lock_configs != '[]'
          ${type != 'central' ? "AND s.reader_configs IS NOT NULL AND s.reader_configs != '[]'" : ""}
      ''';

      // --- 查询 slot_type = '0' (普通区) 的空格口 ---
      String queryType0 = '''
        SELECT 
          s.slot_no, s.barcode, s.cabinet_no, s.shelf_no, s.status as slot_status,
          s.slot_type, s.pickupCode, s.lock_configs, s.reader_configs, s.light_configs,
          s.lastChecked, s.lastOperator
        FROM slots s
        WHERE $baseConditions AND s.slot_type = '0' 
        ORDER BY RANDOM() 
        LIMIT 1
      ''';
      final stmtType0 = _db?.prepare(queryType0);
      final resultsType0 = stmtType0?.select([CabinetConfig.slotEmpty]);
      stmtType0?.dispose();

      if (resultsType0 != null && resultsType0.isNotEmpty) {
        resultSlots[0] = _formatSlotInfo(resultsType0.first);
      }

      // --- 查询 slot_type = '1' (预约区) 的空格口 ---
      String queryType1 = '''
        SELECT 
          s.slot_no, s.barcode, s.cabinet_no, s.shelf_no, s.status as slot_status,
          s.slot_type, s.pickupCode, s.lock_configs, s.reader_configs, s.light_configs,
          s.lastChecked, s.lastOperator
        FROM slots s
        WHERE $baseConditions AND s.slot_type = '1' 
        ORDER BY RANDOM() 
        LIMIT 1
      ''';
      final stmtType1 = _db?.prepare(queryType1);
      final resultsType1 = stmtType1?.select([CabinetConfig.slotEmpty]);
      stmtType1?.dispose();

      if (resultsType1 != null && resultsType1.isNotEmpty) {
        resultSlots[1] = _formatSlotInfo(resultsType1.first);
      }

      // 可以根据业务逻辑决定，如果两个都找不到是返回错误还是成功但包含null的列表
      // if (resultSlots[0] == null && resultSlots[1] == null) {
      //   return DBResult.error('没有找到任何符合条件的空格口');
      // }

      return DBResult.success(resultSlots);

    } catch (e) {
      print('查询空格口失败: $e');
      return DBResult.error('查询空格口失败: $e');
    }
  }

  // 辅助方法：格式化格口信息Map (确保这个方法存在或者从之前的代码中复制过来)
  Map<String, dynamic> _formatSlotInfo(Row row) {
    return {
      'slot_no': row['slot_no'],
      'barcode': row['barcode'],
      'cabinet_no': row['cabinet_no'],
      'shelf_no': row['shelf_no'],
      'slot_status': row['slot_status'],
      'slot_type': row['slot_type'],
      'pickup_code': row['pickupCode'],
      'lock_configs': row['lock_configs'] != null ? jsonDecode(row['lock_configs']) : [],
      'reader_configs': row['reader_configs'] != null ? jsonDecode(row['reader_configs']) : [],
      'light_configs': row['light_configs'] != null ? jsonDecode(row['light_configs']) : [],
      'last_checked': row['lastChecked'],
      'last_operator': row['lastOperator'],
    };
  }

  /// 设置系统类型（rfid/central）
  @override
  Future<DBResult<String>> setSystemType(String systemType) async {
    try {
      if (systemType != 'rfid' && systemType != 'central') {
        return DBResult.error('系统类型只能是 rfid 或 central');
      }

      final updateStmt = _db?.prepare('''
        INSERT OR REPLACE INTO app_settings (key, value, type, description)
        VALUES (?, ?, ?, ?)
      ''');
      
      updateStmt?.execute([
        'system_type',
        systemType,
        'string',
        '书柜系统的类型'
      ]);
      updateStmt?.dispose();

      print('系统类型设置为: $systemType');
      return DBResult.success(systemType);
    } catch (e) {
      print('设置系统类型失败: $e');
      return DBResult.error('设置系统类型失败: $e');
    }
  }

  /// 获取系统类型
  @override
  Future<DBResult<String>> getSystemType() async {
    try {
      final stmt = _db?.prepare(
        'SELECT value FROM app_settings WHERE key = ?'
      );
      final result = stmt?.select(['system_type']);
      stmt?.dispose();

      if (result == null || result.isEmpty) {
        return DBResult.error('未设置系统类型');
      }

      final systemType = result.first['value'] as String;
      return DBResult.success(systemType);
    } catch (e) {
      print('获取系统类型失败: $e');
      return DBResult.error('获取系统类型失败: $e');
    }
  }

  /// 获取或创建特殊书箱格口 (类型为999)
  /// 返回格口配置信息
  @override
  Future<DBResult<SlotConfig>> getOrCreateSpecialSlot() async {
    try {
      // 1. 查询是否已存在特殊格口(类型为999)
      final queryStmt = _db?.prepare('''
        SELECT 
          slot_no,
          lock_configs,
          light_configs,
          reader_configs,
          slot_type
        FROM slots 
        WHERE slot_type = '999' 
        LIMIT 1
      ''');
      
      final results = queryStmt?.select();
      queryStmt?.dispose();
      
      // 2. 如果已存在，直接返回格口配置
      if (results != null && results.isNotEmpty) {
        final row = results.first;
        
        // 解析配置
        List<LockConfig>? lockConfigs;
        if (row['lock_configs'] != null) {
          final List<dynamic> json = jsonDecode(row['lock_configs']);
          lockConfigs = json.map((j) => LockConfig.fromJson(j)).toList();
        }

        List<LightConfig>? lightConfigs;
        if (row['light_configs'] != null) {
          final List<dynamic> json = jsonDecode(row['light_configs']);
          lightConfigs = json.map((j) => LightConfig.fromJson(j)).toList();
        }

        List<ReaderConfig>? readerConfigs;
        if (row['reader_configs'] != null) {
          final List<dynamic> json = jsonDecode(row['reader_configs']);
          readerConfigs = json.map((j) => ReaderConfig.fromJson(j)).toList();
        }

        final config = SlotConfig(
          lockConfigs: lockConfigs,
          lightConfigs: lightConfigs,
          readerConfigs: readerConfigs,
          type: row['slot_type'] as String?,
        );

        print('找到已存在的特殊格口: ${row['slot_no']}');
        return DBResult.success(config);
      }
      
      // 3. 如果不存在，创建新的特殊格口
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
      
      const specialSlotNo = '9999';
      const cabinetNo = 'return_book_box_cabinet_no';
      const shelfNo = 'return_book_box_shelf_no';
      
      // 初始化默认配置
      final defaultConfigs = {
        'lock_configs': jsonEncode([]),
        'light_configs': jsonEncode([]),
        'reader_configs': jsonEncode([]),
      };
      
      // 开始事务
      _db?.execute('BEGIN TRANSACTION');
      
      // 插入特殊格口
      final insertStmt = _db?.prepare('''
        INSERT OR IGNORE INTO slots (
          cabinet_no, shelf_no, slot_no, barcode, status, 
          slot_type, pickupCode, lock_configs, light_configs, reader_configs,
          lastChecked, lastOperator, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''');
      
      insertStmt?.execute([
        cabinetNo,                     // 特殊书柜编号
        shelfNo,                       // 特殊书架编号
        specialSlotNo,                 // 特殊格口编号
        '',                            // 书籍条码（初始为空）
        CabinetConfig.slotEmpty,       // 格口状态（初始为空）
        '999',                         // 格口类型（999表示特殊格口）
        '',                            // 取书码（初始为空）
        defaultConfigs['lock_configs'],
        defaultConfigs['light_configs'],
        defaultConfigs['reader_configs'],
        now,
        'system',
        now,
        now,
      ]);
      
      insertStmt?.dispose();
      
      // 提交事务
      _db?.execute('COMMIT');
      
      // 创建并返回新格口的配置
      final config = SlotConfig(
        lockConfigs: [],
        lightConfigs: [],
        readerConfigs: [],
        type: '999',
      );
      
      print('成功创建特殊格口: $specialSlotNo');
      return DBResult.success(config);
    } catch (e) {
      // 回滚事务
      _db?.execute('ROLLBACK');
      print('获取或创建特殊格口失败: $e');
      return DBResult.error('获取或创建特殊格口失败: $e');
    }
  }
  
  /// 更新书柜的排序顺序
  @override
  Future<DBResult<void>> updateCabinetSortOrder(String cabinetNo, int sortOrder) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      final stmt = _db?.prepare('''
        UPDATE cabinets 
        SET sort_order = ?,
            updated_at = ?
        WHERE cabinet_no = ?
      ''');

      stmt?.execute([sortOrder, now, cabinetNo]);
      stmt?.dispose();

      return DBResult.success();
    } catch (e) {
      print('更新书柜排序失败: $e');
      return DBResult.error('更新书柜排序失败: $e');
    }
  }

  /// 获取书柜配置
  @override
  Future<DBResult<Map<String, dynamic>>> getCabinetConfig(String cabinetNo) async {
    try {
      final stmt = _db?.prepare('''
        SELECT 
          c.*,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no 
           AND s.status = '1') as occupied_slots,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no) as total_slots
        FROM cabinets c
        WHERE c.cabinet_no = ?
      ''');

      final results = stmt?.select([cabinetNo]);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.error('未找到书柜配置信息');
      }

      final cabinetConfig = Map<String, dynamic>.from(results.first);
      
      return DBResult.success(cabinetConfig);
    } catch (e) {
      print('获取书柜配置失败: $e');
      return DBResult.error('获取书柜配置失败: $e');
    }
  }

  @override
  Future<DBResult<int?>> getCabinetOrderMode() {
    // TODO: implement getCabinetOrderMode
    throw UnimplementedError();
  }

  @override
  Future<DBResult<void>> setCabinetOrderMode(int value) {
    // TODO: implement setCabinetOrderMode
    throw UnimplementedError();
  }

  /// 添加认证记录
  @override
  Future<DBResult<void>> addAccessLog({
    required String name,
    required String readerCardNo,
    required DateTime openTime,
    String? authMethod,
    String status = 'success',
    String? remark,
  }) async {
    try {
      final formattedOpenTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(openTime);
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      final stmt = _db?.prepare('''
        INSERT INTO access_logs (
          name, reader_card_no, open_time, auth_method, status, remark, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      ''');

      stmt?.execute([
        name,
        readerCardNo,
        formattedOpenTime,
        authMethod ?? '',
        status,
        remark ?? '',
        now
      ]);
      stmt?.dispose();

      return DBResult.success();
    } catch (e) {
      print('添加认证记录失败: $e');
      return DBResult.error('添加认证记录失败: $e');
    }
  }

  /// 根据时间范围查询认证记录
  @override
  Future<DBResult<List<Map<String, dynamic>>>> queryAccessLogs({
    required DateTime startDate,
    required DateTime endDate,
    int? limit, 
    int? offset,
  }) async {
    try {
      final formattedStartDate = DateFormat('yyyy-MM-dd 00:00:00').format(startDate);
      final formattedEndDate = DateFormat('yyyy-MM-dd 23:59:59').format(endDate);

      String sql = '''
        SELECT 
          id, name, reader_card_no, open_time, auth_method, status, remark
        FROM access_logs
        WHERE open_time BETWEEN ? AND ?
        ORDER BY open_time DESC
      ''';

      if (limit != null) {
        sql += ' LIMIT ?';
        if (offset != null) {
          sql += ' OFFSET ?';
        }
      }

      final stmt = _db?.prepare(sql);

      List<Object?> params = [formattedStartDate, formattedEndDate];
      if (limit != null) {
        params.add(limit);
        if (offset != null) {
          params.add(offset);
        }
      }

      final results = stmt?.select(params);
      stmt?.dispose();

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final logs = results.map((row) => Map<String, dynamic>.from(row)).toList();
      return DBResult.success(logs);
    } catch (e) {
      print('查询认证记录失败: $e');
      return DBResult.error('查询认证记录失败: $e');
    }
  }

  /// 删除指定日期前的认证记录
  @override
  Future<DBResult<int>> cleanupOldAccessLogs(DateTime beforeDate) async {
    try {
      final formattedDate = DateFormat('yyyy-MM-dd 00:00:00').format(beforeDate);

      final stmt = _db?.prepare('''
        DELETE FROM access_logs
        WHERE open_time < ?
      ''');

      stmt?.execute([formattedDate]);
      final changesCount = _db?.getUpdatedRows() ?? 0;
      stmt?.dispose();

      print('已删除 $changesCount 条旧认证记录');
      return DBResult.success(changesCount);
    } catch (e) {
      print('清理旧认证记录失败: $e');
      return DBResult.error('清理旧认证记录失败: $e');
    }
  }
}

/// 书柜配置类
class CabinetConfig {
  // 书柜物理配置
  static const int shelfPerCabinet = 5; // 每个书柜的层数
  static const int slotsPerShelf = 24; // 每层的格口数
  static const int totalSlots = 120; // 每个柜子的总格口数（5层 × 24个）
  static const int antennaPerShelf = 6; // 每层天线数（每4个格口一个天线）

  // 格口类型
  static const String normalSlot = '0'; // 普通区
  static const String reserveSlot = '1'; // 预约区

  // 格口状态
  static const String slotEmpty = '0'; // 空闲
  static const String slotOccupied = '1'; // 占用
  static const String slotDisabled = '2'; // 禁用

  // 书柜状态
  static const String cabinetOnline = '1'; // 在线
  static const String cabinetOffline = '0'; // 离线
  static const String cabinetMaintain = '2'; // 维护中

  // 系统配置
  static const Duration heartbeatInterval = Duration(seconds: 30); // 心跳间隔
  static const Duration offlineTimeout = Duration(minutes: 2); // 离线判定时间

  // 获取指定层的格口范围
  static List<int> getShelfSlotList(int shelfNo) {
    final startSlot = (shelfNo - 1) * slotsPerShelf + 1;
    final endSlot = startSlot + slotsPerShelf - 1;
    return [startSlot, endSlot];
  }

  // 根据格口号获取所属层数
  static int getShelfNoBySlot(int slotNo) {
    return ((slotNo - 1) ~/ slotsPerShelf) + 1;
  }

  // 根据格口号获取天线编号
  static int getAntennaNoBySlot(int slotNo) {
    return ((slotNo - 1) ~/ 4) + 1;
  }

  // 检查格口号是否有效
  static bool isValidSlotNo(int slotNo) {
    return slotNo >= 1 && slotNo <= totalSlots;
  }

  // 检查层号是否有效
  static bool isValidShelfNo(int shelfNo) {
    return shelfNo >= 1 && shelfNo <= shelfPerCabinet;
  }
}
