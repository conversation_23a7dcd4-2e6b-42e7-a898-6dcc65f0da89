import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';

import '../../login/models/operation_type.dart';
import '../view_models/auth_view_model.dart';
import 'auth_view.dart';



class AuthPage extends StatelessWidget {
  final AuthLoginType authLoginType;
  final OperationType operationType;
  const AuthPage({super.key, required this.authLoginType, required this.operationType});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AuthViewModel(
        context: context,
        authLoginType: authLoginType,
        operationType: operationType,
      ),
      child: AuthView(
        authLoginType: authLoginType,
        operationType: operationType,
      ),
    );
  }
}
