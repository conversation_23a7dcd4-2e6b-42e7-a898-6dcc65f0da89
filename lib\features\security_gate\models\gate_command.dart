/// 闸机命令模型
class GateCommand {
  final String type;
  final List<int> data;
  final DateTime timestamp;
  
  GateCommand({
    required this.type,
    required this.data,
  }) : timestamp = DateTime.now();
  
  // 预定义的命令类型常量
  static const String enterStart = 'enter_start';
  static const String enterEnd = 'enter_end';
  static const String exitStart = 'exit_start';
  static const String exitEnd = 'exit_end';
  static const String positionReached = 'position_reached';
  static const String tailgating = 'tailgating';
  static const String doorBlocked = 'door_blocked';
  
  /// 接收命令数据映射（从闸机硬件接收的命令）
  static final Map<String, List<int>> receiveCommandMap = {
    enterStart: [0xAA, 0x00, 0x64, 0x80, 0x00, 0x00, 0x06, 0xD2],
    enterEnd: [0xAA, 0x00, 0x65, 0x80, 0x00, 0x00, 0x07, 0x2E],
    exitStart: [0xAA, 0x00, 0xC8, 0x80, 0x00, 0x00, 0x27, 0x82],
    exitEnd: [0xAA, 0x00, 0xC9, 0x80, 0x00, 0x00, 0x26, 0x7E],
    positionReached: [0xAA, 0x00, 0x0A, 0x80, 0x00, 0x00],
    tailgating: [0xAA, 0x00, 0x0F, 0x80, 0x00, 0x00],
    doorBlocked: [0xAA, 0x00, 0x0B, 0x80, 0x00, 0x00],
  };
  
  /// 发送命令数据映射（发送给闸机硬件的命令）
  static final Map<String, List<int>> sendCommandMap = {
    'enter_open': [0xAA, 0x00, 0x02, 0x01, 0x00, 0x00, 0x48, 0x72],    // 进馆开门
    'exit_open': [0xAA, 0x00, 0x01, 0x01, 0x00, 0x00, 0x48, 0x36],     // 出馆开门
    'failure_signal': [0xAA, 0x00, 0x02, 0x01, 0x00, 0x00, 0x48, 0x72], // 失败信号
  };
  
  /// 根据接收到的数据解析命令类型
  static String? parseCommandType(List<int> data) {
    for (final entry in receiveCommandMap.entries) {
      if (_matchCommand(data, entry.value)) {
        return entry.key;
      }
    }
    return null;
  }
  
  /// 获取发送命令的数据
  static List<int>? getSendCommandData(String commandType) {
    return sendCommandMap[commandType];
  }
  
  /// 匹配命令数据
  static bool _matchCommand(List<int> received, List<int> expected) {
    if (received.length < expected.length) return false;
    
    for (int i = 0; i < expected.length; i++) {
      if (received[i] != expected[i]) return false;
    }
    return true;
  }
  
  /// 获取命令的显示名称
  String get displayName {
    switch (type) {
      case enterStart:
        return '进馆开始';
      case enterEnd:
        return '进馆结束';
      case exitStart:
        return '出馆开始';
      case exitEnd:
        return '出馆结束';
      case positionReached:
        return '到达指定位置';
      case tailgating:
        return '尾随检测';
      case doorBlocked:
        return '开门有人';
      default:
        return '未知命令';
    }
  }
  
  /// 将命令转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'display_name': displayName,
    };
  }
  
  /// 从JSON创建命令
  factory GateCommand.fromJson(Map<String, dynamic> json) {
    return GateCommand(
      type: json['type'] as String,
      data: List<int>.from(json['data'] as List),
    );
  }
  
  @override
  String toString() {
    return 'GateCommand{type: $type, data: $data, timestamp: $timestamp}';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GateCommand &&
        other.type == type &&
        _listEquals(other.data, data);
  }
  
  @override
  int get hashCode => type.hashCode ^ data.hashCode;
  
  /// 比较两个列表是否相等
  static bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}
