import 'package:base_package/base_package.dart';
import 'package:hardware/hardware.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:seaface/seaface.dart';
import 'package:seasetting/seasetting.dart';

import '../database/db_manager.dart';
import '../database/interfaces/db_interface.dart';
import 'app_info_provider.dart';
import 'countdown_provider.dart';
import 'user_provider.dart';

class AppProviders {
  static late final DBInterface db;

  // 初始化方法
  static Future<void> init() async {
    db = await DBManager.getDBInstance();
  }
  static List<SingleChildWidget> get providers => [
    ChangeNotifierProvider.value(value: AppInfoProvider()),
    ChangeNotifierProvider(create: (context) => CurrentLocale()),
    ChangeNotifierProvider(create: (context) => CurrentLayoutProvider()),
    ChangeNotifierProvider(create: (context) => PageCountDownProvder()),
    ChangeNotifierProvider(create: (context) => SettingProvider(externalDB: db)),
    ChangeNotifierProvider(create: (context) => Sip2ConfigProvider()),
    ChangeNotifierProvider(create: (context) => Sip2SocketProvider()),
    ChangeNotifierProvider(create: (context) => HWTagProvider()),
    ChangeNotifierProvider(create: (context) => SFCameraProvider()),
    ChangeNotifierProvider(create: (_) => DBProvider()),
    ChangeNotifierProvider(create: (_) => NetworkProvider()),
    ChangeNotifierProvider(create: (_) => NewBookInfoProvider()),
    ChangeNotifierProvider(create: (_) => UserProvider()),
    ChangeNotifierProvider(create: (_) => CountdownProvider()),
    
  ];
}
