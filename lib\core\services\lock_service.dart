import 'dart:async';

import 'package:flutter_libserialport/flutter_libserialport.dart';
import 'package:hardware/hardware.dart';
import 'package:logging/logging.dart';
import 'package:seasetting/seasetting.dart';

class LockService {
  static final LockService _instance = LockService._internal();
  factory LockService() => _instance;
  LockService._internal();

  final _lockManager = LockManager.instance;
  final _logger = Logger('LockService');

  // Store the settings used for initialization
  List<HWReaderSettingData> _initializedLockSettings = [];

  /// Initializes lock ports based on the provided settings.
  /// Should be called once with all relevant lock configurations.
  Future<void> initLocks(List<HWReaderSettingData> lockSettings) async {
    _logger.info('开始初始化门锁，配置 ${lockSettings.length} 个锁...');
    try {
      // Ensure existing ports are closed before re-initializing
      closePorts();

      // Store settings and pass them to the manager
      _initializedLockSettings = lockSettings.where((data) => data.info is HWLockInfoData).toList();
      _lockManager.settings = _initializedLockSettings;

      // Initialize and attempt to open each configured lock port
      for (var data in _initializedLockSettings) {
        // Type cast is safe due to the 'where' filter above
        final info = data.info as HWLockInfoData;
        if (info.comPort != null && info.comPort!.isNotEmpty) {
          _logger.info('初始化并尝试打开锁端口: ${info.comPort}');
          final lockPort = LockPort(SerialPort(info.comPort!), info.comPort!);
          // Attempt to open the port - error handling is implicit in LockManager?
          // Or should opening be a separate step called when needed?
          // For now, assume init attempts to open.
          final bool opened = lockPort.open(); // Store result if needed for immediate state
          _lockManager.ports.add(lockPort);
          _logger.info('锁端口 ${info.comPort} 添加到管理器，打开结果: $opened。');
          // State update (e.g., initial open status) should happen in the calling code
          // based on the requirements after initialization.
        } else {
          // Log warning if essential info is missing
          _logger.warning('配置中缺少有效的锁串口信息 (door/lock info might be in extras): ${data.readerType} / ${data.extras?.toString()}');
        }
      }
      _logger.info('门锁初始化完成。');
    } catch (e, stack) {
      _logger.severe('初始化门锁过程中发生严重错误', e, stack);
      rethrow; // Propagate error for handling upstream
    }
  }

  /// Opens all configured lock doors.
  Future<void> openAllDoors() async {
    _logger.info('请求打开所有门锁...');
    try {
      await _lockManager.openAllLocks();
      _logger.info('打开所有门锁操作完成。');
    } catch (e, stack) {
      _logger.severe('打开所有门锁失败', e, stack);
      rethrow;
    }
  }

  /// Opens a specific door by its identifier (slotNo).
  Future<bool> openDoor(String doorId) async {
    _logger.info('请求打开门锁: $doorId');
    try {
      final result = await _lockManager.openDoor(doorId);
      _logger.info('打开门锁 $doorId 操作结果: $result');
      return result;
    } catch (e, stack) {
      _logger.severe('打开门锁 $doorId 失败', e, stack);
      return false; // Indicate failure
    }
  }

  /// Queries the status of a specific door (slotNo).
  Future<bool> isDoorOpen(String doorId) async {
    _logger.info('查询门锁状态: $doorId');
    try {
      final result = await _lockManager.queryDoorStatus(doorId);
      _logger.info('查询门锁 $doorId 状态结果: $result (true means open/unlocked)');
      return result;
    } catch (e, stack) {
      _logger.severe('查询门锁 $doorId 状态失败', e, stack);
      return false; // Assume closed/error on failure
    }
  }

  /// Controls the light associated with a specific door (slotNo).
  Future<bool> controlDoorLight(String doorId, bool turnOn) async {
    final action = turnOn ? '开启' : '关闭';
    _logger.info('请求 $action 门锁灯: $doorId');
    try {
      final result = await _lockManager.operateDoorLight(doorId, turnOn);
      _logger.info('$action 门锁灯 $doorId 操作结果: $result');
      return result;
    } catch (e, stack) {
      _logger.severe('$action 门锁灯 $doorId 失败', e, stack);
      return false; // Indicate failure
    }
  }

  /// Closes all opened serial ports managed by this service.
  void closePorts() {
    _logger.info('请求关闭所有门锁端口...');
    try {
       _lockManager.closePorts();
       _logger.info('关闭所有门锁端口操作完成。');
    } catch (e, stack) {
        _logger.severe('关闭门锁端口时出错', e, stack);
    }

  }

  /// Disposes resources, ensuring ports are closed.
  void dispose() {
    _logger.info('释放 LockService 资源...');
    closePorts(); // Ensure all ports are closed on dispose
    _logger.info('LockService 资源释放完成。');
  }
}