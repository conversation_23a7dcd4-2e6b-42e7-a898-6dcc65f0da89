import 'package:flutter/foundation.dart';
import 'package:sea_socket/sea_socket.dart';

import '../post_auth_service.dart';

/// 欢迎处理器 - 显示欢迎信息或执行欢迎动画
class WelcomeHandler implements PostAuthHandler {
  @override
  int get priority => 20;
  
  @override
  Future<bool> handle(Sip2PatronInfoData readerInfo, String authMethod) async {
    try {
      debugPrint('显示欢迎信息给: ${readerInfo.PersonName}');

      // 取消Toast显示，只记录日志
      final userName = readerInfo.PersonName ?? '读者';
      debugPrint('欢迎用户: $userName，认证方式: $authMethod');

      // 注释掉Toast显示
      // showToast(
      //   "欢迎您，$userName！",
      //   duration: Duration(seconds: 3),
      //   position: ToastPosition.bottom,
      //   backgroundColor: Colors.blue.withOpacity(0.8),
      //   radius: 10,
      //   textStyle: TextStyle(fontSize: 18.0, color: Colors.white),
      // );

      return true; // 继续执行链
    } catch (e) {
      debugPrint('欢迎处理失败: $e');
      return true; // 出错也继续执行
    }
  }
} 