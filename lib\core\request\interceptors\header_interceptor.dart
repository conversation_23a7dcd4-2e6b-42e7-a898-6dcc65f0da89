import 'package:dio/dio.dart';

/// 请求头拦截器
/// 为每个请求添加通用请求头
class HeaderInterceptor extends Interceptor {
  /// 通用请求头，应用于所有请求
  final Map<String, dynamic> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// 设置请求头
  /// [key] 请求头名称
  /// [value] 请求头值
  void setHeader(String key, dynamic value) {
    _headers[key] = value;
  }

  /// 移除请求头
  /// [key] 请求头名称
  void removeHeader(String key) {
    _headers.remove(key);
  }

  /// 设置认证Token
  /// [token] 认证令牌
  void setAuthToken(String token) {
    setHeader('Authorization', 'Bearer $token');
  }

  /// 清除认证Token
  void clearAuthToken() {
    removeHeader('Authorization');
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加通用请求头
    options.headers.addAll(_headers);
    
    // 继续处理请求
    super.onRequest(options, handler);
  }
} 