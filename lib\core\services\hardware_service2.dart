import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hardware/hardware.dart';
import 'package:logging/logging.dart';
import 'package:flutter_libserialport/flutter_libserialport.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';


class HardwareService {
  static final HardwareService _instance = HardwareService._internal();
  factory HardwareService() => _instance;
  HardwareService._internal();

  final _lockManager = LockManager.instance;
  final _readerManager = ReaderManager.instance;
  final _logger = Logger('HardwareService');

  // 初始化硬件
  Future<void> initHardware(List<HWReaderSettingData> settings) async {
    try {
      _logger.info('初始化硬件');
      // _lockManager.ports.add(value);

      // 先关闭所有端口
      _lockManager.closePorts();

      // 更新设置
      _lockManager.settings = settings;

      // 初始化端口
      for (var data in settings) {
        if (data.info is HWLockInfoData) {
          final info = data.info as HWLockInfoData;
          if (info.comPort != null) {
            final lockPort = LockPort(SerialPort(info.comPort!), info.comPort!);
            // _lockManager.ports.add(lockPort);
            // _lockManager.closePorts();
            // await Future.delayed(const Duration(milliseconds: 2000));
            // lockPort.close();
            // await Future.delayed(const Duration(milliseconds: 2000));
            final ret = lockPort.open();
            _lockManager.ports.add(lockPort);
            HardwareStateManager.instance.refreshState(data.readerType!, ret);
          }
        }
      }
    } catch (e, stack) {
      _logger.severe('初始化硬件失败', e, stack);
      rethrow;
    }
  }

  // 更新硬件设置
  // void updateSettings(List<HWReaderSettingData> settings) {
  //   _lockManager.settings = settings;
  // }



  // 打开所有门锁
  Future<void> openAllDoors() async {
    try {
      _logger.info('打开所有门锁');
      await _lockManager.openAllLocks();
    } catch (e, stack) {
      _logger.severe('打开所有门锁失败', e, stack);
      rethrow;
    }
  }

  // 打开单个门锁
  Future<bool> openDoor(String doorId) async {
    try {
      _logger.info('打开门锁: $doorId');
      return await _lockManager.openDoor(doorId);
    } catch (e, stack) {
      _logger.severe('打开门锁失败: $doorId', e, stack);
      return false;
    }
  }

  // 查询单个门锁状态
  Future<bool> isDoorOpen(String doorId) async {
    try {
      _logger.info('查询门锁状态: $doorId');
      return await _lockManager.queryDoorStatus(doorId);
    } catch (e, stack) {
      _logger.severe('查询门锁状态失败: $doorId', e, stack);
      return false;
    }
  }

  // 控制单个门锁灯
  Future<bool> controlDoorLight(String doorId, bool turnOn) async {
    try {
      _logger.info('${turnOn ? '开启' : '关闭'}门锁灯: $doorId');
      return await _lockManager.operateDoorLight(doorId, turnOn);
    } catch (e, stack) {
      _logger.severe('${turnOn ? '开启' : '关闭'}门锁灯失败: $doorId', e, stack);
      return false;
    }
  }

  // 阅读器操作方法
  // 配置阅读器
  Future<void> changeReaders(String readerSettings) async {
    try {
      _logger.info('配置阅读器');
      await _readerManager.changeReaders(readerSettings);
    } catch (e, stack) {
      _logger.severe('配置阅读器失败', e, stack);
      rethrow;
    }
  }

  // 打开阅读器
  int open() {
    try {
      _logger.info('打开阅读器');
      return _readerManager.open();
    } catch (e, stack) {
      _logger.severe('打开阅读器失败', e, stack);
      return -1;
    }
  }

  // 关闭阅读器
  int close() {
    try {
      _logger.info('关闭阅读器');
      return _readerManager.close();
    } catch (e, stack) {
      _logger.severe('关闭阅读器失败', e, stack);
      return -1;
    }
  }

  // 开始扫描
  int startInventory() {
    try {
      _logger.info('开始扫描');
      return _readerManager.startInventory();
    } catch (e, stack) {
      _logger.severe('开始扫描失败', e, stack);
      return -1;
    }
  }

  // 停止扫描
  int stopInventory() {
    try {
      _logger.info('停止扫描');
      return _readerManager.stopInventory();
    } catch (e, stack) {
      _logger.severe('停止扫描失败', e, stack);
      return -1;
    }
  }

  // 暂停扫描
  int pauseInventory() {
    try {
      _logger.info('暂停扫描');
      return _readerManager.pauseInventory();
    } catch (e, stack) {
      _logger.severe('暂停扫描失败', e, stack);
      return -1;
    }
  }

  // 恢复扫描
  int resumeInventory() {
    try {
      _logger.info('恢复扫描');
      return _readerManager.resumeInventory();
    } catch (e, stack) {
      _logger.severe('恢复扫描失败', e, stack);
      return -1;
    }
  }

  // 添加公开方法用于监听阅读器状态
  void addReaderListener(VoidCallback listener) {
    _readerManager.controller.addListener(listener);
  }

  void removeReaderListener(VoidCallback listener) {
    _readerManager.controller.removeListener(listener);
  }

  // 获取阅读器状态
  ReaderErrorType getReaderState() {
    return _readerManager.controller.type;
  }

  // 释放资源
  void dispose() {
    _logger.info('释放硬件资源');
    _lockManager.closePorts();
  }
}