import 'package:flutter/material.dart';
import 'package:base_package/base_package.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import '../../core/utils/window_util.dart';

class SysTimeoutButton extends StatelessWidget {
  const SysTimeoutButton({
    this.backAction,
    Key? key,
  }) : super(key: key);

  final VoidCallback? backAction;

  void onBack() {
    backAction?.call();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onBack,
      child: Container(
        width: 210.p,
        height: 90.p,
        decoration: BoxDecoration(
          color: BPUtils.c_E65B6E98,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(20.p),
            bottomRight: Radius.circular(20.p),
          ),
        ),
        alignment: Alignment.center,
        child: Consumer<PageCountDownProvder>(
          builder: (context, provider, child) {
            return Text(
              '退出 ${provider.pageInterval}s',
              style: TextStyle(
                fontSize: 36.p,
                color: Colors.white,
              ),
            );
          },
        ),
      ),
    );
  }
} 