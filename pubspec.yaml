name: a3g
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  provider: ^6.0.5
  window_manager: ^0.3.5
  #  virtual_keyboard_multi_language: ^1.0.3
  sealog:
    path: ../sealog/
  hardware:
    path: ../hardware/
  sea_socket:
    path: ../sea_socket/
  base_package:
    path: ../base_package/
  seasetting:
    path: ../seasetting/
  seaface:
    path: ../seaface/
  mysql1: ^0.20.0
  dropdown_search: ^5.0.0
  flutter_multi_slider: ^2.0.0
  xml2json: ^6.2.0
  path_provider: ^2.1.0
  card_swiper: ^3.0.1
  sqlite3_flutter_libs: ^0.5.17
  sqlite3: ^1.9.3
  material_dialogs: ^1.1.4
  dio: ^5.0.1
  get: ^4.6.5
  barcode_widget: ^2.0.4
  dotted_border: ^2.1.0
  dotted_line: ^3.2.2
  #  file_picker: ^6.1.1
  fast_gbk: ^1.0.0
  oktoast: ^3.3.1
  flutter_easyloading: ^3.0.5
  file_selector: ^1.0.1
  url_launcher: ^6.1.11
  libserialport: ^1.0.13
  flutter_localization: 0.2.0
  qr_flutter: ^4.1.0
  gradient_borders: ^1.0.0
  go_router: ^13.2.0
  intl: ^0.18.1

  # 添加FFI支持
  ffi: ^2.0.2
  # 添加路径处理
  path: ^1.8.3
  file_picker: ^5.3.1  # 添加文件选择器依赖
dev_dependencies:
  flutter_test:
    sdk: flutter

  # FlutterGen
  build_runner: ^2.4.6
  flutter_gen_runner: ^5.3.2

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - images/
    - images/ui-blue/
    - images/ui-ximalaya/
    - images/ui-child/
    - images/common/
    - assets/images/
    - assets/fonts/
    - models/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SOURCEHANSANSCN
      fonts:
        - asset: assets/fonts/SourceHanSansCN-Bold.otf
          weight: 700
        - asset: assets/fonts/SourceHanSansCN-ExtraLight.otf
          weight: 200
        - asset: assets/fonts/SourceHanSansCN-Light.otf
          weight: 300
        - asset: assets/fonts/SourceHanSansCN-Medium.otf
          weight: 500
        - asset: assets/fonts/SourceHanSansCN-Normal.otf
          weight: 400
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


flutter_intl:
  enabled: true
