import 'dart:math';

import 'package:flutter/material.dart';

import '../utils/window_util.dart';

class RotatingGradientRing extends StatefulWidget {
  final double size;
  final double strokeWidth;
  final Duration duration;
  final List<Color>? gradientColors;

  const RotatingGradientRing({
    super.key,
    this.size = 160,
    this.strokeWidth = 20,
    this.duration = const Duration(seconds: 1),
    this.gradientColors,
  });

  @override
  State<RotatingGradientRing> createState() => _RotatingGradientRingState();
}

class _RotatingGradientRingState extends State<RotatingGradientRing>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late _GradientRingPainter _painter;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _painter = _GradientRingPainter(
      strokeWidth: widget.strokeWidth.p,
      gradientColors: widget.gradientColors ?? [
        const Color.fromRGBO(63, 226, 200, 1),
        const Color.fromRGBO(84, 160, 255, 1),
      ],
    );

    _controller.repeat();
  }

  @override
  void didUpdateWidget(RotatingGradientRing oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.strokeWidth != widget.strokeWidth ||
        oldWidget.gradientColors != widget.gradientColors) {
      _painter = _GradientRingPainter(
        strokeWidth: widget.strokeWidth.p,
        gradientColors: widget.gradientColors ?? [
          const Color.fromRGBO(63, 226, 200, 1),
          const Color.fromRGBO(84, 160, 255, 1),
        ],
      );
    }

    if (oldWidget.duration != widget.duration) {
      _controller.duration = widget.duration;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (_, __) => Transform.rotate(
          angle: _rotationAnimation.value,
          child: CustomPaint(
            size: Size(widget.size.p, widget.size.p),
            painter: _painter,
            isComplex: true,
            willChange: true,
          ),
        ),
      ),
    );
  }
}

class _GradientRingPainter extends CustomPainter {
  final double strokeWidth;
  final List<Color> gradientColors;
  final Paint _paint;

  _GradientRingPainter({
    required this.strokeWidth,
    required this.gradientColors,
  }) : _paint = Paint()
    ..style = PaintingStyle.stroke
    ..strokeWidth = strokeWidth
    ..strokeCap = StrokeCap.round;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 每次绘制时创建渐变
    final gradient = SweepGradient(
      colors: gradientColors,
      tileMode: TileMode.clamp,
      startAngle: 0,
      endAngle: 2 * pi,
    );

    // 更新渐变
    _paint.shader = gradient.createShader(
      Rect.fromCircle(center: center, radius: radius),
    );

    // 绘制圆环
    canvas.drawCircle(center, radius, _paint);
  }

  @override
  bool shouldRepaint(covariant _GradientRingPainter oldDelegate) {
    return oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.gradientColors != gradientColors;
  }
}