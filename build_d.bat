@echo off
echo Starting Debug build process...

REM Set paths
set OpenCV_DIR=D:\Users\Administrator\Downloads\opencv\build
echo Using OpenCV at %OpenCV_DIR%

REM Check for CMake
where cmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo CMake not found in PATH, trying to use Visual Studio's CMake...
    if exist "D:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" (
        set CMAKE_PATH="D:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"
        echo Using CMake from Visual Studio at %CMAKE_PATH%
    ) else (
        echo CMake not found. Please install CMake or add it to your PATH.
        echo You can download CMake from https://cmake.org/download/
        goto :error
    )
) else (
    set CMAKE_PATH=cmake
    echo Using CMake from PATH
)

REM Create and enter build directory
echo Creating build directory...
if exist "src\opencv\build" (
    echo Cleaning existing build directory...
    rd /s /q "src\opencv\build"
)
mkdir "src\opencv\build"
cd "src\opencv\build"

REM Run CMake to generate project files
echo Running CMake with Debug configuration...
%CMAKE_PATH% .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_PREFIX_PATH=%OpenCV_DIR% -DCMAKE_BUILD_TYPE=Debug

REM Build the project
echo Building Debug version...
%CMAKE_PATH% --build . --config Debug

REM Install to specified location
echo Installing...
%CMAKE_PATH% --install . --config Debug

REM Return to original directory
cd ..\..\..

REM Create destination directories
echo Creating destination directories...
if not exist "windows\runner\Debug\data" (
    mkdir "windows\runner\Debug\data"
    echo Created directory: windows\runner\Debug\data
)
if not exist "windows\runner\Debug\data\models" (
    mkdir "windows\runner\Debug\data\models"
    echo Created directory: windows\runner\Debug\data\models
)
if not exist "build\windows\x64\runner\Debug\data" (
    mkdir "build\windows\x64\runner\Debug\data"
    echo Created directory: build\windows\x64\runner\Debug\data
)
if not exist "build\windows\x64\runner\Debug\data\models" (
    mkdir "build\windows\x64\runner\Debug\data\models"
    echo Created directory: build\windows\x64\runner\Debug\data\models
)
if not exist "build\windows\runner\Debug\data" (
    mkdir "build\windows\runner\Debug\data"
    echo Created directory: build\windows\runner\Debug\data
)
if not exist "build\windows\runner\Debug\data\models" (
    mkdir "build\windows\runner\Debug\data\models"
    echo Created directory: build\windows\runner\Debug\data\models
)

REM Copy DLL files
echo Copying DLL files...
echo Checking for DLL at: src\opencv\build\bin\Debug\libface_detector.dll

REM Check if DLL exists
if not exist "src\opencv\build\bin\Debug\libface_detector.dll" (
    echo Warning: DLL file not found at src\opencv\build\bin\Debug\libface_detector.dll
    echo This may be because the build failed or the output path is different.
    echo Continuing with other copy operations...
) else (
    REM Copy DLL to various required locations
    echo Copying DLL to multiple locations...
    
    REM 1. Copy to windows runner directory
    copy /Y "src\opencv\build\bin\Debug\libface_detector.dll" "windows\runner\Debug\libface_detector.dll"
    
    REM 2. Copy to flutter app build directory (with x64 path)
    copy /Y "src\opencv\build\bin\Debug\libface_detector.dll" "build\windows\x64\runner\Debug\libface_detector.dll"
    
    REM 3. Copy to actual Flutter build directory
    copy /Y "src\opencv\build\bin\Debug\libface_detector.dll" "build\windows\runner\Debug\libface_detector.dll"
    
    REM Verify all copied files
    echo Verifying all DLL locations...
    if exist "windows\runner\Debug\libface_detector.dll" echo - Found in windows\runner\Debug
    if exist "build\windows\x64\runner\Debug\libface_detector.dll" echo - Found in build\windows\x64\runner\Debug
    if exist "build\windows\runner\Debug\libface_detector.dll" echo - Found in build\windows\runner\Debug
)

REM Copy OpenCV Debug DLLs (only debug versions)
echo Copying OpenCV Debug DLLs to directories...
copy /Y "%OpenCV_DIR%\x64\vc16\bin\opencv_world*d.dll" "windows\runner\Debug\"
echo Verifying OpenCV copy in windows\runner\Debug:
dir "windows\runner\Debug\opencv_world*d.dll"

copy /Y "%OpenCV_DIR%\x64\vc16\bin\opencv_world*d.dll" "build\windows\x64\runner\Debug\"
echo Verifying OpenCV copy in build\windows\x64\runner\Debug:
dir "build\windows\x64\runner\Debug\opencv_world*d.dll"

copy /Y "%OpenCV_DIR%\x64\vc16\bin\opencv_world*d.dll" "build\windows\runner\Debug\"
echo Verifying OpenCV copy in build\windows\runner\Debug:
dir "build\windows\runner\Debug\opencv_world*d.dll"

REM Include only debug DLLs
echo Filtering for debug DLLs (those with 'd' suffix)...
for %%f in ("%OpenCV_DIR%\x64\vc16\bin\opencv_world*.dll") do (
    echo %%~nf | findstr /C:"d$" >nul
    if not errorlevel 1 (
        echo Copying Debug DLL: %%f
        copy /Y "%%f" "windows\runner\Debug\"
        copy /Y "%%f" "build\windows\x64\runner\Debug\"
        copy /Y "%%f" "build\windows\runner\Debug\"
    ) else (
        echo Skipping Release DLL: %%f
    )
)

REM Copy model files
echo Copying model files...
if exist "models\face_detection_yunet_2023mar.onnx" (
    copy /Y "models\face_detection_yunet_2023mar.onnx" "windows\runner\Debug\data\models"
    copy /Y "models\face_detection_yunet_2023mar.onnx" "build\windows\x64\runner\Debug\data\models"
    copy /Y "models\face_detection_yunet_2023mar.onnx" "build\windows\runner\Debug\data\models"
) else (
    echo Warning: Model file 'models\face_detection_yunet_2023mar.onnx' not found!
)

REM Verify DLLs existence
echo Checking main OpenCV DLL in runner directory:
if exist "windows\runner\Debug\opencv_world4110d.dll" (
    echo - opencv_world4110d.dll found in runner directory
) else (
    echo - opencv_world4110d.dll NOT found in runner directory - copy likely failed!
)

echo Checking main OpenCV DLL in Flutter build directory (x64):
if exist "build\windows\x64\runner\Debug\opencv_world4110d.dll" (
    echo - opencv_world4110d.dll found in Flutter build directory (x64)
) else (
    echo - opencv_world4110d.dll NOT found in Flutter build directory (x64) - copy likely failed!
)

echo Checking main OpenCV DLL in Flutter build directory:
if exist "build\windows\runner\Debug\opencv_world4110d.dll" (
    echo - opencv_world4110d.dll found in Flutter build directory
) else (
    echo - opencv_world4110d.dll NOT found in Flutter build directory - copy likely failed!
)

echo Debug build and file copying completed!
goto :end

:error
echo Build process failed with errors.
exit /b 1

:end 