import 'package:flutter/material.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';

class WarningInfoTip extends StatelessWidget {
  final String message;

  const WarningInfoTip({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: 760.p,
        minHeight: 60.p,
      ),
      padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [0.0, 1.0],
          colors: [
            Color.fromRGBO(255, 160, 51, 0.7),
            Color.fromRGBO(255, 235, 206, 0.7),
          ],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(50.p),
          topRight: Radius.circular(50.p),
          bottomRight: Radius.circular(50.p),
          bottomLeft: Radius.zero,
        ),
        border: Border.all(
          color: const Color.fromRGBO(255, 160, 51, 0.7),
          width: 1.p,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,  // 水平居中
        children: [
          Flexible(
            child: Row(
              mainAxisSize: MainAxisSize.min,  // 根据内容确定宽度
              children: [
                Image.asset(
                  AssetUtil.fullPath('warning_icon.png'),
                  width: 32.p,
                  height: 30.p,
                ),
                SizedBox(width: 14.p),
                Flexible(
                  child: Text(
                    message,
                    style: TextStyle(
                      fontSize: 24.p,
                      color: const Color(0xFF222222),
                      fontWeight: FontWeight.w500,
                      height: 1,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}