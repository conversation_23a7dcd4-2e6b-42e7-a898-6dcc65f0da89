import 'package:flutter/material.dart';

class GradientCapsule extends StatelessWidget {
  final double? width;
  final double? height;
  final Widget? child;
  final EdgeInsetsGeometry padding;

  const GradientCapsule({
    Key? key,
    this.width, // 可选宽度，不设置则自适应父组件
    this.height, // 可选高度，不设置则自适应父组件
    this.child,
    this.padding = EdgeInsets.zero,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width, // 不指定则自适应
      height: height, // 不指定则自适应
      padding: padding,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment(-0.8, -0.8), // 近似125度角
          end: Alignment(0.8, 0.8),
          colors: [
            Color(0xFFFEC6D9), // #FEC6D9
            Color(0xFFFFB2B0), // #FFB2B0
            Color(0xFFFFD6A4), // #FFD6A4
          ],
          stops: [0.0, 0.47, 1.0],
        ),
        borderRadius: BorderRadius.circular(50), // 创建胶囊形状
      ),
      child: child,
    );
  }
}