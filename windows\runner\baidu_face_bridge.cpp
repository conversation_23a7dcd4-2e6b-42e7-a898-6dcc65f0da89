﻿#include "baidu_face_bridge.h"
#include <iostream>
#include <string>
#include <memory>
#include <opencv2/opencv.hpp>
#include <sstream>
#include <chrono>

// 百度SDK头文件
#include "baidu_face_api.h"
#include "struct_info.h"

// 全局API实例
static BaiduFaceApi* g_baidu_api = nullptr;
static bool g_sdk_initialized = false;

// SDK路径管理相关全局变量
static std::string g_current_sdk_path = "";
static bool g_dll_path_set = false;

// 辅助函数：复制字符串到新内存
char* copy_string(const std::string& str) {
    char* result = new char[str.length() + 1];
    strcpy_s(result, str.length() + 1, str.c_str());
    return result;
}

// 辅助函数：将字节数组转换为cv::Mat
cv::Mat bytes_to_mat(unsigned char* image_data, int image_length, int width, int height) {
    try {
        if (image_data == nullptr || image_length <= 0) {
            std::cout << "Invalid image data: data=" << (void*)image_data << ", length=" << image_length << std::endl;
            return cv::Mat();
        }
        
        // 尝试解码JPEG数据
        std::vector<uchar> data(image_data, image_data + image_length);
        cv::Mat img = cv::imdecode(data, cv::IMREAD_COLOR);
        
        if (!img.empty()) {
            std::cout << "Successfully decoded JPEG: " << img.cols << "x" << img.rows << std::endl;
            return img;
        }
        
        // 如果解码失败，尝试直接构造Mat（假设是BGR格式）
        if (width > 0 && height > 0 && image_length >= width * height * 3) {
            cv::Mat mat(height, width, CV_8UC3, image_data);
            std::cout << "Created Mat from raw data: " << width << "x" << height << std::endl;
            return mat.clone();
        }
        
        std::cout << "Failed to create Mat from image data" << std::endl;
        return cv::Mat();
    } catch (const std::exception& e) {
        std::cerr << "Error converting bytes to Mat: " << e.what() << std::endl;
        return cv::Mat();
    }
}

// 生成JSON格式的识别结果
std::string create_recognition_result(const std::string& user_id, const std::string& group_id, float score, const std::string& user_info = "") {
    std::stringstream ss;
    ss << "{";
    ss << "\"error_code\": 0,";
    ss << "\"error_msg\": \"SUCCESS\",";
    ss << "\"result\": {";
    ss << "\"user_list\": [{";
    ss << "\"user_id\": \"" << user_id << "\",";
    ss << "\"group_id\": \"" << group_id << "\",";
    ss << "\"score\": " << score;
    if (!user_info.empty()) {
        ss << ",\"user_info\": \"" << user_info << "\"";
    }
    ss << "}]";
    ss << "}";
    ss << "}";
    return ss.str();
}

// 生成错误结果JSON
std::string create_error_result(int error_code, const std::string& error_msg) {
    std::stringstream ss;
    ss << "{";
    ss << "\"error_code\": " << error_code << ",";
    ss << "\"error_msg\": \"" << error_msg << "\"";
    ss << "}";
    return ss.str();
}

// 初始化百度SDK
extern "C" __declspec(dllexport) int baidu_sdk_init(const char* model_path) {
    try {
        std::cout << "=== Starting Baidu Face Recognition SDK Initialization ===" << std::endl;
        
        // 使用全量DLL复制方案，不再需要动态设置DLL路径
        std::cout << "Using full DLL copy deployment, no dynamic path setting needed" << std::endl;
        
        // 如果已经初始化，先清理
        if (g_sdk_initialized && g_baidu_api != nullptr) {
            std::cout << "SDK already initialized, cleaning up old instance..." << std::endl;
            delete g_baidu_api;
            g_baidu_api = nullptr;
            g_sdk_initialized = false;
        }
        
        // 第一步：创建BaiduFaceApi实例
        std::cout << "Step 1: Creating BaiduFaceApi instance..." << std::endl;
        try {
            g_baidu_api = new BaiduFaceApi();
            if (g_baidu_api == nullptr) {
                std::cerr << "Error: Failed to create BaiduFaceApi instance" << std::endl;
                return -1;
            }
            std::cout << "OK: BaiduFaceApi instance created successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Exception creating BaiduFaceApi: " << e.what() << std::endl;
            return -1;
        } catch (...) {
            std::cerr << "Unknown exception creating BaiduFaceApi" << std::endl;
            return -1;
        }
        
        // 第二步：获取设备ID和版本信息（用于验证SDK是否正常工作）
        std::cout << "Step 2: Getting device information..." << std::endl;
        try {
            std::cout << "Step 2.1: Getting device ID..." << std::endl;
            std::string device_id;
            g_baidu_api->get_device_id(device_id);
            if (!device_id.empty()) {
                std::cout << "OK: Device ID acquired successfully (length: " << device_id.length() << ")" << std::endl;
            } else {
                std::cout << "Warning: Device ID is empty" << std::endl;
            }
            
            std::cout << "Step 2.2: Getting SDK version..." << std::endl;
            std::string version;
            g_baidu_api->sdk_version(version);
            std::cout << "Step 2.3: SDK version call completed" << std::endl;
            if (!version.empty()) {
                std::cout << "OK: SDK Version: " << version << std::endl;
            } else {
                std::cout << "Warning: SDK version is empty" << std::endl;
            }
            std::cout << "Step 2.4: Device information step completed" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Warning: Exception during device information acquisition: " << e.what() << std::endl;
        } catch (...) {
            std::cout << "Warning: Unknown exception during device information acquisition" << std::endl;
        }
        
        std::cout << "Step 2.5: About to proceed to Step 3..." << std::endl;
        
        // 第三步：初始化SDK
        std::cout << "Step 3: Calling sdk_init..." << std::endl;
        int init_result = -999; // 初始化为一个明显的错误值
        try {
            if (model_path != nullptr) {
                std::cout << "Step 3.1: Using specified model path: " << model_path << std::endl;
            } else {
                std::cout << "Step 3.1: Using default model path (models folder in current directory)" << std::endl;
            }
            
            std::cout << "Step 3.2: About to call g_baidu_api->sdk_init()..." << std::endl;
            std::cout.flush(); // 强制刷新输出缓冲区
            
            init_result = g_baidu_api->sdk_init(model_path);
            
            std::cout << "Step 3.3: sdk_init call completed" << std::endl;
            std::cout << "Step 3.4: SDK initialization result code: " << init_result << std::endl;
        
            if (init_result != 0) {
                std::cout << "Step 3.5: SDK initialization failed, error code: " << init_result << std::endl;
                std::cout << "Possible causes:" << std::endl;
                std::cout << "1. Model files missing or corrupted" << std::endl;
                std::cout << "2. License file problems" << std::endl;
                std::cout << "3. Missing dependency DLL files" << std::endl;
                
                // 不立即退出，继续尝试其他步骤
                std::cout << "Step 3.6: Continuing initialization despite SDK init failure..." << std::endl;
            } else {
                std::cout << "Step 3.5: SDK initialization successful" << std::endl;
            }
            std::cout << "Step 3.7: SDK init step completed" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Step 3.ERROR: Exception during SDK initialization: " << e.what() << std::endl;
            std::cout << "Step 3.ERROR: Init result was: " << init_result << std::endl;
            std::cout << "Continuing initialization despite exception..." << std::endl;
        } catch (...) {
            std::cout << "Step 3.ERROR: Unknown exception during SDK initialization" << std::endl;
            std::cout << "Step 3.ERROR: Init result was: " << init_result << std::endl;
            std::cout << "Continuing initialization despite exception..." << std::endl;
        }
        
        std::cout << "Step 3.8: About to proceed to Step 4..." << std::endl;
        
        // 第四步：检查授权状态
        std::cout << "Step 4: Checking authorization status..." << std::endl;
        try {
            std::cout << "Step 4.1: About to call is_auth()..." << std::endl;
            bool auth_status = g_baidu_api->is_auth();
            std::cout << "Step 4.2: is_auth() call completed" << std::endl;
            std::cout << "Step 4.3: Authorization status: " << (auth_status ? "Authorized" : "Unauthorized") << std::endl;
            
            if (!auth_status) {
                std::cout << "Step 4.4: SDK not authorized, some features may be limited" << std::endl;
                std::cout << "Please check license files in license directory" << std::endl;
            } else {
                std::cout << "Step 4.4: SDK properly authorized" << std::endl;
            }
            std::cout << "Step 4.5: Authorization check completed" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Step 4.ERROR: Cannot check authorization status: " << e.what() << std::endl;
        } catch (...) {
            std::cout << "Step 4.ERROR: Unknown exception checking authorization" << std::endl;
        }
        
        std::cout << "Step 4.6: About to proceed to Step 5..." << std::endl;
        
        // 第五步：加载人脸数据库
        std::cout << "Step 5: Loading face database..." << std::endl;
        try {
            std::cout << "Step 5.1: About to call load_db_face()..." << std::endl;
            bool load_result = g_baidu_api->load_db_face();
            std::cout << "Step 5.2: load_db_face() call completed" << std::endl;
            std::cout << "Step 5.3: Face database loading: " << (load_result ? "Success" : "Failed") << std::endl;
            
            if (load_result) {
                std::cout << "Step 5.4: About to get face count..." << std::endl;
                // 检查数据库中的人脸数量
                int face_count = g_baidu_api->db_face_count();
                std::cout << "Step 5.5: Face count retrieved successfully" << std::endl;
                std::cout << "Step 5.6: Current face count in database: " << face_count << std::endl;
            } else {
                std::cout << "Step 5.4: Face database loading failed, might be first run or database file missing" << std::endl;
            }
            std::cout << "Step 5.7: Database loading step completed" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Step 5.ERROR: Face database loading exception: " << e.what() << std::endl;
        } catch (...) {
            std::cout << "Step 5.ERROR: Unknown exception loading face database" << std::endl;
        }
        
        std::cout << "Step 5.8: About to proceed to Step 6..." << std::endl;
        
        // 设置初始化标志
        std::cout << "Step 6: Setting initialization flag to true" << std::endl;
        g_sdk_initialized = true;
        std::cout << "Step 6.1: Initialization flag set successfully" << std::endl;
        std::cout << "=== Baidu Face Recognition SDK Initialization Complete ===" << std::endl;
        std::cout << "Step 6.2: Initialization successful, returning 0" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal SDK initialization exception: " << e.what() << std::endl;
        
        // 清理
        if (g_baidu_api != nullptr) {
            try {
                delete g_baidu_api;
            } catch (...) {
                std::cerr << "Exception during cleanup" << std::endl;
            }
            g_baidu_api = nullptr;
        }
        g_sdk_initialized = false;
        
        return -1;
    } catch (...) {
        std::cerr << "Fatal unknown exception during SDK initialization" << std::endl;
        
        // 清理
        if (g_baidu_api != nullptr) {
            try {
                delete g_baidu_api;
            } catch (...) {
                std::cerr << "Exception during cleanup" << std::endl;
            }
            g_baidu_api = nullptr;
        }
        g_sdk_initialized = false;
        
        return -2;
    }
}

// 检查授权状态
extern "C" __declspec(dllexport) int baidu_is_auth() {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::cout << "SDK not initialized, cannot check authorization" << std::endl;
            return 0;
        }
        
        bool auth_result = g_baidu_api->is_auth();
        std::cout << "Authorization check result: " << (auth_result ? "authorized" : "unauthorized") << std::endl;
        return auth_result ? 1 : 0;
    } catch (const std::exception& e) {
        std::cerr << "Authorization check exception: " << e.what() << std::endl;
        return 0;
    }
}

// SDK详细状态信息已在文件末尾定义

// 加载人脸数据库
extern "C" __declspec(dllexport) int baidu_load_db_face() {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::cout << "SDK not initialized, cannot load face database" << std::endl;
            return 0;
        }
        
        bool result = g_baidu_api->load_db_face();
        std::cout << "Face database load result: " << (result ? "success" : "failed") << std::endl;
        return result ? 1 : 0;
    } catch (const std::exception& e) {
        std::cerr << "Load face database exception: " << e.what() << std::endl;
        return 0;
    }
}

// 获取人脸数量
extern "C" __declspec(dllexport) int baidu_get_face_count() {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::cout << "SDK not initialized, cannot get face count" << std::endl;
            return 0;
        }
        
        int count = g_baidu_api->db_face_count();
        std::cout << "Face count in database: " << count << std::endl;
        return count;
    } catch (const std::exception& e) {
        std::cerr << "Get face count exception: " << e.what() << std::endl;
        return 0;
    }
}

// 人脸检测
extern "C" __declspec(dllexport) char* baidu_detect_faces(unsigned char* image_data, int image_length, int width, int height) {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::string result = create_error_result(-1, "SDK not initialized");
            return copy_string(result);
        }
        
        cv::Mat img = bytes_to_mat(image_data, image_length, width, height);
        if (img.empty()) {
            std::string result = create_error_result(-2, "Invalid image data");
            return copy_string(result);
        }
        
        std::vector<FaceBox> faces;
        int detect_result = g_baidu_api->detect(faces, &img);
        
        std::stringstream ss;
        ss << "{";
        ss << "\"error_code\": " << detect_result << ",";
        if (detect_result == 0) {
            ss << "\"error_msg\": \"SUCCESS\",";
            ss << "\"faces\": [";
            for (size_t i = 0; i < faces.size(); i++) {
                if (i > 0) ss << ",";
                ss << "{";
                ss << "\"confidence\": " << faces[i].score << ",";
                ss << "\"location\": {";
                ss << "\"left\": " << (faces[i].center_x - faces[i].width / 2) << ",";
                ss << "\"top\": " << (faces[i].center_y - faces[i].height / 2) << ",";
                ss << "\"width\": " << faces[i].width << ",";
                ss << "\"height\": " << faces[i].height;
                ss << "}";
                ss << "}";
            }
            ss << "]";
        } else {
            ss << "\"error_msg\": \"Face detection failed\"";
        }
        ss << "}";
        
        return copy_string(ss.str());
    } catch (const std::exception& e) {
        std::string result = create_error_result(-99, std::string("Face detection exception: ") + e.what());
        return copy_string(result);
    }
}

// 1:N人脸识别
extern "C" __declspec(dllexport) char* baidu_identify_with_all(unsigned char* image_data, int image_length, int width, int height) {
    try {
        std::cout << "========================================" << std::endl;
        std::cout << "=== Starting 1:N Face Recognition Debug ===" << std::endl;
        std::cout << "========================================" << std::endl;
        
        // Step 1: Parameter validation and logging
        std::cout << "[Step 1] Parameter Validation:" << std::endl;
        std::cout << "  - Image data pointer: " << (void*)image_data << std::endl;
        std::cout << "  - Image data size: " << image_length << " bytes" << std::endl;
        std::cout << "  - Specified width: " << width << std::endl;
        std::cout << "  - Specified height: " << height << std::endl;
        
        if (image_data == nullptr) {
            std::cout << "ERROR: Image data pointer is null" << std::endl;
            std::string result = create_error_result(-1, "Image data is null");
            return copy_string(result);
        }
        
        if (image_length <= 0) {
            std::cout << "ERROR: Invalid image data length: " << image_length << std::endl;
            std::string result = create_error_result(-2, "Invalid image data length");
            return copy_string(result);
        }
        
        std::cout << "OK: Parameter validation passed" << std::endl;
        
        // Step 2: SDK status check
        std::cout << "\n[Step 2] SDK Status Check:" << std::endl;
        std::cout << "  - SDK initialization status: " << (g_sdk_initialized ? "Initialized" : "Not initialized") << std::endl;
        std::cout << "  - API instance status: " << (g_baidu_api != nullptr ? "Exists" : "Not exists") << std::endl;
        
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::cout << "ERROR: SDK not initialized" << std::endl;
            std::string result = create_error_result(-1, "SDK not initialized");
            return copy_string(result);
        }
        
        // Step 3: Authorization check
        std::cout << "\n[Step 3] Authorization Check:" << std::endl;
        bool auth_status = false;
        try {
            auth_status = g_baidu_api->is_auth();
            std::cout << "  - Authorization status: " << (auth_status ? "Authorized" : "Unauthorized") << std::endl;
        } catch (const std::exception& e) {
            std::cout << "  - Authorization check exception: " << e.what() << std::endl;
        }
        
        if (!auth_status) {
            std::cout << "WARNING: SDK not authorized, but continuing with recognition attempt" << std::endl;
            // Note: Not returning error here as recognition might work in some cases without authorization
        }
        
        // Step 4: Face database status check
        std::cout << "\n[Step 4] Face Database Status:" << std::endl;
        int face_count = 0;
        try {
            face_count = g_baidu_api->db_face_count();
            std::cout << "  - Face count in database: " << face_count << std::endl;
        } catch (const std::exception& e) {
            std::cout << "  - Face count retrieval exception: " << e.what() << std::endl;
        }
        
        if (face_count == 0) {
            std::cout << "WARNING: Face database is empty, cannot perform 1:N recognition" << std::endl;
            std::string result = create_error_result(0, "No faces in database for matching");
            return copy_string(result);
        }
        
        // Step 5: Image data analysis
        std::cout << "\n[Step 5] Image Data Analysis:" << std::endl;
        
        // Check if data is JPEG format
        bool is_jpeg = (image_length >= 2 && 
                       image_data[0] == 0xFF && 
                       image_data[1] == 0xD8);
        std::cout << "  - Data format detection: " << (is_jpeg ? "JPEG" : "Raw data") << std::endl;
        
        if (is_jpeg) {
            // Output JPEG header info
            std::cout << "  - JPEG header: 0x" << std::hex << (int)image_data[0] << (int)image_data[1] << std::dec << std::endl;
            
            // Check JPEG end marker
            bool has_eoi = (image_length >= 2 && 
                           image_data[image_length-2] == 0xFF && 
                           image_data[image_length-1] == 0xD9);
            std::cout << "  - JPEG integrity: " << (has_eoi ? "Complete" : "Possibly incomplete") << std::endl;
        }
        
        // Step 6: Image conversion
        std::cout << "\n[Step 6] Converting to OpenCV Mat:" << std::endl;
        cv::Mat img = bytes_to_mat(image_data, image_length, width, height);
        
        if (img.empty()) {
            std::cout << "ERROR: Image conversion failed" << std::endl;
            std::cout << "  - Possible causes: corrupted image data, unsupported format, or decode failure" << std::endl;
            std::string result = create_error_result(-3, "Failed to convert image data to Mat");
            return copy_string(result);
        }
        
        std::cout << "OK: Image conversion successful:" << std::endl;
        std::cout << "  - Converted size: " << img.cols << "x" << img.rows << std::endl;
        std::cout << "  - Channels: " << img.channels() << std::endl;
        std::cout << "  - Data type: " << img.type() << " (CV_8UC3=" << CV_8UC3 << ")" << std::endl;
        std::cout << "  - Data continuity: " << (img.isContinuous() ? "Continuous" : "Not continuous") << std::endl;
        
        // Step 7: Execute face recognition
        std::cout << "\n[Step 7] Executing Baidu SDK Face Recognition:" << std::endl;
        std::cout << "  - Calling identify_with_all interface..." << std::endl;
        
        std::string identify_result;
        
        try {
            // Record recognition start time
            auto start_time = std::chrono::high_resolution_clock::now();
            
            g_baidu_api->identify_with_all(identify_result, &img);
            
            // Record recognition end time
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            std::cout << "  - Recognition time: " << duration.count() << " milliseconds" << std::endl;
            std::cout << "  - Result length: " << identify_result.length() << " characters" << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "ERROR: Recognition process exception: " << e.what() << std::endl;
            std::string result = create_error_result(-4, std::string("Recognition exception: ") + e.what());
            return copy_string(result);
        } catch (...) {
            std::cout << "ERROR: Unknown recognition process exception" << std::endl;
            std::string result = create_error_result(-5, "Unknown recognition exception");
            return copy_string(result);
        }
        
        // Step 8: Result analysis
        std::cout << "\n[Step 8] Recognition Result Analysis:" << std::endl;
        
        if (identify_result.empty()) {
            std::cout << "WARNING: Recognition result is empty" << std::endl;
            std::cout << "  - Possible causes:" << std::endl;
            std::cout << "    1. No face detected in image" << std::endl;
            std::cout << "    2. Face detected but quality insufficient" << std::endl;
            std::cout << "    3. Face detected but no match found in database" << std::endl;
            std::cout << "    4. SDK internal processing failed" << std::endl;
            
            std::string result = create_error_result(0, "No matching face found - empty result");
            return copy_string(result);
        }
        
        std::cout << "OK: Recognition result obtained:" << std::endl;
        std::cout << "  - Result content: " << identify_result << std::endl;
        
        // Try to parse JSON result for format validation
        try {
            // Can add JSON parsing validation here to ensure result format is correct
            if (identify_result.find("error_code") != std::string::npos) {
                std::cout << "  - Result type: Standard API response format" << std::endl;
            } else {
                std::cout << "  - Result type: Custom format" << std::endl;
            }
        } catch (...) {
            std::cout << "  - Result format parsing exception, but returning original result" << std::endl;
        }
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "=== 1:N Face Recognition Debug Complete ===" << std::endl;
        std::cout << "========================================" << std::endl;
        
        return copy_string(identify_result);
        
    } catch (const std::exception& e) {
        std::cerr << "\nERROR: 1:N recognition function exception: " << e.what() << std::endl;
        std::string result = create_error_result(-99, std::string("Function exception: ") + e.what());
        return copy_string(result);
    } catch (...) {
        std::cerr << "\nERROR: 1:N recognition function unknown exception" << std::endl;
        std::string result = create_error_result(-100, "Unknown function exception");
        return copy_string(result);
    }
}

// 用户注册
extern "C" __declspec(dllexport) char* baidu_user_add(unsigned char* image_data, int image_length, int width, int height, 
                                                     const char* user_id, const char* group_id, const char* user_info) {
    try {
        std::cout << "Starting user registration: " << user_id << " to group: " << group_id << std::endl;
        
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::string result = create_error_result(-1, "SDK not initialized");
            return copy_string(result);
        }
        
        cv::Mat img = bytes_to_mat(image_data, image_length, width, height);
        if (img.empty()) {
            std::string result = create_error_result(-2, "Invalid image data");
            return copy_string(result);
        }
        
        std::string add_result;
        g_baidu_api->user_add(add_result, &img, user_id, group_id, user_info);
        
        std::cout << "User registration result: " << add_result << std::endl;
        
        if (add_result.empty()) {
            std::string result = create_error_result(-3, "Registration failed");
            return copy_string(result);
        }
        
        return copy_string(add_result);
    } catch (const std::exception& e) {
        std::cerr << "User registration exception: " << e.what() << std::endl;
        std::string result = create_error_result(-99, std::string("Registration exception: ") + e.what());
        return copy_string(result);
    }
}

// 用户删除
extern "C" __declspec(dllexport) char* baidu_user_delete(const char* user_id, const char* group_id) {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::string result = create_error_result(-1, "SDK not initialized");
            return copy_string(result);
        }
        
        std::string delete_result;
        g_baidu_api->user_delete(delete_result, user_id, group_id);
        
        if (delete_result.empty()) {
            std::string result = create_error_result(-3, "Delete failed");
            return copy_string(result);
        }
        
        return copy_string(delete_result);
    } catch (const std::exception& e) {
        std::cerr << "User delete exception: " << e.what() << std::endl;
        std::string result = create_error_result(-99, std::string("Delete exception: ") + e.what());
        return copy_string(result);
    }
}

// 组添加
extern "C" __declspec(dllexport) char* baidu_group_add(const char* group_id) {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::string result = create_error_result(-1, "SDK not initialized");
            return copy_string(result);
        }
        
        std::string add_result;
        g_baidu_api->group_add(add_result, group_id);
        
        if (add_result.empty()) {
            std::string result = create_error_result(-3, "Group add failed");
            return copy_string(result);
        }
        
        return copy_string(add_result);
    } catch (const std::exception& e) {
        std::cerr << "Group add exception: " << e.what() << std::endl;
        std::string result = create_error_result(-99, std::string("Group add exception: ") + e.what());
        return copy_string(result);
    }
}

// 组删除
extern "C" __declspec(dllexport) char* baidu_group_delete(const char* group_id) {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::string result = create_error_result(-1, "SDK not initialized");
            return copy_string(result);
        }
        
        std::string delete_result;
        g_baidu_api->group_delete(delete_result, group_id);
        
        if (delete_result.empty()) {
            std::string result = create_error_result(-3, "Group delete failed");
            return copy_string(result);
        }
        
        return copy_string(delete_result);
    } catch (const std::exception& e) {
        std::cerr << "Group delete exception: " << e.what() << std::endl;
        std::string result = create_error_result(-99, std::string("Group delete exception: ") + e.what());
        return copy_string(result);
    }
}

// 获取用户列表
extern "C" __declspec(dllexport) char* baidu_get_user_list(const char* group_id) {
    try {
        if (g_baidu_api == nullptr || !g_sdk_initialized) {
            std::string result = create_error_result(-1, "SDK not initialized");
            return copy_string(result);
        }
        
        std::string user_list;
        g_baidu_api->get_user_list(user_list, group_id);
        
        if (user_list.empty()) {
            std::string result = create_error_result(-3, "Get user list failed");
            return copy_string(result);
        }
        
        return copy_string(user_list);
    } catch (const std::exception& e) {
        std::cerr << "Get user list exception: " << e.what() << std::endl;
        std::string result = create_error_result(-99, std::string("Get user list exception: ") + e.what());
        return copy_string(result);
    }
}

// 释放字符串内存
extern "C" __declspec(dllexport) void baidu_free_string(char* str) {
    if (str != nullptr) {
        delete[] str;
    }
}

// 清理SDK
extern "C" __declspec(dllexport) int baidu_cleanup() {
    try {
        std::cout << "Cleaning up Baidu SDK..." << std::endl;
        
        if (g_baidu_api != nullptr) {
            delete g_baidu_api;
            g_baidu_api = nullptr;
        }
        
        g_sdk_initialized = false;
        g_current_sdk_path = "";
        g_dll_path_set = false;
        std::cout << "SDK cleanup completed" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "SDK cleanup exception: " << e.what() << std::endl;
        return -1;
    }
}

// ========== SDK路径管理相关函数 ==========

// 设置SDK路径（简化版本 - 使用全量DLL复制，仅用于兼容性）
extern "C" __declspec(dllexport) int baidu_set_sdk_path(const char* sdk_path) {
    try {
        std::cout << "=== Setting SDK Path (Compatibility Mode) ===" << std::endl;
        
        if (sdk_path == nullptr || strlen(sdk_path) == 0) {
            std::cout << "Warning: SDK path is null or empty, using default" << std::endl;
            g_current_sdk_path = "D:\\FaceOfflineSdk";
        } else {
            g_current_sdk_path = std::string(sdk_path);
        }
        
        g_dll_path_set = true;
        
        std::cout << "SDK path recorded: " << g_current_sdk_path << std::endl;
        std::cout << "Note: Using full DLL copy deployment, no dynamic path setting needed" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in baidu_set_sdk_path: " << e.what() << std::endl;
        return -10;
    } catch (...) {
        std::cerr << "Unknown exception in baidu_set_sdk_path" << std::endl;
        return -11;
    }
}

// 验证SDK路径
extern "C" __declspec(dllexport) char* baidu_validate_sdk_path(const char* sdk_path) {
    try {
        std::cout << "=== Validating SDK Path ===" << std::endl;
        
        if (sdk_path == nullptr || strlen(sdk_path) == 0) {
            return copy_string("{\"valid\": false, \"error\": \"SDK路径为空\"}");
        }
        
        std::string pathStr(sdk_path);
        std::cout << "Validating SDK path: " << pathStr << std::endl;
        
        std::ostringstream result;
        result << "{";
        
        // 检查主目录
        DWORD attributes = GetFileAttributesA(pathStr.c_str());
        if (attributes == INVALID_FILE_ATTRIBUTES) {
            result << "\"valid\": false, \"error\": \"SDK目录不存在: " << pathStr << "\"}";
            return copy_string(result.str());
        }
        
        // 检查x64目录
        std::string x64Path = pathStr + "\\x64";
        DWORD x64Attributes = GetFileAttributesA(x64Path.c_str());
        if (x64Attributes == INVALID_FILE_ATTRIBUTES) {
            result << "\"valid\": false, \"error\": \"x64目录不存在: " << x64Path << "\"}";
            return copy_string(result.str());
        }
        
        // 检查models目录
        std::string modelsPath = pathStr + "\\models";
        DWORD modelsAttributes = GetFileAttributesA(modelsPath.c_str());
        if (modelsAttributes == INVALID_FILE_ATTRIBUTES) {
            result << "\"valid\": false, \"error\": \"models目录不存在: " << modelsPath << "\"}";
            return copy_string(result.str());
        }
        
        // 检查关键DLL文件
        std::vector<std::string> requiredDlls = {
            "BaiduFaceApi.dll",
            "face_sdk.dll", 
            "opencv_world320.dll",
            "paddle_inference.dll",
            "bd_license.dll"
        };
        
        std::vector<std::string> missingDlls;
        for (const auto& dll : requiredDlls) {
            std::string dllPath = x64Path + "\\" + dll;
            DWORD dllAttributes = GetFileAttributesA(dllPath.c_str());
            if (dllAttributes == INVALID_FILE_ATTRIBUTES) {
                missingDlls.push_back(dll);
            }
        }
        
        if (!missingDlls.empty()) {
            result << "\"valid\": false, \"error\": \"缺少关键DLL文件: ";
            for (size_t i = 0; i < missingDlls.size(); ++i) {
                if (i > 0) result << ", ";
                result << missingDlls[i];
            }
            result << "\"}";
            return copy_string(result.str());
        }
        
        // 验证通过
        result << "\"valid\": true, \"message\": \"SDK路径验证成功\", \"version\": \"8.4.0\"}";
        
        std::cout << "SDK path validation successful" << std::endl;
        return copy_string(result.str());
        
    } catch (const std::exception& e) {
        std::ostringstream errorResult;
        errorResult << "{\"valid\": false, \"error\": \"Validation error: " << e.what() << "\"}";
        return copy_string(errorResult.str());
    } catch (...) {
        return copy_string("{\"valid\": false, \"error\": \"Unknown validation error\"}");
    }
}

// 获取SDK状态
extern "C" __declspec(dllexport) char* baidu_get_sdk_status() {
    try {
        std::ostringstream status;
        status << "{";
        status << "\"initialized\": " << (g_sdk_initialized ? "true" : "false") << ",";
        status << "\"dll_path_set\": " << (g_dll_path_set ? "true" : "false") << ",";
        status << "\"current_sdk_path\": \"" << g_current_sdk_path << "\",";
        status << "\"api_instance\": " << (g_baidu_api != nullptr ? "true" : "false");
        
        if (g_sdk_initialized && g_baidu_api != nullptr) {
            try {
                bool isAuth = g_baidu_api->is_auth();
                status << ",\"authorized\": " << (isAuth ? "true" : "false");
                
                if (isAuth) {
                    int faceCount = g_baidu_api->db_face_count();
                    status << ",\"face_count\": " << faceCount;
                }
            } catch (...) {
                status << ",\"authorized\": false, \"auth_check_error\": true";
            }
        }
        
        status << "}";
        
        return copy_string(status.str());
        
    } catch (const std::exception& e) {
        std::ostringstream errorResult;
        errorResult << "{\"error\": \"Status error: " << e.what() << "\"}";
        return copy_string(errorResult.str());
    } catch (...) {
        return copy_string("{\"error\": \"Unknown status error\"}");
    }
} 