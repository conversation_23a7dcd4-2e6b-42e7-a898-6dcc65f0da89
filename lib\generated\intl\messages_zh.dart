// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "AccountPassword": MessageLookupByLibrary.simpleMessage("账号密码"),
        "IncorrectPassword": MessageLookupByLibrary.simpleMessage("读者密码错误"),
        "Predeposit": MessageLookupByLibrary.simpleMessage("预存(元)"),
        "ReaderCardVerification": MessageLookupByLibrary.simpleMessage("读者证认证"),
        "ScanAuth": MessageLookupByLibrary.simpleMessage("二维码认证"),
        "Sending": MessageLookupByLibrary.simpleMessage("正在发送..."),
        "accountBalance": MessageLookupByLibrary.simpleMessage("账户余额"),
        "accountInput": MessageLookupByLibrary.simpleMessage("请输入读者证号/身份证号"),
        "accountLogin": MessageLookupByLibrary.simpleMessage("账号登录"),
        "accountRequired": MessageLookupByLibrary.simpleMessage("账号不能为空"),
        "addressInput": MessageLookupByLibrary.simpleMessage("住址"),
        "agreement": MessageLookupByLibrary.simpleMessage("我已阅读并同意以上内容"),
        "alipay": MessageLookupByLibrary.simpleMessage("支付宝"),
        "alipayCreditRegister": MessageLookupByLibrary.simpleMessage("阿里信用分注册"),
        "alipay_login": MessageLookupByLibrary.simpleMessage("支付宝登录"),
        "alipay_scan_guide":
            MessageLookupByLibrary.simpleMessage("打开手机端支付宝应用 点击左上角扫一扫图标"),
        "applyCard": MessageLookupByLibrary.simpleMessage("办证"),
        "at": MessageLookupByLibrary.simpleMessage("放于"),
        "auth_fail_advice":
            MessageLookupByLibrary.simpleMessage("若授权失败，请查看页面失败原因或咨询芝麻信用客服"),
        "availableInLibrary": MessageLookupByLibrary.simpleMessage("在馆"),
        "available_borrow_count": MessageLookupByLibrary.simpleMessage("可借总数"),
        "available_for_borrow": MessageLookupByLibrary.simpleMessage("可借阅"),
        "barcode": MessageLookupByLibrary.simpleMessage("条码"),
        "barcodeError": MessageLookupByLibrary.simpleMessage("条码解析失败"),
        "barcode_number": MessageLookupByLibrary.simpleMessage("条码号"),
        "birthDateInput": MessageLookupByLibrary.simpleMessage("出生日期"),
        "bookRecommendations": MessageLookupByLibrary.simpleMessage("猜你喜欢的书籍"),
        "bookRecommendationsTitle":
            MessageLookupByLibrary.simpleMessage("好书推荐"),
        "bookTitle": MessageLookupByLibrary.simpleMessage("书名"),
        "bookTitleShort": MessageLookupByLibrary.simpleMessage("题名"),
        "book_overdue": MessageLookupByLibrary.simpleMessage("该单册已逾期"),
        "booksToReturn": MessageLookupByLibrary.simpleMessage("待还书"),
        "borrowBook": MessageLookupByLibrary.simpleMessage("借书"),
        "borrowFailure": MessageLookupByLibrary.simpleMessage("借书失败"),
        "borrowRequestFailed": MessageLookupByLibrary.simpleMessage("请求借书失败"),
        "borrowSuccess": MessageLookupByLibrary.simpleMessage("借书成功"),
        "borrow_fail_request":
            MessageLookupByLibrary.simpleMessage("借书失败，请求借书失败"),
        "borrow_fail_rewrite_security":
            MessageLookupByLibrary.simpleMessage("借书失败，改写防盗位失败"),
        "borrow_success": MessageLookupByLibrary.simpleMessage("成功借阅文献"),
        "borrow_with_alipay_credit": MessageLookupByLibrary.simpleMessage(
            "芝麻分只要550分以上，打开支付宝扫一扫屏幕中的二维码即可开启借书"),
        "borrowableCopies": MessageLookupByLibrary.simpleMessage("可借阅(本)"),
        "borrowed": MessageLookupByLibrary.simpleMessage("已借阅"),
        "borrowedCopies": MessageLookupByLibrary.simpleMessage("已借阅(本)"),
        "borrowing": MessageLookupByLibrary.simpleMessage("在借"),
        "borrowingInProgress": MessageLookupByLibrary.simpleMessage("借书中..."),
        "businessTableFail": MessageLookupByLibrary.simpleMessage("处理业务系统表失败"),
        "callNumber": MessageLookupByLibrary.simpleMessage("索书号"),
        "captchaInput": MessageLookupByLibrary.simpleMessage("请输入验证码"),
        "captchaRequired": MessageLookupByLibrary.simpleMessage("验证码不能为空"),
        "cardApplicationSuccess":
            MessageLookupByLibrary.simpleMessage("您已经成功申请了读者证"),
        "cardCreationFail": MessageLookupByLibrary.simpleMessage("办证失败"),
        "cardCreationSuccess": MessageLookupByLibrary.simpleMessage("办证成功"),
        "cardIdExhausted": MessageLookupByLibrary.simpleMessage("当前读者证号已使用完毕"),
        "cardIdExhaustedContact":
            MessageLookupByLibrary.simpleMessage("当前读者证号已使用完毕，请联系图书馆工作人员"),
        "cardIdFail": MessageLookupByLibrary.simpleMessage("读者证号获取失败"),
        "cardIdFailContact":
            MessageLookupByLibrary.simpleMessage("读者证号获取失败，请联系图书馆工作人员"),
        "cardIssuanceError": MessageLookupByLibrary.simpleMessage("发卡错误"),
        "cardLefts": MessageLookupByLibrary.simpleMessage("卡剩余"),
        "cardLogin": MessageLookupByLibrary.simpleMessage("读者证登录"),
        "cardMachineError":
            MessageLookupByLibrary.simpleMessage("发卡机错误，请联系图书馆工作人员"),
        "cardRequestFail": MessageLookupByLibrary.simpleMessage("请求办证失败"),
        "card_not_exist": MessageLookupByLibrary.simpleMessage("读者证不存在"),
        "cert_number": MessageLookupByLibrary.simpleMessage("证号"),
        "characters": MessageLookupByLibrary.simpleMessage("字符"),
        "citizenCardLogin": MessageLookupByLibrary.simpleMessage("市民卡登录"),
        "clear": MessageLookupByLibrary.simpleMessage("清空"),
        "collectReaderCard":
            MessageLookupByLibrary.simpleMessage("请从出卡口取走您的读者证"),
        "confirm": MessageLookupByLibrary.simpleMessage("确认"),
        "confirmBorrow": MessageLookupByLibrary.simpleMessage("确认借书"),
        "confirmButton": MessageLookupByLibrary.simpleMessage("确定"),
        "confirm_return": MessageLookupByLibrary.simpleMessage("确认还书"),
        "continueBorrowing": MessageLookupByLibrary.simpleMessage("继续借书"),
        "continueButton": MessageLookupByLibrary.simpleMessage("继续"),
        "continue_returning": MessageLookupByLibrary.simpleMessage("继续还书"),
        "copy": MessageLookupByLibrary.simpleMessage("本"),
        "currencyUnit": MessageLookupByLibrary.simpleMessage("元"),
        "currentDeposit": MessageLookupByLibrary.simpleMessage("本次存入"),
        "current_borrow_count": MessageLookupByLibrary.simpleMessage("现借总数"),
        "debt": MessageLookupByLibrary.simpleMessage("欠款"),
        "debt_amount": MessageLookupByLibrary.simpleMessage("欠款(元)"),
        "defaultPassword": MessageLookupByLibrary.simpleMessage("默认密码"),
        "deposit": MessageLookupByLibrary.simpleMessage("押金"),
        "depositInput": MessageLookupByLibrary.simpleMessage("押金"),
        "depositNotSupported": MessageLookupByLibrary.simpleMessage("暂不支持收取押金"),
        "detail_info": MessageLookupByLibrary.simpleMessage("详情信息"),
        "details": MessageLookupByLibrary.simpleMessage("详情"),
        "dueDate": MessageLookupByLibrary.simpleMessage("应还时间"),
        "duplicateRegistration":
            MessageLookupByLibrary.simpleMessage("您已经办理过读者证，请勿重复办证"),
        "eSSCRegistration": MessageLookupByLibrary.simpleMessage("电子社保卡注册"),
        "eSocialSecurityCardLogin":
            MessageLookupByLibrary.simpleMessage("电子社保卡登录"),
        "electronicSocialSecurityCard":
            MessageLookupByLibrary.simpleMessage("电子社保卡"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("为了更好的为您服务，请输入您的手机号码"),
        "ethnicityInput": MessageLookupByLibrary.simpleMessage("民族"),
        "exit": MessageLookupByLibrary.simpleMessage("退出"),
        "expirationInput": MessageLookupByLibrary.simpleMessage("有效期限"),
        "externalCardRegistration":
            MessageLookupByLibrary.simpleMessage("外部刷卡（办证）"),
        "faceMatchRegister": MessageLookupByLibrary.simpleMessage("人脸匹配注册"),
        "face_recognition": MessageLookupByLibrary.simpleMessage("人脸识别"),
        "face_recognition_login":
            MessageLookupByLibrary.simpleMessage("人脸识别登录"),
        "face_to_camera": MessageLookupByLibrary.simpleMessage("请您正视前方摄像头"),
        "failure": MessageLookupByLibrary.simpleMessage("失败"),
        "free_listening": MessageLookupByLibrary.simpleMessage("免费收听啦"),
        "genderInput": MessageLookupByLibrary.simpleMessage("性别"),
        "homePage": MessageLookupByLibrary.simpleMessage("首页"),
        "huiwen_qr_code": MessageLookupByLibrary.simpleMessage("汇文二维码"),
        "idCardLogin": MessageLookupByLibrary.simpleMessage("身份证登录"),
        "idNumberInput": MessageLookupByLibrary.simpleMessage("身份证号"),
        "identityCard": MessageLookupByLibrary.simpleMessage("身份证"),
        "ineligibleAge":
            MessageLookupByLibrary.simpleMessage("对不起，您不符合此读者证办理年龄条件"),
        "ineligibleArea":
            MessageLookupByLibrary.simpleMessage("对不起，您不符合此读者证办理区域条件"),
        "inputPhoneNumber": MessageLookupByLibrary.simpleMessage("请输入手机号"),
        "insertMoney": MessageLookupByLibrary.simpleMessage("请您将纸币放入收钞口"),
        "invalidCredentials":
            MessageLookupByLibrary.simpleMessage("读者证不存在或密码错误"),
        "issuingAuthInput": MessageLookupByLibrary.simpleMessage("签发机关"),
        "loginButton": MessageLookupByLibrary.simpleMessage("登录"),
        "login_method": MessageLookupByLibrary.simpleMessage("登录方式"),
        "manualInput": MessageLookupByLibrary.simpleMessage("手动输入"),
        "manualInputLogin": MessageLookupByLibrary.simpleMessage("手动输入登录"),
        "manualInputRegistration":
            MessageLookupByLibrary.simpleMessage("手动输入注册"),
        "moneyAcceptanceInfo": MessageLookupByLibrary.simpleMessage(
            "本机仅接收10元、20元、50元、100元人民币，不设找零，如有余额将存入预付款账户"),
        "nameInput": MessageLookupByLibrary.simpleMessage("姓名"),
        "no": MessageLookupByLibrary.simpleMessage("否"),
        "noBooksAvailable": MessageLookupByLibrary.simpleMessage("无可借书籍"),
        "noCardTip":
            MessageLookupByLibrary.simpleMessage("卡已用完，暂时不能办理读者证，请联系工作人员！"),
        "noDeposit": MessageLookupByLibrary.simpleMessage("免押金"),
        "no_books_to_return": MessageLookupByLibrary.simpleMessage("无待归还书籍"),
        "note": MessageLookupByLibrary.simpleMessage("备注"),
        "overdue": MessageLookupByLibrary.simpleMessage("超期"),
        "overdue_count": MessageLookupByLibrary.simpleMessage("超期文献数"),
        "passwordFormatError": MessageLookupByLibrary.simpleMessage("密码格式不正确"),
        "passwordInput": MessageLookupByLibrary.simpleMessage("请输入密码"),
        "passwordLengthError": MessageLookupByLibrary.simpleMessage("密码长度不正确"),
        "paymentCodeConfig": MessageLookupByLibrary.simpleMessage("付款码支付配置"),
        "phoneError": MessageLookupByLibrary.simpleMessage("手机号错误"),
        "phoneForService":
            MessageLookupByLibrary.simpleMessage("为了更好的为您服务，请输入您的电话号码"),
        "phoneInput": MessageLookupByLibrary.simpleMessage("请输入您的电话号码"),
        "phoneOrCaptchaRequired":
            MessageLookupByLibrary.simpleMessage("手机号码或者验证码不能为空"),
        "place": MessageLookupByLibrary.simpleMessage("请将"),
        "placeBookOnSensor":
            MessageLookupByLibrary.simpleMessage("请您将待还文献放于桌面感应区"),
        "placeCardOnSensor":
            MessageLookupByLibrary.simpleMessage("请将读者证放于桌面感应区"),
        "placeCardOnSensorForCard":
            MessageLookupByLibrary.simpleMessage("请将一卡通放于感应区"),
        "placeDocument": MessageLookupByLibrary.simpleMessage("请将文献错开平放于桌面感应区"),
        "placeIdCardOnSensor":
            MessageLookupByLibrary.simpleMessage("请将身份证放于桌面感应区"),
        "prepaid_balance": MessageLookupByLibrary.simpleMessage("预存费余额"),
        "printReceipt": MessageLookupByLibrary.simpleMessage("您是否打印凭条"),
        "processingCard":
            MessageLookupByLibrary.simpleMessage("正在为您办理读者证，\n请别走开..."),
        "qrCode": MessageLookupByLibrary.simpleMessage("二维码"),
        "qrCodeLogin": MessageLookupByLibrary.simpleMessage("二维码登录"),
        "qrCodeRegistration": MessageLookupByLibrary.simpleMessage("展示二维码注册"),
        "query": MessageLookupByLibrary.simpleMessage("查询"),
        "quit": MessageLookupByLibrary.simpleMessage("退出"),
        "readerCardAppliedSuccessfully":
            MessageLookupByLibrary.simpleMessage("您已成功申请了读者证"),
        "readerCardID": MessageLookupByLibrary.simpleMessage("读者证号"),
        "readerFailure": MessageLookupByLibrary.simpleMessage("打开阅读器失败"),
        "readerNotFound": MessageLookupByLibrary.simpleMessage("未配置阅读器"),
        "readerTableFail": MessageLookupByLibrary.simpleMessage("处理读者表失败"),
        "reader_card": MessageLookupByLibrary.simpleMessage("读者证"),
        "reader_closing": MessageLookupByLibrary.simpleMessage("正在关闭阅读器，请稍后再试"),
        "regenerate": MessageLookupByLibrary.simpleMessage("重新生成"),
        "register": MessageLookupByLibrary.simpleMessage("注册"),
        "remainingTime": MessageLookupByLibrary.simpleMessage("剩余\n时间"),
        "renew": MessageLookupByLibrary.simpleMessage("续借"),
        "renewBook": MessageLookupByLibrary.simpleMessage("续借"),
        "renewInProgress": MessageLookupByLibrary.simpleMessage("续借中..."),
        "renewOrQuery": MessageLookupByLibrary.simpleMessage("续借/查询"),
        "renew_fail": MessageLookupByLibrary.simpleMessage("续借失败"),
        "renew_success": MessageLookupByLibrary.simpleMessage("续借成功"),
        "requestingInfo": MessageLookupByLibrary.simpleMessage("正在请求读者信息"),
        "resend": MessageLookupByLibrary.simpleMessage("秒后重发"),
        "returnBook": MessageLookupByLibrary.simpleMessage("还书"),
        "returnDate": MessageLookupByLibrary.simpleMessage("归还日期"),
        "returnFailure": MessageLookupByLibrary.simpleMessage("还书失败，请求还书失败"),
        "returnFailureRewrite":
            MessageLookupByLibrary.simpleMessage("还书失败，改写防盗位失败"),
        "returnSuccess": MessageLookupByLibrary.simpleMessage("成功归还文献"),
        "return_date": MessageLookupByLibrary.simpleMessage("归还日期"),
        "return_date_recorded": MessageLookupByLibrary.simpleMessage("还书日期"),
        "return_fail": MessageLookupByLibrary.simpleMessage("还书失败"),
        "return_success": MessageLookupByLibrary.simpleMessage("还书成功"),
        "returningInProgress":
            MessageLookupByLibrary.simpleMessage("正在还书，请勿重复操作"),
        "returningStatus": MessageLookupByLibrary.simpleMessage("归还中..."),
        "scanESCBarcode": MessageLookupByLibrary.simpleMessage("请扫描您的电子社保卡二维码"),
        "scanPayRegister": MessageLookupByLibrary.simpleMessage("扫码支付注册"),
        "scanToListen": MessageLookupByLibrary.simpleMessage("扫码收听"),
        "scanYour": MessageLookupByLibrary.simpleMessage("请扫描您的"),
        "scan_e_social_card":
            MessageLookupByLibrary.simpleMessage("请扫描您的电子社保卡"),
        "scan_huiwen_qr": MessageLookupByLibrary.simpleMessage("请扫描您的汇文二维码"),
        "scan_wechat_qr": MessageLookupByLibrary.simpleMessage("请扫描您的微信二维码"),
        "secondGenIDCardRegistration":
            MessageLookupByLibrary.simpleMessage("二代身份证注册"),
        "selectCardType":
            MessageLookupByLibrary.simpleMessage("请选择下面按钮\n申办对应类型的图书证"),
        "selectOperationType": MessageLookupByLibrary.simpleMessage("请选择操作类型"),
        "selectReaderCardType":
            MessageLookupByLibrary.simpleMessage("请选择需要申办的读者证类型"),
        "selectRegistrationMethod":
            MessageLookupByLibrary.simpleMessage("请选择注册方式"),
        "select_all": MessageLookupByLibrary.simpleMessage("全选"),
        "selfServiceMachine": MessageLookupByLibrary.simpleMessage("自助借还书机"),
        "sendVerificationCode": MessageLookupByLibrary.simpleMessage("发送验证码"),
        "sensingArea": MessageLookupByLibrary.simpleMessage("感应区"),
        "sequenceNumber": MessageLookupByLibrary.simpleMessage("序号"),
        "serviceNotice": MessageLookupByLibrary.simpleMessage("自助办证服务须知"),
        "shanghaiShenShenCodeRegister":
            MessageLookupByLibrary.simpleMessage("上海随申码注册"),
        "socialSecurityCard": MessageLookupByLibrary.simpleMessage("社保卡"),
        "socialSecurityCardLogin":
            MessageLookupByLibrary.simpleMessage("社保卡登录"),
        "spaceLogin": MessageLookupByLibrary.simpleMessage("登录"),
        "sscRegistration": MessageLookupByLibrary.simpleMessage("社保卡注册"),
        "status": MessageLookupByLibrary.simpleMessage("状态"),
        "submittingData": MessageLookupByLibrary.simpleMessage("正在提交数据，请稍等…"),
        "success": MessageLookupByLibrary.simpleMessage("成功"),
        "takeCardAndBooks":
            MessageLookupByLibrary.simpleMessage("请取走您的读者证和所借图书,并请核对打印凭据！"),
        "take_away_card_books":
            MessageLookupByLibrary.simpleMessage("请取走您的读者证和所借图书"),
        "tencentEPassRegister": MessageLookupByLibrary.simpleMessage("腾讯E证通注册"),
        "unsupportedCardType":
            MessageLookupByLibrary.simpleMessage("暂不支持此办证类型"),
        "unsupportedCardTypeContact":
            MessageLookupByLibrary.simpleMessage("暂不支持此办证类型，请联系图书馆工作人员"),
        "unsupportedType": MessageLookupByLibrary.simpleMessage("暂不支持此类型"),
        "validCaptcha": MessageLookupByLibrary.simpleMessage("请输入正确验证码"),
        "validPhoneInput": MessageLookupByLibrary.simpleMessage("请输入正确手机号码"),
        "view_details": MessageLookupByLibrary.simpleMessage("点击详情"),
        "waitingResult": MessageLookupByLibrary.simpleMessage("等待结果"),
        "wechatLogin": MessageLookupByLibrary.simpleMessage("微信登陆"),
        "wechatScanAuth": MessageLookupByLibrary.simpleMessage("微信扫码认证"),
        "wechat_qr_code": MessageLookupByLibrary.simpleMessage("微信二维码"),
        "wechat_scan": MessageLookupByLibrary.simpleMessage("微信扫一扫"),
        "yes": MessageLookupByLibrary.simpleMessage("是"),
        "yourPhoneNumber": MessageLookupByLibrary.simpleMessage("您的电话号码")
      };
}
