import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:a3g/core/utils/window_util.dart';

import '../../../../core/widgets/auth_item.dart';
import '../../../../core/widgets/handle_item.dart';
import '../../view_models/admin_view_model.dart';

class HandleOptions extends StatelessWidget {
  final List<HandleModel?> allTypes;
  const HandleOptions({super.key, required this.allTypes});

  @override
  Widget build(BuildContext context) {
    return Consumer<AdminViewModel>(builder: (context, viewModel, _) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 80.p, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: ListView.builder(
                  physics: const BouncingScrollPhysics(),
                  itemCount: allTypes.length,
                  itemBuilder: (context, index) {
                    final item = allTypes[index];
                    if(item?.type == 'clearBookBox'){
                      item!.map?['bookBoxCount'] = viewModel.bookBoxCount ;
                    }
                    if (item != null && item.type != null) {
                      return HandleItem(
                        title: item.title ?? '',
                        type: item.type ?? '',
                        iconPath: item.icon ?? '',
                        map: item.map ?? {},
                        onTap: (){
                          allTypes[index]?.onTap?.call();
                        },
                      );
                    } else {
                      return const SizedBox();
                    }
                  },
                )),
          ],
        ),
      );
    });
  }
}