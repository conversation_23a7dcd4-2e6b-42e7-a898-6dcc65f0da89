import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:typed_data';

class EventChannelDemo extends StatefulWidget {
  const EventChannelDemo({Key? key}) : super(key: key);

  @override
  State<EventChannelDemo> createState() => _EventChannelDemoState();
}

class _EventChannelDemoState extends State<EventChannelDemo> {
  static const EventChannel _eventChannel = EventChannel('face_detection_events');
  static const MethodChannel _methodChannel = MethodChannel('face_detection');
  
  StreamSubscription? _streamSubscription;
  List<String> _messages = [];
  bool _isListening = false;
  int _receivedNumber = 0;
  Uint8List? _receivedImage;
  double _lastConfidence = 0.0;

  @override
  void initState() {
    super.initState();
    _initEventChannel();
  }

  void _initEventChannel() {
    try {
      // 监听事件流
      _streamSubscription = _eventChannel
          .receiveBroadcastStream()
          .listen(
            (dynamic event) {
              print('EventChannel收到数据: $event');
              setState(() {
                if (event is Map) {
                  // Handle face detection result
                  final imageData = event['imageData'] as List<dynamic>?;
                  final confidence = event['confidence'] as double?;
                  final timestamp = event['timestamp'] as int?;
                  
                  if (imageData != null && confidence != null) {
                    _receivedImage = Uint8List.fromList(imageData.cast<int>());
                    _lastConfidence = confidence;
                    _messages.insert(0, '收到人脸图片: 置信度${confidence.toStringAsFixed(2)} (${DateTime.now()})');
                  } else {
                    _receivedNumber = event['number'] ?? 0;
                    _messages.insert(0, '收到数字: $_receivedNumber (${DateTime.now()})');
                  }
                } else {
                  _receivedNumber = event ?? 0;
                  _messages.insert(0, '收到数据: $event (${DateTime.now()})');
                }
                
                // 保持消息列表不超过20条
                if (_messages.length > 20) {
                  _messages = _messages.take(20).toList();
                }
              });
            },
            onError: (dynamic error) {
              print('EventChannel错误: $error');
              setState(() {
                _messages.insert(0, '错误: $error (${DateTime.now()})');
              });
            },
            onDone: () {
              print('EventChannel流结束');
              setState(() {
                _messages.insert(0, '流结束 (${DateTime.now()})');
                _isListening = false;
              });
            },
          );
      
      setState(() {
        _isListening = true;
        _messages.insert(0, 'EventChannel初始化成功 (${DateTime.now()})');
      });
    } catch (e) {
      print('EventChannel初始化错误: $e');
      setState(() {
        _messages.insert(0, '初始化错误: $e (${DateTime.now()})');
      });
    }
  }

  void _startCppCallback() async {
    try {
      // 如果已经在监听，先停止
      if (_isListening) {
        _stopCppCallback();
        await Future.delayed(Duration(milliseconds: 200)); // 等待停止完成
      }
      
      // 重新初始化EventChannel监听
      _initEventChannel();
      
      await _methodChannel.invokeMethod('startEventStream');
      setState(() {
        _messages.insert(0, '已通知C++开始发送事件 (${DateTime.now()})');
      });
    } catch (e) {
      print('启动C++回调错误: $e');
      setState(() {
        _messages.insert(0, '启动错误: $e (${DateTime.now()})');
      });
    }
  }

  void _stopCppCallback() async {
    try {
      await _methodChannel.invokeMethod('stopEventStream');
      
      // 取消监听
      if (_streamSubscription != null) {
        await _streamSubscription!.cancel();
        _streamSubscription = null;
      }
      
      setState(() {
        _isListening = false;
        _messages.insert(0, '已通知C++停止发送事件 (${DateTime.now()})');
      });
    } catch (e) {
      print('停止C++回调错误: $e');
      setState(() {
        _messages.insert(0, '停止错误: $e (${DateTime.now()})');
      });
    }
  }

  void _clearMessages() {
    setState(() {
      _messages.clear();
    });
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('EventChannel测试'),
        backgroundColor: Colors.blue,
        elevation: 2,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态显示卡片
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('监听状态:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: _isListening ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _isListening ? '正在监听' : '未监听',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('最新数字:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                        Text(
                          '$_receivedNumber',
                          style: const TextStyle(fontSize: 24, color: Colors.blue, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    
                    // 显示人脸检测结果
                    if (_receivedImage != null) ...[
                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 8),
                      const Text('检测到的人脸:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text('置信度: ${_lastConfidence.toStringAsFixed(3)}', 
                           style: TextStyle(fontSize: 14, color: Colors.green.shade700)),
                      const SizedBox(height: 8),
                      Container(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.memory(
                            _receivedImage!,
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) {
                              return const Center(
                                child: Text('无法显示图片', style: TextStyle(color: Colors.red)),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 控制按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _startCppCallback,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('开始C++回调'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _stopCppCallback,
                    icon: const Icon(Icons.stop),
                    label: const Text('停止C++回调'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton.icon(
              onPressed: _clearMessages,
              icon: const Icon(Icons.clear),
              label: const Text('清空消息'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 消息日志
            const Text('消息日志:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _messages.isEmpty
                    ? const Center(
                        child: Text(
                          '暂无消息\n点击"开始C++回调"开始测试',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey, fontSize: 16),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _messages.length,
                        itemBuilder: (context, index) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: index.isEven ? Colors.grey[50] : Colors.white,
                              border: const Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
                            ),
                            child: Text(
                              _messages[index],
                              style: const TextStyle(fontSize: 14),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 