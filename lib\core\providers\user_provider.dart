import 'package:flutter/material.dart';
import 'package:sea_socket/sea_socket.dart';

import '../models/slot.dart';

class UserProvider extends ChangeNotifier {
  Sip2PatronInfoData? _currentUser;
  SlotBookOperate? _waitSlotBookOperate; // 待操作的格口

  Sip2PatronInfoData? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser?.isSuccess ?? false;
  SlotBookOperate? get waitSlotBookOperate => _waitSlotBookOperate;

  // 更新用户信息
  void updateUserInfo(Sip2PatronInfoData userData) {
    _currentUser = userData;
    notifyListeners();
  }

  set updateWaitSlotBookOperate(SlotBookOperate slotBookOperate) {
    _waitSlotBookOperate = slotBookOperate;
  }


  // 清除用户数据
  void clearUserData() {
    _waitSlotBookOperate = null;
    if(_currentUser == null){
      return;
    }
    _currentUser = null;
    notifyListeners();
  }
}