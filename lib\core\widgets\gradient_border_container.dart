import 'dart:math' show pi;
import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';

class GradientBGContainer extends StatelessWidget {
  final Widget? child;
  final double width;
  final double height;

  const GradientBGContainer({
    Key? key,
    this.child,
    this.width = 920,
    this.height = 1437,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color.fromRGBO(255, 255, 255, 0.7), // White 70%
            Color.fromRGBO(218, 234, 255, 0.6), // Light Blue 60%
          ],
          stops: [0.0, 1.0],
          // CSS angle 136deg. Flutter default is 90deg (left-to-right).
          // Rotate clockwise by (136 - 90) = 46 degrees.
          transform: GradientRotation(46 * pi / 180),
        ),
        borderRadius: BorderRadius.circular(48.p),
        // Note: CSS border-image linear-gradient(132deg, white, white)
        // simplifies to a solid white border.
        border: Border.all(
          width: 2.p,
          color: Colors.white,
        ),
      ),
      // Clip the child to follow the parent's border radius
      child: ClipRRect(
        borderRadius: BorderRadius.circular(48 - 2), // Adjust radius for border width
        child: child,
      ),
    );
  }
}