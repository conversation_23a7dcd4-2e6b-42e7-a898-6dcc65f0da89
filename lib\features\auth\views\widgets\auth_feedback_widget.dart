import 'dart:async';
import 'package:flutter/material.dart';
import '../../../../core/utils/window_util.dart';
import '../../../../shared/utils/asset_util.dart';
import '../../models/auth_result.dart';
import 'gradient_capsule.dart';
import 'identity_card.dart';

/// 认证反馈组件状态枚举
enum AuthFeedbackState {
  idle,          // 空闲状态，不显示任何内容
  authenticating, // 认证中
  success,       // 认证成功
  failure,       // 认证失败
  passMode,      // 通行模式
}

/// 认证反馈组件
/// 负责显示认证过程中的各种状态反馈
class AuthFeedbackWidget extends StatefulWidget {
  final AuthFeedbackState state;
  final String? userName;
  final String? userId;
  final String? errorMessage;
  final AuthMethod? authMethod;
  final DateTime? authTime;
  final VoidCallback? onStateChanged;
  final Duration detailDisplayDuration; // 详细信息显示时长
  
  const AuthFeedbackWidget({
    Key? key,
    this.state = AuthFeedbackState.idle,
    this.userName,
    this.userId,
    this.errorMessage,
    this.authMethod,
    this.authTime,
    this.onStateChanged,
    this.detailDisplayDuration = const Duration(seconds: 3),
  }) : super(key: key);

  @override
  State<AuthFeedbackWidget> createState() => _AuthFeedbackWidgetState();
}

class _AuthFeedbackWidgetState extends State<AuthFeedbackWidget> 
    with SingleTickerProviderStateMixin {
  
  Timer? _transitionTimer;
  AuthFeedbackState _currentState = AuthFeedbackState.idle;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _currentState = widget.state;
    
    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // 开始动画
    _animationController.forward();
    
    // 如果初始状态需要处理（比如成功状态需要启动定时器）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.state == AuthFeedbackState.success) {
        _handleStateChange(widget.state);
      }
    });
  }
  
  @override
  void didUpdateWidget(AuthFeedbackWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.state != widget.state) {
      _handleStateChange(widget.state);
    }
  }
  
  /// 处理状态变化
  void _handleStateChange(AuthFeedbackState newState) {
    print('=== AuthFeedbackWidget 状态变化 ===');
    print('新状态: $newState');
    print('用户信息: ${widget.userName} (${widget.userId})');
    
    _transitionTimer?.cancel();
    
    setState(() {
      _currentState = newState;
    });
    
    // 重新启动动画
    _animationController.reset();
    _animationController.forward();
    
    // 如果是成功状态，启动自动切换到通行模式的定时器
    if (newState == AuthFeedbackState.success) {
      print('启动定时器，${widget.detailDisplayDuration.inSeconds}秒后切换到通行模式');
      _transitionTimer = Timer(widget.detailDisplayDuration, () {
        if (mounted && _currentState == AuthFeedbackState.success) {
          print('定时器触发，切换到通行模式');
          setState(() {
            _currentState = AuthFeedbackState.passMode;
          });
          
          // 通知外部状态变化
          widget.onStateChanged?.call();
          
          // 重新启动动画
          _animationController.reset();
          _animationController.forward();
        }
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildContent(),
    );
  }
  
  /// 构建内容
  Widget _buildContent() {
    print('=== AuthFeedbackWidget 构建内容 ===');
    print('当前状态: $_currentState');
    
    switch (_currentState) {
      case AuthFeedbackState.idle:
        print('返回空组件 (idle)');
        return const SizedBox.shrink();
        
      case AuthFeedbackState.authenticating:
        print('返回认证中组件');
        return _buildAuthenticatingWidget();
        
      case AuthFeedbackState.success:
        print('返回成功组件 (详细信息)');
        return _buildSuccessWidget();
        
      case AuthFeedbackState.failure:
        print('返回失败组件');
        return _buildFailureWidget();
        
      case AuthFeedbackState.passMode:
        print('返回通行模式组件');
        return _buildPassModeWidget();
    }
  }
  
  /// 构建认证中组件
  Widget _buildAuthenticatingWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4F83FC)),
          ),
          SizedBox(height: 16),
          Text(
            '正在认证中...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建成功组件（详细信息模式） - 使用原有设计
  Widget _buildSuccessWidget() {
    print('=== 构建成功组件（详细信息） ===');
    print('用户: ${widget.userName}, ID: ${widget.userId}');
    
    return _buildAccess();
  }
  
  /// 构建成功认证卡片 - 完全按照原有_buildAccess设计
  Widget _buildAccess() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.p),
      child: Column(
        children: [
          // 认证方式指示（如果有多认证信息）
          if (widget.authMethod != null) ...[
            Container(
              margin: EdgeInsets.only(bottom: 10.p),
              padding: EdgeInsets.symmetric(horizontal: 15.p, vertical: 5.p),
              decoration: BoxDecoration(
                color: Color(0xFF4F83FC),
                borderRadius: BorderRadius.circular(15.p),
              ),
              child: Text(
                '${_getAuthMethodDisplayName(widget.authMethod!)}认证成功',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20.p,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
          // 身份卡片 - 使用原来的IdentityCard组件
          IdentityCard(
            isPassMode: false,
            name: widget.userName ?? "认证用户",
            idNumber: widget.userId ?? "未知ID", 
            time: widget.authTime != null 
                ? _formatTime(widget.authTime!) 
                : _getCurrentTimeString(),
            avatarUrl: AssetUtil.fullPath('avatar_placeholder'),
          ),
        ],
      ),
    );
  }
  
  /// 构建失败组件 - 参考成功样式，分离认证方式和错误信息显示
  Widget _buildFailureWidget() {
    print('=== 构建失败组件 ===');
    print('认证方式: ${widget.authMethod}');
    print('错误信息: ${widget.errorMessage}');
    
    // 使用传入的错误消息，如果没有则使用默认消息
    String errorMessage = widget.errorMessage ?? '认证失败，请重试';
    
    // 清理错误消息，移除可能包含的认证方式前缀
    if (widget.authMethod != null) {
      String authMethodName = _getAuthMethodDisplayName(widget.authMethod!);
      // 移除各种可能的认证方式前缀
      errorMessage = errorMessage
          .replaceFirst('${authMethodName}认证失败：', '')
          .replaceFirst('${authMethodName}认证失败:', '')
          .replaceFirst('${authMethodName}认证失败', '')
          .replaceFirst('${authMethodName}：', '')
          .replaceFirst('${authMethodName}:', '')
          .trim();
      
      // 如果清理后为空，使用默认消息
      if (errorMessage.isEmpty) {
        errorMessage = '认证失败，请重试';
      }
    }
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.p),
      child: Column(
        children: [
          // 认证方式指示（如果有认证方式信息）- 使用红色底色
          if (widget.authMethod != null) ...[
            Container(
              margin: EdgeInsets.only(bottom: 10.p),
              padding: EdgeInsets.symmetric(horizontal: 15.p, vertical: 5.p),
              decoration: BoxDecoration(
                color: Color(0xFFE13841), // 红色底色表示失败
                borderRadius: BorderRadius.circular(15.p),
              ),
              child: Text(
                '${_getAuthMethodDisplayName(widget.authMethod!)}认证失败',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20.p,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
          // 错误信息提示框 - 只显示错误信息，不包含认证方式
          _buildWarring(errorMessage),
        ],
      ),
    );
  }
  
  /// 构建警告信息组件 - 支持多行错误信息并动态调整高度
  Widget _buildWarring(String? errorMessage) {
    final message = errorMessage ?? '认证失败，请重试';
    
    // 根据文本长度估算需要的行数和高度
    int estimatedLines = (message.length / 15).ceil(); // 大约每15个字符一行
    estimatedLines = estimatedLines.clamp(1, 5); // 最少1行，最多5行
    
    double dynamicHeight = 60.p + (estimatedLines - 1) * 25.p; // 基础高度60，每增加一行+25
    dynamicHeight = dynamicHeight.clamp(80.p, 150.p); // 限制在合理范围内
    
    return GradientCapsule(
      width: 600.p,
      height: dynamicHeight, // 动态高度
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 12.p), // 调整内边距
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 警告图标
            Image(
              width: 32.p,
              height: 28.p,
              image: AssetImage(AssetUtil.fullPath('serious_warning'))
            ),
            SizedBox(width: 16.p), // 减少图标和文字间距
            // 错误信息文本 - 使用Expanded确保充分利用空间
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: const Color(0xFFE13841),
                  fontSize: 26.p, // 进一步减小字体适应更多内容
                  fontWeight: FontWeight.w500,
                  height: 1.3, // 适中的行高
                ),
                textAlign: TextAlign.left,
                maxLines: 5, // 支持最多5行
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 12.p), // 右侧留白
          ],
        ),
      ),
    );
  }
  
  /// 构建通行模式组件 - 使用原有IdentityCard设计
  Widget _buildPassModeWidget() {
    print('=== 构建通行模式组件 ===');
    print('用户: ${widget.userName}, ID: ${widget.userId}');
    print('认证方式: ${widget.authMethod}');
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.p),
      child: IdentityCard(
        isPassMode: true, // 通行模式
        name: widget.userName ?? "认证用户",
        idNumber: widget.userId ?? "未知ID", 
        time: widget.authTime != null 
            ? _formatTime(widget.authTime!) 
            : _getCurrentTimeString(),
        avatarUrl: AssetUtil.fullPath('avatar_placeholder'),
      ),
    );
  }
  
  /// 获取认证方式显示名称  
  String _getAuthMethodDisplayName(AuthMethod? method) {
    if (method == null) return '未知方式';
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.idCard:
        return '身份证';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.socialSecurityCard:
        return '社保卡';
      case AuthMethod.citizenCard:
        return '市民卡';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡';
      case AuthMethod.wechatQRCode:
        return '微信二维码';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码';
      case AuthMethod.aliCreditQRCode:
        return '支付宝信用二维码';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码';
      case AuthMethod.shangHaiQRCode:
        return '上海二维码';
      case AuthMethod.keyboardInput:
        return '键盘输入';
      case AuthMethod.tencentTCard:
        return '腾讯T卡';
      case AuthMethod.imiAuth:
        return 'IMI认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信或支付宝';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用二维码';
      case AuthMethod.jieYueBao:
        return '借阅宝';
    }
  }
  
  /// 格式化时间
  String _formatTime(DateTime time) {
    return "${time.year}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')} "
           "${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:${time.second.toString().padLeft(2, '0')}";
  }
  
  /// 获取当前时间字符串
  String _getCurrentTimeString() {
    final now = DateTime.now();
    return _formatTime(now);
  }
  
  @override
  void dispose() {
    _transitionTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }
}

/// 认证警告组件
class AuthWarningWidget extends StatelessWidget {
  final String message;
  final AuthMethod? authMethod;
  
  const AuthWarningWidget({
    Key? key,
    required this.message,
    this.authMethod,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GradientCapsule(
      width: 480.p,
      height: 70.p,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 警告图标
          Image(
            width: 34.p,
            height: 30.p,
            image: AssetImage(AssetUtil.fullPath('serious_warning')),
          ),
          SizedBox(width: 20.p),
          // 错误信息文本
          Flexible(
            child:               Text(
                message,
                style: TextStyle(
                  color: const Color(0xFFE13841),
                  fontSize: 32.p,
                  fontWeight: FontWeight.w500,
                ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

/// 身份卡片模式枚举
enum AuthIdentityCardMode {
  detail, // 详细信息模式
  pass,   // 通行模式
}

/// 认证身份卡片组件
class AuthIdentityCard extends StatelessWidget {
  final AuthIdentityCardMode mode;
  final String? name;
  final String? idNumber;
  final String? time;
  final String? avatarUrl;
  final AuthMethod? authMethod;

  const AuthIdentityCard({
    Key? key,
    this.mode = AuthIdentityCardMode.detail,
    this.name,
    this.idNumber,
    this.time,
    this.avatarUrl,
    this.authMethod,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment(-0.8, -0.8), // 近似125度角
          end: Alignment(0.8, 0.8),
          colors: [
            Color(0xFFBBFBFF), // #BBFBFF
            Color(0xFFAFDAFF), // #AFDAFF
            Color(0xFFC3CAFF), // #C3CAFF
          ],
          stops: [0.0, 0.47, 1.0],
        ),
        borderRadius: BorderRadius.circular(40.p),
      ),
      padding: EdgeInsets.all(30.p),
      child: Row(
        children: [
          // 左侧头像
          _buildAvatar(),
          SizedBox(width: 30.p),
          
          // 右侧信息
          Expanded(
            child: mode == AuthIdentityCardMode.detail
                ? _buildDetailContent()
                : _buildPassContent(),
          ),
        ],
      ),
    );
  }
  
  /// 构建头像
  Widget _buildAvatar() {
    return Container(
      width: 130.p,
      height: 130.p,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(20.p),
      ),
      child: avatarUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(20.p),
              child: Image.asset(
                avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar();
                },
              ),
            )
          : _buildDefaultAvatar(),
    );
  }
  
  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Center(
      child: Icon(
        Icons.person,
        size: 80.p,
        color: Colors.white,
      ),
    );
  }

  /// 构建详细信息内容
  Widget _buildDetailContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
                 // 认证方式标识（如果有）
         if (authMethod != null) ...[
           Container(
             margin: EdgeInsets.only(bottom: 10.p),
             padding: EdgeInsets.symmetric(horizontal: 12.p, vertical: 4.p),
             decoration: BoxDecoration(
               color: Color(0xFF4F83FC),
               borderRadius: BorderRadius.circular(12.p),
             ),
             child: Text(
               '${_getAuthMethodDisplayName(authMethod!)}认证',
               style: TextStyle(
                 color: Colors.white,
                 fontSize: 18.p,
                 fontWeight: FontWeight.bold,
               ),
             ),
           ),
         ],
         
         // 用户信息
         _buildInfoRow("姓名", name ?? "认证用户"),
         SizedBox(height: 10.p),
         _buildInfoRow("证号", idNumber ?? "未知ID"),
         SizedBox(height: 16.p),
         _buildInfoRow("时间", time ?? "", isTime: true),
      ],
    );
  }
  
  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {bool isTime = false}) {
    return Row(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 32.p,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF12215F),
            height: 32/30,
          ),
        ),
        SizedBox(width: isTime ? 20.p : 30.p),
        Expanded(
          child:             Text(
              value,
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12215F),
                height: 32/30,
              ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 构建通行模式内容
  Widget _buildPassContent() {
    return Padding(
      padding: EdgeInsets.only(left: 100.p),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "请通行",
            style: TextStyle(
              fontSize: 42.p,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF12215F),
            ),
          ),
          if (authMethod != null) ...[
            SizedBox(height: 8.p),
            Text(
              _getAuthMethodDisplayName(authMethod!),
              style: TextStyle(
                fontSize: 24.p,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF12215F).withOpacity(0.8),
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  /// 获取认证方式显示名称
  String _getAuthMethodDisplayName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.idCard:
        return '身份证';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.socialSecurityCard:
        return '社保卡';
      case AuthMethod.citizenCard:
        return '市民卡';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡';
      case AuthMethod.wechatQRCode:
        return '微信二维码';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码';
      case AuthMethod.aliCreditQRCode:
        return '支付宝信用二维码';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码';
      case AuthMethod.shangHaiQRCode:
        return '上海二维码';
      case AuthMethod.keyboardInput:
        return '键盘输入';
      case AuthMethod.tencentTCard:
        return '腾讯T卡';
      case AuthMethod.imiAuth:
        return 'IMI认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信或支付宝';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用二维码';
      case AuthMethod.jieYueBao:
        return '借阅宝';
    }
  }
} 