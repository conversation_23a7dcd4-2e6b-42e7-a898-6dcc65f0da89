import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import 'package:hardware/hardware.dart';

import '../models/auth_result.dart';
import '../view_models/auth_view_model.dart';
import 'auth_view.dart';

class AuthPage extends StatelessWidget {
  final AuthLoginType authLoginType;
  final AuthMethod authMethod;
  
  // 使用工厂构造函数避免常量构造函数问题
  factory AuthPage({
    Key? key, 
    AuthLoginType authLoginType = AuthLoginType.readerCard,
    AuthMethod? authMethod, required operationType,
  }) {
    final actualAuthMethod = authMethod ?? _getAuthMethodFromType(authLoginType);
    return AuthPage._internal(
      key: key,
      authLoginType: authLoginType,
      authMethod: actualAuthMethod,
    );
  }
  
  // 内部构造函数
  const AuthPage._internal({
    super.key, 
    required this.authLoginType,
    required this.authMethod,
  });

  // 根据AuthLoginType确定AuthMethod
  static AuthMethod _getAuthMethodFromType(AuthLoginType type) {
    switch (type) {
      case AuthLoginType.faceAuth:
        return AuthMethod.face;
      case AuthLoginType.IDCard:
        return AuthMethod.idCard;
      case AuthLoginType.readerCard:
        return AuthMethod.readerCard;
      case AuthLoginType.socailSecurityCard:
        return AuthMethod.socialSecurityCard;
      case AuthLoginType.citizenCard:
        return AuthMethod.citizenCard;
      case AuthLoginType.eletricSocialSecurityCard:
        return AuthMethod.eletricSocialSecurityCard;
      case AuthLoginType.wechatQRCode:
        return AuthMethod.wechatQRCode;
      case AuthLoginType.wechatScanQRCode:
        return AuthMethod.wechatScanQRCode;
      case AuthLoginType.alipayQRCode:
        return AuthMethod.alipayQRCode;
      case AuthLoginType.aliCreditQRCode:
        return AuthMethod.aliCreditQRCode;
      case AuthLoginType.huiwenQRCode:
        return AuthMethod.huiwenQRCode;
      case AuthLoginType.shangHaiQRCode:
        return AuthMethod.shangHaiQRCode;
      case AuthLoginType.readerQRCode:
        return AuthMethod.qrCode;
      case AuthLoginType.keyboardInput:
        return AuthMethod.keyboardInput;
      case AuthLoginType.tencentTCard:
        return AuthMethod.tencentTCard;
      case AuthLoginType.IMIAuth:
        return AuthMethod.imiAuth;
      case AuthLoginType.takePhoto:
        return AuthMethod.takePhoto;
      case AuthLoginType.wecharOrAlipay:
        return AuthMethod.wechatOrAlipay;
      case AuthLoginType.alipayQRCode_credit:
        return AuthMethod.alipayQRCodeCredit;
      case AuthLoginType.jieYueBao:
        return AuthMethod.jieYueBao;
      default:
        return AuthMethod.face; // 默认使用人脸认证
    }
  }

  @override
  Widget build(BuildContext context) {
    // 确保HWTagProvider可用
    final tagProvider = Provider.of<HWTagProvider>(context, listen: false);
    
    return ChangeNotifierProvider(
      create: (context) => AuthViewModel(context: context),
      child: AuthView(
        authLoginType: authLoginType,
        authMethod: authMethod,
      )
    );
  }
} 