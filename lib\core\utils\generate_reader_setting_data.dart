import 'dart:convert';
import 'package:base_package/base_package.dart';
import 'package:hardware/hardware.dart';


/// 生成完整的HWReaderSettingData配置类
/// 根据不同的readerType提供合适的默认配置
class HWReaderSettingGenerator {
  /// 生成基础的HWCoderConfigData配置
  static HWCoderConfigData generateCoderConfig(int readerType) {
    // 默认配置
    String bookCode = '';
    String shelfCode = '';
    String readerCardCode = '';
    String cdCode = '';
    String prefixStr = '';
    String suffixStr = '';
    String replaceRuleStr = '';

    // 根据阅读器类型调整配置
    switch (readerType) {
      case 0: // RR9000
        break;

      case 1: // 超高频
        break;

      case 7: // RR9299
      case 8: // RR9299
        // bookCode = '10';
        // readerCardCode = '85'; // 门禁卡特殊代码
        break;

      case 9: // 漂流柜门锁
        bookCode = '10';
        readerCardCode = '86'; // 柜门锁特殊代码
        break;

      case 12: // 电子社保卡
        readerCardCode = '90';
        replaceRuleStr = 'SS|SC';
        break;

      case 13: // 华大社保卡
        readerCardCode = '90';
        replaceRuleStr = 'HD|SC';
        break;
    }

    return HWCoderConfigData(
      bookCode: bookCode,
      shelfCode: shelfCode,
      readerCardCode: readerCardCode,
      cdCode: cdCode,
      prefixStr: prefixStr,
      suffixStr: suffixStr,
      replaceRuleStr: replaceRuleStr,
    );
  }

  /// 根据阅读器类型配置默认的info对象
  static HWExtraSettingData? _generateInfoForType(int readerType, String? selectedCardType) {
    switch (readerType) {
      case 0: // RR9000
        return HWRR9000InfoData(
          decoderType: 'RR9000解码器',
          comPort: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
          inAnt: '1',
          logSwitch: '否',
        );

      case 1: // 超高频
        return HWUHFInfoData(
          decoderType: '超高频解码器',
          comPort: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
          inAnt: '1',
          readPower: '20',
          uidLen: '4',
        );

      case 2: // 人脸识别
        return HWFaceInfoData(
          decoderType: '人脸识别',
          type: '百度人脸识别',
          domain: 'ws://localhost:8080',
          faceDomain: 'http://localhost:8080/api',
          account: 'admin',
          psw: '123456',
        );

      case 3: // 手动输入
        return null; // 手动输入不需要info

      case 4: // 3036阅读器
        return HWCommonInfoData(
          decoderType: '3036解码器',
          comPort: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
          inAnt: '1',
        );

      case 5: // HD100
        if (selectedCardType == '身份证') {
          return HWDecoderExtraData(decoderType: 'HD100身份证解码器');
        } else if (selectedCardType == 'M1卡') {
          return HWM1InfoData(decoderType: 'HD100 M1解码器', cardType: 'typeA');
        } else if (selectedCardType == 'CPU卡') {
          return HWCPUInfoData(
            decoderType: 'HD100 CPU解码器',
            cardType: '非接触类',
            card_ab: 'typeA',
            APDU1: '',
            APDU2: '',
            slot_num: 'HD100_大卡座',
          );
        } else if (selectedCardType == 'UID卡') {
          return HWRemoteUIDReaderInfoData(
            decoderType: 'HD100 UID解码器',
            cardType: 'typeA',
          );
        } else {
          return HWDecoderExtraData(decoderType: 'HD100解码器');
        }

      case 6: // 荣睿双频
        return HWDFInfoData(
          comPort: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
          inAnt: '1',
        );

      case 7: // RR9299
      case 8: // RR9299
        return HWRR9299InfoData(
          comPort: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
          decoderType: 'RR9299解码器',
        );

      case 9: // 漂流柜门锁
        return HWLockInfoData(
          comPort: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
        );

      case 10: // T10
        if (selectedCardType == '身份证') {
          return HWT10IDCardInfoData(
            decoderType: 'T10身份证解码器',
            mode: '串口模式',
            index: '1',
          );
        } else if (selectedCardType == 'M1卡') {
          return HWT10M1InfoData(
            decoderType: 'T10 M1解码器',
            cardType: 'typeA',
            mode: '串口模式',
            index: '1',
          );
        } else {
          return HWDecoderExtraData(decoderType: 'T10解码器');
        }

      case 11: // 解码器
        return HWDecoderExtraData(decoderType: '通用解码器');

      case 12: // 电子社保卡
        if (selectedCardType == '平台电子社保码') {
          return HWPlatformExtraData(
            decoderType: '电子社保解码器',
            port: 'COM3',
            patronUrl: 'http://iot.seaeverit.com:51078/api/v1',
            dataType: 'cardNo',
          );
        } else if (selectedCardType == '直接扫码') {
          return HWScanInfoData(
            port: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
            decoderType: '扫码枪解码器',
            cardTypes: '0',
            openCmd: '',
            closeCmd: '',
          );
        }
        return null;

      case 13: // 华大社保卡
        if (selectedCardType == '华大社保卡') {
          return HWDecoderExtraData(decoderType: '华大社保解码器');
        }
        return null;

      case 14: // 氛围灯
        return HWLightInfoData(
          port: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
        );

      default:
        return HWCommonInfoData(
          decoderType: '通用解码器',
          comPort: BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1',
          inAnt: '1',
        );
    }
  }

  /// 为特定阅读器类型生成extras列表
  static List<HWExtraSettingData>? _generateExtrasForType(int readerType, String? selectedCardType) {
    List<HWExtraSettingData>? extras;

    switch (readerType) {
      case 7: // RR9299
      case 8: // RR9299
        extras = [];
        // 默认添加四个门配置
        ['A1', 'A2', 'A3', 'A4'].forEach((doorId) {
          extras!.add(HWRR9299ExtraData(
            door: doorId,
            inAnt: (int.parse(doorId[1])).toString(),
            timeout: '30',
          ));
        });
        break;

      case 9: // 漂流柜门锁
        extras = [];
        // 默认添加一个锁配置
        extras.add(HWLockExtraData(
          door: 'A1',
          lock: '1',
        ));
        break;

      case 14: // 氛围灯
        extras = [];
        // 默认添加一个通道配置
        extras.add(HWLightExtraData(
          lightPositon: LightPositionMap.values.first,
          channel: '1',
        ));
        break;

      default:
      // 根据卡类型添加extras
        if (selectedCardType != null) {
          HWExtraSettingData? extraData = HWReaderSettingData.CardTypeClass(selectedCardType);
          if (extraData != null) {
            extras = [extraData];

            // 针对特定卡类型设置默认值
            if (selectedCardType == '14443A' || selectedCardType == 'M1卡') {
              if (extras.first is HWReader14443AExtraData) {
                (extras.first as HWReader14443AExtraData).sectionNum = '0';
                (extras.first as HWReader14443AExtraData).blockNum = '1';
                (extras.first as HWReader14443AExtraData).password = 'FFFFFFFFFFFF';
              }
            } else if (selectedCardType == '高频&超高频') {
              if (extras.first is HWDFExtraData) {
                (extras.first as HWDFExtraData).hfDecoderType = '高频解码器';
                (extras.first as HWDFExtraData).uhfDecoderType = '超高频解码器';
              }
            }
          }
        }
        break;
    }

    return extras;
  }

  /// 根据传入的参数生成完整的HWReaderSettingData实例
  ///
  /// [readerType] 阅读器类型代码(0-14)
  /// [type] 标签类型(1:图书,2:层架标,3:读者证,4:键盘输出,5:CD)
  /// [selectedCardType] 可选的卡片类型，不传会使用默认值
  /// [customParams] 自定义参数，可用于覆盖默认值
  static HWReaderSettingData generate({
    required int readerType,
    int? type,
    String? selectedCardType,
    Map<String, dynamic>? customParams,
  }) {
    // 1. 创建编码器配置
    HWCoderConfigData coderConfig = generateCoderConfig(readerType);

    // 2. 确定卡片类型
    String? cardType = selectedCardType;
    if (cardType == null && HWReaderSettingData.CardTypeMap.containsKey(readerType)) {
      List<String>? types = HWReaderSettingData.CardTypeMap[readerType];
      if (types != null && types.isNotEmpty) {
        cardType = types.first;
      }
    }

    // 3. 创建基础设置实例
    HWReaderSettingData settingData = HWReaderSettingData(
      coderConfig: coderConfig,
      readerType: readerType,
      type: type,
      selectedCardType: cardType,
    );

    // 4. 设置info
    settingData.info = _generateInfoForType(readerType, cardType);

    // 5. 设置extras
    settingData.extras = _generateExtrasForType(readerType, cardType);

    // 6. 应用自定义参数
    if (customParams != null) {
      _applyCustomParams(settingData, customParams);
    }

    return settingData;
  }

  /// 应用自定义参数到设置实例
  static void _applyCustomParams(HWReaderSettingData settingData, Map<String, dynamic> params) {
    // 处理基本参数
    if (params.containsKey('id')) settingData.id = params['id'];
    if (params.containsKey('type')) settingData.type = params['type'];

    // 处理info参数
    if (params.containsKey('info') && settingData.info != null) {
      Map<String, dynamic> infoParams = params['info'];
      infoParams.forEach((key, value) {
        settingData.info?.setValueForKey(key, value.toString());
      });
    }

    // 处理extras参数
    if (params.containsKey('extras') && settingData.extras != null && settingData.extras!.isNotEmpty) {
      List<dynamic> extrasParams = params['extras'];
      for (int i = 0; i < extrasParams.length && i < settingData.extras!.length; i++) {
        Map<String, dynamic> extraParam = extrasParams[i];
        extraParam.forEach((key, value) {
          settingData.extras![i].setValueForKey(key, value.toString());
        });
      }
    }

    // 处理额外extras
    if (params.containsKey('addExtras') && params['addExtras'] is List) {
      List<dynamic> addExtras = params['addExtras'];
      settingData.extras ??= [];

      for (var extraData in addExtras) {
        if (extraData is Map<String, dynamic> &&
            extraData.containsKey('type') &&
            extraData.containsKey('data')) {
          String type = extraData['type'];
          Map<String, dynamic> data = extraData['data'];

          HWExtraSettingData? extra = HWReaderSettingData.CardTypeClass(type);
          if (extra != null) {
            data.forEach((key, value) {
              extra.setValueForKey(key, value.toString());
            });
            settingData.extras!.add(extra);
          }
        }
      }
    }

    // 处理编码器配置
    if (params.containsKey('coderConfig')) {
      Map<String, dynamic> coderParams = params['coderConfig'];
      coderParams.forEach((key, value) {
        settingData.coderConfig.setValueForKey(key, value.toString());
      });
    }
  }

  /// 快速创建RR9299门禁设置(readerType=7或8)
  ///
  /// [doorList] 要配置的门列表，例如 ['A1', 'B2', 'C3']
  /// [antennaMap] 可选的门到天线的映射，默认会根据门号分配
  /// [timeoutMap] 可选的门到超时时间的映射，默认为30
  static HWReaderSettingData generateRR9299({
    required int readerType,  // 7或8
    required List<String> doorList,
    Map<String, String>? antennaMap,
    Map<String, String>? timeoutMap,
    String? comPort,
    String? decoderType,
    String? selectedCardType,
  }) {
    if (readerType != 7 && readerType != 8) {
      throw ArgumentError('readerType必须是7或8，表示RR9299设备');
    }

    // 创建基础设置
    HWReaderSettingData settingData = generate(readerType: readerType, type: 3, selectedCardType: selectedCardType);

    // 设置info
    if (settingData.info is HWRR9299InfoData) {
      HWRR9299InfoData info = settingData.info as HWRR9299InfoData;
      if (comPort != null) info.comPort = comPort;
      if (decoderType != null) info.decoderType = decoderType;
    }

    // 设置extras - 每个门一个配置
    settingData.extras = [];
    for (String door in doorList) {
      String ant = (antennaMap != null && antennaMap.containsKey(door))
          ? antennaMap[door]!
          : _getDefaultAntenna(door);

      String timeout = (timeoutMap != null && timeoutMap.containsKey(door))
          ? timeoutMap[door]!
          : '30';

      settingData.extras!.add(HWRR9299ExtraData(
        door: door,
        inAnt: ant,
        timeout: timeout,
      ));
    }

    return settingData;
  }

  /// 快速创建超高频读写器设置(readerType=1)
  ///
  /// [type] 标签类型(1:图书,2:层架标,3:读者证,4:键盘输出,5:CD)
  /// [doorList] 要配置的天线门列表，例如 ['A1', 'B2', 'C3']
  /// [antennaMap] 可选的门到天线的映射，默认会根据门号分配
  /// [comPort] 串口号，默认使用系统第一个可用端口
  /// [inAnt] 默认天线编号，默认为'1'(当不使用doorList时)
  /// [readPower] 读功率，默认为'20'
  /// [writePower] 写功率，默认为'20'
  /// [rssi] 信号值筛选，可选
  /// [gpioEnable] GPIO控制，默认为'否'
  /// [uidLen] 数据长度，默认为'4'
  /// [uidRemove] 需要移除的不合格UID，仅当uidLen为4时生效，多个匹配以|分割
  /// [decoderType] 解码器类型，默认为'超高频解码器'
  static HWReaderSettingData generateUHF({
    int? readerType,
    List<String>? doorList,
    Map<String, String>? antennaMap,
    String? comPort,
    String? inAnt = '1',
    String? readPower = '20',
    String? writePower = '20',
    String? rssi,
    String? gpioEnable = '否',
    String? uidLen = '4',
    String? uidRemove,
    String? decoderType = '超高频解码器',
    String? selectedCardType,
    Map<String, dynamic>? customParams,
  }) {
    // 创建基础超高频设置
    HWReaderSettingData settingData = generate(
      readerType: 1, // 超高频类型编码
      selectedCardType: selectedCardType,
    );

    // 配置UHF特有参数
    if (settingData.info is HWUHFInfoData) {
      HWUHFInfoData info = settingData.info as HWUHFInfoData;

      // 设置解码器基本信息
      info.decoderType = decoderType ?? '超高频解码器';
      info.comPort = comPort ?? (BPUtils.ports.isNotEmpty ? BPUtils.ports.first : 'COM1');

      // 设置天线和功率参数（如果没有门列表，使用默认参数）
      if (doorList == null || doorList.isEmpty) {
        info.inAnt = inAnt;
      } else {
        // 当有门列表时，info.inAnt使用第一个门的天线
        String firstDoor = doorList.first;
        String firstAnt = (antennaMap != null && antennaMap.containsKey(firstDoor))
            ? antennaMap[firstDoor]!
            : _getDefaultAntenna(firstDoor);
        info.inAnt = firstAnt;
      }

      info.readPower = readPower;
      info.writePower = writePower;

      // 设置高级参数
      if (rssi != null) info.rssi = rssi;
      info.gpioEnable = gpioEnable;
      info.uidLen = uidLen;
      if (uidRemove != null) info.uidRemove = uidRemove;
    }


    // 设置extras - 每个门一个配置
    settingData.extras = [];
    for (String door in doorList!) {
      String ant = (antennaMap != null && antennaMap.containsKey(door))
          ? antennaMap[door]!
          : _getDefaultAntenna(door);
      settingData.extras!.add(HWUHFInfoData(
        inAnt: ant,
        readPower: readPower,
      ));
    }
    return settingData;
  }

  /// 获取门的默认天线号
  static String _getDefaultAntenna(String door) {
    if (door.length != 2) return '1';

    String area = door[0];
    int number = int.tryParse(door[1]) ?? 1;

    switch (area) {
      case 'A': return number.toString();
      case 'B': return (number + 4).toString();
      case 'C': return (number + 8).toString();
      case 'D': return (number + 12).toString();
      default: return number.toString();
    }
  }
}