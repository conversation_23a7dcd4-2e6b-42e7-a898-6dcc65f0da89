import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';

/// 窗口工具类
class WindowUtil {
  static double _scale = 1.0;
  static double _height = 1280;
  static double _width = 800;

  // /// 更新缩放比例（只需在 MyHomePage 初始化时调用一次）
  static void updateScale(BuildContext context) {
    final layout = context.read<CurrentLayoutProvider>().value;
    final size = MediaQuery.of(context).size;
    final designSize = layout == SeaLayout.horizontal
        ? const Size(1280, 800)   // 横屏模式
        : const Size(800, 1280);  // 竖屏模式

    final scaleX = size.width / designSize.width;
    final scaleY = size.height / designSize.height;
    _scale = scaleX < scaleY ? scaleX : scaleY;

    _width = designSize.width * _scale;
    _height = designSize.height * _scale;
  }


  /// 获取缩放后的尺寸
  static double p(num value) => (value * _scale).floorToDouble();

  /// 获取缩放后的宽度
  static double get width => _width;

  /// 获取缩放后的高度
  static double get height => _height;
}

/// 扩展方法，方便使用
extension NumExtension on num {
  /// 缩放尺寸
  double get p => WindowUtil.p(this);
}