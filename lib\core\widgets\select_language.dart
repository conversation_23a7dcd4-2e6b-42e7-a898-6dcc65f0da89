import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:gradient_borders/gradient_borders.dart';

import '../utils/window_util.dart';

class SelectLanguage extends StatelessWidget {
  const SelectLanguage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320.p,
      height: 70.p,
      decoration: BoxDecoration(
        borderRadius:  BorderRadius.horizontal(
          left: Radius.circular(35.p),
        ),
        border:  const GradientBoxBorder(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color(0xFF54A0FF),
              Color(0x0054A0FF),
            ],
          ),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(width: 20.p),
          CircleAvatar(
            radius: 24.p,
          ),
          SizedBox(width: 20.p),
          Expanded(
            child: Text(
              '中文',
              style: TextStyle(
                color: const Color(0xFF54A0FF),
                fontSize: 32.p,
                fontWeight: FontWeight.w500,
                height: 32/36
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          Icon(
            Icons.arrow_drop_down,
            size: 60.p,
            color: const Color(0xFF54A0FF),
          ),
          SizedBox(width: 40.p),
        ],
      ),
    );
  }
}



