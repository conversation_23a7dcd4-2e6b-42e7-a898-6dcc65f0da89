{"renewInProgress": "Renewal in progress...", "noCardTip": "The card has been used up and the reader's card cannot be processed temporarily. Please contact the staff！", "cardLefts": "Remaining cards", "Sending": "Sending...", "resend": "s", "AccountPassword": "Account/Password", "ReaderCardVerification": "Reader Card Verification", "ScanAuth": "Scan QR Code Authentication", "wechatScanAuth": "WeChat Scan QR Code Authentication", "IncorrectPassword": "Incorrect password", "accountLogin": "Account <PERSON>gin", "accountInput": "Enter patron ID or ID card number", "passwordInput": "Enter Password", "loginButton": "<PERSON><PERSON>", "spaceLogin": " <PERSON><PERSON>", "accountRequired": "Account cannot be empty", "passwordLengthError": "Incorrect password length", "passwordFormatError": "Incorrect password format", "requestingInfo": "Requesting reader information", "invalidCredentials": "Reader ID does not exist or incorrect password", "readerFailure": "Reader initialization failed", "barcodeError": "Barcode parsing failed", "cardCreationFail": "Card creation failed", "waitingResult": "Waiting for result", "processingCard": "Processing reader card, please wait...", "cardIdFail": "Failed to retrieve reader card ID", "cardIdFailContact": "Failed to retrieve reader card ID, please contact library staff", "cardIssuanceError": "Card issuance error", "cardMachineError": "Card machine error, please contact library staff", "cardIdExhausted": "All reader card IDs have been used", "cardIdExhaustedContact": "All reader card IDs have been used, please contact library staff", "unsupportedCardType": "Unsupported card type", "unsupportedCardTypeContact": "Unsupported card type, please contact library staff", "depositNotSupported": "<PERSON><PERSON><PERSON><PERSON> not supported", "cardCreationSuccess": "Card creation successful", "readerTableFail": "Failed to process reader table", "businessTableFail": "Failed to process business system table", "cardRequestFail": "Failed to request card creation", "nameInput": "Name", "genderInput": "Gender", "ethnicityInput": "Ethnicity", "birthDateInput": "Date of Birth", "issuingAuthInput": "Issuing Authority", "idNumberInput": "ID Number", "expirationInput": "Expiration Date", "addressInput": "Address", "captchaRequired": "Captcha cannot be empty", "phoneError": "Invalid phone number", "depositInput": "<PERSON><PERSON><PERSON><PERSON>", "currencyUnit": "Yuan", "phoneForService": "For better service, please enter your phone number", "phoneInput": "Enter your phone number", "validCaptcha": "Enter correct captcha", "captchaInput": "Enter cap<PERSON>a", "phoneOrCaptchaRequired": "Phone number or captcha cannot be empty", "validPhoneInput": "Enter valid phone number", "confirmButton": "Confirm", "serviceNotice": "Self-service card issuance notice", "agreement": "I have read and agreed to the above", "continueButton": "Continue", "readerCardAppliedSuccessfully": "You have successfully applied for a reader card", "collectReaderCard": "Please collect your reader card from the dispenser", "confirm": "Confirm", "socialSecurityCard": "Social Security Card", "identityCard": "ID Card", "register": " Register", "scanESCBarcode": "Scan your electronic social security card QR code", "place": "Place", "at": "at", "sensingArea": "Sensing Area", "electronicSocialSecurityCard": "Electronic Social Security Card", "scanYour": "Scan your", "qrCode": " QR Code", "selectRegistrationMethod": "Select Registration Method", "readerNotFound": "Reader not configured", "sscRegistration": "Social Security Card Registration", "secondGenIDCardRegistration": "Second Generation ID Card Registration", "duplicateRegistration": "You have already registered a reader card, please do not register again", "eSSCRegistration": "Electronic Social Security Card Registration", "unsupportedType": "Unsupported type", "noDeposit": "Deposit-free", "selectReaderCardType": "Select Reader Card Type", "ineligibleAge": "Age Ineligible", "ineligibleArea": "Area Ineligible", "placeDocument": "Place Document on Sensing Area", "success": "Success", "copy": "items", "failure": "Failure", "sequenceNumber": "No", "barcode": "Barcode", "bookTitle": "Book Title", "dueDate": "Due Date", "note": "Note", "booksToReturn": "Books to Return", "availableInLibrary": "In Library", "borrowSuccess": "Borrow Success", "bookTitleShort": "Title", "callNumber": "Call Number", "borrowFailure": "Borrow Failure", "continueBorrowing": "Continue Borrowing", "confirmBorrow": "Confirm <PERSON>", "readerCardID": "Reader Card ID", "borrowableCopies": "Available Copies", "borrowedCopies": "Borrowed Copies", "defaultPassword": "Default Password", "Predeposit": "Prepaid (Yuan)", "returningInProgress": "Returning in Progress, Do Not Repeat", "noBooksAvailable": "No Books Available", "borrowingInProgress": "Borrowing in Progress", "borrowRequestFailed": "Borrow Request Failed", "borrow_fail_request": "Borrow Request Failed", "borrow_fail_rewrite_security": "Borrow Failed, Security Tag Rewrite Failed", "cert_number": "Certificate Number", "borrow_success": "Document Successfully Borrowed", "status": "Status", "return_date": "Return Date", "take_away_card_books": "Please Take Your Reader Card and Borrowed Books", "free_listening": "Free Listening", "wechat_scan": "Scan with WeChat", "reader_card": "Reader Card", "scan_e_social_card": "Please Scan Your Electronic Social Security Card", "wechat_qr_code": "WeChat QR Code", "huiwen_qr_code": "HuiWen QR Code", "scan_wechat_qr": "Please Scan Your WeChat QR Code", "scan_huiwen_qr": "Please Scan Your HuiWen QR Code", "alipay_login": "Login with <PERSON><PERSON><PERSON>", "alipay": "Alipay", "alipay_scan_guide": "Open <PERSON><PERSON><PERSON> and <PERSON><PERSON> in Top Left Corner", "borrow_with_alipay_credit": "With a Sesame Credit Score of 550 or above, scan the QR code on the screen to borrow books", "auth_fail_advice": "If authorization fails, please check the failure reason on the page or contact Sesame Credit customer service", "regenerate": "Regenerate", "face_recognition_login": "Login with Face Recognition", "face_recognition": "Face Recognition", "face_to_camera": "Please Face the Camera", "login_method": "Login Method", "card_not_exist": "Reader Card Does Not Exist", "reader_closing": "Reader is Closing, Please Try Again Later", "detail_info": "Detailed Information", "borrowing": "Borrowing", "overdue": "Overdue", "renew_success": "Renewal Success", "renew_fail": "Renewal Failed", "barcode_number": "Barcode Number", "renew": "<PERSON>w", "debt_amount": "Debt Amount (Yuan)", "details": "Details", "current_borrow_count": "Current Borrowing Count", "available_borrow_count": "Available Borrowing Count", "borrowed": "Borrowed", "overdue_count": "Overdue Document Count", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "debt": "Debt", "prepaid_balance": "Prepaid Balance", "select_all": "Select All", "view_details": "View Details", "available_for_borrow": "Available for Borrowing", "return_success": "Return Success", "return_date_recorded": "Recorded Return Date", "return_fail": "Return Failed", "continue_returning": "Continue Returning", "confirm_return": "Confirm Return", "book_overdue": "This Item is Overdue", "no_books_to_return": "No Books to Return", "returningStatus": "Returning in progress...", "returnFailure": "Return Failed, Request Failed", "returnFailureRewrite": "Return Failed, Security Tag Rewrite Failed", "placeBookOnSensor": "Please place the document to be returned on the sensor area", "returnSuccess": "Document Successfully Returned", "takeCardAndBooks": "Please take your reader card and borrowed books, and verify the printed receipt!", "returnDate": "Return Date", "bookRecommendations": "Recommended Books for You", "selfServiceMachine": "Self-Service Borrowing and Returning Machine", "sendVerificationCode": "Send Verification Code", "printReceipt": "Do you want to print a receipt?", "no": "No", "yes": "Yes", "clear": "Clear", "characters": "Characters", "quit": "Quit", "bookRecommendationsTitle": "Book Recommendations", "scanToListen": "<PERSON>an to Listen", "homePage": "Home Page", "borrowBook": "Borrow Book", "returnBook": "Return Book", "renewBook": "Renew Book", "applyCard": "Apply for Card", "renewOrQuery": "Renew/Query", "query": "Query", "remainingTime": "Remaining Time", "selectOperationType": "Select Operation Type", "qrCodeLogin": "QR Code Login", "wechatLogin": "<PERSON><PERSON><PERSON>", "cardLogin": "Reader Card Login", "manualInputLogin": "Manual Input Login", "manualInput": "Manual Input", "idCardLogin": "ID Card Login", "socialSecurityCardLogin": "Social Security Card Login", "eSocialSecurityCardLogin": "Electronic Social Security Card Login", "citizenCardLogin": "Citizen <PERSON>", "exit": "Exit", "placeCardOnSensor": "Please place the reader card on the sensor area", "placeIdCardOnSensor": "Please place the ID card on the sensor area", "tencentEPassRegister": "Tencent E-Pass Registration", "qrCodeRegistration": "QR Code Display Registration", "alipayCreditRegister": "Alipay Credit Score Registration", "faceMatchRegister": "Face Matching Registration", "scanPayRegister": "Scan to Pay Registration", "externalCardRegistration": "External Card Registration (for Card Application)", "paymentCodeConfig": "Payment Code Configuration", "manualInputRegistration": "Manual Input Registration", "shanghaiShenShenCodeRegister": "Shanghai ShenShen Code Registration", "placeCardOnSensorForCard": "Please place the card on the sensor area", "enterPhoneNumber": "For better service, please enter your mobile number", "inputPhoneNumber": "Enter Mobile Number", "yourPhoneNumber": "Your Phone Number", "selectCardType": "Please select the button below to apply for the corresponding type of library card", "submittingData": "Submitting data, please wait...", "insertMoney": "Please insert the banknote into the slot", "moneyAcceptanceInfo": "This machine only accepts RMB 10, 20, 50, and 100 yuan. No change is provided. Any remaining balance will be credited to your prepaid account.", "accountBalance": "Account <PERSON><PERSON>", "currentDeposit": "Current Deposit", "cardApplicationSuccess": "You have successfully applied for a reader card"}