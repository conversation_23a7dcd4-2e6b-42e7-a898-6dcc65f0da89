import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/router/route_extension.dart';
import '../../../../core/utils/window_util.dart';
import '../../../../core/widgets/auth_item.dart';
// import '../../../auth/views/auth_page.dart';
import '../../view_models/login_view_model.dart';

class AuthOptions extends StatelessWidget {
  const AuthOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginViewModel>(builder: (context, viewModel, _) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 80.p, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                '请选择登录方式',
                style: TextStyle(
                    fontSize: 48.p,
                    color: const Color(0xFF222222),
                    fontWeight: FontWeight.w500,
                    height: 1),
              ),
            ),
            SizedBox(height: 25.p),
            Expanded(
                child: ListView.builder(
              physics: const BouncingScrollPhysics(),
              itemCount: viewModel.loginTypes.length,
              itemBuilder: (context, index) {
                final item = viewModel.loginTypes[index];
                if (item != null && item.type != null) {
                  return AuthItem(
                    title: item.title ?? '',
                    iconPath: item.icon ?? '',
                    onTap: () {
                      AppNavigator.toAuth(operationType: viewModel.operationType, authLoginType: item.type!,);
                    },
                  );
                } else {
                  return const SizedBox();
                }
              },
            )),
          ],
        ),
      );
    });
  }
}
