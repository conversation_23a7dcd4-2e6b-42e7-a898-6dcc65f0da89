import 'dart:math' as math;

import 'package:a3g/shared/utils/asset_util.dart';
import 'package:flutter/material.dart';
import 'package:seasetting/seasetting.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/user_provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/base_page2.dart';
import '../../home/<USER>/home_view.dart';
import '../models/auth_result.dart';
import '../view_models/auth_view_model.dart';
import 'widgets/face_scanner_effect.dart';
import 'widgets/gradient_capsule.dart';
import 'widgets/reader_card_auth_widget.dart';

class FaceDetectionPage extends StatefulWidget {
  const FaceDetectionPage({super.key});

  @override
  State<FaceDetectionPage> createState() => _FaceDetectionPageState();
}

class _FaceDetectionPageState extends State<FaceDetectionPage> {
  // 当前认证方式，默认为人脸认证
  AuthMethod _currentAuthMode = AuthMethod.face;
  late AuthViewModel _authViewModel;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _authViewModel = Provider.of<AuthViewModel>(context, listen: false);
      _authViewModel.init();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 切换认证方式
  void _switchAuthMode(AuthMethod mode) {
    setState(() {
      _currentAuthMode = mode;
    });
    
    // 通知视图模型更新认证方式
    _authViewModel.setAuthMethod(mode);
  }

  @override
  Widget build(BuildContext context) {
    return BasePage2(
      topWrapper: _buildHeader(),
      mainWrapper: _buildFaceDetectionArea(),
      // bottomWrapper: _buildFooter(),
      // overlayColor: Colors.black.withOpacity(0.5),
    );
  }

  Widget _buildHeader() {
    return const HeaderWidget();
  }

  Widget _buildFaceDetectionArea() {
    // 监听认证状态变化，当认证成功时自动跳转到借书页面
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        // 如果用户已认证，显示借书页面
        if (userProvider.isAuthenticated) {
          // 使用Future.microtask确保导航发生在构建完成后
          // Future.microtask(() {
          //   Navigator.of(context).pushReplacement(
          //     MaterialPageRoute(builder: (_) => const BorrowBookPage())
          //   );
          // });
          // 返回一个加载指示器，防止短暂的空白页面
          return const Center(child: CircularProgressIndicator());
        }
        
        // 用户未认证，显示认证界面
        return Container(
          color: Colors.green,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 根据当前认证方式显示不同的认证区域
              _buildAuthAreaByMode(),
              
              // 认证方式切换按钮
              Padding(
                padding: EdgeInsets.symmetric(vertical: 20.p),
                child: _buildAuthModeSwitcher(),
              ),
              
              // 人脸检测结果区域 - 保持位置不变
              Expanded(
                child: Center(
                  child: _buildAccess(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 认证方式切换UI
  Widget _buildAuthModeSwitcher() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildAuthModeButton(AuthMethod.face, '人脸认证'),
        SizedBox(width: 20.p),
        _buildAuthModeButton(AuthMethod.idCard, '身份证认证'),
        SizedBox(width: 20.p),
        _buildAuthModeButton(AuthMethod.readerCard, '读者证认证'),
        SizedBox(width: 20.p),
        _buildAuthModeButton(AuthMethod.qrCode, '二维码认证'),
      ],
    );
  }

  // 认证方式按钮
  Widget _buildAuthModeButton(AuthMethod mode, String label) {
    return ElevatedButton(
      onPressed: () => _switchAuthMode(mode),
      style: ElevatedButton.styleFrom(
        backgroundColor: _currentAuthMode == mode 
          ? const Color(0xFF4F83FC) 
          : const Color(0xFF12215F),
        padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.p),
        ),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: Colors.white,
          fontSize: 24.p,
        ),
      ),
    );
  }

  // 根据当前模式返回相应的认证区域Widget
  Widget _buildAuthAreaByMode() {
    switch (_currentAuthMode) {
      case AuthMethod.face:
        return _buildFaceAuthArea();
      case AuthMethod.idCard:
        return _buildIDCardAuthArea();
      case AuthMethod.readerCard:
        return _buildReaderCardAuthArea();
      case AuthMethod.qrCode:
        return _buildQRCodeAuthArea();
      case AuthMethod.socialSecurityCard:
      case AuthMethod.citizenCard:
      case AuthMethod.eletricSocialSecurityCard:
      case AuthMethod.keyboardInput:
      case AuthMethod.tencentTCard:
      case AuthMethod.imiAuth:
      case AuthMethod.jieYueBao:
        return _buildReaderCardAuthArea();
      case AuthMethod.wechatQRCode:
      case AuthMethod.wechatScanQRCode:
      case AuthMethod.alipayQRCode:
      case AuthMethod.aliCreditQRCode:
      case AuthMethod.huiwenQRCode:
      case AuthMethod.shangHaiQRCode:
      case AuthMethod.alipayQRCodeCredit:
      case AuthMethod.wechatOrAlipay:
        return _buildQRCodeAuthArea();
      case AuthMethod.takePhoto:
        return _buildFaceAuthArea();
    }
  }

  // 人脸认证区域
  Widget _buildFaceAuthArea() {
    return Container(
      width: 620.p,
      height: 620.p,
      margin: EdgeInsets.only(top: 126.p),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        image: DecorationImage(
          image: AssetImage(AssetUtil.fullPath('circle_border')),
        ),
      ),
      child: Center(
        child: Container(
            margin: EdgeInsets.only(
                left: 20.p, right: 20.p, top: 20.p, bottom: 33.p),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Color(0xFF12215F),
            ),
            child: FaceScannerEffect(
              size: 620.p,
              startColor: const Color(0xFF4F83FC),
              // 渐变起始色
              endColor: const Color(0xFF4F83FC),
              // 渐变结束色（与起始色相同，只是透明度不同）
              startOpacity: 0.4,
              // 100%不透明（可调整）
              endOpacity: 0.05,
              // 调整为5%不透明度（可以根据视觉效果调整）
              borderColor: Colors.transparent,
            )),
      ),
    );
  }

  // 身份证认证区域
  Widget _buildIDCardAuthArea() {
    return SizedBox(
      width: 620.p,
      height: 746.p,
      child: const ReaderCardAuthWidget(AuthLoginType.IDCard),
    );
  }

  // 读者证认证区域
  Widget _buildReaderCardAuthArea() {
    return SizedBox(
      width: 620.p,
      height: 746.p,
      child: const ReaderCardAuthWidget(AuthLoginType.readerCard),
    );
  }

  // 二维码认证区域
  Widget _buildQRCodeAuthArea() {
    return Container(
      width: 620.p,
      height: 620.p,
      margin: EdgeInsets.only(top: 126.p),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: const Color(0xFF4F83FC), width: 2.p),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.qr_code_scanner,
              size: 200.p,
              color: const Color(0xFF4F83FC),
            ),
            SizedBox(height: 20.p),
            Text(
              '请将二维码对准扫描区域',
              style: TextStyle(
                color: Colors.white,
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarring(){
    return GradientCapsule(
      width: 480.p,
      height: 70.p,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Icon(Icons.warning_amber, color: Colors.white, size: 20.p),
          Image(
              width: 34.p,
              height: 30.p,
              image:
              AssetImage(AssetUtil.fullPath('serious_warning'))),
          SizedBox(width: 20.p),
          Text(
            '没有检测到录入人脸',
            style: TextStyle(
              color: const Color(0xFFE13841),
              fontSize: 32.p,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccess(){
    // 根据需要选择一种模式
    final bool useDetailMode = true; // 根据实际需求设置此变量
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.p),
      child: IdentityCard(
        isPassMode: !useDetailMode,
        name: useDetailMode ? "安娜" : null,
        idNumber: useDetailMode ? "00013456" : null,
        time: useDetailMode ? "2025-05-07 14:55:29" : null,
        avatarUrl: "https://example.com/avatar.jpg", // 替换为本地资源路径
      ),
    );
  }
}

class IdentityCard extends StatelessWidget {
  final String? name;
  final String? idNumber;
  final String? time;
  final String? avatarUrl;
  final bool isPassMode;

  const IdentityCard({
    Key? key,
    this.name,
    this.idNumber,
    this.time,
    this.avatarUrl,
    this.isPassMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 190.p,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment(-0.8, -0.8), // 近似125度角
          end: Alignment(0.8, 0.8),
          colors: [
            Color(0xFFBBFBFF), // #BBFBFF
            Color(0xFFAFDAFF), // #AFDAFF
            Color(0xFFC3CAFF), // #C3CAFF
          ],
          stops: [0.0, 0.47, 1.0],
        ),
        borderRadius: BorderRadius.circular(40.p),
      ),
      padding:  EdgeInsets.all(30.p),
      child: Row(
        children: [
          // 左侧头像 - 改用本地图片以避免网络延迟
          Container(
            width: 130.p,
            height: 130.p,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(20.p),
              // 使用简单的颜色替代图片，避免资源加载问题
              // image: avatarUrl != null
              //     ? DecorationImage(
              //         image: AssetImage(AssetUtil.fullPath('default_avatar')),
              //         fit: BoxFit.cover,
              //       )
              //     : null,
            ),
            // 使用简单的占位符替代图片
            child: Center(
              child: Icon(
                Icons.person,
                size: 80.p,
                color: Colors.white,
              ),
            ),
          ),
           SizedBox(width: 30.p),

          // 右侧信息
          Expanded(
            child: isPassMode
                ? _buildPassModeContent()
                : _buildDetailModeContent(),
          ),
        ],
      ),
    );
  }

  // 详细信息模式内容
  Widget _buildDetailModeContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
             Text(
              "姓名",
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF12215F),
                height: 32/30
              ),
            ),
             SizedBox(width: 30.p),
            Text(
              name ?? "",
              style:  TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12215F),
                  height: 32/30
              ),
            ),
          ],
        ),
         SizedBox(height: 10.p),
        Row(
          children: [
             Text(
              "证号",
               style: TextStyle(
                 fontSize: 32.p,
                 fontWeight: FontWeight.w400,
                 color: const Color(0xFF12215F),
                   height: 32/30
               ),
            ),
             SizedBox(width: 30.p),
            Text(
              idNumber ?? "",
              style:  TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12215F),
                  height: 32/30
              ),
            ),
          ],
        ),
         SizedBox(height: 16.p),
        Row(
          children: [
             Text(
              "时间",
               style: TextStyle(
                 fontSize: 32.p,
                 fontWeight: FontWeight.w400,
                 color: const Color(0xFF12215F),
                   height: 32/30
               ),
            ),
             SizedBox(width: 20.p),
            Text(
              time ?? "",
              style:  TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12215F),
                  height: 32/30
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 通行模式内容
  Widget _buildPassModeContent() {
    return Padding(
      padding: EdgeInsets.only(left: 100.p),
      child: Text(
        "请通行",
        style: TextStyle(
          fontSize: 42.p,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF12215F),
        ),
      ),
    );
  }
}


