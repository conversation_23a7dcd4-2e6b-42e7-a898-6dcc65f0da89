// ignore_for_file: must_be_immutable

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:a3g/core/utils/window_util.dart';
import 'package:seasetting/seasetting.dart';
import '../../../generated/l10n.dart';
import 'gradientView.dart';


class ReaderEletricSSCardView extends StatefulWidget {
  ReaderEletricSSCardView(this.loginType, {required this.onSubmitted, Key? key})
      : super(key: key);
  AuthLoginType loginType;
  ValueChanged<String>? onSubmitted;

  @override
  State<ReaderEletricSSCardView> createState() =>
      _ReaderEletricSSCardViewState();
}

class _ReaderEletricSSCardViewState extends State<ReaderEletricSSCardView> {
  TextEditingController ctr = TextEditingController();
  FocusNode focus = FocusNode();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    focus.requestFocus();

    focus.addListener(focusListener);
  }

  focusListener() {
    if (!focus.hasFocus) {
      focus.requestFocus();
    }
  }

  onSubmitted(String value) {
    print(value);
    widget.onSubmitted?.call(value.trim());
    ctr.clear();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    focus.removeListener(focusListener);
  }

  String getIcon(AuthLoginType loginType) {
    if (widget.loginType == AuthLoginType.eletricSocialSecurityCard) {
      return 'images/common/刷社保卡.gif';
    } else if (widget.loginType == AuthLoginType.wechatScanQRCode) {
      return 'images/common/扫码认证.gif';
    } else if (widget.loginType == AuthLoginType.huiwenQRCode) {
      return 'images/common/扫码认证.gif';
    } else if (widget.loginType == AuthLoginType.wechatQRCode) {
      return 'images/common/微信二维码.gif';
    } else if (widget.loginType == AuthLoginType.aliCreditQRCode) {
      return 'images/common/支付宝芝麻信用.gif';
    } else if (widget.loginType == AuthLoginType.shangHaiQRCode) {
      return 'images/common/随申码.gif';
    } else if (widget.loginType == AuthLoginType.readerQRCode) {
      return 'images/common/读者二维码.gif';
    } else if (widget.loginType == AuthLoginType.alipayQRCode_credit) {
      return 'images/common/支付宝信用.gif';
    } else if (widget.loginType == AuthLoginType.wecharOrAlipay) {
      return 'images/common/微信支付宝.gif';
    } else {
      return 'images/common/扫码认证.gif';
    }
  }

  @override
  Widget build(BuildContext context) {
    String name = '';
    String subName = '';

    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => widget.loginType == AuthLoginTypeMap[element.type]).firstOrNull;

    if (widget.loginType == AuthLoginType.eletricSocialSecurityCard) {
      name = S.current.electronicSocialSecurityCard;
      subName = S.current.scan_e_social_card;
    } else if (widget.loginType == AuthLoginType.wechatScanQRCode) {
      name = S.current.wechat_qr_code;
      subName = S.current.scan_wechat_qr;
    }else if (widget.loginType == AuthLoginType.huiwenQRCode){
      name = S.current.huiwen_qr_code;
      subName = S.current.scan_huiwen_qr;
    }

    return ListView(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Consumer<SettingProvider>(
                builder: (context, setting, child) {
                  if (setting.getTheme() == ThemeType.child) {
                    return Padding(padding: EdgeInsets.symmetric(vertical: 30.p), child: Text(
                      (titleData?.title.isNotEmpty??false) ? titleData!.title : '$name${S.current.spaceLogin}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                        fontSize: 48.p,
                      ),
                    ),);
                  }
                  return Padding(padding: EdgeInsets.symmetric(vertical: 30.p), child: Text(
                    (titleData?.title.isNotEmpty??false) ? titleData!.title :  '$name${S.current.spaceLogin}',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: BPUtils.c_ff12215F,
                      fontWeight: FontWeight.w600,
                      fontSize: 48.p,
                    ),
                  ),);
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 20.p),
        Row(
          children: [
            SizedBox(
              width: 0,
              height: 0,
              child: TextField(
                focusNode: focus,
                controller: ctr,
                onSubmitted: onSubmitted,
              ),
            )
          ],
        ),
        // SizedBox(height: 134.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Consumer2<CurrentLayoutProvider, SettingProvider>(
              builder: (context, layoutProvider, settingProvider, child) {
                if (settingProvider.getTheme() == ThemeType.child) {
                  return Flexible(
                      child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 40.p,
                      vertical: 7.p,
                    ),
                    decoration: BoxDecoration(
                      color: BPUtils.c_FFCCEFFF,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.p),
                        topRight: Radius.circular(16.p),
                        bottomRight: Radius.circular(16.p),
                        bottomLeft: Radius.circular(4.p),
                      ),
                    ),
                    child: Text(
                      (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle : subName,
                      style: TextStyle(
                        color: BPUtils.c_FF1481D7,
                        fontWeight: FontWeight.normal,
                        fontSize: 28.p,
                      ),
                    ),
                  ));
                } else if (settingProvider.getTheme() == ThemeType.ximalaya) {
                  return Flexible(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 40.p),
                        child: GradientView(
                        radius: BorderRadius.only(
                          topLeft: Radius.circular(20.p),
                          topRight: Radius.circular(20.p),
                          bottomLeft: Radius.circular(4.p),
                          bottomRight: Radius.circular(20.p),
                        ),
                        shadowColor: Colors.transparent,
                        stops: const [0, 0.5, 1],
                        colors: const [
                          BPUtils.c_FFBBFBFF,
                          BPUtils.c_FFAFDAFF,
                          BPUtils.c_FFC3CAFF,
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 5.p,
                              horizontal: 35.p),
                          child: Text(
                            (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle : '${S.current.place}$name${S.current.at}$name${S.current.sensingArea}',
                            style: TextStyle(
                              color: BPUtils.c_FF1D62FD,
                              fontWeight: FontWeight.normal,
                              fontSize: 28.p,
                            ),
                          ),
                        ),
                      ),));
                } else {
                  return Flexible(
                      child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 40.p,
                      vertical: 7.p,
                    ),
                    decoration: BoxDecoration(
                      color: BPUtils.c_FFB7CDFF,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.p),
                        topRight: Radius.circular(16.p),
                        bottomRight: Radius.circular(16.p),
                        bottomLeft: Radius.circular(4.p),
                      ),
                    ),
                    child: Text(
                      (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle : subName,
                      style: TextStyle(
                        color: BPUtils.c_FF1D62FD,
                        fontWeight: FontWeight.normal,
                        fontSize: 28.p,
                      ),
                    ),
                  ));
                }
              },
            ),
          ],
        ),
        SizedBox(height: 40.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Consumer<SettingProvider>(builder: (conetxt, setting, child) {
              SysConfigData? sysData = setting.options?.options.where((element) => element.name == '机器型号').firstOrNull;
              print(sysData);
              String imageName = 'images/common/扫码动画.gif';
              if (sysData?.data.name == 'SCH-A4'){
                imageName = 'images/common/扫码动画_A4.gif';
              }
              Widget child = Image.asset(
                imageName,
                width: 760.p,
                height: 500.p,
                key: const Key('images/common/扫码动画.gif'),
              );
              // if (setting.getTheme() == ThemeType.child) {
              //   return ChildThemeYellowBorder(child: child);
              // }
              return child;
            }),
          ],
        ),
      ],
    );
  }
}