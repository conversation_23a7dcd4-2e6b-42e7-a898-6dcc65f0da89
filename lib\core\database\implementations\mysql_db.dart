import 'dart:async';
import 'dart:convert';
import 'package:base_package/base_package.dart';
import 'package:intl/intl.dart';
import 'package:mysql1/mysql1.dart';
import 'package:seasetting/seasetting.dart';
import 'package:seasetting/setting/mini_smart_library/models/cabinet.dart';
import '../../../features/admin/models/error.dart';
import '../../models/book.dart';
import '../../models/slot.dart';
import '../interfaces/db_interface.dart';
import 'sqlite_db.dart';

class MysqlDB implements DBInterface {
  MySqlConnection? _connection;
  static MysqlDB? _instance;
  final int _timeInterval = 3600; // 1 hour
  Timer? _heartbeatTimer;

  MysqlDB._();

  static Future<MysqlDB> instance() async {
    if (_instance == null) {
      _instance = MysqlDB._();
      await _instance!._init();
    }
    return _instance!;
  }

  String books_sql = """
CREATE TABLE IF NOT EXISTS books (
  barcode VARCHAR(50) PRIMARY KEY,       -- 书籍条形码（主键）
  uid VARCHAR(50),                       -- 书籍唯一标识符
  title TEXT,                           -- 书籍标题
  author VARCHAR(100),                   -- 作者
  isbn VARCHAR(20),                      -- 国际标准书号（ISBN）
  epc VARCHAR(50),                       -- RFID 标签 EPC
  tid VARCHAR(50),                       -- 标签 ID
  call_number VARCHAR(50),               -- 索书号
  image_url TEXT,                       -- 封面图片 URL
  price VARCHAR(20),                     -- 书籍价格
  circulation_type VARCHAR(10),          -- 流通类型
  permanent_location VARCHAR(100),        -- 永久存放位置
  current_location VARCHAR(100),         -- 当前存放位置
  publisher VARCHAR(100),                -- 出版社
  pages VARCHAR(10),                     -- 页数
  subject VARCHAR(100),                  -- 主题或分类
  extra TEXT,                           -- 额外信息
  slot_no VARCHAR(20),                   -- 格口编号（如：A1-1-101）
  status VARCHAR(20),                    -- 状态（在馆/借出/损坏等）
  upload VARCHAR(5),                     -- 是否已上传到系统
  created_at VARCHAR(50),                -- 创建时间
  updated_at VARCHAR(50),                -- 更新时间
  reservation VARCHAR(5),                -- 是否预约（Y N）
  INDEX idx_slot_no (slot_no),           -- 格口号索引
  INDEX idx_circulation_type (circulation_type)  -- 流通类型索引
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
""";

  String slots_sql = """
CREATE TABLE IF NOT EXISTS slots (
  cabinet_no VARCHAR(50),                   -- 书柜编号（如 A1）
  shelf_no VARCHAR(50),                     -- 书架编号（如 A1-1）
  slot_no VARCHAR(50),                      -- 格口编号（1~120）
  barcode VARCHAR(255),                     -- 书籍条形码（外键，指向 books 表）
  status VARCHAR(20),                       -- 格口状态（0:空、1:占用、2:禁用）
  slot_type VARCHAR(20),                    -- 格口类型（0:普通区、1:预约区）
  pickupCode VARCHAR(50),                   -- 格口的取书码(默认空串)
  lock_configs JSON,                        -- 门锁配置(JSON数组) [{cardType, serialPort}, ...]
  light_configs JSON,                       -- 灯配置(JSON数组) [{cardType, serialPort}, ...]
  reader_configs JSON,                      -- 阅读器配置(JSON数组) [{cardType, serialPort, decoder, antennas}, ...]
  lastChecked VARCHAR(50),                  -- 最后检查时间
  lastOperator VARCHAR(50),                 -- 最后操作人员
  created_at VARCHAR(50),                   -- 创建时间
  updated_at VARCHAR(50),                   -- 更新时间
  PRIMARY KEY (cabinet_no, slot_no),
  FOREIGN KEY (barcode) REFERENCES books(barcode),
  INDEX idx_status (status),
  INDEX idx_barcode (barcode)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
""";

  String cabinets_sql = """
  CREATE TABLE IF NOT EXISTS cabinets (
    cabinet_no VARCHAR(50) PRIMARY KEY,
    cabinet_name VARCHAR(255),
    status VARCHAR(50),
    sort_order INTEGER,
    created_at VARCHAR(50),
    updated_at VARCHAR(50),
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
  """;

  String settings_sql = """
CREATE TABLE IF NOT EXISTS app_settings (
  `key` VARCHAR(255) PRIMARY KEY,
  `value` TEXT,
  `type` VARCHAR(50),
  `description` TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
""";

  String error_logs_sql = """
    CREATE TABLE IF NOT EXISTS error_logs (
      id INT AUTO_INCREMENT PRIMARY KEY,
      slot_no VARCHAR(20),
      book_title VARCHAR(255),
      error_reason TEXT,
      remark TEXT,
      status VARCHAR(20),
      operator VARCHAR(100),                -- 添加操作者字段
      created_at VARCHAR(50),
      updated_at VARCHAR(50)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  """;

  String access_logs_sql = """
  CREATE TABLE IF NOT EXISTS access_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,             -- 用户姓名
    reader_card_no VARCHAR(50) NOT NULL,    -- 读者证号
    open_time VARCHAR(50) NOT NULL,         -- 开门时间
    auth_method VARCHAR(50),                -- 认证方式(face, id_card, qr_code等)
    status VARCHAR(20) NOT NULL DEFAULT 'success',  -- 认证状态
    remark TEXT,                            -- 备注
    created_at VARCHAR(50),                 -- 记录创建时间
    INDEX idx_open_time (open_time)         -- 添加时间索引
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  """;

  Future<void> _init() async {
    try {
      _connection = await _connectMysql();
      await _createTables(books_sql);
      await _createTables(slots_sql);
      await _createTables(cabinets_sql);
      await _createTables(settings_sql);
      await _createTables(error_logs_sql);
      await _createTables(access_logs_sql);
      await checkAndSetDefaultBookCount();
      _startHeartBeat();
    } catch (e) {
      throw Exception('Failed to initialize MySQL connection: $e');
    }
  }

  static Future<MySqlConnection> _connectMysql() async {
    var settings = ConnectionSettings(
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      db: 'sea_a3g',
    );
    return await MySqlConnection.connect(settings);
  }

  Future<void> _createTables(String sql) async {
    await _connection?.query(sql);
  }

  @override
  Future<DBResult<void>> deleteDatabase() async {
    try {
      // 对于 MySQL，我们通常是删除所有表而不是数据库
      final tables = ['books', 'slots', 'cabinets'];

      await _connection?.query('SET FOREIGN_KEY_CHECKS = 0');

      for (var table in tables) {
        await _connection?.query('DROP TABLE IF EXISTS $table');
      }

      await _connection?.query('SET FOREIGN_KEY_CHECKS = 1');

      print('MySQL 数据表已删除');
      // 2. 重新创建表结构
      await _createTables(books_sql);
      await _createTables(slots_sql);
      await _createTables(cabinets_sql);
      await _createTables(settings_sql);
      await _createTables(error_logs_sql);
      await _createTables(access_logs_sql);
      print('MySQL 数据表已重新创建');
      return DBResult.success();
    } catch (e) {
      return DBResult.error('删除 MySQL 数据表失败: $e');
    }
  }

  @override
  Future<void> close() async {
    await _connection?.close();
    _heartbeatTimer?.cancel();
    _instance = null;
  }

  void _startHeartBeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer =
        Timer.periodic(Duration(seconds: _timeInterval), (timer) async {
      try {
        await _connection?.query('SELECT 1');
      } catch (e) {
        print('MySQL heartbeat failed: $e');
        await _reconnect();
      }
    });
  }

  Future<void> _reconnect() async {
    try {
      await _connection?.close();
      _connection = await _connectMysql();
    } catch (e) {
      print('MySQL reconnection failed: $e');
    }
  }

  @override
  Future<DBResult<List<Map<String, dynamic>>>> getAllCabinets() async {
    try {
      var results = await _connection?.query('''
        SELECT c.*,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no 
           AND s.status = '1') as occupied_slots,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no) as total_slots
        FROM cabinets c
        ORDER BY cabinet_no
      ''');

      final cabinets = results?.map((row) => row.fields).toList() ?? [];
      return DBResult.success(cabinets);
    } catch (e) {
      return DBResult.error('获取书柜列表失败: $e');
    }
  }

  @override
  Future<DBResult<void>> addNewCabinet({
    required String cabinetNo,
    String? cabinetName,
    required int sortOrder,
  }) async {
    try {
      await _connection?.query('START TRANSACTION');

      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      await _connection?.query('''
        INSERT INTO cabinets (
          cabinet_no, cabinet_name, status, sort_order, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?)
      ''', [cabinetNo, cabinetName, CabinetConfig.cabinetOnline, sortOrder, now, now]);

      await initializeCabinetSlots(cabinetNo);

      await _connection?.query('COMMIT');
      return DBResult.success();
    } catch (e) {
      await _connection?.query('ROLLBACK');
      return DBResult.error('添加书柜失败: $e');
    }
  }

  @override
  Future<DBResult<void>> updateCabinetStatus(
      String cabinetNo, String status) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      await _connection?.query('''
        UPDATE cabinets 
        SET status = ?,
            updated_at = ?
        WHERE cabinet_no = ?
      ''', [status, now, cabinetNo]);

      return DBResult.success();
    } catch (e) {
      return DBResult.error('更新书柜状态失败: $e');
    }
  }

  @override
  Future<DBResult<void>> removeCabinet(String cabinetNo) async {
    try {
      await _connection?.query('START TRANSACTION');

      await _connection
          ?.query('DELETE FROM slots WHERE cabinet_no = ?', [cabinetNo]);

      await _connection
          ?.query('DELETE FROM cabinets WHERE cabinet_no = ?', [cabinetNo]);

      await _connection?.query('COMMIT');
      return DBResult.success();
    } catch (e) {
      await _connection?.query('ROLLBACK');
      return DBResult.error('移除书柜失败: $e');
    }
  }

  /// 初始化指定书柜的所有格口
  @override
  Future<DBResult<void>> initializeCabinetSlots(String cabinetNo) async {
    final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    
    try {
      // 准备批量插入的值
      List<List<dynamic>> values = [];
      
      // 遍历每层书架 (1-5层)
      for (int shelf = 1; shelf <= CabinetConfig.shelfPerCabinet; shelf++) {
        // 当前层的书架编号 (如: A1-1)
        final shelfNo = '$cabinetNo-$shelf';

        // 遍历每层的格口 (24个格口)
        for (int slot = 1; slot <= CabinetConfig.slotsPerShelf; slot++) {
          // 格口编号 (如: A1-1-101)
          final slotNoStr = '$cabinetNo-$shelf-${(shelf * 100 + slot).toString().padLeft(3, '0')}';

          // 初始化默认配置
          final defaultConfigs = {
            'lock_configs': jsonEncode([]),
            'light_configs': jsonEncode([]),
            'reader_configs': jsonEncode([]),
          };

          values.add([
            cabinetNo,                    // 书柜编号 (A1)
            shelfNo,                      // 书架编号 (A1-1)
            slotNoStr,                    // 格口编号 (A1-1-101)
            '',                           // 书籍条码（初始为空）
            CabinetConfig.slotEmpty,      // 格口状态（初始为空）
            '0',                          // 格口类型（0:普通区）
            '',                           // 取书码（初始为空）
            defaultConfigs['lock_configs'],    
            defaultConfigs['light_configs'],   
            defaultConfigs['reader_configs'],  
            now,                          
            'system',                     
            now,                          
            now,                          
          ]);
        }
      }

      // 批量插入数据
      await _connection?.queryMulti('''
      INSERT IGNORE INTO slots (
        cabinet_no, shelf_no, slot_no, barcode, status, 
        slot_type, pickupCode, lock_configs, light_configs, reader_configs,
        lastChecked, lastOperator, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', values);

      return DBResult.success();
    } catch (e) {
      print('初始化格口失败: $e');
      return DBResult.error('初始化格口失败: $e');
    }
  }

  @override
  Future<DBResult<bool>> verifyCabinetSlots(String cabinetNo) async {
    try {
      var results = await _connection?.query('''
        SELECT COUNT(*) as count 
        FROM slots 
        WHERE cabinet_no = ?
      ''', [cabinetNo]);

      final count = results?.first.fields['count'] as int;
      final expectedCount =
          CabinetConfig.shelfPerCabinet * CabinetConfig.slotsPerShelf;

      if (count != expectedCount) {
        return DBResult.error('格口数量不正确: 预期 $expectedCount, 实际 $count');
      }

      return DBResult.success(true);
    } catch (e) {
      return DBResult.error('验证格口失败: $e');
    }
  }

  @override
  Future<DBResult<List<Map<String, dynamic>>>> getCabinetSlots(String cabinetNo) async {
    try {
      final results = await _connection?.query('''
      SELECT 
        s.cabinet_no,
        s.shelf_no,
        s.slot_no,
        s.status as slot_status,
        s.slot_type,
        s.pickupCode,
        s.lock_configs,
        s.light_configs,
        s.reader_configs,
        s.lastChecked,
        s.lastOperator,
        s.created_at,
        s.updated_at,
        b.barcode,
        b.title as book_title,
        b.author as book_author,
        b.isbn as book_isbn,
        b.call_number,
        b.image_url,
        b.permanent_location,
        b.current_location,
        b.status as book_status,
        b.reservation as book_reservation
      FROM slots s
      LEFT JOIN books b ON s.barcode = b.barcode
      WHERE s.cabinet_no = ?
      ORDER BY CAST(s.slot_no AS UNSIGNED) ASC
    ''', [cabinetNo]);

      if (results == null || results.isEmpty) {
        print('No slots found for cabinet: $cabinetNo');
        return DBResult.success([]);
      }

      final slots = results.map((row) {
        final map = Map<String, dynamic>.from(row.fields);

        // 解析配置 JSON
        try {
          if (map['lock_configs'] != null) {
            map['lock_configs'] = jsonDecode(map['lock_configs']);
          }
          if (map['light_configs'] != null) {
            map['light_configs'] = jsonDecode(map['light_configs']);
          }
          if (map['reader_configs'] != null) {
            map['reader_configs'] = jsonDecode(map['reader_configs']);
          }
        } catch (e) {
          print('JSON parsing error: $e');
        }

        // 添加计算字段
        map['is_empty'] = map['slot_status'] == CabinetConfig.slotEmpty;
        map['is_occupied'] = map['slot_status'] == CabinetConfig.slotOccupied;
        map['is_disabled'] = map['slot_status'] == CabinetConfig.slotDisabled;

        // 如果有书籍信息，整理到 book 对象中
        if (map['barcode'] != null) {
          map['book'] = {
            'barcode': map['barcode'],
            'title': map['book_title'],
            'author': map['book_author'],
            'isbn': map['book_isbn'],
            'call_number': map['call_number'],
            'image_url': map['image_url'],
            'permanent_location': map['permanent_location'],
            'current_location': map['current_location'],
            'status': map['book_status'],
            'reservation': map['book_reservation'] ?? 'N'
          };
        }

        // 删除重复的字段
        ['book_title', 'book_author', 'book_isbn', 'call_number', 'image_url', 
         'permanent_location', 'current_location', 'book_status', 'book_reservation'].forEach((key) {
          map.remove(key);
        });

        print('Slot data: $map');  // 调试输出
        return map;
      }).toList();

      return DBResult.success(slots);
    } catch (e) {
      print('Error getting slots: $e');  // 调试输出
      return DBResult.error('获取格口信息失败: $e');
    }
  }

  @override
  Future<DBResult<Map<String, dynamic>>> getSlotConfig(String slotNo) async {
    try {
      var results = await _connection?.query('''
        SELECT s.*, c.cabinet_name, c.status as cabinet_status
        FROM slots s
        LEFT JOIN cabinets c ON s.cabinet_no = c.cabinet_no
        WHERE s.slot_no = ?
      ''', [slotNo]);

      if (results?.isEmpty ?? true) {
        return DBResult.error('格口不存在');
      }

      return DBResult.success(results!.first.fields);
    } catch (e) {
      return DBResult.error('获取格口配置失败: $e');
    }
  }

  @override
  Future<DBResult<void>> updateSlotConfig(
      {required String slotNo, required Map<String, String> config}) async {
    try {
      await _connection?.query('START TRANSACTION');

      var setFields = config.keys.map((key) => '$key = ?').join(', ');
      var values = [...config.values, DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()), slotNo];

      await _connection?.query('''
        UPDATE slots 
        SET $setFields, updated_at = ?
        WHERE slot_no = ?
      ''', values);

      await _connection?.query('COMMIT');
      return DBResult.success();
    } catch (e) {
      await _connection?.query('ROLLBACK');
      return DBResult.error('更新格口配置失败: $e');
    }
  }

  @override
  Future<DBResult<List<Book>>> queryBooksByShelfNo(String shelfNo) async {
    try {
      final results = await _connection?.query('''
        SELECT DISTINCT
          b.*,
          s.slot_no,
          s.status as slot_status,
          s.pickupCode,
          s.slot_type,
          b.reservation as book_reservation
        FROM slots s
        LEFT JOIN books b ON s.slot_no = b.slot_no
        WHERE s.shelf_no = ?
          AND s.status = ?
          AND b.barcode IS NOT NULL
        ORDER BY s.slot_no ASC
      ''', [
        shelfNo,
        CabinetConfig.slotOccupied,
      ]);

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final books = results.map((row) {
        final Map<String, dynamic> bookData = {
          'barcode': row.fields['barcode'],
          'title': row.fields['title'],
          'author': row.fields['author'],
          'isbn': row.fields['isbn'],
          'call_number': row.fields['call_number'],
          'image_url': row.fields['image_url'],
          'circulation_type': row.fields['circulation_type'],
          'permanent_location': row.fields['permanent_location'],
          'current_location': row.fields['current_location'],
          'status': row.fields['status'],
          'slot_no': row.fields['slot_no'],
          'slot_status': row.fields['slot_status'],
          'slot_type': row.fields['slot_type'],
          'pickupCode': row.fields['pickupCode'],
          'reservation': row.fields['book_reservation'] ?? 'N'
        };
        return Book.fromJson(bookData);
      }).toList();

      return DBResult.success(books);
    } catch (e) {
      print('查询书架书籍失败: $e');
      return DBResult.error('查询书架书籍失败: $e');
    }
  }

  /// 更新格口配置
  @override
  Future<DBResult<void>> updateSlotConfigs({
    required String cabinetNo,
    required String slotNo,
    List<LockConfig>? lockConfigs,
    List<LightConfig>? lightConfigs,
    List<ReaderConfig>? readerConfigs,
    String? type,
    String? status,
  }) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      // 1. 检查格口是否存在
      final checkResult = await _connection?.query(
          'SELECT 1 FROM slots WHERE cabinet_no = ? AND slot_no = ?',
          [cabinetNo, slotNo]
      );

      if (checkResult == null || checkResult.isEmpty) {
        return DBResult.error('格口不存在');
      }

      // 2. 转换配置为 JSON 字符串
      String? lockConfigsJson;
      if (lockConfigs != null) {
        final List<Map<String, dynamic>> configs = lockConfigs.map((c) => c.toJson()).toList();
        lockConfigsJson = jsonEncode(configs);
      }

      String? lightConfigsJson;
      if (lightConfigs != null) {
        final List<Map<String, dynamic>> configs = lightConfigs.map((c) => c.toJson()).toList();
        lightConfigsJson = jsonEncode(configs);
      }

      String? readerConfigsJson;
      if (readerConfigs != null) {
        final List<Map<String, dynamic>> configs = readerConfigs.map((c) => c.toJson()).toList();
        readerConfigsJson = jsonEncode(configs);
      }

      // 3. 开始事务
      await _connection?.query('START TRANSACTION');

      // 4. 更新数据库
      await _connection?.query('''
      UPDATE slots 
      SET 
        lock_configs = COALESCE(?, lock_configs),
        light_configs = COALESCE(?, light_configs),
        reader_configs = COALESCE(?, reader_configs),
        slot_type = COALESCE(?, slot_type),
        updated_at = ?,
        lastOperator = ?
      WHERE cabinet_no = ? AND slot_no = ?
    ''', [
        lockConfigsJson,
        lightConfigsJson,
        readerConfigsJson,
        type,
        now,
        'system',  // 操作人员
        cabinetNo,
        slotNo,
      ]);

      // 5. 提交事务
      await _connection?.query('COMMIT');

      print('格口配置更新成功: $cabinetNo-$slotNo');
      return DBResult.success();
    } catch (e) {
      // 6. 回滚事务
      await _connection?.query('ROLLBACK');
      print('更新格口配置失败: $e');
      return DBResult.error('更新格口配置失败: $e');
    }
  }

  /// 获取格口配置
  @override
  Future<DBResult<SlotConfig>> getSlotConfigs(String cabinetNo, String slotNo) async {
    try {
      // 1. 查询格口配置
      final results = await _connection?.query('''
      SELECT 
        lock_configs,
        light_configs,
        reader_configs,
        status,
        slot_type,
        lastChecked,
        lastOperator,
        updated_at
      FROM slots 
      WHERE cabinet_no = ? AND slot_no = ?
    ''', [cabinetNo, slotNo]);

      if (results == null || results.isEmpty) {
        return DBResult.error('格口不存在');
      }

      final row = results.first;

      // 2. 解析锁配置
      List<LockConfig>? lockConfigs;
      if (row['lock_configs'] != null) {
        final List<dynamic> json = jsonDecode(row['lock_configs']);
        lockConfigs = json.map((j) => LockConfig.fromJson(j)).toList();
      }

      // 3. 解析灯配置
      List<LightConfig>? lightConfigs;
      if (row['light_configs'] != null) {
        final List<dynamic> json = jsonDecode(row['light_configs']);
        lightConfigs = json.map((j) => LightConfig.fromJson(j)).toList();
      }

      // 4. 解析读写器配置
      List<ReaderConfig>? readerConfigs;
      if (row['reader_configs'] != null) {
        final List<dynamic> json = jsonDecode(row['reader_configs']);
        readerConfigs = json.map((j) => ReaderConfig.fromJson(j)).toList();
      }

      // 5. 创建配置对象
      final config = SlotConfig(
        lockConfigs: lockConfigs,
        lightConfigs: lightConfigs,
        readerConfigs: readerConfigs,
        type: row['slot_type'] as String?,
      );

      print('获取格口配置成功: $cabinetNo-$slotNo');
      print('Lock configs: ${lockConfigs?.length ?? 0}');
      print('Light configs: ${lightConfigs?.length ?? 0}');
      print('Reader configs: ${readerConfigs?.length ?? 0}');
      print('Slot type: ${row['slot_type']}');

      return DBResult.success(config);
    } catch (e) {
      print('获取格口配置失败: $e');
      return DBResult.error('获取格口配置失败: $e');
    }
  }

  /// 检查并设置默认书箱数量
  @override
  Future<DBResult<int>> checkAndSetDefaultBookCount() async {
    try {
      // 1. 检查是否已存在配置
      final results = await _connection?.query(
        'SELECT value FROM app_settings WHERE `key` = ?',
        ['cabinet_book_count']
      );

      if (results == null || results.isEmpty) {
        // 2. 不存在则插入默认值
        const defaultCount = 0;  // 默认书箱容量
        await _connection?.query('''
          INSERT INTO app_settings (`key`, value, type, description)
          VALUES (?, ?, ?, ?)
        ''', [
          'cabinet_book_count',
          defaultCount.toString(),
          'number',
          '书箱最大可存放书籍数量'
        ]);

        print('已设置默认书箱数量: $defaultCount');
        return DBResult.success(defaultCount);
      }

      // 3. 已存在则返回当前值
      final count = int.parse(results.first['value']);
      print('当前书箱数量设置: $count');
      return DBResult.success(count);
    } catch (e) {
      print('检查书箱数量设置失败: $e');
      return DBResult.error('检查书箱数量设置失败: $e');
    }
  }

  /// 重置书箱书籍数量
  @override
  Future<DBResult<void>> setBookBoxCount(int newCount) async {
    try {
      await _connection?.query('''
        UPDATE app_settings 
        SET value = ? 
        WHERE `key` = 'cabinet_book_count'
      ''', [newCount.toString()]);

      print('已重置书箱数量为: $newCount');
      return DBResult.success();
    } catch (e) {
      print('重置书箱数量失败: $e');
      return DBResult.error('重置书箱数量失败: $e');
    }
  }

  /// 检查是否超过最大数量
  @override
  Future<DBResult<bool>> isExceedMaxCount(int currentCount) async {
    try {
      final results = await _connection?.query(
        'SELECT value FROM app_settings WHERE `key` = ?',
        ['cabinet_book_count']
      );

      if (results == null || results.isEmpty) {
        return DBResult.error('未找到书箱数量配置');
      }

      final maxCount = int.parse(results.first['value']);
      return DBResult.success(currentCount >= maxCount);
    } catch (e) {
      print('检查书箱数量失败: $e');
      return DBResult.error('检查书箱数量失败: $e');
    }
  }

  /// 当前数量自增1
  @override
  Future<DBResult<int>> incrementBookCount() async {
    try {
      // 1. 获取当前值
      final results = await _connection?.query(
        'SELECT value FROM app_settings WHERE `key` = ?',
        ['current_book_count']
      );

      int currentCount = 0;
      if (results != null && results.isNotEmpty) {
        currentCount = int.parse(results.first['value']);
      }

      // 2. 自增并更新
      currentCount++;
      
      await _connection?.query('''
        INSERT INTO app_settings (`key`, value, type, description)
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE value = ?
      ''', [
        'current_book_count',
        currentCount.toString(),
        'number',
        '当前书箱中的书籍数量',
        currentCount.toString()
      ]);

      print('当前书籍数量: $currentCount');
      return DBResult.success(currentCount);
    } catch (e) {
      print('更新书籍数量失败: $e');
      return DBResult.error('更新书籍数量失败: $e');
    }
  }

  /// 获取正常状态的所有书柜
  @override
  Future<DBResult<List<Cabinet>>> getActiveCabinets() async {
    try {
      final results = await _connection?.query('''
        SELECT 
          c.*,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no 
           AND s.status = '1') as occupied_slots,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no) as total_slots
        FROM cabinets c
        WHERE c.status = '1'  -- 1表示在线状态
        ORDER BY c.cabinet_no ASC
      ''');

      if (results == null || results.isEmpty) {
        print('No active cabinets found');
        return DBResult.success([]);
      }

      final cabinets = results.map((row) => 
        Cabinet.fromJson(Map<String, dynamic>.from(row.fields))
      ).toList();

      return DBResult.success(cabinets);
    } catch (e) {
      print('获取正常书柜失败: $e');
      return DBResult.error('获取正常书柜失败: $e');
    }
  }


  @override
  Future<DBResult<void>> addErrorRecord(ErrorRecord record) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      await _connection?.query('''
        INSERT INTO error_logs (
          slot_no, book_title, error_reason, remark, status, operator, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      ''', [
        record.slotNo ?? '',
        record.bookTitle ?? '',
        record.errorReason ?? '',
        record.remark ?? '',
        record.status ?? '0',  // 默认未处理
        record.operator ?? 'system',
        now,
        now
      ]);

      return DBResult.success();
    } catch (e) {
      return DBResult.error('添加错误日志失败: $e');
    }
  }

  /// 更新错误记录状态
  @override
  Future<DBResult<void>> updateErrorRecordStatus({
    required int id,
    required String status,
    String? operator,
  }) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      await _connection?.query('''
        UPDATE error_logs 
        SET status = ?,
            operator = ?,
            updated_at = ?
        WHERE id = ?
      ''', [
        status,
        operator ?? 'system',
        now,
        id,
      ]);

      return DBResult.success();
    } catch (e) {
      return DBResult.error('更新错误记录状态失败: $e');
    }
  }

  /// 获取所有错误记录（可选状态筛选）
  @override
  Future<DBResult<List<Map<String, dynamic>>>> getErrorRecords({String? status}) async {
    try {
      String sql = '''
        SELECT * FROM error_logs 
        ${status != null ? 'WHERE status = ?' : ''}
        ORDER BY created_at DESC
      ''';

      final results = await _connection?.query(
        sql,
        status != null ? [status] : null,
      );

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      return DBResult.success(
        results.map((row) => row.fields).toList(),
      );
    } catch (e) {
      return DBResult.error('获取错误记录失败: $e');
    }
  }
  /// 根据状态和类型获取格口
  Future<DBResult<List<Slot>>> getSlotsByStatusAndType({
    required String cabinetNo,
    String? status,
    String? slotType,
  }) async {
    try {
      // 构建查询条件
      List<String> conditions = ['s.cabinet_no = ?'];
      List<Object> params = [cabinetNo];
      
      if (status != null) {
        conditions.add('s.status = ?');
        params.add(status);
      }
      
      if (slotType != null) {
        conditions.add('s.slot_type = ?');
        params.add(slotType);
      }

      String whereClause = 'WHERE ${conditions.join(' AND ')}';

      final results = await _connection?.query('''
        SELECT 
          s.*,
          b.title as book_title,
          b.author as book_author,
          b.reservation as book_reservation
        FROM slots s
        LEFT JOIN books b ON s.barcode = b.barcode
        $whereClause
        ORDER BY s.slot_no ASC
      ''', params);

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final slots = results.map((row) {
        return Slot.fromJson(row.fields);
      }).toList();

      return DBResult.success(slots);
    } catch (e) {
      return DBResult.error('获取格口失败: $e');
    }
  }

  /// 获取所有书柜
  Future<DBResult<List<Cabinet>>> getCabinets() async {
    try {
      final results = await _connection?.query('''
        SELECT * FROM cabinets 
        WHERE status = '1'  -- 1表示正常状态
        ORDER BY cabinet_no ASC
      ''');

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final cabinets = results.map((row) => 
        Cabinet.fromJson(row.fields)
      ).toList();

      return DBResult.success(cabinets);
    } catch (e) {
      return DBResult.error('获取书柜列表失败: $e');
    }
  }

  /// 保存书籍信息到 books 表，并更新格口的 barcode
  @override
  Future<DBResult<void>> saveBookAndUpdateSlot({
    required String barcode,
    required String slotNo,
    required String status,
    required Book bookInfo,
  }) async {
    try {
      // 开始事务
      await _connection?.query('START TRANSACTION');

      // 检查书籍是否已存在
      final existingBook = await getBookByBarcode(barcode);
      if (existingBook != null) {
        // 如果书籍已存在，更新书籍信息
        throw Exception('该条码已存在，请处理');
      } else {
        // 如果书籍不存在，插入新书籍信息
        await insertBookItem(barcode, bookInfo);
      }

      // 更新格口的 barcode
      await updateSlotBarcode(slotNo, barcode,status);

      // 提交事务
      await _connection?.query('COMMIT');
      return DBResult.success();
    } catch (e) {
      // 回滚事务
      await _connection?.query('ROLLBACK');
      print('保存书籍信息并更新格口失败: $e');
      return DBResult.error('保存书籍信息并更新格口失败: $e');
    }
  }

  /// 获取书籍信息
  Future<Map<String, dynamic>?> getBookByBarcode(String barcode) async {
    final results = await _connection?.query('SELECT * FROM books WHERE barcode = ?', [barcode]);
    return results?.isNotEmpty == true ? results?.first.fields : null;
  }

  /// 更新书籍信息
  Future<void> updateBookItem(String barcode, Book bookInfo) async {
    await _connection?.query('''
      UPDATE books SET 
        title = ?, 
        author = ?, 
        isbn = ?, 
        call_number = ?, 
        image_url = ?, 
        circulation_type = ?, 
        permanent_location = ?, 
        current_location = ?, 
        status = ?, 
        reservation = ?,
        updated_at = ?
      WHERE barcode = ?
    ''', [
      bookInfo.title,
      bookInfo.author,
      bookInfo.isbn,
      bookInfo.callno,
      bookInfo.media_type,
      bookInfo.circulation_type,
      bookInfo.permanent_location,
      bookInfo.current_location,
      bookInfo.status,
      bookInfo.reservation ?? 'N',
      DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
      barcode,
    ]);
  }

  /// 插入新书籍信息
  Future<void> insertBookItem(String barcode, Book bookInfo) async {
    bookInfo.barcode = barcode;
    await _connection?.query('''
      INSERT INTO books (
        barcode, title, author, isbn, call_number, 
        image_url, circulation_type, permanent_location, 
        current_location, status, reservation, created_at, updated_at
      ) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', [
      bookInfo.barcode,
      bookInfo.title,
      bookInfo.author,
      bookInfo.isbn,
      bookInfo.callno,
      bookInfo.media_type,
      bookInfo.circulation_type,
      bookInfo.permanent_location,
      bookInfo.current_location,
      bookInfo.reservation ?? 'N',
      DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
      DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
    ]);
  }

  /// 更新格口的 barcode
  Future<void> updateSlotBarcode(String slotNo, String barcode, String status) async {
    // 解析 slotNo 获取 cabinetNo 和 shelfNo
    final parts = slotNo.split('-');
    if (parts.length != 3) {
      throw Exception('Invalid slotNo format. Expected format: cabinetNo-shelfNo-slotNo');
    }
    final cabinetNo = parts[0];
    final shelfNo = parts[1];

    await _connection?.query('''
      UPDATE slots SET barcode = ?, status = ? WHERE cabinet_no = ? AND shelf_no = ? AND slot_no = ?
    ''', [barcode, status, cabinetNo, shelfNo, slotNo]);
  }

  @override
  Future<DBResult<List<Map<String, dynamic>>>> querySlotsByCabinetAndStatus(
      String cabinetNo, String status, String? slotType) async {
    try {
      List<String> conditions = ['s.cabinet_no = ?', 's.status = ?'];
      List<Object> params = [cabinetNo, status];
      
      if (slotType != null) {
        conditions.add('s.slot_type = ?');
        params.add(slotType);
      }

      String whereClause = 'WHERE ${conditions.join(' AND ')}';

      final results = await _connection?.query('''
      SELECT 
        s.slot_no AS slot_no, 
        s.barcode, 
        s.cabinet_no AS cabinet_no, 
        s.shelf_no AS shelf_no,
        s.status AS slot_status, 
        s.slot_type AS slot_type,
        s.pickupCode AS pickup_code,
        s.lock_configs AS lock_configs,
        s.reader_configs AS reader_configs,
        s.light_configs AS light_configs,
        b.barcode AS book_barcode, 
        b.title AS book_title, 
        b.author AS book_author,
        b.isbn AS book_isbn,
        b.image_url AS book_image_url,
        b.circulation_type AS book_circulation_type,
        b.permanent_location AS book_permanent_location,
        b.current_location AS book_current_location,
        b.status AS book_status,
        b.reservation AS book_reservation
      FROM slots s
      LEFT JOIN books b ON s.barcode = b.barcode
      $whereClause
      ORDER BY s.slot_no
    ''', params);

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      List<Map<String, dynamic>> slotsWithBooks = [];

      for (var row in results) {
        dynamic lockConfigs = [];
        dynamic readerConfigs = [];
        dynamic lightConfigs = [];
        
        try {
          if (row['lock_configs'] != null) {
            lockConfigs = jsonDecode(row['lock_configs']);
          }
          if (row['reader_configs'] != null) {
            readerConfigs = jsonDecode(row['reader_configs']);
          }
          if (row['light_configs'] != null) {
            lightConfigs = jsonDecode(row['light_configs']);
          }
        } catch (e) {
          print('JSON parsing error: $e');
        }
        
        Map<String, dynamic> slotInfo = {
          'slot_no': row['slot_no'],
          'barcode': row['barcode'],
          'cabinet_no': row['cabinet_no'],
          'shelf_no': row['shelf_no'],
          'slot_status': row['slot_status'],
          'slot_type': row['slot_type'],
          'pickup_code': row['pickup_code'],
          'lock_configs': lockConfigs,
          'reader_configs': readerConfigs,
          'light_configs': lightConfigs,
        };

        if (row['book_barcode'] != null) {
          slotInfo['book'] = {
            'barcode': row['book_barcode'],
            'title': row['book_title'],
            'author': row['book_author'],
            'isbn': row['book_isbn'],
            'image_url': row['book_image_url'],
            'circulation_type': row['book_circulation_type'],
            'permanent_location': row['book_permanent_location'],
            'current_location': row['book_current_location'],
            'status': row['book_status'],
            'reservation': row['book_reservation'] ?? 'N'
          };
        }

        slotsWithBooks.add(slotInfo);
      }

      return DBResult.success(slotsWithBooks);
    } catch (e) {
      print('查询格口信息失败: $e');
      return DBResult.error('查询格口信息失败: $e');
    }
  }

  /// 删除书籍信息并更新格口状态为空闲
  @override
  Future<DBResult<void>> delBookAndUpdateSlot({
    required String slotNo,
    required String barcode,
  }) async {
    try {
      // 开始事务
      await _connection?.query('START TRANSACTION');

      // 解析 slotNo 获取 cabinetNo 和 shelfNo
      final parts = slotNo.split('-');
      if (parts.length != 3) {
        throw Exception('Invalid slotNo format. Expected format: cabinetNo-shelfNo-slotNo');
      }
      final cabinetNo = parts[0];
      final shelfNo = parts[1];

      // 验证格口和书籍信息是否匹配
      final checkResults = await _connection?.query('''
        SELECT barcode 
        FROM slots 
        WHERE cabinet_no = ? AND shelf_no = ? AND slot_no = ? AND barcode = ?
      ''', [cabinetNo, shelfNo, slotNo, barcode]);

      if (checkResults == null || checkResults.isEmpty) {
        throw Exception('格口中没有找到对应的书籍');
      }

      // 删除书籍信息
      await _connection?.query(
        'DELETE FROM books WHERE barcode = ?',
        [barcode]
      );

      // 更新格口状态为空闲
      await _connection?.query('''
        UPDATE slots 
        SET barcode = '',
            status = ?,
            pickupCode = '',
            updated_at = ?
        WHERE cabinet_no = ? AND shelf_no = ? AND slot_no = ?
      ''', [
        CabinetConfig.slotEmpty,  // 设置格口状态为空闲
        DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
        cabinetNo,
        shelfNo,
        slotNo,
      ]);

      // 提交事务
      await _connection?.query('COMMIT');
      print('删除书籍信息成功');
      return DBResult.success();
    } catch (e) {
      // 回滚事务
      await _connection?.query('ROLLBACK');
      print('删除书籍信息失败: $e');
      return DBResult.error('删除书籍信息失败: $e');
    }
  }

  @override
  Future<DBResult<Map<String, dynamic>>> querySlot(String slotNo) async {
    try {
      final parts = slotNo.split('-');
      if (parts.length != 3) {
        return DBResult.error('格口号格式错误，应为: cabinetNo-shelfNo-slotNo');
      }
      final cabinetNo = parts[0];

      final results = await _connection?.query('''
        SELECT 
          s.slot_no,
          s.barcode,
          s.cabinet_no,
          s.shelf_no,
          s.status as slot_status,
          s.slot_type,
          s.pickupCode,
          s.lock_configs,
          s.reader_configs,
          s.light_configs,
          s.lastChecked,
          s.lastOperator,
          b.barcode as book_barcode,
          b.title as book_title,
          b.author as book_author,
          b.isbn as book_isbn,
          b.call_number as book_call_number,
          b.image_url as book_image_url,
          b.circulation_type as book_circulation_type,
          b.permanent_location as book_permanent_location,
          b.current_location as book_current_location,
          b.status as book_status,
          b.reservation as book_reservation
        FROM slots s
        LEFT JOIN books b ON s.barcode = b.barcode
        WHERE s.cabinet_no = ? AND s.slot_no = ?
      ''', [cabinetNo, slotNo]);

      if (results == null || results.isEmpty) {
        return DBResult.error('未找到该格口');
      }

      final row = results.first;
      
      Map<String, dynamic> slotInfo = {
        'slot_no': row['slot_no'],
        'barcode': row['barcode'],
        'cabinet_no': row['cabinet_no'],
        'shelf_no': row['shelf_no'],
        'slot_status': row['slot_status'],
        'slot_type': row['slot_type'],
        'pickup_code': row['pickupCode'],
        'lock_configs': row['lock_configs'] != null ? jsonDecode(row['lock_configs']) : [],
        'reader_configs': row['reader_configs'] != null ? jsonDecode(row['reader_configs']) : [],
        'light_configs': row['light_configs'] != null ? jsonDecode(row['light_configs']) : [],
        'last_checked': row['lastChecked'],
        'last_operator': row['lastOperator'],
      };

      if (row['book_barcode'] != null) {
        slotInfo['book'] = {
          'barcode': row['book_barcode'],
          'title': row['book_title'],
          'author': row['book_author'],
          'isbn': row['book_isbn'],
          'call_number': row['book_call_number'],
          'image_url': row['book_image_url'],
          'circulation_type': row['book_circulation_type'],
          'permanent_location': row['book_permanent_location'],
          'current_location': row['book_current_location'],
          'status': row['book_status'],
          'reservation': row['book_reservation'] ?? 'N',
        };
      }

      return DBResult.success(slotInfo);
    } catch (e) {
      print('查询格口信息失败: $e');
      return DBResult.error('查询格口信息失败: $e');
    }
  }

  @override
  Future<DBResult<List<Map<String, dynamic>>>> querySlotsByBarcode(String barcode) async {
    try {
      final results = await _connection?.query('''
      SELECT 
        s.slot_no AS slot_no, 
        s.cabinet_no AS cabinet_no, 
        s.status AS slot_status, 
        s.slot_type AS slot_type,
        s.pickupCode AS pickup_code,
        s.lock_configs AS lock_configs,
        s.reader_configs AS reader_configs,
        s.light_configs AS light_configs,
        b.barcode AS book_barcode, 
        b.title AS book_title, 
        b.author AS book_author,
        b.isbn AS book_isbn,
        b.image_url AS book_image_url,
        b.circulation_type AS book_circulation_type,
        b.permanent_location AS book_permanent_location,
        b.current_location AS book_current_location,
        b.status AS book_status,
        b.reservation AS book_reservation
      FROM slots s
      LEFT JOIN books b ON s.barcode = b.barcode
      WHERE s.barcode = ? 
    ''', [barcode]);

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      List<Map<String, dynamic>> slotsWithBooks = [];

      for (var row in results) {
        dynamic lockConfigs = [];
        dynamic readerConfigs = [];
        dynamic lightConfigs = [];

        try {
          if (row['lock_configs'] != null && row['lock_configs'] != '') {
            lockConfigs = jsonDecode(row['lock_configs']);
          }
          if (row['reader_configs'] != null && row['reader_configs'] != '') {
            readerConfigs = jsonDecode(row['reader_configs']);
          }
          if (row['light_configs'] != null && row['light_configs'] != '') {
            lightConfigs = jsonDecode(row['light_configs']);
          }
        } catch (e) {
          print('JSON parsing error: $e');
        }

        Map<String, dynamic> slotInfo = {
          'slot_no': row['slot_no'],
          'cabinet_no': row['cabinet_no'],
          'slot_status': row['slot_status'],
          'slot_type': row['slot_type'],
          'pickup_code': row['pickup_code'],
          'lock_configs': lockConfigs,
          'reader_configs': readerConfigs,
          'light_configs': lightConfigs,
          'book': {
            'barcode': row['book_barcode'],
            'title': row['book_title'],
            'author': row['book_author'],
            'isbn': row['book_isbn'],
            'image_url': row['book_image_url'],
            'circulation_type': row['book_circulation_type'],
            'permanent_location': row['book_permanent_location'],
            'current_location': row['book_current_location'],
            'status': row['book_status'],
            'reservation': row['book_reservation'] ?? 'N'
          }
        };

        if (row['book_barcode'] == null) {
          slotInfo.remove('book');
        }

        slotsWithBooks.add(slotInfo);
      }

      return DBResult.success(slotsWithBooks);
    } catch (e) {
      print('查询格口信息失败: $e');
      return DBResult.error('查询格口信息失败: $e');
    }
  }

  /// 查询空格口，尝试返回普通区(type=0)和预约区(type=1)各一个。
  /// 返回一个包含两个元素的列表，第一个为普通空格口，第二个为预约空格口。
  /// 如果找不到对应类型的空格口，则该位置为 null。
  @override
  Future<DBResult<List<Map<String, dynamic>?>>> queryEmptySlot({String type = 'rfid'}) async {
    try {
      List<Map<String, dynamic>?> resultSlots = [null, null];
      // 基础查询条件字符串
      final baseConditions = '''
        (s.barcode IS NULL OR s.barcode = '') 
          AND s.status = ? 
          AND s.lock_configs IS NOT NULL AND JSON_LENGTH(s.lock_configs) > 0 
          ${type != 'central' ? "AND s.reader_configs IS NOT NULL AND JSON_LENGTH(s.reader_configs) > 0" : ""}
      '''; // 使用 JSON_LENGTH > 0 替代 != '[]'

      // --- 查询 slot_type = '0' (普通区) 的空格口 ---
      String queryType0 = '''
        SELECT 
          s.slot_no, s.barcode, s.cabinet_no, s.shelf_no, s.status as slot_status,
          s.slot_type, s.pickupCode, s.lock_configs, s.reader_configs, s.light_configs,
          s.lastChecked, s.lastOperator
        FROM slots s
        WHERE $baseConditions AND s.slot_type = '0' 
        ORDER BY RAND() 
        LIMIT 1
      ''';
      final resultsType0 = await _connection?.query(queryType0, [CabinetConfig.slotEmpty]);

      if (resultsType0 != null && resultsType0.isNotEmpty) {
        resultSlots[0] = _formatSlotInfo(resultsType0.first);
      }

      // --- 查询 slot_type = '1' (预约区) 的空格口 ---
      String queryType1 = '''
        SELECT 
          s.slot_no, s.barcode, s.cabinet_no, s.shelf_no, s.status as slot_status,
          s.slot_type, s.pickupCode, s.lock_configs, s.reader_configs, s.light_configs,
          s.lastChecked, s.lastOperator
        FROM slots s
        WHERE $baseConditions AND s.slot_type = '1' 
        ORDER BY RAND() 
        LIMIT 1
      ''';
      final resultsType1 = await _connection?.query(queryType1, [CabinetConfig.slotEmpty]);

      if (resultsType1 != null && resultsType1.isNotEmpty) {
        resultSlots[1] = _formatSlotInfo(resultsType1.first);
      }

      // 可以根据业务逻辑决定，如果两个都找不到是返回错误还是成功但包含null的列表
      // if (resultSlots[0] == null && resultSlots[1] == null) {
      //   return DBResult.error('没有找到任何符合条件的空格口');
      // }

      return DBResult.success(resultSlots);

    } catch (e) {
      print('查询空格口失败 (MySQL): $e');
      return DBResult.error('查询空格口失败 (MySQL): $e');
    }
  }

  // 辅助方法：格式化格口信息Map (用于 MySQL)
  Map<String, dynamic> _formatSlotInfo(ResultRow row) {
    Map<String, dynamic> slotInfo = {
      'slot_no': row['slot_no'],
      'barcode': row['barcode'],
      'cabinet_no': row['cabinet_no'],
      'shelf_no': row['shelf_no'],
      'slot_status': row['slot_status'],
      'slot_type': row['slot_type'],
      'pickup_code': row['pickupCode'], // 确保字段名匹配 pickupCode
      'last_checked': row['lastChecked'],
      'last_operator': row['lastOperator'],
      // 初始化为列表，防止null
      'lock_configs': [],
      'reader_configs': [],
      'light_configs': [],
    };

    try {
      // 检查并解码 JSON 字段
      if (row['lock_configs'] != null) {
        // MySQL JSON 类型可以直接解码，但也可能存的是字符串
        var rawLock = row['lock_configs'];
        if (rawLock is String && rawLock.isNotEmpty) {
           slotInfo['lock_configs'] = jsonDecode(rawLock);
        } else if (rawLock is List || rawLock is Map) {
           // 如果已经是解码后的类型
           slotInfo['lock_configs'] = rawLock;
        }
      }
      if (row['reader_configs'] != null) {
         var rawReader = row['reader_configs'];
         if (rawReader is String && rawReader.isNotEmpty) {
           slotInfo['reader_configs'] = jsonDecode(rawReader);
         } else if (rawReader is List || rawReader is Map) {
            slotInfo['reader_configs'] = rawReader;
         }
      }
      if (row['light_configs'] != null) {
         var rawLight = row['light_configs'];
         if (rawLight is String && rawLight.isNotEmpty) {
           slotInfo['light_configs'] = jsonDecode(rawLight);
         } else if (rawLight is List || rawLight is Map) {
           slotInfo['light_configs'] = rawLight;
         }
      }
    } catch (e) {
      print('JSON解析错误 (MySQL): $e for slot ${row['slot_no']}');
      // 保留空列表作为默认值
    }
    return slotInfo;
  }

  /// 设置系统类型（rfid/central）
  Future<DBResult<String>> setSystemType(String systemType) async {
    try {
      if (systemType != 'rfid' && systemType != 'central') {
        return DBResult.error('系统类型只能是 rfid 或 central');
      }

      await _connection?.query('''
        INSERT INTO app_settings (`key`, value, type, description)
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
          value = VALUES(value),
          type = VALUES(type),
          description = VALUES(description)
      ''', [
        'system_type',
        systemType,
        'string',
        '书柜系统的类型'
      ]);

      print('系统类型设置为: $systemType');
      return DBResult.success(systemType);
    } catch (e) {
      print('设置系统类型失败: $e');
      return DBResult.error('设置系统类型失败: $e');
    }
  }

  /// 获取系统类型
  Future<DBResult<String>> getSystemType() async {
    try {
      final results = await _connection?.query(
        'SELECT value FROM app_settings WHERE `key` = ?',
        ['system_type']
      );

      if (results == null || results.isEmpty) {
        return DBResult.error('未设置系统类型');
      }

      final systemType = results.first['value'] as String;
      return DBResult.success(systemType);
    } catch (e) {
      print('获取系统类型失败: $e');
      return DBResult.error('获取系统类型失败: $e');
    }
  }

  /// 查询书箱书籍数量
  @override
  Future<DBResult<int>> getBookBoxCount() async {
    try {
      final results = await _connection?.query(
        'SELECT value FROM app_settings WHERE `key` = ?',
        ['cabinet_book_count']
      );

      if (results == null || results.isEmpty) {
        return DBResult.error('未找到书箱数量配置');
      }

      final count = int.parse(results.first['value']);
      return DBResult.success(count);
    } catch (e) {
      print('获取书箱数量失败: $e');
      return DBResult.error('获取书箱数量失败: $e');
    }
  }

  /// 获取或创建特殊书箱格口 (类型为999)
  /// 返回格口配置信息
  @override
  Future<DBResult<SlotConfig>> getOrCreateSpecialSlot() async {
    try {
      // 1. 查询是否已存在特殊格口(类型为999)
      final results = await _connection?.query('''
        SELECT 
          slot_no,
          lock_configs,
          light_configs,
          reader_configs,
          slot_type
        FROM slots 
        WHERE slot_type = '999' 
        LIMIT 1
      ''');
      
      // 2. 如果已存在，直接返回格口配置
      if (results != null && results.isNotEmpty) {
        final row = results.first;
        
        // 解析配置
        List<LockConfig>? lockConfigs;
        if (row['lock_configs'] != null) {
          final List<dynamic> json = jsonDecode(row['lock_configs']);
          lockConfigs = json.map((j) => LockConfig.fromJson(j)).toList();
        }

        List<LightConfig>? lightConfigs;
        if (row['light_configs'] != null) {
          final List<dynamic> json = jsonDecode(row['light_configs']);
          lightConfigs = json.map((j) => LightConfig.fromJson(j)).toList();
        }

        List<ReaderConfig>? readerConfigs;
        if (row['reader_configs'] != null) {
          final List<dynamic> json = jsonDecode(row['reader_configs']);
          readerConfigs = json.map((j) => ReaderConfig.fromJson(j)).toList();
        }

        final config = SlotConfig(
          lockConfigs: lockConfigs,
          lightConfigs: lightConfigs,
          readerConfigs: readerConfigs,
          type: row['slot_type'] as String?,
        );

        print('找到已存在的特殊格口: ${row['slot_no']}');
        return DBResult.success(config);
      }
      
      // 3. 如果不存在，创建新的特殊格口
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
      
      const specialSlotNo = '9999';
      const cabinetNo = 'return_book_box_cabinet_no';
      const shelfNo = 'return_book_box_shelf_no';
      
      // 初始化默认配置
      final defaultConfigs = {
        'lock_configs': jsonEncode([]),
        'light_configs': jsonEncode([]),
        'reader_configs': jsonEncode([]),
      };
      
      // 开始事务
      await _connection?.query('START TRANSACTION');
      
      // 插入特殊格口
      await _connection?.query('''
        INSERT IGNORE INTO slots (
          cabinet_no, shelf_no, slot_no, barcode, status, 
          slot_type, pickupCode, lock_configs, light_configs, reader_configs,
          lastChecked, lastOperator, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ''', [
        cabinetNo,                     // 特殊书柜编号
        shelfNo,                       // 特殊书架编号
        specialSlotNo,                 // 特殊格口编号
        '',                            // 书籍条码（初始为空）
        CabinetConfig.slotEmpty,       // 格口状态（初始为空）
        '999',                         // 格口类型（999表示特殊格口）
        '',                            // 取书码（初始为空）
        defaultConfigs['lock_configs'],
        defaultConfigs['light_configs'],
        defaultConfigs['reader_configs'],
        now,
        'system',
        now,
        now,
      ]);
      
      // 提交事务
      await _connection?.query('COMMIT');
      
      // 创建并返回新格口的配置
      final config = SlotConfig(
        lockConfigs: [],
        lightConfigs: [],
        readerConfigs: [],
        type: '999',
      );
      
      print('成功创建特殊格口: $specialSlotNo');
      return DBResult.success(config);
    } catch (e) {
      // 回滚事务
      await _connection?.query('ROLLBACK');
      print('获取或创建特殊格口失败: $e');
      return DBResult.error('获取或创建特殊格口失败: $e');
    }
  }

  /// 更新书柜的排序顺序
  @override
  Future<DBResult<void>> updateCabinetSortOrder(String cabinetNo, int sortOrder) async {
    try {
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      await _connection?.query('''
        UPDATE cabinets 
        SET sort_order = ?,
            updated_at = ?
        WHERE cabinet_no = ?
      ''', [sortOrder, now, cabinetNo]);

      return DBResult.success();
    } catch (e) {
      print('更新书柜排序失败: $e');
      return DBResult.error('更新书柜排序失败: $e');
    }
  }

  /// 获取书柜配置
  @override
  Future<DBResult<Map<String, dynamic>>> getCabinetConfig(String cabinetNo) async {
    try {
      final results = await _connection?.query('''
        SELECT 
          c.*,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no 
           AND s.status = '1') as occupied_slots,
          (SELECT COUNT(*) FROM slots s 
           WHERE s.cabinet_no = c.cabinet_no) as total_slots
        FROM cabinets c
        WHERE c.cabinet_no = ?
      ''', [cabinetNo]);

      if (results == null || results.isEmpty) {
        return DBResult.error('未找到书柜配置信息');
      }

      final cabinetConfig = Map<String, dynamic>.from(results.first.fields);
      
      return DBResult.success(cabinetConfig);
    } catch (e) {
      print('获取书柜配置失败: $e');
      return DBResult.error('获取书柜配置失败: $e');
    }
  }

  @override
  Future<DBResult<int?>> getCabinetOrderMode() {
    // TODO: implement getCabinetOrderMode
    throw UnimplementedError();
  }

  @override
  Future<DBResult<void>> setCabinetOrderMode(int value) {
    // TODO: implement setCabinetOrderMode
    throw UnimplementedError();
  }

  /// 添加认证记录
  @override
  Future<DBResult<void>> addAccessLog({
    required String name,
    required String readerCardNo,
    required DateTime openTime,
    String? authMethod,
    String status = 'success',
    String? remark,
  }) async {
    try {
      final formattedOpenTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(openTime);
      final now = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

      await _connection?.query('''
        INSERT INTO access_logs (
          name, reader_card_no, open_time, auth_method, status, remark, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      ''', [
        name,
        readerCardNo,
        formattedOpenTime,
        authMethod ?? '',
        status,
        remark ?? '',
        now
      ]);

      return DBResult.success();
    } catch (e) {
      print('添加认证记录失败: $e');
      return DBResult.error('添加认证记录失败: $e');
    }
  }

  /// 根据时间范围查询认证记录
  @override
  Future<DBResult<List<Map<String, dynamic>>>> queryAccessLogs({
    required DateTime startDate,
    required DateTime endDate,
    int? limit, 
    int? offset,
  }) async {
    try {
      final formattedStartDate = DateFormat('yyyy-MM-dd 00:00:00').format(startDate);
      final formattedEndDate = DateFormat('yyyy-MM-dd 23:59:59').format(endDate);

      String sql = '''
        SELECT 
          id, name, reader_card_no, open_time, auth_method, status, remark
        FROM access_logs
        WHERE open_time BETWEEN ? AND ?
        ORDER BY open_time DESC
      ''';

      List<Object> params = [formattedStartDate, formattedEndDate];

      if (limit != null) {
        sql += ' LIMIT ?';
        params.add(limit);
        
        if (offset != null) {
          sql += ' OFFSET ?';
          params.add(offset);
        }
      }

      final results = await _connection?.query(sql, params);

      if (results == null || results.isEmpty) {
        return DBResult.success([]);
      }

      final logs = results.map((row) => row.fields).toList();
      return DBResult.success(logs);
    } catch (e) {
      print('查询认证记录失败: $e');
      return DBResult.error('查询认证记录失败: $e');
    }
  }

  /// 删除指定日期前的认证记录
  @override
  Future<DBResult<int>> cleanupOldAccessLogs(DateTime beforeDate) async {
    try {
      final formattedDate = DateFormat('yyyy-MM-dd 00:00:00').format(beforeDate);

      final result = await _connection?.query('''
        DELETE FROM access_logs
        WHERE open_time < ?
      ''', [formattedDate]);

      final affectedRows = result?.affectedRows ?? 0;
      print('已删除 $affectedRows 条旧认证记录');
      return DBResult.success(affectedRows);
    } catch (e) {
      print('清理旧认证记录失败: $e');
      return DBResult.error('清理旧认证记录失败: $e');
    }
  }

}
