import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import 'package:seasetting/seasetting.dart';

/// 身份信息卡片组件 - 显示读者基本信息
class IdentityCard extends StatelessWidget {
  final String? name;
  final String? idNumber;
  final String? time;
  final String? avatarUrl;
  final bool isPassMode;

  const IdentityCard({
    Key? key,
    this.name,
    this.idNumber,
    this.time,
    this.avatarUrl,
    this.isPassMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment(-0.8, -0.8), // 近似125度角
          end: Alignment(0.8, 0.8),
          colors: [
            Color(0xFFBBFBFF), // #BBFBFF
            Color(0xFFAFDAFF), // #AFDAFF
            Color(0xFFC3CAFF), // #C3CAFF
          ],
          stops: [0.0, 0.47, 1.0],
        ),
        borderRadius: BorderRadius.circular(40.p),
      ),
      padding: EdgeInsets.all(30.p),
      child: Row(
        children: [
          // 左侧头像
          Container(
            width: 130.p,
            height: 130.p,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(20.p),
            ),
            // 头像占位符
            child: Center(
              child: Icon(
                Icons.person,
                size: 80.p,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(width: 30.p),

          // 右侧信息
          Expanded(
            child: isPassMode
                ? _buildPassModeContent()
                : _buildDetailModeContent(),
          ),
        ],
      ),
    );
  }

  // 详细信息模式内容
  Widget _buildDetailModeContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Text(
              "姓名",
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF12215F),
                height: 32/30
              ),
            ),
            SizedBox(width: 30.p),
            Text(
              name ?? "",
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12215F),
                height: 32/30
              ),
            ),
          ],
        ),
        SizedBox(height: 10.p),
        Row(
          children: [
            Text(
              "证号",
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF12215F),
                height: 32/30
              ),
            ),
            SizedBox(width: 30.p),
            Text(
              idNumber ?? "",
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12215F),
                height: 32/30
              ),
            ),
          ],
        ),
        SizedBox(height: 16.p),
        Row(
          children: [
            Text(
              "时间",
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF12215F),
                height: 32/30
              ),
            ),
            SizedBox(width: 20.p),
            Text(
              time ?? "",
              style: TextStyle(
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12215F),
                height: 32/30
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 通行模式内容
  Widget _buildPassModeContent() {
    return Padding(
      padding: EdgeInsets.only(left: 100.p),
      child: Text(
        "请通行",
        style: TextStyle(
          fontSize: 42.p,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF12215F),
        ),
      ),
    );
  }
} 