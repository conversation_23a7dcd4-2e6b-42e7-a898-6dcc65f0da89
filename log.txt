2025-08-01 16:32:36.152399: 61
2025-08-01 16:32:36.380840: socket 连接成功,isBroadcast:false
2025-08-01 16:32:36.380840: changeSocketStatus:true
2025-08-01 16:32:36.380840: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-01 16:32:36.380840: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-01 16:32:36.390814: 已添加配置变化监听器
2025-08-01 16:32:36.391830: 认证优先级管理器: 开始加载认证方式
2025-08-01 16:32:36.391830: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-08-01 16:32:36.392941: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-01 16:32:36.392941: 认证优先级管理器: 按配置顺序添加 人脸识别认证 -> 人脸识别
2025-08-01 16:32:36.392941: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-08-01 16:32:36.393809: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-01 16:32:36.393809: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-08-01 16:32:36.393809: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-08-01 16:32:36.393809: 认证优先级管理器: 最终排序结果: 人脸识别 -> 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-01 16:32:36.393809: 认证优先级管理器: 主要认证方式: 人脸识别
2025-08-01 16:32:36.393809: 更新认证方式显示文字: 人脸识别/微信扫码/读者证/社保卡/电子社保卡
2025-08-01 16:32:36.442673: Rsp : 941AY1AZFDFC
2025-08-01 16:32:36.461996: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-01 16:32:36.461996: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-01 16:32:36.462828: 发送心跳
2025-08-01 16:32:36.462828: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-01 16:32:36.750233: dispose IndexPage
2025-08-01 16:32:36.750233: IndexPage dispose
2025-08-01 16:32:36.896860: Rsp : 98YYYNNN00500320250801    1634062.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD524
2025-08-01 16:32:38.373618: 手动触发跳转到认证页面
2025-08-01 16:32:38.391569: AuthView初始化 - 主认证方式: 人脸识别
2025-08-01 16:32:38.391569: 尝试初始化多认证系统
2025-08-01 16:32:38.391569: 多认证管理器状态变更: initializing
2025-08-01 16:32:38.391569: 认证优先级管理器: 开始加载认证方式
2025-08-01 16:32:38.391569: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-08-01 16:32:38.391569: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-01 16:32:38.391569: 认证优先级管理器: 按配置顺序添加 人脸识别认证 -> 人脸识别
2025-08-01 16:32:38.391569: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-08-01 16:32:38.391569: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-01 16:32:38.391569: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-08-01 16:32:38.392579: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-08-01 16:32:38.392579: 认证优先级管理器: 最终排序结果: 人脸识别 -> 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-01 16:32:38.392579: 认证优先级管理器: 主要认证方式: 人脸识别
2025-08-01 16:32:38.392579: 多认证管理器: 从优先级管理器加载的认证方式: 人脸识别 -> 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-01 16:32:38.392579: 多认证管理器: 当前默认显示方式: 人脸识别
2025-08-01 16:32:38.392579: 初始化人脸认证服务
2025-08-01 16:32:38.392579: 📷 开始人脸识别认证服务初始化流程...
2025-08-01 16:32:38.392579: 🔧 第一步：初始化摄像头系统...
2025-08-01 16:32:38.393563: 📷 检查人脸检测器初始化状态...
2025-08-01 16:32:38.393563: 🔧 开始初始化人脸检测器...
2025-08-01 16:32:38.393563: 尝试加载库: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\libface_detector.dll
2025-08-01 16:32:38.393563: 文件不存在: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\libface_detector.dll
2025-08-01 16:32:38.393563: 尝试加载库: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\data\libface_detector.dll
2025-08-01 16:32:38.393563: 文件不存在: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\data\libface_detector.dll
2025-08-01 16:32:38.393563: 尝试加载库: D:\gdwork\code\a3g\build\windows\runner\Release\libface_detector.dll
2025-08-01 16:32:38.394560: 成功加载库: D:\gdwork\code\a3g\build\windows\runner\Release\libface_detector.dll
2025-08-01 16:32:38.394560: 成功加载系统负载和处理间隔函数
2025-08-01 16:32:40.645143: 开始初始化门锁连接...
2025-08-01 16:32:40.652125: 设置认证方法: AuthMethod.face, 当前方法: AuthMethod.face
2025-08-01 16:32:40.652125: ✅ 人脸检测器初始化完成，耗时: 4ms
2025-08-01 16:32:40.652125: 🔍 检测可用摄像头设备...
2025-08-01 16:32:40.653122: ✅ 检测到 1 个摄像头设备: [0]
2025-08-01 16:32:40.653122: 📹 启动摄像头（带重试机制）...
2025-08-01 16:32:40.653122: 📹 尝试使用摄像头ID: 0
2025-08-01 16:32:40.653122: 📹 摄像头启动尝试 1/2，ID: 0
2025-08-01 16:32:40.653122: 创建门锁连接: 主门锁设备 (COM1)
2025-08-01 16:32:40.653122: 门锁继电器数据监听已启动
2025-08-01 16:32:40.653122: 门锁继电器连接成功: COM1
2025-08-01 16:32:40.653122: changeReaders
2025-08-01 16:32:40.653122: createIsolate isOpen:false,isOpening:false
2025-08-01 16:32:40.654119: ✅ 摄像头启动成功，ID: 0，尝试次数: 1，耗时: 1388ms，后端: DirectShow
2025-08-01 16:32:40.654119: 🔧 配置摄像头参数...
2025-08-01 16:32:40.654119: ✅ 摄像头参数配置完成
2025-08-01 16:32:40.654119: 🤖 预加载人脸检测模型...
2025-08-01 16:32:40.654119: 预加载DNN模型...
2025-08-01 16:32:40.654119: DNN模型预加载完成，获取到 0 个结果
2025-08-01 16:32:40.654119: 门锁连接成功: COM1
2025-08-01 16:32:40.654119: 门锁连接初始化完成，共建立 1 个连接
2025-08-01 16:32:40.654119: ✅ 人脸检测模型预加载完成
2025-08-01 16:32:40.655116: 🔍 验证摄像头功能...
2025-08-01 16:32:40.655116: ⚠️ 摄像头功能验证失败，但继续执行
2025-08-01 16:32:40.655116: ✅ 人脸检测器初始化完成
2025-08-01 16:32:40.655116: ✅ 摄像头系统初始化成功
2025-08-01 16:32:40.655116: 🔧 第二步：初始化百度人脸识别SDK...
2025-08-01 16:32:40.656113: 配置文件不存在，使用默认配置: D:\gdwork\code\a3g\build\windows\runner\Release\sdk_config.json
2025-08-01 16:32:40.656113: 从配置获取SDK路径: D:\FaceOfflineSdk
2025-08-01 16:32:47.781579: ✅ SDK路径验证成功
2025-08-01 16:32:47.781579: 🔧 配置SDK路径: D:\FaceOfflineSdk
2025-08-01 16:32:47.781579: SDK未初始化，无法设置路径
2025-08-01 16:32:47.781579: ✅ SDK路径配置完成，错误码: -100
2025-08-01 16:32:47.782577: 🚀 开始初始化百度人脸识别SDK...
2025-08-01 16:32:47.782577: 百度SDK初始化成功, 人脸数据库加载结果: 1
2025-08-01 16:32:47.783573: ✅ 百度人脸识别SDK初始化成功
2025-08-01 16:32:47.783573: ✅ 百度SDK状态: 已初始化，人脸数量: 4，授权状态: 已授权
2025-08-01 16:32:47.783573: ✅ 百度人脸识别SDK初始化成功
2025-08-01 16:32:47.783573: 🔧 第三步：启动系统监控...
2025-08-01 16:32:47.783573: ✅ 系统监控启动成功
2025-08-01 16:32:47.784571: ✅ 人脸识别认证服务初始化完成
2025-08-01 16:32:47.784571: 人脸认证服务初始化成功
2025-08-01 16:32:47.784571: 初始化人脸认证服务
2025-08-01 16:32:47.784571: 人脸识别 认证服务初始化成功
2025-08-01 16:32:47.784571: 初始化二维码扫描认证服务
2025-08-01 16:32:47.784571: 初始化二维码扫码器
2025-08-01 16:32:47.784571: 二维码扫码器初始化完成
2025-08-01 16:32:47.784571: 二维码扫描认证服务初始化成功
2025-08-01 16:32:47.784571: 初始化共享二维码扫描认证服务
2025-08-01 16:32:47.785567: 微信扫码 认证服务初始化成功
2025-08-01 16:32:47.785567: 初始化读卡器认证服务
2025-08-01 16:32:47.785567: 读卡器认证服务初始化成功
2025-08-01 16:32:47.785567: 初始化共享读卡器认证服务
2025-08-01 16:32:47.785567: 读者证 认证服务初始化成功
2025-08-01 16:32:47.786565: 社保卡 认证服务初始化成功
2025-08-01 16:32:47.786565: 电子社保卡 认证服务初始化成功
2025-08-01 16:32:47.786565: 认证服务初始化完成，共初始化 5 种认证方式
2025-08-01 16:32:47.786565: 多认证管理器状态变更: idle
2025-08-01 16:32:47.786565: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.face, AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-01 16:32:47.786565: 检测到多种认证方式(5种)，启用多认证模式
2025-08-01 16:32:47.786565: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:32:47.786565: 多认证管理器状态变更: listening
2025-08-01 16:32:47.786565: 启动所有认证方式监听: [AuthMethod.face, AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-01 16:32:47.786565: 准备启动 3 个物理认证服务
2025-08-01 16:32:47.786565: 开始人脸认证监听
2025-08-01 16:32:47.786565: 🚀 开始人脸识别监听（使用FaceCapturePolling方式）...
2025-08-01 16:32:47.787565: 🆕 创建新的人脸捕获轮询服务...
2025-08-01 16:32:47.787565: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:32:47.787565: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:32:47.787565: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:32:47.787565: ✅ 人脸识别监听启动成功
2025-08-01 16:32:47.787565: 人脸认证监听启动成功
2025-08-01 16:32:47.787565: 人脸识别 认证服务启动成功
2025-08-01 16:32:47.787565: 开始二维码扫描认证监听
2025-08-01 16:32:47.787565: 开始二维码扫描监听
2025-08-01 16:32:47.787565: 二维码扫描认证监听启动成功
2025-08-01 16:32:47.788577: 微信扫码 认证服务启动成功
2025-08-01 16:32:47.788577: 开始读卡器认证监听
2025-08-01 16:32:47.788577: 强制重新配置读卡器以确保状态一致性
2025-08-01 16:32:47.788577: 完全重置读卡器连接和监听器状态...
2025-08-01 16:32:47.788577: 已移除读卡器状态监听器
2025-08-01 16:32:47.788577: 已移除标签数据监听器
2025-08-01 16:32:47.788577: 所有卡片监听器已移除
2025-08-01 16:32:47.788577: stopInventory newPort:null
2025-08-01 16:32:47.789558: createIsolate newport null
2025-08-01 16:32:47.991051: 发送 关闭 阅读器newPort:SendPort
2025-08-01 16:32:47.991051: 读卡器连接已完全关闭
2025-08-01 16:32:47.991051: 读卡器连接和监听器状态已完全重置
2025-08-01 16:32:47.992047: 添加设备配置: 读者证认证 -> 1个设备
2025-08-01 16:32:47.992047: 添加设备配置: 人脸识别认证 -> 1个设备
2025-08-01 16:32:47.992047: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-01 16:32:47.992047: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-01 16:32:47.992047: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-01 16:32:47.992047: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-01 16:32:47.992047: 添加有效设备: type=10, id=10
2025-08-01 16:32:47.992047: 验证读卡器配置: 类型=2, 解码器=不解析
2025-08-01 16:32:47.993045: 添加有效设备: type=2, id=2
2025-08-01 16:32:47.993045: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-01 16:32:47.993045: 添加有效设备: type=13, id=13
2025-08-01 16:32:47.993045: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-01 16:32:47.993045: 添加有效设备: type=12, id=12
2025-08-01 16:32:47.993045: 总共加载了4个设备配置
2025-08-01 16:32:47.993045: changeReaders
2025-08-01 16:32:47.994042: createIsolate isOpen:false,isOpening:true
2025-08-01 16:32:47.994042: open():SendPort
2025-08-01 16:32:47.994042: untilDetcted():SendPort
2025-08-01 16:32:47.994042: 读卡器配置完成，共 4 个设备
2025-08-01 16:32:47.994042: 已移除读卡器状态监听器
2025-08-01 16:32:47.994042: 已移除标签数据监听器
2025-08-01 16:32:47.994042: 所有卡片监听器已移除
2025-08-01 16:32:47.994042: 已添加读卡器状态监听器
2025-08-01 16:32:47.995039: 已添加标签数据监听器
2025-08-01 16:32:47.995039: 开始监听卡片数据 - 所有监听器已就绪
2025-08-01 16:32:47.995039: 读卡器认证监听启动成功
2025-08-01 16:32:47.995039: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-01 16:32:47.995039: 所有认证服务启动完成，成功启动 3 个服务
2025-08-01 16:32:47.995039: 当前可用的认证方式: 人脸识别、微信扫码、读者证、社保卡、电子社保卡
2025-08-01 16:32:47.995039: 多认证系统初始化完成，正在同时监听以下认证方式: 人脸识别、微信扫码、读者证、社保卡、电子社保卡
2025-08-01 16:32:47.996036: subThread :ReaderCommand.close
2025-08-01 16:32:47.996036: commandRsp:ReaderCommand.close
2025-08-01 16:32:47.996036: cacheUsedReaders:()
2025-08-01 16:32:47.996036: close:done
2025-08-01 16:32:47.997035: changeType:ReaderErrorType.closeSuccess
2025-08-01 16:32:47.997035: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-01 16:32:47.997035: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-01 16:32:47.997035: already close all reader
2025-08-01 16:32:47.998046: subThread :ReaderCommand.readerList
2025-08-01 16:32:47.998046: commandRsp:ReaderCommand.readerList
2025-08-01 16:32:47.998046: readerList：4,readerSetting：4
2025-08-01 16:32:47.999029: cacheUsedReaders:4
2025-08-01 16:32:47.999029: subThread :ReaderCommand.open
2025-08-01 16:32:47.999483: commandRsp:ReaderCommand.open
2025-08-01 16:32:48.010061: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:32:48.010061: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:32:48.010061: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:32:48.010061: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:32:48.010061: 构建读者证认证界面
2025-08-01 16:32:48.010061: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:32:48.010061: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:32:48.010061: === 构建多认证底部组件 ===
2025-08-01 16:32:48.011058: 显示底部组件: false
2025-08-01 16:32:48.011058: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:32:48.011058: 用户信息: null (null)
2025-08-01 16:32:48.011058: 底部组件被隐藏，返回空组件
2025-08-01 16:32:48.011058: CameraPreviewWidget: 开始从 seasetting 加载配置...
2025-08-01 16:32:48.011058: CameraPreviewWidget: 开始初始化人脸识别服务...
2025-08-01 16:32:48.011058: 人脸识别认证服务已初始化
2025-08-01 16:32:48.012055: Instance of 'SysConfigData'
2025-08-01 16:32:48.012055: Instance of 'SysConfigData'
2025-08-01 16:32:48.012055: 人脸识别已在监听中
2025-08-01 16:32:48.012055: 人脸检测配置加载成功
2025-08-01 16:32:48.012055: 人脸检测配置管理器初始化成功
2025-08-01 16:32:48.013053: CameraPreviewWidget: 人脸识别服务初始化成功
2025-08-01 16:32:49.714057: CameraPreviewWidget: 从配置加载摄像头ID: 0
2025-08-01 16:32:49.714057: CameraPreviewWidget: 配置加载完成 - JPEG质量: 62, 帧间隔: 80, 检测间隔: 80, 摄像头ID: 0
2025-08-01 16:32:49.714057: 📷 CameraPreviewWidget: 检查摄像头检测器初始化状态...
2025-08-01 16:32:49.714057: ✅ CameraPreviewWidget: 摄像头检测器已初始化，跳过初始化步骤
2025-08-01 16:32:49.714057: 📹 CameraPreviewWidget: 启动摄像头 0...
2025-08-01 16:32:49.714057: ✅ CameraPreviewWidget: 摄像头启动成功，耗时: 1701ms
2025-08-01 16:32:49.714057: CameraPreviewWidget: 预加载DNN模型...
2025-08-01 16:32:49.715054: CameraPreviewWidget: DNN模型预加载完成，获取到 0 个结果
2025-08-01 16:32:49.715054: CameraPreviewWidget: 帧捕获已启动，间隔: 80ms
2025-08-01 16:32:49.715054: CameraPreviewWidget: 人脸检测器已启动，检测间隔: 80ms
2025-08-01 16:32:49.715054: ✅ CameraPreviewWidget: 摄像头初始化完成
2025-08-01 16:32:49.716052: open():SendPort
2025-08-01 16:32:49.716052: untilDetcted():SendPort
2025-08-01 16:32:49.716052: dc_init:0xb4 100
2025-08-01 16:32:49.716052: open reader readerType ：10 ret：0
2025-08-01 16:32:49.716052: wsurl:************:9091
2025-08-01 16:32:49.716052: subThread :ReaderCommand.untilDetected
2025-08-01 16:32:49.717048: commandRsp:ReaderCommand.untilDetected
2025-08-01 16:32:49.717048: dc_config_card:0
2025-08-01 16:32:49.717048: dc_card_n_hex:1,len:0
2025-08-01 16:32:49.717048: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:32:49.717048: iReadCardBas ret:4294967294
2025-08-01 16:32:49.717048: 无卡
2025-08-01 16:32:49.717048: 无卡
2025-08-01 16:32:49.717048: dc_config_card:0
2025-08-01 16:32:49.717048: dc_card_n_hex:1,len:0
2025-08-01 16:32:49.718045: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:32:49.788302: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:32:49.788)
2025-08-01 16:32:49.788302: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:32:49.788)
2025-08-01 16:32:49.788302: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:32:50.012760: iReadCardBas ret:4294967294
2025-08-01 16:32:50.012760: 无卡
2025-08-01 16:32:50.012760: 无卡
2025-08-01 16:32:50.012760: subThread :ReaderCommand.readerList
2025-08-01 16:32:50.012760: commandRsp:ReaderCommand.readerList
2025-08-01 16:32:50.013711: readerList：1,readerSetting：1
2025-08-01 16:32:50.013711: cacheUsedReaders:4
2025-08-01 16:32:50.013711: subThread :ReaderCommand.open
2025-08-01 16:32:50.014216: commandRsp:ReaderCommand.open
2025-08-01 16:32:50.014216: wsurl:************:9091
2025-08-01 16:32:50.014216: subThread :ReaderCommand.untilDetected
2025-08-01 16:32:50.014216: commandRsp:ReaderCommand.untilDetected
2025-08-01 16:32:51.788299: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:32:51.788)
2025-08-01 16:32:51.788299: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:32:51.788)
2025-08-01 16:32:51.788299: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:32:52.198506: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.439553: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.518618: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.597412: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.677265: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.757569: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.838352: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.917755: AuthView: 检测到 1 个人脸
2025-08-01 16:32:52.999581: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.079494: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.158025: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.177969: ready error :HttpException: Connection closed before full header was received, uri = http://************:9091
2025-08-01 16:32:53.177969: ready done
2025-08-01 16:32:53.177969: open reader readerType ：2 ret：-1
2025-08-01 16:32:53.177969: Error: WebSocketChannelException: WebSocketChannelException: HttpException: Connection closed before full header was received, uri = http://************:9091
2025-08-01 16:32:53.177969: open reader readerType ：13 ret：0
2025-08-01 16:32:53.187949: 去开启 读 监听
2025-08-01 16:32:53.188949: widget.port.isOpen:true
2025-08-01 16:32:53.190945: 打开COM4读写成功
2025-08-01 16:32:53.190945: 发送：[16, 54, 0d]
2025-08-01 16:32:53.190945: WebSocket 连接已关闭
2025-08-01 16:32:53.190945: open reader readerType ：12 ret：0
2025-08-01 16:32:53.191941: [[10, 0], [2, -1], [13, 0], [12, 0]]
2025-08-01 16:32:53.191941: changeType:ReaderErrorType.openSuccess
2025-08-01 16:32:53.191941: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-01 16:32:53.191941: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-01 16:32:53.192938: 读卡器连接成功
2025-08-01 16:32:53.192938: 读卡器连接成功，确保扫描状态正常
2025-08-01 16:32:53.192938: 恢复读卡器扫描状态...
2025-08-01 16:32:53.192938: 读卡器扫描状态已恢复
2025-08-01 16:32:53.194933: subThread :ReaderCommand.resumeInventory
2025-08-01 16:32:53.194933: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:32:53.238467: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.318602: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.398315: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.478101: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.557889: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.637721: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.717985: AuthView: 检测到 1 个人脸
2025-08-01 16:32:53.788426: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:32:53.788)
2025-08-01 16:32:53.789418: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:32:53.788)
2025-08-01 16:32:53.789418: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:32:53.789418: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:32:53.789418: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:32:53.789418: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 142.0904541015625, 数据大小: 0 bytes
2025-08-01 16:32:53.789418: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:32:53.789418: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:32:53.789418: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 7288 bytes, 实际置信度: 142.0904541015625
2025-08-01 16:32:53.789418: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:32:53.790418: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:32:53.790418: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:32:53.790418: ⏹️ 停止人脸捕获轮询
2025-08-01 16:32:53.790418: 暂停人脸轮询，开始认证流程
2025-08-01 16:32:54.076268: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:32:54.076268: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "e6351773f8ee3bb3742749c046ea314a",
		"log_id" : "1754037174076",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 92.35,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:32:54.076268: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:32:54.077265: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=92.35
2025-08-01 16:32:54.077265: 人脸识别成功，得分: 92.35，用户ID: 111111
2025-08-01 16:32:54.077265: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:32:54.077265: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:54.077265: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:54.077265: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:32:54.078263: 🔄 开始重启人脸识别服务...
2025-08-01 16:32:54.078263: 轮询未在运行
2025-08-01 16:32:54.078263: 轮询未在运行
2025-08-01 16:32:54.078263: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:32:54.078263: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:32:54.078263: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:32:54.078263: 多认证管理器: 人脸识别获得认证请求锁
2025-08-01 16:32:54.078263: 63 CardType 值为空
2025-08-01 16:32:54.078263: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163254  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY3AZF01D
2025-08-01 16:32:54.079261: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:32:54.079261: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:32:54.079261: 多认证管理器状态变更: authenticating
2025-08-01 16:32:54.079261: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:32:54.079261: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:32:54.079261: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:32:54.079261: === 更新认证反馈状态 ===
2025-08-01 16:32:54.079261: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:32:54.080258: 用户姓名: null
2025-08-01 16:32:54.080258: 用户ID: null
2025-08-01 16:32:54.080258: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:32:54.080258: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:54.080258: 显示底部组件: true
2025-08-01 16:32:54.082253: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.086127: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:32:54.086127: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:32:54.086127: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:32:54.086127: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:32:54.086127: 构建读者证认证界面
2025-08-01 16:32:54.086127: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:32:54.087127: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:32:54.087127: === 构建多认证底部组件 ===
2025-08-01 16:32:54.087127: 显示底部组件: true
2025-08-01 16:32:54.087127: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:32:54.087127: 用户信息: null (null)
2025-08-01 16:32:54.087127: 创建AuthFeedbackWidget
2025-08-01 16:32:54.087127: 实际认证方式: AuthMethod.face
2025-08-01 16:32:54.087127: Instance of 'SysConfigData'
2025-08-01 16:32:54.087127: Instance of 'SysConfigData'
2025-08-01 16:32:54.087127: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:32:54.088124: 当前状态: AuthFeedbackState.failure
2025-08-01 16:32:54.088124: 返回失败组件
2025-08-01 16:32:54.088124: === 构建失败组件 ===
2025-08-01 16:32:54.088124: 认证方式: AuthMethod.face
2025-08-01 16:32:54.088124: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:54.118154: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.196848: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.278585: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.358669: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.437725: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.517997: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.551927: Rsp : 64              00120250801    163423000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY3AZDBBF
2025-08-01 16:32:54.559885: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163423, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 3AZDBBF}
2025-08-01 16:32:54.559885: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163423, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 3AZDBBF}
2025-08-01 16:32:54.559885: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-08-01 16:32:54.559885: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-08-01 16:32:54.559885: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-08-01 16:32:54.559885: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:32:54.560882: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:32:54.560882: 多认证管理器状态变更: completed
2025-08-01 16:32:54.560882: 多认证管理器: 调用认证后处理服务 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:32:54.560882: 执行认证后处理流程，共2个处理器
2025-08-01 16:32:54.560882: 执行处理器: DoorLockHandler
2025-08-01 16:32:54.560882: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:32:54.561880: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:32:54.561880: 收到多认证结果: 人脸识别 - success
2025-08-01 16:32:54.561880: 认证成功，更新主显示方式为: 人脸识别
2025-08-01 16:32:54.561880: === 更新认证反馈状态 ===
2025-08-01 16:32:54.561880: 结果状态: AuthStatus.success
2025-08-01 16:32:54.561880: 用户姓名: gd
2025-08-01 16:32:54.561880: 用户ID: 111111
2025-08-01 16:32:54.561880: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:32:54.561880: 反馈状态: AuthFeedbackState.success
2025-08-01 16:32:54.562877: 用户信息: gd (111111)
2025-08-01 16:32:54.562877: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:32:54.562877: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:32:54.562877: 使用认证页面的门锁连接: COM1
2025-08-01 16:32:54.562877: 使用继电器通道: 1
2025-08-01 16:32:54.562877: 使用继电器通道: 1
2025-08-01 16:32:54.562877: 开锁前先关锁（不等待返回）...
2025-08-01 16:32:54.562877: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:32:54.562877: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:32:54.562877: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:32:54.562877: 等待100毫秒确保命令发送...
2025-08-01 16:32:54.570856: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:32:54.571854: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:32:54.571854: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:32:54.571854: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:32:54.571854: 构建读者证认证界面
2025-08-01 16:32:54.571854: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:32:54.571854: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:32:54.571854: === 构建多认证底部组件 ===
2025-08-01 16:32:54.571854: 显示底部组件: true
2025-08-01 16:32:54.571854: 反馈状态: AuthFeedbackState.success
2025-08-01 16:32:54.571854: 用户信息: gd (111111)
2025-08-01 16:32:54.572851: 创建AuthFeedbackWidget
2025-08-01 16:32:54.572851: 实际认证方式: AuthMethod.face
2025-08-01 16:32:54.572851: Instance of 'SysConfigData'
2025-08-01 16:32:54.572851: Instance of 'SysConfigData'
2025-08-01 16:32:54.572851: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:32:54.572851: 新状态: AuthFeedbackState.success
2025-08-01 16:32:54.572851: 用户信息: gd (111111)
2025-08-01 16:32:54.572851: 启动定时器，3秒后切换到通行模式
2025-08-01 16:32:54.572851: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:32:54.572851: 当前状态: AuthFeedbackState.success
2025-08-01 16:32:54.572851: 返回成功组件 (详细信息)
2025-08-01 16:32:54.573848: === 构建成功组件（详细信息） ===
2025-08-01 16:32:54.573848: 用户: gd, ID: 111111
2025-08-01 16:32:54.578235: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:32:54.578235: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:32:54.579043: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:32:54.579043: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:32:54.579043: ✅ 人脸识别服务重启完成
2025-08-01 16:32:54.597240: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.664722: 正在打开绿灯（不等待返回）...
2025-08-01 16:32:54.664722: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:32:54.665552: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:32:54.665552: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:32:54.665552: 正在开门（不等待返回）...
2025-08-01 16:32:54.665552: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:32:54.665552: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:32:54.665552: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:32:54.666536: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:32:54.666536: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:32:54.666536: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:32:54.664722
2025-08-01 16:32:54.666536: 门锁操作完成，连接由认证页面管理
2025-08-01 16:32:54.666536: 自动开门成功 - 用户: gd
2025-08-01 16:32:54.666536: 执行处理器: WelcomeHandler
2025-08-01 16:32:54.667543: 显示欢迎信息给: gd
2025-08-01 16:32:54.667543: 欢迎用户: gd，认证方式: 人脸识别
2025-08-01 16:32:54.667543: 认证后处理流程执行完毕
2025-08-01 16:32:54.667543: 多认证管理器: 认证后处理流程执行完毕
2025-08-01 16:32:54.677507: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.758374: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.838698: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.918519: AuthView: 检测到 1 个人脸
2025-08-01 16:32:54.996936: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.018647: ready error :HttpException: Connection closed before full header was received, uri = http://************:9091
2025-08-01 16:32:55.018647: ready done
2025-08-01 16:32:55.018647: open reader readerType ：2 ret：-1
2025-08-01 16:32:55.018647: isConnect:false
2025-08-01 16:32:55.018647: disconnect
2025-08-01 16:32:55.018647: [[2, -1]]
2025-08-01 16:32:55.018647: changeType:ReaderErrorType.openFail
2025-08-01 16:32:55.019645: 读卡器状态变化: ReaderErrorType.openFail
2025-08-01 16:32:55.019645: 读卡器状态变化: ReaderErrorType.openFail
2025-08-01 16:32:55.019645: 读卡器连接失败，尝试重新连接
2025-08-01 16:32:55.019645: 处理读卡器连接错误
2025-08-01 16:32:55.077886: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.157638: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.237512: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.318313: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.397135: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.477660: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.558458: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.638377: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.666330: 异步执行关锁动作（不等待返回）...
2025-08-01 16:32:55.666330: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:32:55.666330: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:32:55.667301: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:32:55.718177: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.719166: 正在关闭绿灯...
2025-08-01 16:32:55.719166: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:32:55.719166: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:32:55.719166: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:32:55.720166: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:32:55.719166
2025-08-01 16:32:55.798484: AuthView: 检测到 1 个人脸
2025-08-01 16:32:55.877833: AuthView: 检测到 1 个人脸
2025-08-01 16:32:56.579646: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:32:56.578)
2025-08-01 16:32:56.579646: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:32:56.578)
2025-08-01 16:32:56.579646: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:32:56.579646: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:32:56.579646: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:32:56.580753: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 245.31106567382812, 数据大小: 0 bytes
2025-08-01 16:32:56.580753: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:32:56.580753: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:32:56.581269: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3936 bytes, 实际置信度: 245.31106567382812
2025-08-01 16:32:56.581269: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:32:56.581269: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:32:56.581269: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:32:56.581269: ⏹️ 停止人脸捕获轮询
2025-08-01 16:32:56.581269: 暂停人脸轮询，开始认证流程
2025-08-01 16:32:56.702946: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:32:56.702946: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "28327057963f17e2a1819013ab12fdfc",
		"log_id" : "1754037176702",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 92.64,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:32:56.702946: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:32:56.702946: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=92.64
2025-08-01 16:32:56.704941: 人脸识别成功，得分: 92.64，用户ID: 111111
2025-08-01 16:32:56.705939: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:32:56.705939: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:56.706937: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:56.706937: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:32:56.706937: 🔄 开始重启人脸识别服务...
2025-08-01 16:32:56.706937: 轮询未在运行
2025-08-01 16:32:56.706937: 轮询未在运行
2025-08-01 16:32:56.706937: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:32:56.706937: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:32:56.707933: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:32:56.707933: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:32:56.707933: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:32:56.707933: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:32:56.707933: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:32:57.021186: 尝试重新配置读卡器
2025-08-01 16:32:57.021186: 添加设备配置: 读者证认证 -> 1个设备
2025-08-01 16:32:57.021186: 添加设备配置: 人脸识别认证 -> 1个设备
2025-08-01 16:32:57.021186: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-01 16:32:57.022180: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-01 16:32:57.022180: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-01 16:32:57.022180: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-01 16:32:57.022180: 添加有效设备: type=10, id=10
2025-08-01 16:32:57.022180: 验证读卡器配置: 类型=2, 解码器=不解析
2025-08-01 16:32:57.022180: 添加有效设备: type=2, id=2
2025-08-01 16:32:57.022180: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-01 16:32:57.022180: 添加有效设备: type=13, id=13
2025-08-01 16:32:57.023177: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-01 16:32:57.023177: 添加有效设备: type=12, id=12
2025-08-01 16:32:57.023177: 总共加载了4个设备配置
2025-08-01 16:32:57.023177: stopInventory newPort:SendPort
2025-08-01 16:32:57.023177: subThread :ReaderCommand.stopInventory
2025-08-01 16:32:57.023177: commandRsp:ReaderCommand.stopInventory
2025-08-01 16:32:57.080792: 认证完成，还原到默认显示方式
2025-08-01 16:32:57.084838: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:32:57.084838: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:32:57.084838: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:32:57.084838: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:32:57.084838: 构建读者证认证界面
2025-08-01 16:32:57.084838: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:32:57.085836: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:32:57.085836: === 构建多认证底部组件 ===
2025-08-01 16:32:57.085836: 显示底部组件: false
2025-08-01 16:32:57.085836: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:32:57.085836: 用户信息: null (null)
2025-08-01 16:32:57.085836: 底部组件被隐藏，返回空组件
2025-08-01 16:32:57.085836: Instance of 'SysConfigData'
2025-08-01 16:32:57.085836: Instance of 'SysConfigData'
2025-08-01 16:32:57.124105: 发送 关闭 阅读器newPort:SendPort
2025-08-01 16:32:57.124105: 读卡器连接已完全关闭
2025-08-01 16:32:57.124105: changeReaders
2025-08-01 16:32:57.124105: createIsolate isOpen:true,isOpening:false
2025-08-01 16:32:57.125093: open():SendPort
2025-08-01 16:32:57.125093: untilDetcted():SendPort
2025-08-01 16:32:57.125093: 读卡器配置完成，共 4 个设备
2025-08-01 16:32:57.125093: subThread :ReaderCommand.close
2025-08-01 16:32:57.125093: commandRsp:ReaderCommand.close
2025-08-01 16:32:57.126103: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析, domain: ************:9091, account: syncDev, psw: syncDev123!, faceDomain: http://10.80.255.253:9000, type: 大连图书馆}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 2, type: 3, readerType: 2, selectedCardType: null, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-01 16:32:57.126103: close:T10Bridge
2025-08-01 16:32:57.126103: dc_exit:0
2025-08-01 16:32:57.126103: close:HWFaceBridge
2025-08-01 16:32:57.126103: isConnect:false
2025-08-01 16:32:57.126103: disconnect
2025-08-01 16:32:57.127088: close:HD100SSBridge
2025-08-01 16:32:57.127088: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-01 16:32:57.127088: HD100SS close result: -1
2025-08-01 16:32:57.127088: close:ScanerBridge
2025-08-01 16:32:57.128084: 发送：[16, 55, 0d]
2025-08-01 16:32:57.128084: subThread :ReaderCommand.readerList
2025-08-01 16:32:57.128084: commandRsp:ReaderCommand.readerList
2025-08-01 16:32:57.128084: readerList：4,readerSetting：4
2025-08-01 16:32:57.129082: cacheUsedReaders:4
2025-08-01 16:32:57.129082: subThread :ReaderCommand.open
2025-08-01 16:32:57.129082: commandRsp:ReaderCommand.open
2025-08-01 16:32:57.206396: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:32:57.206396: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:32:57.206396: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:32:57.207390: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:32:57.207390: ✅ 人脸识别服务重启完成
2025-08-01 16:32:57.229664: dc_init:0xb4 100
2025-08-01 16:32:57.230673: open reader readerType ：10 ret：0
2025-08-01 16:32:57.230673: wsurl:************:9091
2025-08-01 16:32:57.230920: subThread :ReaderCommand.untilDetected
2025-08-01 16:32:57.231430: commandRsp:ReaderCommand.untilDetected
2025-08-01 16:32:57.243578: close:done
2025-08-01 16:32:57.243578: changeType:ReaderErrorType.closeSuccess
2025-08-01 16:32:57.243578: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-01 16:32:57.243578: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-01 16:32:57.244552: already close all reader
2025-08-01 16:32:57.563091: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:32:57.563091: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:32:57.563091: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:32:57.563091: 开锁前关锁命令已发送
2025-08-01 16:32:57.666239: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:32:57.667092: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:32:57.667092: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:32:57.667092: 绿灯命令已发送
2025-08-01 16:32:57.667092: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:32:57.667092: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:32:57.667092: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:32:57.667092: 开锁命令已发送
2025-08-01 16:32:57.717625: AuthView: 检测到 1 个人脸
2025-08-01 16:32:57.797332: AuthView: 检测到 1 个人脸
2025-08-01 16:32:57.877922: AuthView: 检测到 1 个人脸
2025-08-01 16:32:57.958283: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.038727: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.117533: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.197584: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.277497: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.357707: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.438025: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.518484: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.597480: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.668924: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:32:58.668924: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:32:58.668924: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:32:58.668924: 异步关锁命令已发送
2025-08-01 16:32:58.677984: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.721178: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:32:58.721178: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:32:58.721998: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:32:58.721998: 绿灯关闭失败: 操作失败
2025-08-01 16:32:58.758530: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.837551: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.918452: AuthView: 检测到 1 个人脸
2025-08-01 16:32:58.996982: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.077414: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.159081: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.207596: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:32:59.207)
2025-08-01 16:32:59.208593: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:32:59.207)
2025-08-01 16:32:59.208593: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:32:59.208593: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:32:59.208593: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:32:59.208593: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 162.68531799316406, 数据大小: 0 bytes
2025-08-01 16:32:59.208593: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:32:59.208593: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:32:59.208593: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6384 bytes, 实际置信度: 162.68531799316406
2025-08-01 16:32:59.209598: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:32:59.209598: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:32:59.209598: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:32:59.209598: ⏹️ 停止人脸捕获轮询
2025-08-01 16:32:59.209598: 暂停人脸轮询，开始认证流程
2025-08-01 16:32:59.332577: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:32:59.332577: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "417cae22e902ecebc4af27c73921af20",
		"log_id" : "1754037179332",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 94.06,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:32:59.332577: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:32:59.332577: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=94.06
2025-08-01 16:32:59.333553: 人脸识别成功，得分: 94.06，用户ID: 111111
2025-08-01 16:32:59.333553: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:32:59.333553: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:59.333553: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:32:59.333553: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:32:59.333553: 🔄 开始重启人脸识别服务...
2025-08-01 16:32:59.334552: 轮询未在运行
2025-08-01 16:32:59.334552: 轮询未在运行
2025-08-01 16:32:59.334552: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:32:59.334552: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:32:59.334552: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:32:59.334552: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:32:59.334552: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:32:59.334552: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:32:59.335547: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:32:59.336577: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.397757: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.477665: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.558504: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.560658: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-08-01 16:32:59.638176: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.719603: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.797520: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.834313: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:32:59.835136: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:32:59.835136: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:32:59.835136: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:32:59.835136: ✅ 人脸识别服务重启完成
2025-08-01 16:32:59.878841: AuthView: 检测到 1 个人脸
2025-08-01 16:32:59.957795: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.038776: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.118765: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.198050: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.278010: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.357887: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.438759: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.518059: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.598443: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.679048: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.759337: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.837938: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.919068: AuthView: 检测到 1 个人脸
2025-08-01 16:33:00.998304: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.077792: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.158565: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.237007: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.319175: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.398392: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.478648: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.557934: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.637072: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.717815: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.798643: AuthView: 检测到 1 个人脸
2025-08-01 16:33:01.834867: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:01.834)
2025-08-01 16:33:01.834867: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:01.834)
2025-08-01 16:33:01.834867: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:01.835865: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:01.835865: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:01.835865: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 123.8746109008789, 数据大小: 0 bytes
2025-08-01 16:33:01.835865: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:01.835865: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:01.835865: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6162 bytes, 实际置信度: 123.8746109008789
2025-08-01 16:33:01.835865: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:01.835865: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:01.835865: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:01.835865: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:01.835865: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:01.960586: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:01.960586: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "1c7e72714b2c456522e8259a3109e6b3",
		"log_id" : "1754037181960",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 95.43,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:01.960586: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:01.960586: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=95.43
2025-08-01 16:33:01.961583: 人脸识别成功，得分: 95.43，用户ID: 111111
2025-08-01 16:33:01.961583: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:01.961583: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:01.961583: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:01.961583: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:01.962461: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:01.962461: 轮询未在运行
2025-08-01 16:33:01.962461: 轮询未在运行
2025-08-01 16:33:01.962461: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:01.962965: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:01.962965: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:01.962965: 多认证管理器: 人脸识别获得认证请求锁
2025-08-01 16:33:01.962965: 63 CardType 值为空
2025-08-01 16:33:01.962965: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163301  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY4AZF023
2025-08-01 16:33:01.962965: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:01.964962: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.036829: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.117041: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.199569: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.237890: ready error :HttpException: Connection closed before full header was received, uri = http://************:9091
2025-08-01 16:33:02.237890: ready done
2025-08-01 16:33:02.237890: open reader readerType ：2 ret：-1
2025-08-01 16:33:02.237890: changeType:ReaderErrorType.openFail
2025-08-01 16:33:02.237890: 读卡器状态变化: ReaderErrorType.openFail
2025-08-01 16:33:02.237890: 读卡器状态变化: ReaderErrorType.openFail
2025-08-01 16:33:02.237890: 读卡器连接失败，尝试重新连接
2025-08-01 16:33:02.238891: 处理读卡器连接错误
2025-08-01 16:33:02.238891: open Concurrent modification during iteration: Instance(length:0) of '_GrowableList'.
2025-08-01 16:33:02.238891: Error: WebSocketChannelException: WebSocketChannelException: HttpException: Connection closed before full header was received, uri = http://************:9091
2025-08-01 16:33:02.238891: WebSocket 连接已关闭
2025-08-01 16:33:02.276784: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.295487: Rsp : 64              00120250801    163431000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY4AZDBBF
2025-08-01 16:33:02.311775: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163431, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 4AZDBBF}
2025-08-01 16:33:02.312682: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163431, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 4AZDBBF}
2025-08-01 16:33:02.312682: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-08-01 16:33:02.312682: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-08-01 16:33:02.312682: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-08-01 16:33:02.313681: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:02.313681: 多认证管理器状态变更: authenticating
2025-08-01 16:33:02.313681: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:33:02.313681: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:02.313681: 多认证管理器状态变更: completed
2025-08-01 16:33:02.314664: 多认证管理器: 调用认证后处理服务 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:02.314664: 执行认证后处理流程，共2个处理器
2025-08-01 16:33:02.314664: 执行处理器: DoorLockHandler
2025-08-01 16:33:02.314664: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:02.314664: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:02.315661: 收到多认证结果: 人脸识别 - success
2025-08-01 16:33:02.315661: 认证成功，更新主显示方式为: 人脸识别
2025-08-01 16:33:02.315661: === 更新认证反馈状态 ===
2025-08-01 16:33:02.315661: 结果状态: AuthStatus.success
2025-08-01 16:33:02.315661: 用户姓名: gd
2025-08-01 16:33:02.315661: 用户ID: 111111
2025-08-01 16:33:02.315661: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:33:02.315661: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:02.316672: 用户信息: gd (111111)
2025-08-01 16:33:02.316672: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:33:02.316672: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:33:02.316672: 使用认证页面的门锁连接: COM1
2025-08-01 16:33:02.316672: 使用继电器通道: 1
2025-08-01 16:33:02.316672: 使用继电器通道: 1
2025-08-01 16:33:02.316672: 开锁前先关锁（不等待返回）...
2025-08-01 16:33:02.316672: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:02.317659: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:02.317659: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:02.317659: 等待100毫秒确保命令发送...
2025-08-01 16:33:02.334758: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:02.334758: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:02.334758: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:02.334758: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:02.335733: 构建读者证认证界面
2025-08-01 16:33:02.335733: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:02.335733: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:02.335733: === 构建多认证底部组件 ===
2025-08-01 16:33:02.335733: 显示底部组件: true
2025-08-01 16:33:02.335733: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:02.335733: 用户信息: gd (111111)
2025-08-01 16:33:02.335733: 创建AuthFeedbackWidget
2025-08-01 16:33:02.335733: 实际认证方式: AuthMethod.face
2025-08-01 16:33:02.336730: Instance of 'SysConfigData'
2025-08-01 16:33:02.336730: Instance of 'SysConfigData'
2025-08-01 16:33:02.336730: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:02.336730: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:02.336730: 返回成功组件 (详细信息)
2025-08-01 16:33:02.336730: === 构建成功组件（详细信息） ===
2025-08-01 16:33:02.336730: 用户: gd, ID: 111111
2025-08-01 16:33:02.336730: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:33:02.336730: 新状态: AuthFeedbackState.success
2025-08-01 16:33:02.337727: 用户信息: gd (111111)
2025-08-01 16:33:02.337727: 启动定时器，3秒后切换到通行模式
2025-08-01 16:33:02.349695: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:02.349695: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:02.349695: 返回成功组件 (详细信息)
2025-08-01 16:33:02.349695: === 构建成功组件（详细信息） ===
2025-08-01 16:33:02.349695: 用户: gd, ID: 111111
2025-08-01 16:33:02.357677: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.416590: 正在打开绿灯（不等待返回）...
2025-08-01 16:33:02.416590: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:33:02.416590: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:33:02.417587: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:33:02.417587: 正在开门（不等待返回）...
2025-08-01 16:33:02.417587: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:33:02.417587: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:33:02.417587: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:33:02.417587: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:33:02.417587: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:33:02.417587: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:33:02.416590
2025-08-01 16:33:02.417587: 门锁操作完成，连接由认证页面管理
2025-08-01 16:33:02.418584: 自动开门成功 - 用户: gd
2025-08-01 16:33:02.418584: 执行处理器: WelcomeHandler
2025-08-01 16:33:02.418584: 显示欢迎信息给: gd
2025-08-01 16:33:02.418584: 欢迎用户: gd，认证方式: 人脸识别
2025-08-01 16:33:02.418584: 认证后处理流程执行完毕
2025-08-01 16:33:02.418584: 多认证管理器: 认证后处理流程执行完毕
2025-08-01 16:33:02.438558: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.461654: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:02.461654: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:02.462659: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:02.462659: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:02.462659: ✅ 人脸识别服务重启完成
2025-08-01 16:33:02.518494: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.562256: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-01 16:33:02.563076: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:02.563076: 多认证管理器状态变更: listening
2025-08-01 16:33:02.563076: 🔍 检查人脸识别服务健康状态...
2025-08-01 16:33:02.563076: 🔄 重置人脸识别服务认证状态...
2025-08-01 16:33:02.567295: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:02.567295: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:02.568291: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:02.568291: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:02.568291: 构建读者证认证界面
2025-08-01 16:33:02.568291: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:02.568291: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:02.568291: === 构建多认证底部组件 ===
2025-08-01 16:33:02.568291: 显示底部组件: true
2025-08-01 16:33:02.568291: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:02.568291: 用户信息: gd (111111)
2025-08-01 16:33:02.568291: 创建AuthFeedbackWidget
2025-08-01 16:33:02.568291: 实际认证方式: AuthMethod.face
2025-08-01 16:33:02.569296: Instance of 'SysConfigData'
2025-08-01 16:33:02.569296: Instance of 'SysConfigData'
2025-08-01 16:33:02.569296: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:02.569296: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:02.569296: 返回成功组件 (详细信息)
2025-08-01 16:33:02.569296: === 构建成功组件（详细信息） ===
2025-08-01 16:33:02.569296: 用户: gd, ID: 111111
2025-08-01 16:33:02.598211: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.678263: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.758244: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.837044: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.917977: AuthView: 检测到 1 个人脸
2025-08-01 16:33:02.997194: AuthView: 检测到 1 个人脸
2025-08-01 16:33:03.077843: AuthView: 检测到 1 个人脸
2025-08-01 16:33:03.158334: AuthView: 检测到 1 个人脸
2025-08-01 16:33:03.238254: AuthView: 检测到 1 个人脸
2025-08-01 16:33:03.317768: AuthView: 检测到 1 个人脸
2025-08-01 16:33:03.397295: AuthView: 检测到 1 个人脸
2025-08-01 16:33:03.418302: 异步执行关锁动作（不等待返回）...
2025-08-01 16:33:03.418302: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:03.418302: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:03.418302: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:03.469513: 正在关闭绿灯...
2025-08-01 16:33:03.470333: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:33:03.470333: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:33:03.470333: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:33:03.470333: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:33:03.469513
2025-08-01 16:33:03.477564: AuthView: 检测到 1 个人脸
2025-08-01 16:33:04.239824: 尝试重新配置读卡器
2025-08-01 16:33:04.240643: 添加设备配置: 读者证认证 -> 1个设备
2025-08-01 16:33:04.240643: 添加设备配置: 人脸识别认证 -> 1个设备
2025-08-01 16:33:04.240643: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-01 16:33:04.240643: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-01 16:33:04.240643: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-01 16:33:04.240643: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-01 16:33:04.240643: 添加有效设备: type=10, id=10
2025-08-01 16:33:04.241640: 验证读卡器配置: 类型=2, 解码器=不解析
2025-08-01 16:33:04.241640: 添加有效设备: type=2, id=2
2025-08-01 16:33:04.241640: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-01 16:33:04.241640: 添加有效设备: type=13, id=13
2025-08-01 16:33:04.241640: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-01 16:33:04.241640: 添加有效设备: type=12, id=12
2025-08-01 16:33:04.241640: 总共加载了4个设备配置
2025-08-01 16:33:04.241640: stopInventory newPort:SendPort
2025-08-01 16:33:04.241640: subThread :ReaderCommand.stopInventory
2025-08-01 16:33:04.242648: commandRsp:ReaderCommand.stopInventory
2025-08-01 16:33:04.343279: 发送 关闭 阅读器newPort:SendPort
2025-08-01 16:33:04.343279: 读卡器连接已完全关闭
2025-08-01 16:33:04.344274: changeReaders
2025-08-01 16:33:04.344274: createIsolate isOpen:true,isOpening:false
2025-08-01 16:33:04.344274: open():SendPort
2025-08-01 16:33:04.344274: untilDetcted():SendPort
2025-08-01 16:33:04.344274: 读卡器配置完成，共 4 个设备
2025-08-01 16:33:04.344274: subThread :ReaderCommand.close
2025-08-01 16:33:04.345264: commandRsp:ReaderCommand.close
2025-08-01 16:33:04.345264: cacheUsedReaders:()
2025-08-01 16:33:04.345505: close:done
2025-08-01 16:33:04.345505: changeType:ReaderErrorType.closeSuccess
2025-08-01 16:33:04.345505: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-01 16:33:04.346009: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-01 16:33:04.346009: already close all reader
2025-08-01 16:33:04.346009: subThread :ReaderCommand.readerList
2025-08-01 16:33:04.346009: commandRsp:ReaderCommand.readerList
2025-08-01 16:33:04.346009: readerList：4,readerSetting：4
2025-08-01 16:33:04.347010: cacheUsedReaders:4
2025-08-01 16:33:04.347010: subThread :ReaderCommand.open
2025-08-01 16:33:04.347010: commandRsp:ReaderCommand.open
2025-08-01 16:33:04.347010: open reader readerType ：10 ret：0
2025-08-01 16:33:04.348006: wsurl:************:9091
2025-08-01 16:33:04.348006: subThread :ReaderCommand.untilDetected
2025-08-01 16:33:04.348006: commandRsp:ReaderCommand.untilDetected
2025-08-01 16:33:04.463711: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:04.462)
2025-08-01 16:33:04.464680: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:04.462)
2025-08-01 16:33:04.464680: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:04.464680: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:04.464680: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:04.464680: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 133.41815185546875, 数据大小: 0 bytes
2025-08-01 16:33:04.464680: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:04.464680: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:04.464680: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6640 bytes, 实际置信度: 133.41815185546875
2025-08-01 16:33:04.464680: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:04.464680: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:04.464680: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:04.464680: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:04.465679: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:04.591380: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:04.591380: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "8010bb96a72f870d136bd12ec3e59ed6",
		"log_id" : "1754037184591",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 95.68,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:04.593401: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:04.593401: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=95.68
2025-08-01 16:33:04.594371: 人脸识别成功，得分: 95.68，用户ID: 111111
2025-08-01 16:33:04.594371: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:04.594371: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:04.594371: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:04.594371: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:04.594371: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:04.594371: 轮询未在运行
2025-08-01 16:33:04.595368: 轮询未在运行
2025-08-01 16:33:04.595368: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:04.595368: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:04.595368: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:04.595368: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:04.595368: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:04.595368: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:04.595368: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:04.596367: 多认证管理器状态变更: authenticating
2025-08-01 16:33:04.596367: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:04.596367: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:04.597363: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:04.597363: === 更新认证反馈状态 ===
2025-08-01 16:33:04.597363: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:04.597363: 用户姓名: null
2025-08-01 16:33:04.597363: 用户ID: null
2025-08-01 16:33:04.597363: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:04.597363: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:33:04.597363: 显示底部组件: true
2025-08-01 16:33:04.598361: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:04.598361: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:04.598361: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:04.598361: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:04.598361: === 更新认证反馈状态 ===
2025-08-01 16:33:04.598361: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:04.598361: 用户姓名: null
2025-08-01 16:33:04.599370: 用户ID: null
2025-08-01 16:33:04.599370: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:04.599370: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:04.599370: 显示底部组件: true
2025-08-01 16:33:04.601352: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:04.601352: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:04.601352: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:04.602350: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:04.602350: 构建读者证认证界面
2025-08-01 16:33:04.602350: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:04.602350: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:04.602350: === 构建多认证底部组件 ===
2025-08-01 16:33:04.602350: 显示底部组件: true
2025-08-01 16:33:04.602350: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:04.602350: 用户信息: null (null)
2025-08-01 16:33:04.602350: 创建AuthFeedbackWidget
2025-08-01 16:33:04.602350: 实际认证方式: AuthMethod.face
2025-08-01 16:33:04.603348: Instance of 'SysConfigData'
2025-08-01 16:33:04.603348: Instance of 'SysConfigData'
2025-08-01 16:33:04.603348: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:33:04.603348: 新状态: AuthFeedbackState.failure
2025-08-01 16:33:04.603348: 用户信息: null (null)
2025-08-01 16:33:04.603348: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:04.603348: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:04.603348: 返回失败组件
2025-08-01 16:33:04.603348: === 构建失败组件 ===
2025-08-01 16:33:04.604350: 认证方式: AuthMethod.face
2025-08-01 16:33:04.604350: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:04.853525: dc_config_card:0
2025-08-01 16:33:04.869681: dc_card_n_hex:1,len:0
2025-08-01 16:33:04.869681: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:05.095934: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:05.095934: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:05.095934: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:05.095934: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:05.095934: ✅ 人脸识别服务重启完成
2025-08-01 16:33:05.318401: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:33:05.319390: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:05.319390: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:05.319390: 开锁前关锁命令已发送
2025-08-01 16:33:05.418799: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:33:05.418799: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:05.418799: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:05.418799: 绿灯命令已发送
2025-08-01 16:33:05.418799: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:33:05.419800: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:05.419800: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:05.419800: 开锁命令已发送
2025-08-01 16:33:05.610455: iReadCardBas ret:4294967294
2025-08-01 16:33:05.610455: 无卡
2025-08-01 16:33:05.610455: 无卡
2025-08-01 16:33:05.613482: dc_config_card:0
2025-08-01 16:33:05.629403: dc_card_n_hex:1,len:0
2025-08-01 16:33:05.629403: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:06.276997: iReadCardBas ret:4294967294
2025-08-01 16:33:06.276997: 无卡
2025-08-01 16:33:06.277994: 无卡
2025-08-01 16:33:06.277994: dc_config_card:0
2025-08-01 16:33:06.293952: dc_card_n_hex:1,len:0
2025-08-01 16:33:06.293952: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:06.419256: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:33:06.419256: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:06.419256: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:06.419256: 异步关锁命令已发送
2025-08-01 16:33:06.471647: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:33:06.471647: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:06.471647: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:06.471647: 绿灯关闭失败: 操作失败
2025-08-01 16:33:06.940312: iReadCardBas ret:4294967294
2025-08-01 16:33:06.940312: 无卡
2025-08-01 16:33:06.940312: 无卡
2025-08-01 16:33:06.941310: dc_config_card:0
2025-08-01 16:33:06.957498: dc_card_n_hex:1,len:0
2025-08-01 16:33:06.957498: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:07.098239: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:07.098)
2025-08-01 16:33:07.099060: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:33:07.098)
2025-08-01 16:33:07.099060: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:33:07.598019: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:07.598019: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:07.598019: 多认证管理器状态变更: listening
2025-08-01 16:33:07.601987: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:07.601987: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:07.601987: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:07.601987: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:07.601987: 构建读者证认证界面
2025-08-01 16:33:07.602972: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:07.603105: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:07.603105: === 构建多认证底部组件 ===
2025-08-01 16:33:07.603105: 显示底部组件: true
2025-08-01 16:33:07.603105: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:07.603609: 用户信息: null (null)
2025-08-01 16:33:07.603609: 创建AuthFeedbackWidget
2025-08-01 16:33:07.603609: 实际认证方式: AuthMethod.face
2025-08-01 16:33:07.603609: Instance of 'SysConfigData'
2025-08-01 16:33:07.603609: Instance of 'SysConfigData'
2025-08-01 16:33:07.603609: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:07.603609: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:07.603609: 返回失败组件
2025-08-01 16:33:07.604622: === 构建失败组件 ===
2025-08-01 16:33:07.604622: 认证方式: AuthMethod.face
2025-08-01 16:33:07.604622: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:07.604622: 认证完成，还原到默认显示方式
2025-08-01 16:33:07.604622: iReadCardBas ret:4294967294
2025-08-01 16:33:07.604622: 无卡
2025-08-01 16:33:07.605607: 无卡
2025-08-01 16:33:07.605607: dc_config_card:0
2025-08-01 16:33:07.617576: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:07.617576: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:07.618573: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:07.618573: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:07.618573: 构建读者证认证界面
2025-08-01 16:33:07.618573: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:07.618573: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:07.618573: === 构建多认证底部组件 ===
2025-08-01 16:33:07.618573: 显示底部组件: false
2025-08-01 16:33:07.618573: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:33:07.618573: 用户信息: null (null)
2025-08-01 16:33:07.618573: 底部组件被隐藏，返回空组件
2025-08-01 16:33:07.618573: Instance of 'SysConfigData'
2025-08-01 16:33:07.618573: Instance of 'SysConfigData'
2025-08-01 16:33:07.621564: dc_card_n_hex:1,len:0
2025-08-01 16:33:07.621564: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:08.267555: iReadCardBas ret:4294967294
2025-08-01 16:33:08.267555: 无卡
2025-08-01 16:33:08.268551: 无卡
2025-08-01 16:33:08.269547: dc_config_card:0
2025-08-01 16:33:08.285506: dc_card_n_hex:1,len:0
2025-08-01 16:33:08.285506: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:08.518492: AuthView: 检测到 1 个人脸
2025-08-01 16:33:08.597996: AuthView: 检测到 1 个人脸
2025-08-01 16:33:08.676789: AuthView: 检测到 1 个人脸
2025-08-01 16:33:08.757678: AuthView: 检测到 1 个人脸
2025-08-01 16:33:08.837888: AuthView: 检测到 1 个人脸
2025-08-01 16:33:08.918453: AuthView: 检测到 1 个人脸
2025-08-01 16:33:08.931866: iReadCardBas ret:4294967294
2025-08-01 16:33:08.931866: 无卡
2025-08-01 16:33:08.932865: 无卡
2025-08-01 16:33:08.933862: dc_config_card:0
2025-08-01 16:33:08.950289: dc_card_n_hex:1,len:0
2025-08-01 16:33:08.950289: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:08.997032: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.076937: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.098850: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:09.098)
2025-08-01 16:33:09.098850: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:09.098)
2025-08-01 16:33:09.099283: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:09.099283: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:09.099283: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:09.099283: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 167.53427124023438, 数据大小: 0 bytes
2025-08-01 16:33:09.099283: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:09.099283: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:09.099788: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 2475 bytes, 实际置信度: 167.53427124023438
2025-08-01 16:33:09.099788: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:09.099788: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:09.099788: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:09.099788: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:09.099788: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:09.222248: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:09.222248: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "04f791bc44a16f7a17c08ecd991a0b98",
		"log_id" : "1754037189222",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 54.28,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:09.222248: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:09.223219: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=54.28
2025-08-01 16:33:09.223219: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:09.223219: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:09.224216: 认证完成，恢复人脸轮询
2025-08-01 16:33:09.224216: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:09.224216: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:09.224216: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:09.224216: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:09.224216: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:09.224216: 轮询未在运行
2025-08-01 16:33:09.225225: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:09.225225: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:09.225225: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:09.225225: 多认证管理器状态变更: authenticating
2025-08-01 16:33:09.225225: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:09.226210: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:09.226210: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:09.226210: === 更新认证反馈状态 ===
2025-08-01 16:33:09.226210: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:09.226210: 用户姓名: null
2025-08-01 16:33:09.226210: 用户ID: null
2025-08-01 16:33:09.226210: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:09.227207: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:09.227207: 显示底部组件: true
2025-08-01 16:33:09.230202: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:09.230202: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:09.230202: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:09.231198: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:09.231198: 构建读者证认证界面
2025-08-01 16:33:09.231198: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:09.231198: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:09.231198: === 构建多认证底部组件 ===
2025-08-01 16:33:09.231198: 显示底部组件: true
2025-08-01 16:33:09.231198: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:09.231198: 用户信息: null (null)
2025-08-01 16:33:09.232196: 创建AuthFeedbackWidget
2025-08-01 16:33:09.232196: 实际认证方式: AuthMethod.face
2025-08-01 16:33:09.232196: Instance of 'SysConfigData'
2025-08-01 16:33:09.232196: Instance of 'SysConfigData'
2025-08-01 16:33:09.232196: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:09.232196: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:09.232196: 返回失败组件
2025-08-01 16:33:09.232196: === 构建失败组件 ===
2025-08-01 16:33:09.232196: 认证方式: AuthMethod.face
2025-08-01 16:33:09.232196: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:09.234210: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.237182: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.318313: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.397522: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.476961: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.557734: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.595784: iReadCardBas ret:4294967294
2025-08-01 16:33:09.596781: 无卡
2025-08-01 16:33:09.596781: 无卡
2025-08-01 16:33:09.596781: ready error :HttpException: Connection closed before full header was received, uri = http://************:9091
2025-08-01 16:33:09.596781: ready done
2025-08-01 16:33:09.596781: open reader readerType ：2 ret：-1
2025-08-01 16:33:09.596781: Error: WebSocketChannelException: WebSocketChannelException: HttpException: Connection closed before full header was received, uri = http://************:9091
2025-08-01 16:33:09.597789: open reader readerType ：13 ret：0
2025-08-01 16:33:09.608749: 去开启 读 监听
2025-08-01 16:33:09.608749: widget.port.isOpen:true
2025-08-01 16:33:09.611741: 打开COM4读写成功
2025-08-01 16:33:09.611741: 发送：[16, 54, 0d]
2025-08-01 16:33:09.612748: WebSocket 连接已关闭
2025-08-01 16:33:09.612748: open reader readerType ：12 ret：0
2025-08-01 16:33:09.612748: [[10, 0], [2, -1], [13, 0], [12, 0]]
2025-08-01 16:33:09.613737: changeType:ReaderErrorType.openSuccess
2025-08-01 16:33:09.613737: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-01 16:33:09.613737: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-01 16:33:09.613737: 读卡器连接成功
2025-08-01 16:33:09.613737: 读卡器连接成功，确保扫描状态正常
2025-08-01 16:33:09.613737: 恢复读卡器扫描状态...
2025-08-01 16:33:09.613737: 读卡器扫描状态已恢复
2025-08-01 16:33:09.615732: dc_config_card:0
2025-08-01 16:33:09.629831: dc_card_n_hex:1,len:0
2025-08-01 16:33:09.629831: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:09.637804: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.718898: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.725025: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:09.725880: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:09.725880: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:09.725880: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:09.725880: ✅ 人脸识别服务重启完成
2025-08-01 16:33:09.797233: AuthView: 检测到 1 个人脸
2025-08-01 16:33:09.958410: AuthView: 检测到 1 个人脸
2025-08-01 16:33:10.277529: iReadCardBas ret:4294967294
2025-08-01 16:33:10.277529: 无卡
2025-08-01 16:33:10.278499: 无卡
2025-08-01 16:33:10.278499: subThread :ReaderCommand.resumeInventory
2025-08-01 16:33:10.278499: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:33:10.285517: dc_config_card:0
2025-08-01 16:33:10.301675: dc_card_n_hex:1,len:0
2025-08-01 16:33:10.301675: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:10.948359: iReadCardBas ret:4294967294
2025-08-01 16:33:10.948359: 无卡
2025-08-01 16:33:10.949298: 无卡
2025-08-01 16:33:10.957516: dc_config_card:0
2025-08-01 16:33:10.973698: dc_card_n_hex:1,len:0
2025-08-01 16:33:10.973698: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:11.620159: iReadCardBas ret:4294967294
2025-08-01 16:33:11.620159: 无卡
2025-08-01 16:33:11.621135: 无卡
2025-08-01 16:33:11.622133: dc_config_card:0
2025-08-01 16:33:11.638090: dc_card_n_hex:1,len:0
2025-08-01 16:33:11.639094: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:11.727268: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:11.726)
2025-08-01 16:33:11.727268: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:11.726)
2025-08-01 16:33:11.727268: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:11.727268: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:11.728276: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:11.728276: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 147.90126037597656, 数据大小: 0 bytes
2025-08-01 16:33:11.728276: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:11.728276: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:11.728276: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6311 bytes, 实际置信度: 147.90126037597656
2025-08-01 16:33:11.728276: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:11.728276: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:11.729260: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:11.729260: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:11.729260: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:11.846736: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:11.846736: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "cdf9100a4ede8767c24240f1bd9efb7b",
		"log_id" : "1754037191846",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 93.37,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:11.846736: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:11.847733: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=93.37
2025-08-01 16:33:11.847733: 人脸识别成功，得分: 93.37，用户ID: 111111
2025-08-01 16:33:11.847733: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:11.847733: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:11.848730: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:11.848730: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:11.848730: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:11.848730: 轮询未在运行
2025-08-01 16:33:11.848730: 轮询未在运行
2025-08-01 16:33:11.848730: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:11.849728: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:11.849728: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:11.849728: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:11.849728: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:11.849728: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:11.849728: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:11.850725: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:11.850725: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:11.850725: === 更新认证反馈状态 ===
2025-08-01 16:33:11.850725: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:11.850725: 用户姓名: null
2025-08-01 16:33:11.850725: 用户ID: null
2025-08-01 16:33:11.850725: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:11.851722: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:33:11.851722: 显示底部组件: true
2025-08-01 16:33:11.851722: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:11.851722: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:11.851722: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:11.851722: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:11.851722: === 更新认证反馈状态 ===
2025-08-01 16:33:11.852720: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:11.852720: 用户姓名: null
2025-08-01 16:33:11.852720: 用户ID: null
2025-08-01 16:33:11.852720: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:11.852720: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:11.852720: 显示底部组件: true
2025-08-01 16:33:11.854723: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:11.855712: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:11.855712: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:11.855712: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:11.855712: 构建读者证认证界面
2025-08-01 16:33:11.855712: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:11.855712: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:11.855712: === 构建多认证底部组件 ===
2025-08-01 16:33:11.855712: 显示底部组件: true
2025-08-01 16:33:11.855712: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:11.856709: 用户信息: null (null)
2025-08-01 16:33:11.856709: 创建AuthFeedbackWidget
2025-08-01 16:33:11.856709: 实际认证方式: AuthMethod.face
2025-08-01 16:33:11.856709: Instance of 'SysConfigData'
2025-08-01 16:33:11.856709: Instance of 'SysConfigData'
2025-08-01 16:33:11.856709: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:11.856709: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:11.856709: 返回失败组件
2025-08-01 16:33:11.856709: === 构建失败组件 ===
2025-08-01 16:33:11.856709: 认证方式: AuthMethod.face
2025-08-01 16:33:11.856709: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:12.227327: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:12.227327: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:12.228166: 多认证管理器状态变更: listening
2025-08-01 16:33:12.235146: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:12.235146: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:12.235146: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:12.235146: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:12.235146: 构建读者证认证界面
2025-08-01 16:33:12.235146: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:12.235146: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:12.236145: === 构建多认证底部组件 ===
2025-08-01 16:33:12.236145: 显示底部组件: true
2025-08-01 16:33:12.236145: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:12.236145: 用户信息: null (null)
2025-08-01 16:33:12.236145: 创建AuthFeedbackWidget
2025-08-01 16:33:12.236145: 实际认证方式: AuthMethod.face
2025-08-01 16:33:12.236145: Instance of 'SysConfigData'
2025-08-01 16:33:12.236145: Instance of 'SysConfigData'
2025-08-01 16:33:12.236145: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:12.236145: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:12.237141: 返回失败组件
2025-08-01 16:33:12.237141: === 构建失败组件 ===
2025-08-01 16:33:12.237141: 认证方式: AuthMethod.face
2025-08-01 16:33:12.237141: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:12.284015: iReadCardBas ret:4294967294
2025-08-01 16:33:12.284015: 无卡
2025-08-01 16:33:12.284015: 无卡
2025-08-01 16:33:12.286018: dc_config_card:0
2025-08-01 16:33:12.302061: dc_card_n_hex:1,len:0
2025-08-01 16:33:12.302061: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:12.349141: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:12.349141: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:12.349141: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:12.350150: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:12.350150: ✅ 人脸识别服务重启完成
2025-08-01 16:33:12.948061: iReadCardBas ret:4294967294
2025-08-01 16:33:12.949057: 无卡
2025-08-01 16:33:12.949057: 无卡
2025-08-01 16:33:12.950055: dc_config_card:0
2025-08-01 16:33:12.966198: dc_card_n_hex:1,len:0
2025-08-01 16:33:12.966198: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:13.612356: iReadCardBas ret:4294967294
2025-08-01 16:33:13.612356: 无卡
2025-08-01 16:33:13.613354: 无卡
2025-08-01 16:33:13.613354: dc_config_card:0
2025-08-01 16:33:13.629723: dc_card_n_hex:1,len:0
2025-08-01 16:33:13.629723: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:14.278256: iReadCardBas ret:4294967294
2025-08-01 16:33:14.278256: 无卡
2025-08-01 16:33:14.279254: 无卡
2025-08-01 16:33:14.285239: dc_config_card:0
2025-08-01 16:33:14.301461: dc_card_n_hex:1,len:0
2025-08-01 16:33:14.302457: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:14.350202: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:14.350)
2025-08-01 16:33:14.350202: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:33:14.350)
2025-08-01 16:33:14.350202: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:33:14.853293: 认证完成，还原到默认显示方式
2025-08-01 16:33:14.868064: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:14.868064: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:14.868064: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:14.868064: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:14.868064: 构建读者证认证界面
2025-08-01 16:33:14.868064: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:14.868064: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:14.868064: === 构建多认证底部组件 ===
2025-08-01 16:33:14.868064: 显示底部组件: false
2025-08-01 16:33:14.869075: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:33:14.869075: 用户信息: null (null)
2025-08-01 16:33:14.869075: 底部组件被隐藏，返回空组件
2025-08-01 16:33:14.869075: Instance of 'SysConfigData'
2025-08-01 16:33:14.869075: Instance of 'SysConfigData'
2025-08-01 16:33:14.948650: iReadCardBas ret:4294967294
2025-08-01 16:33:14.948650: 无卡
2025-08-01 16:33:14.948650: 无卡
2025-08-01 16:33:14.957633: dc_config_card:0
2025-08-01 16:33:14.974033: dc_card_n_hex:1,len:0
2025-08-01 16:33:14.974033: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:15.620636: iReadCardBas ret:4294967294
2025-08-01 16:33:15.620636: 无卡
2025-08-01 16:33:15.621633: 无卡
2025-08-01 16:33:15.629691: dc_config_card:0
2025-08-01 16:33:15.645662: dc_card_n_hex:1,len:0
2025-08-01 16:33:15.646566: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:16.292988: iReadCardBas ret:4294967294
2025-08-01 16:33:16.292988: 无卡
2025-08-01 16:33:16.292988: 无卡
2025-08-01 16:33:16.302087: dc_config_card:0
2025-08-01 16:33:16.317940: dc_card_n_hex:1,len:0
2025-08-01 16:33:16.317940: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:16.350362: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:16.350)
2025-08-01 16:33:16.350362: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:33:16.350)
2025-08-01 16:33:16.350362: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:33:16.852876: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-08-01 16:33:16.964161: iReadCardBas ret:4294967294
2025-08-01 16:33:16.965170: 无卡
2025-08-01 16:33:16.965170: 无卡
2025-08-01 16:33:16.973443: dc_config_card:0
2025-08-01 16:33:16.989686: dc_card_n_hex:1,len:0
2025-08-01 16:33:16.990518: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:17.476931: AuthView: 检测到 1 个人脸
2025-08-01 16:33:17.558148: AuthView: 检测到 1 个人脸
2025-08-01 16:33:17.636573: iReadCardBas ret:4294967294
2025-08-01 16:33:17.636573: 无卡
2025-08-01 16:33:17.637536: 无卡
2025-08-01 16:33:17.638562: AuthView: 检测到 1 个人脸
2025-08-01 16:33:17.645675: dc_config_card:0
2025-08-01 16:33:17.661933: dc_card_n_hex:1,len:0
2025-08-01 16:33:17.661933: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:17.717631: AuthView: 检测到 1 个人脸
2025-08-01 16:33:17.797808: AuthView: 检测到 1 个人脸
2025-08-01 16:33:17.876995: AuthView: 检测到 1 个人脸
2025-08-01 16:33:17.958315: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.037589: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.118225: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.197273: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.277127: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.308527: iReadCardBas ret:4294967294
2025-08-01 16:33:18.308527: 无卡
2025-08-01 16:33:18.308527: 无卡
2025-08-01 16:33:18.309547: dc_config_card:0
2025-08-01 16:33:18.325486: dc_card_n_hex:1,len:0
2025-08-01 16:33:18.325486: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:18.350642: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:18.350)
2025-08-01 16:33:18.351651: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:18.350)
2025-08-01 16:33:18.351651: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:18.351651: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:18.351651: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:18.351651: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 167.06393432617188, 数据大小: 0 bytes
2025-08-01 16:33:18.351651: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:18.352636: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:18.352636: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 2030 bytes, 实际置信度: 167.06393432617188
2025-08-01 16:33:18.352636: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:18.352636: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:18.352636: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:18.352636: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:18.352636: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:18.369618: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:18.369618: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"log_id" : "1754037198369"
	},
	"errno" : -1008,
	"msg" : "get face feature fail"
}
2025-08-01 16:33:18.369618: ❌ 百度SDK返回错误: errno=-1008, msg=get face feature fail
2025-08-01 16:33:18.369618: 人脸识别异常: Exception: 人脸特征提取失败，请确保图像中有清晰的人脸
2025-08-01 16:33:18.370588: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:18.370588: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:18.370588: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:18.370588: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:18.371596: 轮询未在运行
2025-08-01 16:33:18.371596: 轮询未在运行
2025-08-01 16:33:18.371596: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:18.371596: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:18.372582: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:18.372582: 多认证管理器状态变更: authenticating
2025-08-01 16:33:18.372582: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:18.372582: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:18.372582: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:18.372582: === 更新认证反馈状态 ===
2025-08-01 16:33:18.373580: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:18.373580: 用户姓名: null
2025-08-01 16:33:18.373580: 用户ID: null
2025-08-01 16:33:18.373580: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:18.373580: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:18.373580: 显示底部组件: true
2025-08-01 16:33:18.375575: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.385549: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:18.385549: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:18.385549: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:18.385549: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:18.385549: 构建读者证认证界面
2025-08-01 16:33:18.386559: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:18.386559: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:18.386559: === 构建多认证底部组件 ===
2025-08-01 16:33:18.386559: 显示底部组件: true
2025-08-01 16:33:18.386559: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:18.386559: 用户信息: null (null)
2025-08-01 16:33:18.386559: 创建AuthFeedbackWidget
2025-08-01 16:33:18.386559: 实际认证方式: AuthMethod.face
2025-08-01 16:33:18.386559: Instance of 'SysConfigData'
2025-08-01 16:33:18.386559: Instance of 'SysConfigData'
2025-08-01 16:33:18.386559: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:18.387544: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:18.387544: 返回失败组件
2025-08-01 16:33:18.387544: === 构建失败组件 ===
2025-08-01 16:33:18.387544: 认证方式: AuthMethod.face
2025-08-01 16:33:18.387544: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:18.437989: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.518868: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.597928: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.678007: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.758001: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.837607: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.870972: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:18.870972: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:18.870972: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:18.870972: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:18.870972: ✅ 人脸识别服务重启完成
2025-08-01 16:33:18.918517: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.972049: iReadCardBas ret:4294967294
2025-08-01 16:33:18.973096: 无卡
2025-08-01 16:33:18.973096: 无卡
2025-08-01 16:33:18.982103: dc_config_card:0
2025-08-01 16:33:18.998476: AuthView: 检测到 1 个人脸
2025-08-01 16:33:18.998476: dc_card_n_hex:1,len:0
2025-08-01 16:33:18.998476: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:19.077337: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.157680: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.237924: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.317610: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.397162: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.477878: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.557007: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.638089: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.644072: iReadCardBas ret:4294967294
2025-08-01 16:33:19.644072: 无卡
2025-08-01 16:33:19.644072: 无卡
2025-08-01 16:33:19.645070: dc_config_card:0
2025-08-01 16:33:19.662025: dc_card_n_hex:1,len:0
2025-08-01 16:33:19.662025: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:19.717875: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.797535: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.887477: AuthView: 检测到 1 个人脸
2025-08-01 16:33:19.958181: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.038405: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.118611: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.198399: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.276809: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.307947: iReadCardBas ret:4294967294
2025-08-01 16:33:20.307947: 无卡
2025-08-01 16:33:20.307947: 无卡
2025-08-01 16:33:20.309984: dc_config_card:0
2025-08-01 16:33:20.325383: dc_card_n_hex:1,len:0
2025-08-01 16:33:20.326353: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:20.358295: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.437018: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.518519: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.598043: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.677062: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.758975: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.838475: AuthView: 检测到 1 个人脸
2025-08-01 16:33:20.873535: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:20.872)
2025-08-01 16:33:20.873535: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:20.872)
2025-08-01 16:33:20.873535: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:20.873535: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:20.873535: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:20.873535: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 83.99684143066406, 数据大小: 0 bytes
2025-08-01 16:33:20.873535: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:20.873535: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:20.873535: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6515 bytes, 实际置信度: 83.99684143066406
2025-08-01 16:33:20.874531: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:20.874531: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:20.874531: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:20.874531: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:20.874531: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:20.994211: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:20.995210: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "078b349ce695392803c1d82f6e238879",
		"log_id" : "1754037200994",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 91.81,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:20.995210: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:20.995210: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=91.81
2025-08-01 16:33:20.995210: 人脸识别成功，得分: 91.81，用户ID: 111111
2025-08-01 16:33:20.996205: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:20.996205: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:20.996205: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:20.996205: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:20.997208: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:20.997208: 轮询未在运行
2025-08-01 16:33:20.997208: 轮询未在运行
2025-08-01 16:33:20.997208: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:20.997208: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:20.997208: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:20.997208: 多认证管理器: 人脸识别获得认证请求锁
2025-08-01 16:33:20.998199: 63 CardType 值为空
2025-08-01 16:33:20.998199: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163320  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY5AZF021
2025-08-01 16:33:20.998199: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:20.998199: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:20.998199: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:20.999196: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:20.999196: === 更新认证反馈状态 ===
2025-08-01 16:33:20.999196: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:20.999196: 用户姓名: null
2025-08-01 16:33:20.999196: 用户ID: null
2025-08-01 16:33:20.999196: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:20.999196: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:21.000194: 显示底部组件: true
2025-08-01 16:33:21.001191: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.002189: iReadCardBas ret:4294967294
2025-08-01 16:33:21.002189: 无卡
2025-08-01 16:33:21.002189: 无卡
2025-08-01 16:33:21.002189: dc_config_card:0
2025-08-01 16:33:21.002189: dc_card_n_hex:1,len:0
2025-08-01 16:33:21.002189: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:21.018148: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:21.018148: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:21.018148: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:21.018148: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:21.018148: 构建读者证认证界面
2025-08-01 16:33:21.018148: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:21.019144: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:21.019144: === 构建多认证底部组件 ===
2025-08-01 16:33:21.019144: 显示底部组件: true
2025-08-01 16:33:21.019144: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:21.019144: 用户信息: null (null)
2025-08-01 16:33:21.019144: 创建AuthFeedbackWidget
2025-08-01 16:33:21.019144: 实际认证方式: AuthMethod.face
2025-08-01 16:33:21.019144: Instance of 'SysConfigData'
2025-08-01 16:33:21.019144: Instance of 'SysConfigData'
2025-08-01 16:33:21.019144: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:21.019144: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:21.019144: 返回失败组件
2025-08-01 16:33:21.019144: === 构建失败组件 ===
2025-08-01 16:33:21.020142: 认证方式: AuthMethod.face
2025-08-01 16:33:21.020142: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:21.079191: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.158565: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.238351: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.241323: Rsp : 64              00120250801    163450000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY5AZDBBD
2025-08-01 16:33:21.247340: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163450, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 5AZDBBD}
2025-08-01 16:33:21.247340: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163450, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 5AZDBBD}
2025-08-01 16:33:21.248304: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-08-01 16:33:21.248304: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-08-01 16:33:21.248304: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-08-01 16:33:21.248304: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:33:21.248304: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:21.248304: 多认证管理器状态变更: completed
2025-08-01 16:33:21.249302: 多认证管理器: 调用认证后处理服务 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:21.249302: 执行认证后处理流程，共2个处理器
2025-08-01 16:33:21.249302: 执行处理器: DoorLockHandler
2025-08-01 16:33:21.249302: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:21.250298: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:21.250298: 收到多认证结果: 人脸识别 - success
2025-08-01 16:33:21.250298: 认证成功，更新主显示方式为: 人脸识别
2025-08-01 16:33:21.250298: === 更新认证反馈状态 ===
2025-08-01 16:33:21.250298: 结果状态: AuthStatus.success
2025-08-01 16:33:21.250298: 用户姓名: gd
2025-08-01 16:33:21.250298: 用户ID: 111111
2025-08-01 16:33:21.250298: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:33:21.250298: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:21.250298: 用户信息: gd (111111)
2025-08-01 16:33:21.251295: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:33:21.251295: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:33:21.251295: 使用认证页面的门锁连接: COM1
2025-08-01 16:33:21.251295: 使用继电器通道: 1
2025-08-01 16:33:21.251295: 使用继电器通道: 1
2025-08-01 16:33:21.251295: 开锁前先关锁（不等待返回）...
2025-08-01 16:33:21.251295: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:21.251295: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:21.252293: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:21.252293: 等待100毫秒确保命令发送...
2025-08-01 16:33:21.254290: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:21.254290: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:21.254290: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:21.255285: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:21.255285: 构建读者证认证界面
2025-08-01 16:33:21.255285: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:21.255285: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:21.255285: === 构建多认证底部组件 ===
2025-08-01 16:33:21.255285: 显示底部组件: true
2025-08-01 16:33:21.255285: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:21.255285: 用户信息: gd (111111)
2025-08-01 16:33:21.255285: 创建AuthFeedbackWidget
2025-08-01 16:33:21.255285: 实际认证方式: AuthMethod.face
2025-08-01 16:33:21.256282: Instance of 'SysConfigData'
2025-08-01 16:33:21.256282: Instance of 'SysConfigData'
2025-08-01 16:33:21.256282: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:33:21.256282: 新状态: AuthFeedbackState.success
2025-08-01 16:33:21.256282: 用户信息: gd (111111)
2025-08-01 16:33:21.256282: 启动定时器，3秒后切换到通行模式
2025-08-01 16:33:21.256282: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:21.256282: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:21.257281: 返回成功组件 (详细信息)
2025-08-01 16:33:21.257281: === 构建成功组件（详细信息） ===
2025-08-01 16:33:21.257281: 用户: gd, ID: 111111
2025-08-01 16:33:21.318642: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.351738: 正在打开绿灯（不等待返回）...
2025-08-01 16:33:21.352587: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:33:21.352587: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:33:21.352587: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:33:21.352587: 正在开门（不等待返回）...
2025-08-01 16:33:21.352587: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:33:21.352587: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:33:21.352587: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:33:21.352587: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:33:21.353581: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:33:21.353581: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:33:21.351738
2025-08-01 16:33:21.353581: 门锁操作完成，连接由认证页面管理
2025-08-01 16:33:21.353581: 自动开门成功 - 用户: gd
2025-08-01 16:33:21.353581: 执行处理器: WelcomeHandler
2025-08-01 16:33:21.353581: 显示欢迎信息给: gd
2025-08-01 16:33:21.353581: 欢迎用户: gd，认证方式: 人脸识别
2025-08-01 16:33:21.354567: 认证后处理流程执行完毕
2025-08-01 16:33:21.354567: 多认证管理器: 认证后处理流程执行完毕
2025-08-01 16:33:21.398365: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.477337: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.496630: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:21.496630: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:21.496630: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:21.497623: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:21.497623: ✅ 人脸识别服务重启完成
2025-08-01 16:33:21.557148: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.636156: iReadCardBas ret:4294967294
2025-08-01 16:33:21.636156: 无卡
2025-08-01 16:33:21.636156: 无卡
2025-08-01 16:33:21.638148: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.639146: dc_config_card:0
2025-08-01 16:33:21.653109: dc_card_n_hex:1,len:0
2025-08-01 16:33:21.654106: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:21.718204: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.797941: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.877855: AuthView: 检测到 1 个人脸
2025-08-01 16:33:21.958739: AuthView: 检测到 1 个人脸
2025-08-01 16:33:22.037121: AuthView: 检测到 1 个人脸
2025-08-01 16:33:22.118586: AuthView: 检测到 1 个人脸
2025-08-01 16:33:22.198029: AuthView: 检测到 1 个人脸
2025-08-01 16:33:22.276745: AuthView: 检测到 1 个人脸
2025-08-01 16:33:22.300563: iReadCardBas ret:4294967294
2025-08-01 16:33:22.300563: 无卡
2025-08-01 16:33:22.300563: 无卡
2025-08-01 16:33:22.301559: dc_config_card:0
2025-08-01 16:33:22.317225: dc_card_n_hex:1,len:0
2025-08-01 16:33:22.318220: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:22.353225: 异步执行关锁动作（不等待返回）...
2025-08-01 16:33:22.353225: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:22.353225: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:22.354075: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:22.358279: AuthView: 检测到 1 个人脸
2025-08-01 16:33:22.404826: 正在关闭绿灯...
2025-08-01 16:33:22.404826: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:33:22.404826: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:33:22.405836: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:33:22.405836: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:33:22.404826
2025-08-01 16:33:22.438269: AuthView: 检测到 1 个人脸
2025-08-01 16:33:22.964554: iReadCardBas ret:4294967294
2025-08-01 16:33:22.964554: 无卡
2025-08-01 16:33:22.964554: 无卡
2025-08-01 16:33:22.973529: dc_config_card:0
2025-08-01 16:33:22.989663: dc_card_n_hex:1,len:0
2025-08-01 16:33:22.990486: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:23.498971: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:23.498)
2025-08-01 16:33:23.498971: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:23.498)
2025-08-01 16:33:23.498971: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:23.498971: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:23.498971: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:23.498971: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 77.2813720703125, 数据大小: 0 bytes
2025-08-01 16:33:23.498971: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:23.499968: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:23.499968: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6945 bytes, 实际置信度: 77.2813720703125
2025-08-01 16:33:23.499968: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:23.499968: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:23.499968: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:23.499968: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:23.499968: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:23.617653: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:23.617653: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "002899457e511931f6f7abfb8dc80ec9",
		"log_id" : "1754037203618",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 93.07,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:23.618662: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:23.618662: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=93.07
2025-08-01 16:33:23.618662: 人脸识别成功，得分: 93.07，用户ID: 111111
2025-08-01 16:33:23.618662: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:23.619654: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:23.619654: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:23.619654: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:23.619654: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:23.619654: 轮询未在运行
2025-08-01 16:33:23.619654: 轮询未在运行
2025-08-01 16:33:23.619654: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:23.620655: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:23.620655: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:23.620655: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:23.620655: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:23.620655: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:23.620655: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:23.636505: iReadCardBas ret:4294967294
2025-08-01 16:33:23.636505: 无卡
2025-08-01 16:33:23.636505: 无卡
2025-08-01 16:33:23.639499: dc_config_card:0
2025-08-01 16:33:23.653526: dc_card_n_hex:1,len:0
2025-08-01 16:33:23.653526: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:23.999943: 认证完成，还原到默认显示方式
2025-08-01 16:33:24.018327: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:24.018327: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:24.018327: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:24.018327: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:24.018327: 构建读者证认证界面
2025-08-01 16:33:24.018327: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:24.019324: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:24.019324: === 构建多认证底部组件 ===
2025-08-01 16:33:24.019324: 显示底部组件: false
2025-08-01 16:33:24.019324: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:33:24.019324: 用户信息: null (null)
2025-08-01 16:33:24.019324: 底部组件被隐藏，返回空组件
2025-08-01 16:33:24.019324: Instance of 'SysConfigData'
2025-08-01 16:33:24.020322: Instance of 'SysConfigData'
2025-08-01 16:33:24.120249: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:24.120249: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:24.120249: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:24.120249: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:24.120249: ✅ 人脸识别服务重启完成
2025-08-01 16:33:24.251621: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:33:24.251621: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:24.251621: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:24.252606: 开锁前关锁命令已发送
2025-08-01 16:33:24.299916: iReadCardBas ret:4294967294
2025-08-01 16:33:24.299916: 无卡
2025-08-01 16:33:24.300915: 无卡
2025-08-01 16:33:24.301943: dc_config_card:0
2025-08-01 16:33:24.317643: dc_card_n_hex:1,len:0
2025-08-01 16:33:24.318491: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:24.354130: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:33:24.354130: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:24.354130: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:24.354130: 绿灯命令已发送
2025-08-01 16:33:24.355091: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:33:24.355091: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:24.355091: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:24.355091: 开锁命令已发送
2025-08-01 16:33:24.964643: iReadCardBas ret:4294967294
2025-08-01 16:33:24.964643: 无卡
2025-08-01 16:33:24.964643: 无卡
2025-08-01 16:33:24.973618: dc_config_card:0
2025-08-01 16:33:24.989770: dc_card_n_hex:1,len:0
2025-08-01 16:33:24.989770: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:25.354421: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:33:25.354421: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:25.355230: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:25.355230: 异步关锁命令已发送
2025-08-01 16:33:25.407018: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:33:25.407018: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:25.407018: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:25.407018: 绿灯关闭失败: 操作失败
2025-08-01 16:33:25.635878: iReadCardBas ret:4294967294
2025-08-01 16:33:25.635878: 无卡
2025-08-01 16:33:25.635878: 无卡
2025-08-01 16:33:25.637872: dc_config_card:0
2025-08-01 16:33:25.653829: dc_card_n_hex:1,len:0
2025-08-01 16:33:25.653829: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:26.121433: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:26.121)
2025-08-01 16:33:26.122254: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:33:26.121)
2025-08-01 16:33:26.122254: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:33:26.249669: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-08-01 16:33:26.299642: iReadCardBas ret:4294967294
2025-08-01 16:33:26.300468: 无卡
2025-08-01 16:33:26.300468: 无卡
2025-08-01 16:33:26.301459: dc_config_card:0
2025-08-01 16:33:26.317817: dc_card_n_hex:1,len:0
2025-08-01 16:33:26.317817: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:26.964728: iReadCardBas ret:4294967294
2025-08-01 16:33:26.964728: 无卡
2025-08-01 16:33:26.965732: 无卡
2025-08-01 16:33:26.973908: dc_config_card:0
2025-08-01 16:33:26.990091: dc_card_n_hex:1,len:0
2025-08-01 16:33:26.990091: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:27.238054: AuthView: 检测到 1 个人脸
2025-08-01 16:33:27.636602: iReadCardBas ret:4294967294
2025-08-01 16:33:27.637601: 无卡
2025-08-01 16:33:27.637601: 无卡
2025-08-01 16:33:27.645754: dc_config_card:0
2025-08-01 16:33:27.661253: dc_card_n_hex:1,len:0
2025-08-01 16:33:27.662250: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:28.121281: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:28.121)
2025-08-01 16:33:28.122348: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:28.121)
2025-08-01 16:33:28.122348: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:28.122851: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:28.122851: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:28.122851: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 180.25279235839844, 数据大小: 0 bytes
2025-08-01 16:33:28.122851: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:28.122851: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:28.122851: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3630 bytes, 实际置信度: 180.25279235839844
2025-08-01 16:33:28.122851: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:28.122851: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:28.123853: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:28.123853: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:28.123853: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:28.255500: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:28.255500: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "0039a2dbaa6525c3fe563b9ead3cf035",
		"log_id" : "1754037208255",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 44.47,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:28.255500: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:28.255500: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=44.47
2025-08-01 16:33:28.256504: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:28.256504: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:28.256504: 认证完成，恢复人脸轮询
2025-08-01 16:33:28.256504: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:28.256504: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:28.256504: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:28.257494: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:28.257494: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:28.257494: 轮询未在运行
2025-08-01 16:33:28.257494: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:28.257494: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:28.308416: iReadCardBas ret:4294967294
2025-08-01 16:33:28.308416: 无卡
2025-08-01 16:33:28.308416: 无卡
2025-08-01 16:33:28.309412: dc_config_card:0
2025-08-01 16:33:28.325601: dc_card_n_hex:1,len:0
2025-08-01 16:33:28.325601: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:28.757608: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:28.757608: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:28.757608: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:28.757608: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:28.757608: ✅ 人脸识别服务重启完成
2025-08-01 16:33:28.971624: iReadCardBas ret:4294967294
2025-08-01 16:33:28.972626: 无卡
2025-08-01 16:33:28.972626: 无卡
2025-08-01 16:33:28.973623: dc_config_card:0
2025-08-01 16:33:28.989099: dc_card_n_hex:1,len:0
2025-08-01 16:33:28.990097: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:29.249797: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-01 16:33:29.249797: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:29.249797: 多认证管理器状态变更: listening
2025-08-01 16:33:29.250795: 🔍 检查人脸识别服务健康状态...
2025-08-01 16:33:29.250795: 🔄 重置人脸识别服务认证状态...
2025-08-01 16:33:29.268758: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:29.268758: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:29.268758: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:29.268758: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:29.268758: 构建读者证认证界面
2025-08-01 16:33:29.268758: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:29.268758: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:29.268758: === 构建多认证底部组件 ===
2025-08-01 16:33:29.269744: 显示底部组件: false
2025-08-01 16:33:29.269744: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:33:29.269744: 用户信息: null (null)
2025-08-01 16:33:29.269744: 底部组件被隐藏，返回空组件
2025-08-01 16:33:29.269744: Instance of 'SysConfigData'
2025-08-01 16:33:29.269744: Instance of 'SysConfigData'
2025-08-01 16:33:29.635404: iReadCardBas ret:4294967294
2025-08-01 16:33:29.635404: 无卡
2025-08-01 16:33:29.636416: 无卡
2025-08-01 16:33:29.637399: dc_config_card:0
2025-08-01 16:33:29.653385: dc_card_n_hex:1,len:0
2025-08-01 16:33:29.654355: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:30.197404: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.277607: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.299916: iReadCardBas ret:4294967294
2025-08-01 16:33:30.299916: 无卡
2025-08-01 16:33:30.300912: 无卡
2025-08-01 16:33:30.301911: dc_config_card:0
2025-08-01 16:33:30.317868: dc_card_n_hex:1,len:0
2025-08-01 16:33:30.317868: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:30.357837: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.437326: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.518583: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.598391: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.677558: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.758960: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.758960: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:30.758)
2025-08-01 16:33:30.759938: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:30.758)
2025-08-01 16:33:30.759938: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:30.759938: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:30.759938: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:30.759938: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 166.94142150878906, 数据大小: 0 bytes
2025-08-01 16:33:30.759938: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:30.760929: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:30.760929: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 1534 bytes, 实际置信度: 166.94142150878906
2025-08-01 16:33:30.760929: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:30.760929: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:30.760929: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:30.760929: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:30.760929: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:30.778881: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:30.778881: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"log_id" : "1754037210778"
	},
	"errno" : -1008,
	"msg" : "get face feature fail"
}
2025-08-01 16:33:30.778881: ❌ 百度SDK返回错误: errno=-1008, msg=get face feature fail
2025-08-01 16:33:30.778881: 人脸识别异常: Exception: 人脸特征提取失败，请确保图像中有清晰的人脸
2025-08-01 16:33:30.778881: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:30.779876: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:30.779876: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:30.779876: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:30.779876: 轮询未在运行
2025-08-01 16:33:30.779876: 轮询未在运行
2025-08-01 16:33:30.779876: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:30.779876: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:30.779876: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:30.779876: 多认证管理器状态变更: authenticating
2025-08-01 16:33:30.780874: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:30.780874: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:30.780874: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:30.780874: === 更新认证反馈状态 ===
2025-08-01 16:33:30.780874: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:30.780874: 用户姓名: null
2025-08-01 16:33:30.781872: 用户ID: null
2025-08-01 16:33:30.781872: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:30.781872: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:30.781872: 显示底部组件: true
2025-08-01 16:33:30.783865: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:30.784863: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:30.784863: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:30.784863: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:30.784863: 构建读者证认证界面
2025-08-01 16:33:30.784863: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:30.784863: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:30.784863: === 构建多认证底部组件 ===
2025-08-01 16:33:30.784863: 显示底部组件: true
2025-08-01 16:33:30.784863: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:30.784863: 用户信息: null (null)
2025-08-01 16:33:30.784863: 创建AuthFeedbackWidget
2025-08-01 16:33:30.785861: 实际认证方式: AuthMethod.face
2025-08-01 16:33:30.785861: Instance of 'SysConfigData'
2025-08-01 16:33:30.785861: Instance of 'SysConfigData'
2025-08-01 16:33:30.785861: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:30.785861: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:30.785861: 返回失败组件
2025-08-01 16:33:30.785861: === 构建失败组件 ===
2025-08-01 16:33:30.785861: 认证方式: AuthMethod.face
2025-08-01 16:33:30.785861: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:30.837593: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.918258: AuthView: 检测到 1 个人脸
2025-08-01 16:33:30.964263: iReadCardBas ret:4294967294
2025-08-01 16:33:30.964263: 无卡
2025-08-01 16:33:30.964263: 无卡
2025-08-01 16:33:30.966207: dc_config_card:0
2025-08-01 16:33:30.981720: dc_card_n_hex:1,len:0
2025-08-01 16:33:30.981720: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:30.996979: AuthView: 检测到 1 个人脸
2025-08-01 16:33:31.280633: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:31.280633: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:31.280633: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:31.281458: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:31.281458: ✅ 人脸识别服务重启完成
2025-08-01 16:33:31.318736: AuthView: 检测到 1 个人脸
2025-08-01 16:33:31.478307: AuthView: 检测到 1 个人脸
2025-08-01 16:33:31.558404: AuthView: 检测到 1 个人脸
2025-08-01 16:33:31.628617: iReadCardBas ret:4294967294
2025-08-01 16:33:31.628617: 无卡
2025-08-01 16:33:31.628617: 无卡
2025-08-01 16:33:31.638557: dc_config_card:0
2025-08-01 16:33:31.653634: dc_card_n_hex:1,len:0
2025-08-01 16:33:31.653634: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:31.957766: AuthView: 检测到 1 个人脸
2025-08-01 16:33:32.300618: iReadCardBas ret:4294967294
2025-08-01 16:33:32.300908: 无卡
2025-08-01 16:33:32.300908: 无卡
2025-08-01 16:33:32.301480: dc_config_card:0
2025-08-01 16:33:32.317510: dc_card_n_hex:1,len:0
2025-08-01 16:33:32.317510: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:32.965053: iReadCardBas ret:4294967294
2025-08-01 16:33:32.965053: 无卡
2025-08-01 16:33:32.965053: 无卡
2025-08-01 16:33:32.973280: dc_config_card:0
2025-08-01 16:33:32.990221: dc_card_n_hex:1,len:0
2025-08-01 16:33:32.990221: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:33.283546: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:33.283)
2025-08-01 16:33:33.284465: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:33.283)
2025-08-01 16:33:33.284465: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:33.284465: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:33.284465: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:33.284465: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 160.93954467773438, 数据大小: 0 bytes
2025-08-01 16:33:33.284465: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:33.284465: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:33.284465: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 1531 bytes, 实际置信度: 160.93954467773438
2025-08-01 16:33:33.285462: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:33.285462: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:33.285462: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:33.285462: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:33.285462: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:33.299426: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:33.299426: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"log_id" : "1754037213299"
	},
	"errno" : -1008,
	"msg" : "get face feature fail"
}
2025-08-01 16:33:33.299426: ❌ 百度SDK返回错误: errno=-1008, msg=get face feature fail
2025-08-01 16:33:33.299426: 人脸识别异常: Exception: 人脸特征提取失败，请确保图像中有清晰的人脸
2025-08-01 16:33:33.300423: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:33.300423: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:33.300423: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:33.300423: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:33.300423: 轮询未在运行
2025-08-01 16:33:33.301421: 轮询未在运行
2025-08-01 16:33:33.301421: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:33.301421: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:33.301421: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:33.301421: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:33.301421: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:33.301421: === 更新认证反馈状态 ===
2025-08-01 16:33:33.301421: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:33.302427: 用户姓名: null
2025-08-01 16:33:33.302427: 用户ID: null
2025-08-01 16:33:33.302427: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:33.302427: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:33.302427: 显示底部组件: true
2025-08-01 16:33:33.318599: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:33.318599: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:33.318599: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:33.318599: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:33.318599: 构建读者证认证界面
2025-08-01 16:33:33.318599: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:33.318599: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:33.319607: === 构建多认证底部组件 ===
2025-08-01 16:33:33.319607: 显示底部组件: true
2025-08-01 16:33:33.319607: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:33.319607: 用户信息: null (null)
2025-08-01 16:33:33.319607: 创建AuthFeedbackWidget
2025-08-01 16:33:33.319607: 实际认证方式: AuthMethod.face
2025-08-01 16:33:33.319607: Instance of 'SysConfigData'
2025-08-01 16:33:33.319607: Instance of 'SysConfigData'
2025-08-01 16:33:33.319607: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:33.319607: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:33.319607: 返回失败组件
2025-08-01 16:33:33.320610: === 构建失败组件 ===
2025-08-01 16:33:33.320610: 认证方式: AuthMethod.face
2025-08-01 16:33:33.320610: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:33.635819: iReadCardBas ret:4294967294
2025-08-01 16:33:33.636823: 无卡
2025-08-01 16:33:33.636823: 无卡
2025-08-01 16:33:33.638811: dc_config_card:0
2025-08-01 16:33:33.653771: dc_card_n_hex:1,len:0
2025-08-01 16:33:33.653771: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:33.782150: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:33.782150: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:33.782654: 多认证管理器状态变更: listening
2025-08-01 16:33:33.784654: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:33.784654: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:33.784654: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:33.784654: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:33.784654: 构建读者证认证界面
2025-08-01 16:33:33.784654: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:33.784654: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:33.784654: === 构建多认证底部组件 ===
2025-08-01 16:33:33.784654: 显示底部组件: true
2025-08-01 16:33:33.785650: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:33.785650: 用户信息: null (null)
2025-08-01 16:33:33.785650: 创建AuthFeedbackWidget
2025-08-01 16:33:33.785650: 实际认证方式: AuthMethod.face
2025-08-01 16:33:33.785650: Instance of 'SysConfigData'
2025-08-01 16:33:33.785650: Instance of 'SysConfigData'
2025-08-01 16:33:33.785650: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:33.785650: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:33.785650: 返回失败组件
2025-08-01 16:33:33.785650: === 构建失败组件 ===
2025-08-01 16:33:33.786647: 认证方式: AuthMethod.face
2025-08-01 16:33:33.786647: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:33.802672: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:33.802672: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:33.802672: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:33.802672: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:33.802672: ✅ 人脸识别服务重启完成
2025-08-01 16:33:34.299996: iReadCardBas ret:4294967294
2025-08-01 16:33:34.299996: 无卡
2025-08-01 16:33:34.299996: 无卡
2025-08-01 16:33:34.301990: dc_config_card:0
2025-08-01 16:33:34.317577: dc_card_n_hex:1,len:0
2025-08-01 16:33:34.318405: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:34.964935: iReadCardBas ret:4294967294
2025-08-01 16:33:34.964935: 无卡
2025-08-01 16:33:34.964935: 无卡
2025-08-01 16:33:34.973910: dc_config_card:0
2025-08-01 16:33:34.989903: dc_card_n_hex:1,len:0
2025-08-01 16:33:34.989903: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:35.318274: AuthView: 检测到 1 个人脸
2025-08-01 16:33:35.397589: AuthView: 检测到 1 个人脸
2025-08-01 16:33:35.476926: AuthView: 检测到 1 个人脸
2025-08-01 16:33:35.558754: AuthView: 检测到 1 个人脸
2025-08-01 16:33:35.635673: iReadCardBas ret:4294967294
2025-08-01 16:33:35.635673: 无卡
2025-08-01 16:33:35.635673: 无卡
2025-08-01 16:33:35.637672: AuthView: 检测到 1 个人脸
2025-08-01 16:33:35.638670: dc_config_card:0
2025-08-01 16:33:35.653919: dc_card_n_hex:1,len:0
2025-08-01 16:33:35.653919: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:35.717076: AuthView: 检测到 1 个人脸
2025-08-01 16:33:35.797863: AuthView: 检测到 1 个人脸
2025-08-01 16:33:35.804831: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:35.804)
2025-08-01 16:33:35.804831: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:35.804)
2025-08-01 16:33:35.804831: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:35.804831: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:35.804831: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:35.804831: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 116.10260772705078, 数据大小: 0 bytes
2025-08-01 16:33:35.804831: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:35.805814: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:35.805814: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 5065 bytes, 实际置信度: 116.10260772705078
2025-08-01 16:33:35.805814: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:35.805814: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:35.805814: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:35.805814: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:35.805814: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:35.957102: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:35.958108: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "b2fba3ebc1dd94cefee4aa9f3498bf4b",
		"log_id" : "1754037215957",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 62.0,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:35.958108: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:35.958108: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=62.0
2025-08-01 16:33:35.958108: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:35.958108: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:35.958108: 认证完成，恢复人脸轮询
2025-08-01 16:33:35.958108: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:35.959108: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:35.959108: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:35.959108: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:35.959108: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:35.959108: 轮询未在运行
2025-08-01 16:33:35.959108: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:35.959108: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:35.959108: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:35.959108: 多认证管理器状态变更: authenticating
2025-08-01 16:33:35.960103: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:35.960103: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:35.960103: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:35.960103: === 更新认证反馈状态 ===
2025-08-01 16:33:35.960103: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:35.960103: 用户姓名: null
2025-08-01 16:33:35.960103: 用户ID: null
2025-08-01 16:33:35.960103: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:35.960103: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:35.961102: 显示底部组件: true
2025-08-01 16:33:35.962089: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:35.963087: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:35.963087: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:35.963087: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:35.963087: 构建读者证认证界面
2025-08-01 16:33:35.963087: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:35.963087: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:35.963087: === 构建多认证底部组件 ===
2025-08-01 16:33:35.963087: 显示底部组件: true
2025-08-01 16:33:35.963087: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:35.963087: 用户信息: null (null)
2025-08-01 16:33:35.963087: 创建AuthFeedbackWidget
2025-08-01 16:33:35.964084: 实际认证方式: AuthMethod.face
2025-08-01 16:33:35.964084: Instance of 'SysConfigData'
2025-08-01 16:33:35.964084: Instance of 'SysConfigData'
2025-08-01 16:33:35.964084: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:35.964084: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:35.964084: 返回失败组件
2025-08-01 16:33:35.964084: === 构建失败组件 ===
2025-08-01 16:33:35.964084: 认证方式: AuthMethod.face
2025-08-01 16:33:35.964084: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:35.966098: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.038010: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.118472: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.198360: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.277429: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.299511: iReadCardBas ret:4294967294
2025-08-01 16:33:36.300476: 无卡
2025-08-01 16:33:36.300476: 无卡
2025-08-01 16:33:36.301516: dc_config_card:0
2025-08-01 16:33:36.303497: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:36.303497: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:36.303497: 多认证管理器状态变更: listening
2025-08-01 16:33:36.317431: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:36.317431: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:36.318428: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:36.318428: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:36.318428: 构建读者证认证界面
2025-08-01 16:33:36.318428: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:36.318428: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:36.318428: === 构建多认证底部组件 ===
2025-08-01 16:33:36.318428: 显示底部组件: true
2025-08-01 16:33:36.318428: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:36.318428: 用户信息: null (null)
2025-08-01 16:33:36.318428: 创建AuthFeedbackWidget
2025-08-01 16:33:36.318428: 实际认证方式: AuthMethod.face
2025-08-01 16:33:36.318428: Instance of 'SysConfigData'
2025-08-01 16:33:36.319426: Instance of 'SysConfigData'
2025-08-01 16:33:36.319426: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:36.319426: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:36.319426: 返回失败组件
2025-08-01 16:33:36.319426: === 构建失败组件 ===
2025-08-01 16:33:36.319426: 认证方式: AuthMethod.face
2025-08-01 16:33:36.319426: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:36.319426: dc_card_n_hex:1,len:0
2025-08-01 16:33:36.320422: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:36.358349: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.437386: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.459508: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:36.459508: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:36.460323: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:36.460323: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:36.460323: ✅ 人脸识别服务重启完成
2025-08-01 16:33:36.516782: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.597483: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.676848: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.759079: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.838454: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.911309: 发送心跳
2025-08-01 16:33:36.911309: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY6AZFC9B
2025-08-01 16:33:36.917104: AuthView: 检测到 1 个人脸
2025-08-01 16:33:36.939059: Rsp : 98YYYNNN00500320250801    1635062.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY6AZD51F
2025-08-01 16:33:36.963982: iReadCardBas ret:4294967294
2025-08-01 16:33:36.963982: 无卡
2025-08-01 16:33:36.964977: 无卡
2025-08-01 16:33:36.966048: dc_config_card:0
2025-08-01 16:33:36.981816: dc_card_n_hex:1,len:0
2025-08-01 16:33:36.981816: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:36.997811: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.076664: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.157933: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.238225: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.317961: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.397747: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.478530: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.556728: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.628990: iReadCardBas ret:4294967294
2025-08-01 16:33:37.628990: 无卡
2025-08-01 16:33:37.628990: 无卡
2025-08-01 16:33:37.636972: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.637965: dc_config_card:0
2025-08-01 16:33:37.653923: dc_card_n_hex:1,len:0
2025-08-01 16:33:37.653923: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:37.716877: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.797606: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.877449: AuthView: 检测到 1 个人脸
2025-08-01 16:33:37.957026: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.038202: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.117598: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.197387: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.279544: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.300: iReadCardBas ret:4294967294
2025-08-01 16:33:38.300950: 无卡
2025-08-01 16:33:38.300950: 无卡
2025-08-01 16:33:38.301946: dc_config_card:0
2025-08-01 16:33:38.318137: dc_card_n_hex:1,len:0
2025-08-01 16:33:38.318137: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:38.357989: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.437760: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.460762: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:38.460)
2025-08-01 16:33:38.461724: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:38.460)
2025-08-01 16:33:38.461724: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:38.461724: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:38.461724: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:38.461724: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 93.**************, 数据大小: 0 bytes
2025-08-01 16:33:38.461724: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:38.461724: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:38.462722: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6853 bytes, 实际置信度: 93.**************
2025-08-01 16:33:38.462722: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:38.462722: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:38.462722: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:38.462722: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:38.462722: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:38.579441: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:38.579441: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "1e0388fefc080a676bc4f88fbe8e5579",
		"log_id" : "1754037218579",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 73.58,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:38.580415: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:38.580415: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=73.58
2025-08-01 16:33:38.580415: 人脸识别成功，得分: 73.58，用户ID: 111111
2025-08-01 16:33:38.580415: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:38.581405: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:38.581405: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:38.581405: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:38.581405: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:38.581405: 轮询未在运行
2025-08-01 16:33:38.581405: 轮询未在运行
2025-08-01 16:33:38.581405: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:38.581405: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:38.581405: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:38.581405: 多认证管理器: 人脸识别获得认证请求锁
2025-08-01 16:33:38.582401: 63 CardType 值为空
2025-08-01 16:33:38.582401: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163338  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY7AZF016
2025-08-01 16:33:38.582401: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:38.582401: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:38.582401: 多认证管理器状态变更: authenticating
2025-08-01 16:33:38.583399: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:38.583399: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:38.583399: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:38.583399: === 更新认证反馈状态 ===
2025-08-01 16:33:38.583399: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:38.583399: 用户姓名: null
2025-08-01 16:33:38.583399: 用户ID: null
2025-08-01 16:33:38.583399: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:38.583399: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:38.584397: 显示底部组件: true
2025-08-01 16:33:38.586394: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:38.586394: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:38.586394: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:38.586394: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:38.586394: 构建读者证认证界面
2025-08-01 16:33:38.586394: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:38.586394: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:38.586394: === 构建多认证底部组件 ===
2025-08-01 16:33:38.586394: 显示底部组件: true
2025-08-01 16:33:38.586394: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:38.586394: 用户信息: null (null)
2025-08-01 16:33:38.586394: 创建AuthFeedbackWidget
2025-08-01 16:33:38.587388: 实际认证方式: AuthMethod.face
2025-08-01 16:33:38.587388: Instance of 'SysConfigData'
2025-08-01 16:33:38.587388: Instance of 'SysConfigData'
2025-08-01 16:33:38.587388: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:38.587388: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:38.587388: 返回失败组件
2025-08-01 16:33:38.587388: === 构建失败组件 ===
2025-08-01 16:33:38.587388: 认证方式: AuthMethod.face
2025-08-01 16:33:38.587388: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:38.589383: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.598387: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.678173: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.757980: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.838131: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.918438: AuthView: 检测到 1 个人脸
2025-08-01 16:33:38.960691: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:38.961530: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:38.961530: 多认证管理器状态变更: listening
2025-08-01 16:33:38.964792: iReadCardBas ret:4294967294
2025-08-01 16:33:38.964792: 无卡
2025-08-01 16:33:38.965765: 无卡
2025-08-01 16:33:38.967755: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:38.967755: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:38.967755: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:38.967755: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:38.967755: 构建读者证认证界面
2025-08-01 16:33:38.967755: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:38.967755: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:38.967755: === 构建多认证底部组件 ===
2025-08-01 16:33:38.968745: 显示底部组件: true
2025-08-01 16:33:38.968745: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:38.968745: 用户信息: null (null)
2025-08-01 16:33:38.968745: 创建AuthFeedbackWidget
2025-08-01 16:33:38.968745: 实际认证方式: AuthMethod.face
2025-08-01 16:33:38.968745: Instance of 'SysConfigData'
2025-08-01 16:33:38.968745: Instance of 'SysConfigData'
2025-08-01 16:33:38.969742: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:38.969742: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:38.969742: 返回失败组件
2025-08-01 16:33:38.969742: === 构建失败组件 ===
2025-08-01 16:33:38.969742: 认证方式: AuthMethod.face
2025-08-01 16:33:38.969742: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:38.973732: dc_config_card:0
2025-08-01 16:33:38.990030: dc_card_n_hex:1,len:0
2025-08-01 16:33:38.990030: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:38.998012: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.055839: Rsp : 64              00120250801    163507000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY7AZDBB8
2025-08-01 16:33:39.064996: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163507, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 7AZDBB8}
2025-08-01 16:33:39.064996: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163507, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 7AZDBB8}
2025-08-01 16:33:39.065802: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-08-01 16:33:39.065802: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-08-01 16:33:39.065802: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-08-01 16:33:39.065802: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:39.065802: 多认证管理器状态变更: authenticating
2025-08-01 16:33:39.066801: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:33:39.066801: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:39.066801: 多认证管理器状态变更: completed
2025-08-01 16:33:39.066801: 多认证管理器: 调用认证后处理服务 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:39.066801: 执行认证后处理流程，共2个处理器
2025-08-01 16:33:39.067797: 执行处理器: DoorLockHandler
2025-08-01 16:33:39.067797: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:39.067797: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:39.067797: 收到多认证结果: 人脸识别 - success
2025-08-01 16:33:39.067797: 认证成功，更新主显示方式为: 人脸识别
2025-08-01 16:33:39.067797: === 更新认证反馈状态 ===
2025-08-01 16:33:39.068794: 结果状态: AuthStatus.success
2025-08-01 16:33:39.068794: 用户姓名: gd
2025-08-01 16:33:39.068794: 用户ID: 111111
2025-08-01 16:33:39.068794: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:33:39.068794: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:39.069410: 用户信息: gd (111111)
2025-08-01 16:33:39.069410: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:33:39.069410: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:33:39.069410: 使用认证页面的门锁连接: COM1
2025-08-01 16:33:39.069410: 使用继电器通道: 1
2025-08-01 16:33:39.069410: 使用继电器通道: 1
2025-08-01 16:33:39.069913: 开锁前先关锁（不等待返回）...
2025-08-01 16:33:39.069913: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:39.069913: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:39.069913: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:39.069913: 等待100毫秒确保命令发送...
2025-08-01 16:33:39.072908: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:39.072908: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:39.072908: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:39.072908: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:39.072908: 构建读者证认证界面
2025-08-01 16:33:39.072908: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:39.073906: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:39.073906: === 构建多认证底部组件 ===
2025-08-01 16:33:39.073906: 显示底部组件: true
2025-08-01 16:33:39.073906: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:39.073906: 用户信息: gd (111111)
2025-08-01 16:33:39.073906: 创建AuthFeedbackWidget
2025-08-01 16:33:39.073906: 实际认证方式: AuthMethod.face
2025-08-01 16:33:39.073906: Instance of 'SysConfigData'
2025-08-01 16:33:39.073906: Instance of 'SysConfigData'
2025-08-01 16:33:39.073906: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:33:39.073906: 新状态: AuthFeedbackState.success
2025-08-01 16:33:39.073906: 用户信息: gd (111111)
2025-08-01 16:33:39.074904: 启动定时器，3秒后切换到通行模式
2025-08-01 16:33:39.074904: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:39.074904: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:39.074904: 返回成功组件 (详细信息)
2025-08-01 16:33:39.074904: === 构建成功组件（详细信息） ===
2025-08-01 16:33:39.074904: 用户: gd, ID: 111111
2025-08-01 16:33:39.077895: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.082004: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:39.082004: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:39.082923: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:39.082923: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:39.082923: ✅ 人脸识别服务重启完成
2025-08-01 16:33:39.158189: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.169591: 正在打开绿灯（不等待返回）...
2025-08-01 16:33:39.169591: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:33:39.170578: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:33:39.170578: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:33:39.170578: 正在开门（不等待返回）...
2025-08-01 16:33:39.170578: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:33:39.170578: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:33:39.170578: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:33:39.170578: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:33:39.171578: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:33:39.171578: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:33:39.169591
2025-08-01 16:33:39.171578: 门锁操作完成，连接由认证页面管理
2025-08-01 16:33:39.172573: 自动开门成功 - 用户: gd
2025-08-01 16:33:39.172573: 执行处理器: WelcomeHandler
2025-08-01 16:33:39.172573: 显示欢迎信息给: gd
2025-08-01 16:33:39.172573: 欢迎用户: gd，认证方式: 人脸识别
2025-08-01 16:33:39.172573: 认证后处理流程执行完毕
2025-08-01 16:33:39.172573: 多认证管理器: 认证后处理流程执行完毕
2025-08-01 16:33:39.237728: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.318270: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.397369: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.477041: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.557825: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.635616: iReadCardBas ret:4294967294
2025-08-01 16:33:39.636614: 无卡
2025-08-01 16:33:39.636614: 无卡
2025-08-01 16:33:39.638609: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.638609: dc_config_card:0
2025-08-01 16:33:39.653569: dc_card_n_hex:1,len:0
2025-08-01 16:33:39.653569: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:39.718407: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.797759: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.878469: AuthView: 检测到 1 个人脸
2025-08-01 16:33:39.957085: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.038303: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.118388: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.171363: 异步执行关锁动作（不等待返回）...
2025-08-01 16:33:40.171363: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:40.171363: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:40.172186: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:40.197131: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.223225: 正在关闭绿灯...
2025-08-01 16:33:40.224049: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:33:40.224049: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:33:40.224049: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:33:40.224049: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:33:40.223225
2025-08-01 16:33:40.277254: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.300265: iReadCardBas ret:4294967294
2025-08-01 16:33:40.300265: 无卡
2025-08-01 16:33:40.300265: 无卡
2025-08-01 16:33:40.301261: dc_config_card:0
2025-08-01 16:33:40.317442: dc_card_n_hex:1,len:0
2025-08-01 16:33:40.318460: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:40.357796: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.437820: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.518679: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.597175: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.679075: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.757993: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.838157: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.917386: AuthView: 检测到 1 个人脸
2025-08-01 16:33:40.964105: iReadCardBas ret:4294967294
2025-08-01 16:33:40.964921: 无卡
2025-08-01 16:33:40.964921: 无卡
2025-08-01 16:33:40.973899: dc_config_card:0
2025-08-01 16:33:40.990131: dc_card_n_hex:1,len:0
2025-08-01 16:33:40.990131: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:41.083792: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:41.082)
2025-08-01 16:33:41.083792: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:41.082)
2025-08-01 16:33:41.083792: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:41.083792: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:41.083792: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:41.083792: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 135.3653106689453, 数据大小: 0 bytes
2025-08-01 16:33:41.083792: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:41.084789: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:41.084789: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 4497 bytes, 实际置信度: 135.3653106689453
2025-08-01 16:33:41.084789: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:41.084789: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:41.084789: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:41.084789: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:41.084789: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:41.209830: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:41.210800: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "4310bf3d2c5cf6550b5e22997e4059ad",
		"log_id" : "1754037221210",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 75.41,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:41.210800: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:41.210800: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=75.41
2025-08-01 16:33:41.210800: 人脸识别成功，得分: 75.41，用户ID: 111111
2025-08-01 16:33:41.210800: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:41.210800: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:41.211797: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:41.211797: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:41.211797: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:41.211797: 轮询未在运行
2025-08-01 16:33:41.211797: 轮询未在运行
2025-08-01 16:33:41.211797: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:41.211797: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:41.211797: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:41.211797: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:41.212802: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:41.212802: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:41.212802: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:41.583447: 认证完成，还原到默认显示方式
2025-08-01 16:33:41.600634: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:41.600634: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:41.600634: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:41.600634: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:41.601631: 构建读者证认证界面
2025-08-01 16:33:41.601631: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:41.601631: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:41.601631: === 构建多认证底部组件 ===
2025-08-01 16:33:41.601631: 显示底部组件: false
2025-08-01 16:33:41.601631: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:33:41.601631: 用户信息: null (null)
2025-08-01 16:33:41.601631: 底部组件被隐藏，返回空组件
2025-08-01 16:33:41.601631: Instance of 'SysConfigData'
2025-08-01 16:33:41.601631: Instance of 'SysConfigData'
2025-08-01 16:33:41.636758: iReadCardBas ret:4294967294
2025-08-01 16:33:41.637756: 无卡
2025-08-01 16:33:41.637756: 无卡
2025-08-01 16:33:41.645751: dc_config_card:0
2025-08-01 16:33:41.661880: dc_card_n_hex:1,len:0
2025-08-01 16:33:41.661880: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:41.712257: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:41.712257: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:41.712257: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:41.713258: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:41.713258: ✅ 人脸识别服务重启完成
2025-08-01 16:33:42.070319: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:33:42.070319: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:42.070319: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:42.071184: 开锁前关锁命令已发送
2025-08-01 16:33:42.171465: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:33:42.171465: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:42.171465: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:42.171465: 绿灯命令已发送
2025-08-01 16:33:42.172480: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:33:42.172480: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:42.172480: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:42.172480: 开锁命令已发送
2025-08-01 16:33:42.308719: iReadCardBas ret:4294967294
2025-08-01 16:33:42.308719: 无卡
2025-08-01 16:33:42.308719: 无卡
2025-08-01 16:33:42.317826: dc_config_card:0
2025-08-01 16:33:42.333414: dc_card_n_hex:1,len:0
2025-08-01 16:33:42.334241: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:42.980525: iReadCardBas ret:4294967294
2025-08-01 16:33:42.980525: 无卡
2025-08-01 16:33:42.980525: 无卡
2025-08-01 16:33:42.989698: dc_config_card:0
2025-08-01 16:33:43.006208: dc_card_n_hex:1,len:0
2025-08-01 16:33:43.006208: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:43.171765: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:33:43.171765: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:43.171765: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:43.171765: 异步关锁命令已发送
2025-08-01 16:33:43.224251: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:33:43.224251: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:43.224251: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:43.224251: 绿灯关闭失败: 操作失败
2025-08-01 16:33:43.398662: AuthView: 检测到 1 个人脸
2025-08-01 16:33:43.477741: AuthView: 检测到 1 个人脸
2025-08-01 16:33:43.557367: AuthView: 检测到 1 个人脸
2025-08-01 16:33:43.636850: AuthView: 检测到 1 个人脸
2025-08-01 16:33:43.652038: iReadCardBas ret:4294967294
2025-08-01 16:33:43.652038: 无卡
2025-08-01 16:33:43.653027: 无卡
2025-08-01 16:33:43.653027: dc_config_card:0
2025-08-01 16:33:43.669996: dc_card_n_hex:1,len:0
2025-08-01 16:33:43.669996: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:43.713503: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:43.713)
2025-08-01 16:33:43.714398: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:43.713)
2025-08-01 16:33:43.714398: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:43.714398: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:43.714398: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:43.714398: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 118.53033447265625, 数据大小: 0 bytes
2025-08-01 16:33:43.715415: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:43.715415: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:43.715415: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 4578 bytes, 实际置信度: 118.53033447265625
2025-08-01 16:33:43.715415: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:43.715415: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:43.715415: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:43.715415: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:43.716391: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:43.843317: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:43.843317: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "67d926a2dbffc515c345909d071eb819",
		"log_id" : "1754037223843",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 54.84,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:43.843317: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:43.843317: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=54.84
2025-08-01 16:33:43.844314: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:43.844314: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:43.844314: 认证完成，恢复人脸轮询
2025-08-01 16:33:43.844314: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:43.844314: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:43.844314: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:43.845312: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:43.845312: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:43.845312: 轮询未在运行
2025-08-01 16:33:43.845312: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:43.845312: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:43.848304: AuthView: 检测到 1 个人脸
2025-08-01 16:33:43.877283: AuthView: 检测到 1 个人脸
2025-08-01 16:33:43.956607: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.036725: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.067973: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-08-01 16:33:44.116931: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.196818: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.278545: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.316658: iReadCardBas ret:4294967294
2025-08-01 16:33:44.316658: 无卡
2025-08-01 16:33:44.316658: 无卡
2025-08-01 16:33:44.317620: dc_config_card:0
2025-08-01 16:33:44.333853: dc_card_n_hex:1,len:0
2025-08-01 16:33:44.333853: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:44.345919: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:44.346756: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:44.346756: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:44.346756: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:44.346756: ✅ 人脸识别服务重启完成
2025-08-01 16:33:44.359705: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.456966: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.518006: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.597071: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.676684: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.758733: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.836991: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.918086: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.979578: iReadCardBas ret:4294967294
2025-08-01 16:33:44.979578: 无卡
2025-08-01 16:33:44.980575: 无卡
2025-08-01 16:33:44.981601: dc_config_card:0
2025-08-01 16:33:44.997530: AuthView: 检测到 1 个人脸
2025-08-01 16:33:44.997530: dc_card_n_hex:1,len:0
2025-08-01 16:33:44.998527: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:45.077059: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.158454: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.238009: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.318279: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.397844: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.477755: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.557175: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.637260: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.644243: iReadCardBas ret:4294967294
2025-08-01 16:33:45.644243: 无卡
2025-08-01 16:33:45.644243: 无卡
2025-08-01 16:33:45.645239: dc_config_card:0
2025-08-01 16:33:45.661195: dc_card_n_hex:1,len:0
2025-08-01 16:33:45.662193: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:45.719610: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.796907: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.878132: AuthView: 检测到 1 个人脸
2025-08-01 16:33:45.957669: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.037027: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.118309: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.197593: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.278025: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.308219: iReadCardBas ret:4294967294
2025-08-01 16:33:46.308219: 无卡
2025-08-01 16:33:46.308219: 无卡
2025-08-01 16:33:46.310161: dc_config_card:0
2025-08-01 16:33:46.325152: dc_card_n_hex:1,len:0
2025-08-01 16:33:46.326149: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:46.348188: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:46.347)
2025-08-01 16:33:46.348188: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:46.347)
2025-08-01 16:33:46.348188: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:46.349190: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:46.349190: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:46.349190: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 115.50733184814453, 数据大小: 0 bytes
2025-08-01 16:33:46.349190: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:46.349190: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:46.349190: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 6646 bytes, 实际置信度: 115.50733184814453
2025-08-01 16:33:46.349190: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:46.350182: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:46.350182: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:46.350182: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:46.350182: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:46.477872: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:46.477872: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "512208873c71087055f90706c5a9a3f3",
		"log_id" : "1754037226477",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 78.28,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:46.478838: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:46.478838: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=78.28
2025-08-01 16:33:46.478838: 人脸识别成功，得分: 78.28，用户ID: 111111
2025-08-01 16:33:46.478838: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:46.478838: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:46.479835: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:46.479835: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:46.479835: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:46.479835: 轮询未在运行
2025-08-01 16:33:46.479835: 轮询未在运行
2025-08-01 16:33:46.479835: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:46.479835: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:46.480844: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:46.480844: 多认证管理器: 人脸识别获得认证请求锁
2025-08-01 16:33:46.480844: 63 CardType 值为空
2025-08-01 16:33:46.480844: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163346  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY8AZF016
2025-08-01 16:33:46.480844: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:46.482855: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.518750: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.597600: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.660581: Rsp : 64              00120250801    163515000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY8AZDBB8
2025-08-01 16:33:46.677536: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.681556: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163515, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 8AZDBB8}
2025-08-01 16:33:46.681556: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163515, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 8AZDBB8}
2025-08-01 16:33:46.681556: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-08-01 16:33:46.681556: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-08-01 16:33:46.682524: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-08-01 16:33:46.682524: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:46.682524: 多认证管理器状态变更: authenticating
2025-08-01 16:33:46.682524: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:33:46.682524: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:46.682524: 多认证管理器状态变更: completed
2025-08-01 16:33:46.682524: 多认证管理器: 调用认证后处理服务 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:46.683521: 执行认证后处理流程，共2个处理器
2025-08-01 16:33:46.683521: 执行处理器: DoorLockHandler
2025-08-01 16:33:46.683521: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:33:46.683521: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:46.683521: 收到多认证结果: 人脸识别 - success
2025-08-01 16:33:46.683521: 认证成功，更新主显示方式为: 人脸识别
2025-08-01 16:33:46.683521: === 更新认证反馈状态 ===
2025-08-01 16:33:46.684518: 结果状态: AuthStatus.success
2025-08-01 16:33:46.684518: 用户姓名: gd
2025-08-01 16:33:46.684518: 用户ID: 111111
2025-08-01 16:33:46.684518: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:33:46.684518: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:46.684518: 用户信息: gd (111111)
2025-08-01 16:33:46.684518: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:33:46.684518: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:33:46.684518: 使用认证页面的门锁连接: COM1
2025-08-01 16:33:46.685515: 使用继电器通道: 1
2025-08-01 16:33:46.685515: 使用继电器通道: 1
2025-08-01 16:33:46.685515: 开锁前先关锁（不等待返回）...
2025-08-01 16:33:46.685515: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:46.685515: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:46.685515: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:46.685515: 等待100毫秒确保命令发送...
2025-08-01 16:33:46.688508: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:46.688508: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:46.688508: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:46.688508: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:46.688508: 构建读者证认证界面
2025-08-01 16:33:46.688508: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:46.688508: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:46.688508: === 构建多认证底部组件 ===
2025-08-01 16:33:46.688508: 显示底部组件: true
2025-08-01 16:33:46.688508: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:46.688508: 用户信息: gd (111111)
2025-08-01 16:33:46.689505: 创建AuthFeedbackWidget
2025-08-01 16:33:46.689505: 实际认证方式: AuthMethod.face
2025-08-01 16:33:46.689505: Instance of 'SysConfigData'
2025-08-01 16:33:46.689505: Instance of 'SysConfigData'
2025-08-01 16:33:46.689505: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:46.689505: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:46.689505: 返回成功组件 (详细信息)
2025-08-01 16:33:46.690503: === 构建成功组件（详细信息） ===
2025-08-01 16:33:46.690503: 用户: gd, ID: 111111
2025-08-01 16:33:46.690503: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:33:46.690503: 新状态: AuthFeedbackState.success
2025-08-01 16:33:46.690503: 用户信息: gd (111111)
2025-08-01 16:33:46.690503: 启动定时器，3秒后切换到通行模式
2025-08-01 16:33:46.699480: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:46.699480: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:46.699480: 返回成功组件 (详细信息)
2025-08-01 16:33:46.699480: === 构建成功组件（详细信息） ===
2025-08-01 16:33:46.699480: 用户: gd, ID: 111111
2025-08-01 16:33:46.758390: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.785249: 正在打开绿灯（不等待返回）...
2025-08-01 16:33:46.785249: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:33:46.785249: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:33:46.785249: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:33:46.785249: 正在开门（不等待返回）...
2025-08-01 16:33:46.785249: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:33:46.785249: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:33:46.786245: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:33:46.786245: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:33:46.786245: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:33:46.786245: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:33:46.785249
2025-08-01 16:33:46.786245: 门锁操作完成，连接由认证页面管理
2025-08-01 16:33:46.786245: 自动开门成功 - 用户: gd
2025-08-01 16:33:46.786245: 执行处理器: WelcomeHandler
2025-08-01 16:33:46.786245: 显示欢迎信息给: gd
2025-08-01 16:33:46.786245: 欢迎用户: gd，认证方式: 人脸识别
2025-08-01 16:33:46.786245: 认证后处理流程执行完毕
2025-08-01 16:33:46.787242: 多认证管理器: 认证后处理流程执行完毕
2025-08-01 16:33:46.838732: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.918281: AuthView: 检测到 1 个人脸
2025-08-01 16:33:46.972433: iReadCardBas ret:4294967294
2025-08-01 16:33:46.972433: 无卡
2025-08-01 16:33:46.972433: 无卡
2025-08-01 16:33:46.973451: dc_config_card:0
2025-08-01 16:33:46.981430: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:46.981430: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:46.981430: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:46.981430: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:46.981430: ✅ 人脸识别服务重启完成
2025-08-01 16:33:46.989562: dc_card_n_hex:1,len:0
2025-08-01 16:33:46.990386: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:46.998390: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.067021: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-01 16:33:47.067021: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:47.067844: 多认证管理器状态变更: listening
2025-08-01 16:33:47.067844: 🔍 检查人脸识别服务健康状态...
2025-08-01 16:33:47.067844: 🔄 重置人脸识别服务认证状态...
2025-08-01 16:33:47.077073: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.085277: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:47.085277: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:47.085277: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:47.085277: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:47.085277: 构建读者证认证界面
2025-08-01 16:33:47.085277: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:47.085277: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:47.085277: === 构建多认证底部组件 ===
2025-08-01 16:33:47.086274: 显示底部组件: true
2025-08-01 16:33:47.086274: 反馈状态: AuthFeedbackState.success
2025-08-01 16:33:47.086274: 用户信息: gd (111111)
2025-08-01 16:33:47.086274: 创建AuthFeedbackWidget
2025-08-01 16:33:47.086274: 实际认证方式: AuthMethod.face
2025-08-01 16:33:47.086274: Instance of 'SysConfigData'
2025-08-01 16:33:47.086274: Instance of 'SysConfigData'
2025-08-01 16:33:47.087271: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:47.087271: 当前状态: AuthFeedbackState.success
2025-08-01 16:33:47.087271: 返回成功组件 (详细信息)
2025-08-01 16:33:47.087271: === 构建成功组件（详细信息） ===
2025-08-01 16:33:47.087271: 用户: gd, ID: 111111
2025-08-01 16:33:47.158004: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.236949: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.318993: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.397305: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.477356: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.558851: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.636682: iReadCardBas ret:4294967294
2025-08-01 16:33:47.636682: 无卡
2025-08-01 16:33:47.636682: 无卡
2025-08-01 16:33:47.638706: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.646018: dc_config_card:0
2025-08-01 16:33:47.661802: dc_card_n_hex:1,len:0
2025-08-01 16:33:47.661802: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:47.716880: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.786893: 异步执行关锁动作（不等待返回）...
2025-08-01 16:33:47.787709: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:47.787709: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:47.787709: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:47.798703: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.838022: 正在关闭绿灯...
2025-08-01 16:33:47.838022: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:33:47.838022: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:33:47.838022: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:33:47.838022: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:33:47.838022
2025-08-01 16:33:47.877721: AuthView: 检测到 1 个人脸
2025-08-01 16:33:47.956709: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.037922: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.117873: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.197854: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.277614: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.308019: iReadCardBas ret:4294967294
2025-08-01 16:33:48.308019: 无卡
2025-08-01 16:33:48.308019: 无卡
2025-08-01 16:33:48.310013: dc_config_card:0
2025-08-01 16:33:48.325198: dc_card_n_hex:1,len:0
2025-08-01 16:33:48.326194: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:48.358588: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.437675: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.518337: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.597106: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.678341: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.837346: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.917985: AuthView: 检测到 1 个人脸
2025-08-01 16:33:48.971961: iReadCardBas ret:4294967294
2025-08-01 16:33:48.971961: 无卡
2025-08-01 16:33:48.972940: 无卡
2025-08-01 16:33:48.973937: dc_config_card:0
2025-08-01 16:33:48.982917: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:48.982)
2025-08-01 16:33:48.983908: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:48.982)
2025-08-01 16:33:48.983908: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:48.983908: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:48.983908: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:48.983908: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 237.76028442382812, 数据大小: 0 bytes
2025-08-01 16:33:48.983908: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:48.983908: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:48.983908: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 5755 bytes, 实际置信度: 237.76028442382812
2025-08-01 16:33:48.983908: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:48.983908: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:48.984906: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:48.984906: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:48.984906: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:49.109514: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:49.110484: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "551e722a5ca98f49c4fbbc66e4aac310",
		"log_id" : "1754037229109",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 77.59,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:49.110484: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:49.110484: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=77.59
2025-08-01 16:33:49.110484: 人脸识别成功，得分: 77.59，用户ID: 111111
2025-08-01 16:33:49.111497: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:49.111497: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:49.111497: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:49.111497: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:49.111497: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:49.111497: 轮询未在运行
2025-08-01 16:33:49.111497: 轮询未在运行
2025-08-01 16:33:49.111497: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:49.112490: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:49.112490: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:49.112490: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:49.112490: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:49.112490: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:49.112490: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:49.113477: 多认证管理器状态变更: authenticating
2025-08-01 16:33:49.113477: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:49.113477: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:49.113477: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:49.113477: === 更新认证反馈状态 ===
2025-08-01 16:33:49.114473: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:49.114609: 用户姓名: null
2025-08-01 16:33:49.114609: 用户ID: null
2025-08-01 16:33:49.114609: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:49.114609: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:33:49.114609: 显示底部组件: true
2025-08-01 16:33:49.115113: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:49.115113: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:49.115113: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:49.115113: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:49.115113: === 更新认证反馈状态 ===
2025-08-01 16:33:49.115113: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:49.115113: 用户姓名: null
2025-08-01 16:33:49.116114: 用户ID: null
2025-08-01 16:33:49.116114: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:49.116114: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:49.116114: 显示底部组件: true
2025-08-01 16:33:49.118109: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.118109: dc_card_n_hex:1,len:0
2025-08-01 16:33:49.118109: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:49.135066: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:49.135066: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:49.135066: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:49.136063: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:49.136063: 构建读者证认证界面
2025-08-01 16:33:49.136063: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:49.136063: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:49.136063: === 构建多认证底部组件 ===
2025-08-01 16:33:49.136063: 显示底部组件: true
2025-08-01 16:33:49.136063: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:49.136063: 用户信息: null (null)
2025-08-01 16:33:49.136063: 创建AuthFeedbackWidget
2025-08-01 16:33:49.136063: 实际认证方式: AuthMethod.face
2025-08-01 16:33:49.137060: Instance of 'SysConfigData'
2025-08-01 16:33:49.137060: Instance of 'SysConfigData'
2025-08-01 16:33:49.137060: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:33:49.137060: 新状态: AuthFeedbackState.failure
2025-08-01 16:33:49.137060: 用户信息: null (null)
2025-08-01 16:33:49.137060: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:49.137060: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:49.137060: 返回失败组件
2025-08-01 16:33:49.137060: === 构建失败组件 ===
2025-08-01 16:33:49.137060: 认证方式: AuthMethod.face
2025-08-01 16:33:49.137060: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:49.159001: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.237418: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.317802: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.397896: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.477833: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.613259: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:49.613259: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:49.613259: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:49.613259: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:49.614236: ✅ 人脸识别服务重启完成
2025-08-01 16:33:49.635218: iReadCardBas ret:4294967294
2025-08-01 16:33:49.636204: 无卡
2025-08-01 16:33:49.636204: 无卡
2025-08-01 16:33:49.638207: dc_config_card:0
2025-08-01 16:33:49.653190: dc_card_n_hex:1,len:0
2025-08-01 16:33:49.654156: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:49.686099: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:33:49.686099: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:49.686099: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:49.686099: 开锁前关锁命令已发送
2025-08-01 16:33:49.717739: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.786778: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:33:49.786778: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:49.786778: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:33:49.787763: 绿灯命令已发送
2025-08-01 16:33:49.787763: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:33:49.787763: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:49.787763: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:33:49.787763: 开锁命令已发送
2025-08-01 16:33:49.797717: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.878515: AuthView: 检测到 1 个人脸
2025-08-01 16:33:49.958620: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.037503: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.116867: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.198188: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.287787: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.300163: iReadCardBas ret:4294967294
2025-08-01 16:33:50.301158: 无卡
2025-08-01 16:33:50.301158: 无卡
2025-08-01 16:33:50.301158: dc_config_card:0
2025-08-01 16:33:50.317125: dc_card_n_hex:1,len:0
2025-08-01 16:33:50.318112: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:50.357212: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.437670: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.519075: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.598266: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.676862: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.758323: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.788395: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:33:50.788395: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:50.788395: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:33:50.789219: 异步关锁命令已发送
2025-08-01 16:33:50.838089: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.840255: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:33:50.840255: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:50.841082: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:33:50.841082: 绿灯关闭失败: 操作失败
2025-08-01 16:33:50.918230: AuthView: 检测到 1 个人脸
2025-08-01 16:33:50.963702: iReadCardBas ret:4294967294
2025-08-01 16:33:50.964716: 无卡
2025-08-01 16:33:50.964716: 无卡
2025-08-01 16:33:50.965716: dc_config_card:0
2025-08-01 16:33:50.981799: dc_card_n_hex:1,len:0
2025-08-01 16:33:50.981799: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:50.998754: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.078495: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.158046: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.237833: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.318756: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.397705: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.477581: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.558551: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.615533: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:51.615)
2025-08-01 16:33:51.616537: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:51.615)
2025-08-01 16:33:51.616537: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:51.616537: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:51.616537: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:51.616537: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 162.18545532226562, 数据大小: 0 bytes
2025-08-01 16:33:51.616537: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:51.617538: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:51.617538: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 5864 bytes, 实际置信度: 162.18545532226562
2025-08-01 16:33:51.617538: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:51.617538: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:51.617538: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:51.617538: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:51.617538: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:51.747207: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:51.747207: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "b1ab0ddb2de24fd91cac75e2e6676e7b",
		"log_id" : "1754037231747",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 87.35,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:51.748176: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:51.748176: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=87.35
2025-08-01 16:33:51.748176: 人脸识别成功，得分: 87.35，用户ID: 111111
2025-08-01 16:33:51.748176: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:51.748176: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:51.749174: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:51.749174: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:51.749174: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:51.749174: 轮询未在运行
2025-08-01 16:33:51.749174: 轮询未在运行
2025-08-01 16:33:51.749174: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:51.749174: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:51.749174: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:51.750170: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:51.750170: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:51.750170: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:51.750170: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:51.750170: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:51.751168: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:51.751168: === 更新认证反馈状态 ===
2025-08-01 16:33:51.751168: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:51.751168: 用户姓名: null
2025-08-01 16:33:51.751168: 用户ID: null
2025-08-01 16:33:51.751168: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:51.751168: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:33:51.751168: 显示底部组件: true
2025-08-01 16:33:51.752165: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:51.752165: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:51.752165: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:51.752165: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:51.752165: === 更新认证反馈状态 ===
2025-08-01 16:33:51.752165: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:51.752165: 用户姓名: null
2025-08-01 16:33:51.753163: 用户ID: null
2025-08-01 16:33:51.753163: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:51.753163: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:51.753163: 显示底部组件: true
2025-08-01 16:33:51.754915: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:51.754915: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:51.754915: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:51.754915: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:51.754915: 构建读者证认证界面
2025-08-01 16:33:51.754915: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:51.755911: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:51.755911: === 构建多认证底部组件 ===
2025-08-01 16:33:51.755911: 显示底部组件: true
2025-08-01 16:33:51.755911: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:51.755911: 用户信息: null (null)
2025-08-01 16:33:51.755911: 创建AuthFeedbackWidget
2025-08-01 16:33:51.755911: 实际认证方式: AuthMethod.face
2025-08-01 16:33:51.756913: Instance of 'SysConfigData'
2025-08-01 16:33:51.756913: Instance of 'SysConfigData'
2025-08-01 16:33:51.756913: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:51.756913: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:51.756913: 返回失败组件
2025-08-01 16:33:51.756913: === 构建失败组件 ===
2025-08-01 16:33:51.756913: 认证方式: AuthMethod.face
2025-08-01 16:33:51.756913: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:51.757906: iReadCardBas ret:4294967294
2025-08-01 16:33:51.757906: 无卡
2025-08-01 16:33:51.757906: 无卡
2025-08-01 16:33:51.757906: dc_config_card:0
2025-08-01 16:33:51.759923: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.759923: dc_card_n_hex:1,len:0
2025-08-01 16:33:51.759923: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:51.798014: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.876752: AuthView: 检测到 1 个人脸
2025-08-01 16:33:51.957777: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.037208: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.114345: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:52.115172: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:52.115172: 多认证管理器状态变更: listening
2025-08-01 16:33:52.118155: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:52.118155: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:52.118155: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:52.118155: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:52.119152: 构建读者证认证界面
2025-08-01 16:33:52.119152: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:52.119152: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:52.119152: === 构建多认证底部组件 ===
2025-08-01 16:33:52.119152: 显示底部组件: true
2025-08-01 16:33:52.119152: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:52.119152: 用户信息: null (null)
2025-08-01 16:33:52.119152: 创建AuthFeedbackWidget
2025-08-01 16:33:52.119152: 实际认证方式: AuthMethod.face
2025-08-01 16:33:52.119152: Instance of 'SysConfigData'
2025-08-01 16:33:52.120157: Instance of 'SysConfigData'
2025-08-01 16:33:52.120157: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:52.120157: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:52.120157: 返回失败组件
2025-08-01 16:33:52.120157: === 构建失败组件 ===
2025-08-01 16:33:52.120157: 认证方式: AuthMethod.face
2025-08-01 16:33:52.120157: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:52.122168: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.197147: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.249805: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:52.249805: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:52.250668: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:52.250668: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:52.250668: ✅ 人脸识别服务重启完成
2025-08-01 16:33:52.277605: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.291712: iReadCardBas ret:4294967294
2025-08-01 16:33:52.292567: 无卡
2025-08-01 16:33:52.292567: 无卡
2025-08-01 16:33:52.293573: dc_config_card:0
2025-08-01 16:33:52.309986: dc_card_n_hex:1,len:0
2025-08-01 16:33:52.309986: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:52.358162: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.437958: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.517942: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.597483: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.678549: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.756784: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.837659: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.917447: AuthView: 检测到 1 个人脸
2025-08-01 16:33:52.955578: iReadCardBas ret:4294967294
2025-08-01 16:33:52.956478: 无卡
2025-08-01 16:33:52.956478: 无卡
2025-08-01 16:33:52.957476: dc_config_card:0
2025-08-01 16:33:52.973721: dc_card_n_hex:1,len:0
2025-08-01 16:33:52.973721: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:52.998046: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.157266: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.237259: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.318088: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.397561: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.476809: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.557469: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.620051: iReadCardBas ret:4294967294
2025-08-01 16:33:53.620051: 无卡
2025-08-01 16:33:53.620051: 无卡
2025-08-01 16:33:53.622044: dc_config_card:0
2025-08-01 16:33:53.638213: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.639190: dc_card_n_hex:1,len:0
2025-08-01 16:33:53.639190: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:53.717674: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.797838: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.877286: AuthView: 检测到 1 个人脸
2025-08-01 16:33:53.958414: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.037568: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.117698: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.198279: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.252683: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:54.251)
2025-08-01 16:33:54.252683: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:54.251)
2025-08-01 16:33:54.252683: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:54.252683: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:54.252683: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:54.253659: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 96.58757781982422, 数据大小: 0 bytes
2025-08-01 16:33:54.253659: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:54.253659: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:54.253659: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 5194 bytes, 实际置信度: 96.58757781982422
2025-08-01 16:33:54.253659: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:54.253659: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:54.253659: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:54.254655: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:54.254655: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:54.372367: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:54.373342: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "4d34a84bba84ea5df222f369e9104c48",
		"log_id" : "1754037234372",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 83.31,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:54.373342: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:54.373342: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=83.31
2025-08-01 16:33:54.373342: 人脸识别成功，得分: 83.31，用户ID: 111111
2025-08-01 16:33:54.373342: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:54.373342: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:54.374334: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:54.374334: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:54.374334: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:54.374334: 轮询未在运行
2025-08-01 16:33:54.374334: 轮询未在运行
2025-08-01 16:33:54.374334: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:54.374334: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:54.374334: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:54.374334: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:54.374334: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:54.374334: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:54.375332: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:54.375332: 多认证管理器状态变更: authenticating
2025-08-01 16:33:54.375332: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:54.375332: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:54.375332: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:54.375332: === 更新认证反馈状态 ===
2025-08-01 16:33:54.375332: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:54.376329: 用户姓名: null
2025-08-01 16:33:54.376329: 用户ID: null
2025-08-01 16:33:54.376329: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:54.376329: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:33:54.376329: 显示底部组件: true
2025-08-01 16:33:54.376329: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:54.376329: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:54.376329: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:54.376329: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:54.376329: === 更新认证反馈状态 ===
2025-08-01 16:33:54.377338: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:54.377338: 用户姓名: null
2025-08-01 16:33:54.377338: 用户ID: null
2025-08-01 16:33:54.377338: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:54.377338: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:54.377338: 显示底部组件: true
2025-08-01 16:33:54.379348: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.379348: iReadCardBas ret:4294967294
2025-08-01 16:33:54.379348: 无卡
2025-08-01 16:33:54.380317: 无卡
2025-08-01 16:33:54.380317: dc_config_card:0
2025-08-01 16:33:54.380317: dc_card_n_hex:1,len:0
2025-08-01 16:33:54.381315: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:54.384308: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:54.385320: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:54.385320: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:54.385320: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:54.385320: 构建读者证认证界面
2025-08-01 16:33:54.385320: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:54.385320: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:54.385320: === 构建多认证底部组件 ===
2025-08-01 16:33:54.385320: 显示底部组件: true
2025-08-01 16:33:54.385320: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:54.385320: 用户信息: null (null)
2025-08-01 16:33:54.385320: 创建AuthFeedbackWidget
2025-08-01 16:33:54.386302: 实际认证方式: AuthMethod.face
2025-08-01 16:33:54.386302: Instance of 'SysConfigData'
2025-08-01 16:33:54.386302: Instance of 'SysConfigData'
2025-08-01 16:33:54.386302: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:54.386302: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:54.386302: 返回失败组件
2025-08-01 16:33:54.386302: === 构建失败组件 ===
2025-08-01 16:33:54.386302: 认证方式: AuthMethod.face
2025-08-01 16:33:54.386302: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:54.437748: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.517649: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.597088: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.678351: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.752449: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:54.752449: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:54.753349: 多认证管理器状态变更: listening
2025-08-01 16:33:54.758336: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.768423: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:54.768423: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:54.768423: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:54.768423: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:54.768423: 构建读者证认证界面
2025-08-01 16:33:54.768423: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:54.768423: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:54.768423: === 构建多认证底部组件 ===
2025-08-01 16:33:54.769419: 显示底部组件: true
2025-08-01 16:33:54.769419: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:54.769419: 用户信息: null (null)
2025-08-01 16:33:54.769419: 创建AuthFeedbackWidget
2025-08-01 16:33:54.769419: 实际认证方式: AuthMethod.face
2025-08-01 16:33:54.769419: Instance of 'SysConfigData'
2025-08-01 16:33:54.769419: Instance of 'SysConfigData'
2025-08-01 16:33:54.769419: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:54.769419: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:54.769419: 返回失败组件
2025-08-01 16:33:54.769419: === 构建失败组件 ===
2025-08-01 16:33:54.769419: 认证方式: AuthMethod.face
2025-08-01 16:33:54.770416: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:54.837611: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.875660: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:54.876495: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:54.876495: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:54.876495: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:54.876495: ✅ 人脸识别服务重启完成
2025-08-01 16:33:54.918423: AuthView: 检测到 1 个人脸
2025-08-01 16:33:54.947501: iReadCardBas ret:4294967294
2025-08-01 16:33:54.948474: 无卡
2025-08-01 16:33:54.948474: 无卡
2025-08-01 16:33:54.949545: dc_config_card:0
2025-08-01 16:33:54.965445: dc_card_n_hex:1,len:0
2025-08-01 16:33:54.965445: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:54.997568: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.077947: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.157980: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.236822: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.317803: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.397561: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.478206: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.558135: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.611382: iReadCardBas ret:4294967294
2025-08-01 16:33:55.611382: 无卡
2025-08-01 16:33:55.612380: 无卡
2025-08-01 16:33:55.613532: dc_config_card:0
2025-08-01 16:33:55.629736: dc_card_n_hex:1,len:0
2025-08-01 16:33:55.629736: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:55.637696: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.717687: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.796949: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.878018: AuthView: 检测到 1 个人脸
2025-08-01 16:33:55.957321: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.038871: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.117226: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.198044: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.277430: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.278430: iReadCardBas ret:4294967294
2025-08-01 16:33:56.278430: 无卡
2025-08-01 16:33:56.278430: 无卡
2025-08-01 16:33:56.285422: dc_config_card:0
2025-08-01 16:33:56.301303: dc_card_n_hex:1,len:0
2025-08-01 16:33:56.301303: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:56.357400: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.438627: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.597224: AuthView: 检测到 1 个人脸
2025-08-01 16:33:56.878886: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:56.878)
2025-08-01 16:33:56.878886: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:33:56.878)
2025-08-01 16:33:56.878886: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:33:56.878886: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:33:56.878886: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:33:56.879887: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 170.24777221679688, 数据大小: 0 bytes
2025-08-01 16:33:56.879887: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:33:56.879887: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:33:56.879887: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 4169 bytes, 实际置信度: 170.24777221679688
2025-08-01 16:33:56.879887: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:33:56.879887: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:33:56.879887: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:33:56.880879: ⏹️ 停止人脸捕获轮询
2025-08-01 16:33:56.880879: 暂停人脸轮询，开始认证流程
2025-08-01 16:33:57.012425: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:33:57.012425: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "242627fcb29d271f5fac5ae4249eff49",
		"log_id" : "1754037237012",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 91.03,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:33:57.013393: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:33:57.013393: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=91.03
2025-08-01 16:33:57.013393: 人脸识别成功，得分: 91.03，用户ID: 111111
2025-08-01 16:33:57.014391: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:33:57.014391: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:57.014391: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:57.014391: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:33:57.014391: 🔄 开始重启人脸识别服务...
2025-08-01 16:33:57.014391: 轮询未在运行
2025-08-01 16:33:57.014391: 轮询未在运行
2025-08-01 16:33:57.015398: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:33:57.015398: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:33:57.015398: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:33:57.015398: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:33:57.015398: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:57.015398: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:57.016385: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:57.016385: 多认证管理器状态变更: authenticating
2025-08-01 16:33:57.016385: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:57.017383: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:57.017383: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:57.017383: === 更新认证反馈状态 ===
2025-08-01 16:33:57.017383: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:57.017383: 用户姓名: null
2025-08-01 16:33:57.017383: 用户ID: null
2025-08-01 16:33:57.017383: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:57.017383: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:33:57.017383: 显示底部组件: true
2025-08-01 16:33:57.017383: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:57.017383: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:33:57.018380: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:33:57.018380: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:33:57.018380: === 更新认证反馈状态 ===
2025-08-01 16:33:57.018380: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:33:57.018380: 用户姓名: null
2025-08-01 16:33:57.018380: 用户ID: null
2025-08-01 16:33:57.018380: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:33:57.018380: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:57.018380: 显示底部组件: true
2025-08-01 16:33:57.020376: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:57.021372: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:57.021372: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:57.021372: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:57.021372: 构建读者证认证界面
2025-08-01 16:33:57.021372: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:57.021372: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:57.021372: === 构建多认证底部组件 ===
2025-08-01 16:33:57.021372: 显示底部组件: true
2025-08-01 16:33:57.021372: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:57.021372: 用户信息: null (null)
2025-08-01 16:33:57.021372: 创建AuthFeedbackWidget
2025-08-01 16:33:57.021372: 实际认证方式: AuthMethod.face
2025-08-01 16:33:57.022370: Instance of 'SysConfigData'
2025-08-01 16:33:57.022370: Instance of 'SysConfigData'
2025-08-01 16:33:57.022370: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:57.022370: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:57.022370: 返回失败组件
2025-08-01 16:33:57.022370: === 构建失败组件 ===
2025-08-01 16:33:57.022370: 认证方式: AuthMethod.face
2025-08-01 16:33:57.022370: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:57.024366: iReadCardBas ret:4294967294
2025-08-01 16:33:57.024366: 无卡
2025-08-01 16:33:57.024366: 无卡
2025-08-01 16:33:57.025362: dc_config_card:0
2025-08-01 16:33:57.025362: dc_card_n_hex:1,len:0
2025-08-01 16:33:57.025362: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:57.376019: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:33:57.376019: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:33:57.376990: 多认证管理器状态变更: listening
2025-08-01 16:33:57.384969: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:33:57.384969: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:33:57.384969: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:33:57.384969: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:33:57.384969: 构建读者证认证界面
2025-08-01 16:33:57.384969: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:33:57.385966: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:33:57.385966: === 构建多认证底部组件 ===
2025-08-01 16:33:57.385966: 显示底部组件: true
2025-08-01 16:33:57.385966: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:33:57.385966: 用户信息: null (null)
2025-08-01 16:33:57.385966: 创建AuthFeedbackWidget
2025-08-01 16:33:57.385966: 实际认证方式: AuthMethod.face
2025-08-01 16:33:57.385966: Instance of 'SysConfigData'
2025-08-01 16:33:57.385966: Instance of 'SysConfigData'
2025-08-01 16:33:57.386963: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:33:57.386963: 当前状态: AuthFeedbackState.failure
2025-08-01 16:33:57.386963: 返回失败组件
2025-08-01 16:33:57.386963: === 构建失败组件 ===
2025-08-01 16:33:57.386963: 认证方式: AuthMethod.face
2025-08-01 16:33:57.386963: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:33:57.516441: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:33:57.516441: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:33:57.516441: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:33:57.517267: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:33:57.517267: ✅ 人脸识别服务重启完成
2025-08-01 16:33:57.619989: iReadCardBas ret:4294967294
2025-08-01 16:33:57.620841: 无卡
2025-08-01 16:33:57.620841: 无卡
2025-08-01 16:33:57.621829: dc_config_card:0
2025-08-01 16:33:57.637782: dc_card_n_hex:1,len:0
2025-08-01 16:33:57.637782: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:58.284170: iReadCardBas ret:4294967294
2025-08-01 16:33:58.284170: 无卡
2025-08-01 16:33:58.284170: 无卡
2025-08-01 16:33:58.285191: dc_config_card:0
2025-08-01 16:33:58.374024: dc_card_n_hex:0,len:4
2025-08-01 16:33:58.374024: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:33:58.382251: dc_authentication_pass:0
2025-08-01 16:33:58.390217: dc_read:0
2025-08-01 16:33:58.390217: data:31313131313100000000000000000000,coder:Std14443ACoder
2025-08-01 16:33:58.390217: parseRet：{"barCode":"111111"},decoder:14443AStd
2025-08-01 16:33:58.391214: 检测到二维码数据: 111111
2025-08-01 16:33:58.391214: 未知的二维码类型: 111111
2025-08-01 16:33:58.391214: 检测到读卡器数据: 1条
2025-08-01 16:33:58.391214: 读卡器数据认证：
2025-08-01 16:33:58.391214:   设备类型: 10
2025-08-01 16:33:58.392211:   条码: 111111
2025-08-01 16:33:58.392211:   标签UID: A577EB2D
2025-08-01 16:33:58.392211:   对应登录类型: AuthLoginType.readerCard
2025-08-01 16:33:58.392211:   根据读卡器类型10确定认证方式为: 读者证
2025-08-01 16:33:58.392211:   开始调用认证API: 111111
2025-08-01 16:33:58.392211: 正在认证用户: 111111, 方式: 读者证
2025-08-01 16:33:58.392211: 多认证管理器: 认证请求被拒绝，当前正在进行人脸识别认证
2025-08-01 16:33:58.392211: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:33:58.393208:   认证结果: AuthStatus.failureNoMatch, 方式: 读者证
2025-08-01 16:33:58.393208: 认证失败，继续监听下一个用户
2025-08-01 16:33:58.393208: 恢复读卡器扫描状态...
2025-08-01 16:33:58.393208: 读卡器扫描状态已恢复
2025-08-01 16:33:58.393208: 收到非当前认证方式的结果: 读者证，当前认证方式: 人脸识别，忽略此结果
2025-08-01 16:33:58.393208: 收到非当前认证方式的结果: 读者证，当前认证方式: 人脸识别，忽略此结果
2025-08-01 16:33:58.393208: 收到非当前认证方式的结果: 读者证，当前认证方式: 人脸识别，忽略此结果
2025-08-01 16:33:58.393208: 63 CardType 值为空
2025-08-01 16:33:58.394206: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163358  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY9AZF012
2025-08-01 16:33:58.394206: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:58.668093: Rsp : 64              00120250801    163527000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY9AZDBB4
2025-08-01 16:33:58.672092: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163527, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 9AZDBB4}
2025-08-01 16:33:58.672092: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163527, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 9AZDBB4}
2025-08-01 16:33:58.672092: 认证成功后清理完成，读卡器连接和监听器保持开启以供后续使用
2025-08-01 16:33:58.689028: 保存登录记录成功: 111111
2025-08-01 16:33:58.689028: 已通知认证成功状态变更
2025-08-01 16:33:58.706348: 保存认证记录到access_logs表成功
2025-08-01 16:33:58.707247: 认证成功: 读者姓名=gd, 读者编号=111111
2025-08-01 16:33:58.707247: 调用认证后处理服务: gd, 认证方式: 人脸认证
2025-08-01 16:33:58.707247: 执行认证后处理流程，共2个处理器
2025-08-01 16:33:58.707247: 执行处理器: DoorLockHandler
2025-08-01 16:33:58.707247: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸认证
2025-08-01 16:33:58.708243: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:33:58.708243: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:33:58.708243: 使用认证页面的门锁连接: COM1
2025-08-01 16:33:58.708243: 使用继电器通道: 1
2025-08-01 16:33:58.708243: 使用继电器通道: 1
2025-08-01 16:33:58.708243: 开锁前先关锁（不等待返回）...
2025-08-01 16:33:58.708243: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:58.709241: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:58.709241: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:58.709241: 等待100毫秒确保命令发送...
2025-08-01 16:33:58.809174: 正在打开绿灯（不等待返回）...
2025-08-01 16:33:58.810071: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:33:58.810071: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:33:58.810071: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:33:58.810071: 正在开门（不等待返回）...
2025-08-01 16:33:58.810071: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:33:58.810071: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:33:58.811076: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:33:58.811076: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:33:58.811076: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:33:58.811076: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:33:58.809174
2025-08-01 16:33:58.811076: 门锁操作完成，连接由认证页面管理
2025-08-01 16:33:58.812064: 自动开门成功 - 用户: gd
2025-08-01 16:33:58.812064: 执行处理器: WelcomeHandler
2025-08-01 16:33:58.812064: 显示欢迎信息给: gd
2025-08-01 16:33:58.812064: 欢迎用户: gd，认证方式: 人脸认证
2025-08-01 16:33:58.812064: 认证后处理流程执行完毕
2025-08-01 16:33:58.812064: 认证后处理流程执行完毕
2025-08-01 16:33:59.036236: iReadCardBas ret:4294967294
2025-08-01 16:33:59.036236: 无卡
2025-08-01 16:33:59.036236: 无卡
2025-08-01 16:33:59.037247: 检测到了卡 来暂停 ：1
2025-08-01 16:33:59.037247: subThread :ReaderCommand.resumeInventory
2025-08-01 16:33:59.037247: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:33:59.037247: dc_config_card:0
2025-08-01 16:33:59.133774: dc_card_n_hex:0,len:4
2025-08-01 16:33:59.133774: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:33:59.141766: dc_authentication_pass:0
2025-08-01 16:33:59.150188: dc_read:0
2025-08-01 16:33:59.150188: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:33:59.518252: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:33:59.518)
2025-08-01 16:33:59.518252: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:33:59.518)
2025-08-01 16:33:59.518252: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:33:59.796390: iReadCardBas ret:4294967294
2025-08-01 16:33:59.796390: 无卡
2025-08-01 16:33:59.796390: 无卡
2025-08-01 16:33:59.797387: 检测到了卡 来暂停 ：1
2025-08-01 16:33:59.797387: 检测到二维码数据: 111111
2025-08-01 16:33:59.797387: 未知的二维码类型: 111111
2025-08-01 16:33:59.799403: subThread :ReaderCommand.resumeInventory
2025-08-01 16:33:59.799403: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:33:59.810711: 异步执行关锁动作（不等待返回）...
2025-08-01 16:33:59.810711: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:33:59.810711: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:33:59.811543: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:33:59.854174: dc_config_card:0
2025-08-01 16:33:59.862221: 正在关闭绿灯...
2025-08-01 16:33:59.862221: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:33:59.863147: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:33:59.863147: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:33:59.863147: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:33:59.862221
2025-08-01 16:33:59.949877: dc_card_n_hex:0,len:4
2025-08-01 16:33:59.949877: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:33:59.957682: dc_authentication_pass:0
2025-08-01 16:33:59.965689: dc_read:0
2025-08-01 16:33:59.965689: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:00.019749: 认证完成，还原到默认显示方式
2025-08-01 16:34:00.034719: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:00.034719: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:00.035705: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:00.035705: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:00.035705: 构建读者证认证界面
2025-08-01 16:34:00.035705: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:00.035705: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:00.035705: === 构建多认证底部组件 ===
2025-08-01 16:34:00.035705: 显示底部组件: false
2025-08-01 16:34:00.035705: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:00.035705: 用户信息: null (null)
2025-08-01 16:34:00.035705: 底部组件被隐藏，返回空组件
2025-08-01 16:34:00.035705: Instance of 'SysConfigData'
2025-08-01 16:34:00.036703: Instance of 'SysConfigData'
2025-08-01 16:34:00.612095: iReadCardBas ret:4294967294
2025-08-01 16:34:00.612095: 无卡
2025-08-01 16:34:00.612095: 无卡
2025-08-01 16:34:00.613090: 检测到了卡 来暂停 ：1
2025-08-01 16:34:00.613090: 检测到二维码数据: 111111
2025-08-01 16:34:00.613090: 未知的二维码类型: 111111
2025-08-01 16:34:00.613090: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:00.613090: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:00.853763: dc_config_card:0
2025-08-01 16:34:00.950094: dc_card_n_hex:0,len:4
2025-08-01 16:34:00.950094: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:00.957341: dc_authentication_pass:0
2025-08-01 16:34:00.965337: dc_read:0
2025-08-01 16:34:00.966314: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:01.518604: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:01.518)
2025-08-01 16:34:01.518604: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:01.518)
2025-08-01 16:34:01.518604: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:01.612484: iReadCardBas ret:4294967294
2025-08-01 16:34:01.612484: 无卡
2025-08-01 16:34:01.612484: 无卡
2025-08-01 16:34:01.612484: 检测到了卡 来暂停 ：1
2025-08-01 16:34:01.613461: 检测到二维码数据: 111111
2025-08-01 16:34:01.613461: 未知的二维码类型: 111111
2025-08-01 16:34:01.613461: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:01.613461: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:01.709870: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:34:01.710686: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:01.710686: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:01.710686: 开锁前关锁命令已发送
2025-08-01 16:34:01.811531: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:34:01.812318: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:01.812318: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:01.812318: 绿灯命令已发送
2025-08-01 16:34:01.812318: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:34:01.812318: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:01.813318: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:01.813318: 开锁命令已发送
2025-08-01 16:34:01.853831: dc_config_card:0
2025-08-01 16:34:01.949758: dc_card_n_hex:0,len:4
2025-08-01 16:34:01.949758: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:01.957550: dc_authentication_pass:0
2025-08-01 16:34:01.965526: dc_read:0
2025-08-01 16:34:01.965526: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:02.018841: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-08-01 16:34:02.611835: iReadCardBas ret:4294967294
2025-08-01 16:34:02.612726: 无卡
2025-08-01 16:34:02.612726: 无卡
2025-08-01 16:34:02.612726: 检测到了卡 来暂停 ：1
2025-08-01 16:34:02.613719: 检测到二维码数据: 111111
2025-08-01 16:34:02.613719: 未知的二维码类型: 111111
2025-08-01 16:34:02.613719: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:02.613719: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:02.812909: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:34:02.812909: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:02.813740: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:02.813740: 异步关锁命令已发送
2025-08-01 16:34:02.853224: dc_config_card:0
2025-08-01 16:34:02.863352: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:34:02.863352: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:02.863352: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:02.864171: 绿灯关闭失败: 操作失败
2025-08-01 16:34:02.950002: dc_card_n_hex:0,len:4
2025-08-01 16:34:02.950002: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:02.958086: dc_authentication_pass:0
2025-08-01 16:34:02.965966: dc_read:0
2025-08-01 16:34:02.965966: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:03.517677: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:03.517)
2025-08-01 16:34:03.517677: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:03.517)
2025-08-01 16:34:03.517677: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:03.612289: iReadCardBas ret:4294967294
2025-08-01 16:34:03.613287: 无卡
2025-08-01 16:34:03.613287: 无卡
2025-08-01 16:34:03.613287: 检测到了卡 来暂停 ：1
2025-08-01 16:34:03.613287: 检测到二维码数据: 111111
2025-08-01 16:34:03.614285: 未知的二维码类型: 111111
2025-08-01 16:34:03.614285: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:03.614285: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:03.853887: dc_config_card:0
2025-08-01 16:34:03.949477: dc_card_n_hex:0,len:4
2025-08-01 16:34:03.950301: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:03.957284: dc_authentication_pass:0
2025-08-01 16:34:03.965261: dc_read:0
2025-08-01 16:34:03.966298: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:04.612239: iReadCardBas ret:4294967294
2025-08-01 16:34:04.613237: 无卡
2025-08-01 16:34:04.613237: 无卡
2025-08-01 16:34:04.613237: 检测到了卡 来暂停 ：1
2025-08-01 16:34:04.614233: 检测到二维码数据: 111111
2025-08-01 16:34:04.614233: 未知的二维码类型: 111111
2025-08-01 16:34:04.614233: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:04.615230: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:04.853631: dc_config_card:0
2025-08-01 16:34:04.950045: dc_card_n_hex:0,len:4
2025-08-01 16:34:04.950045: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:04.958024: dc_authentication_pass:0
2025-08-01 16:34:04.966002: dc_read:0
2025-08-01 16:34:04.966002: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:05.517738: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:05.517)
2025-08-01 16:34:05.518574: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:05.517)
2025-08-01 16:34:05.518574: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:05.611814: iReadCardBas ret:4294967294
2025-08-01 16:34:05.612808: 无卡
2025-08-01 16:34:05.612808: 无卡
2025-08-01 16:34:05.612808: 检测到了卡 来暂停 ：1
2025-08-01 16:34:05.612808: 检测到二维码数据: 111111
2025-08-01 16:34:05.612808: 未知的二维码类型: 111111
2025-08-01 16:34:05.613805: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:05.613805: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:05.853239: dc_config_card:0
2025-08-01 16:34:05.870114: dc_card_n_hex:1,len:0
2025-08-01 16:34:05.870114: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:06.515946: iReadCardBas ret:4294967294
2025-08-01 16:34:06.516939: 无卡
2025-08-01 16:34:06.516939: 无卡
2025-08-01 16:34:06.517937: dc_config_card:0
2025-08-01 16:34:06.533243: dc_card_n_hex:1,len:0
2025-08-01 16:34:06.534100: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:07.179521: iReadCardBas ret:4294967294
2025-08-01 16:34:07.180499: 无卡
2025-08-01 16:34:07.180499: 无卡
2025-08-01 16:34:07.181736: dc_config_card:0
2025-08-01 16:34:07.269589: dc_card_n_hex:0,len:4
2025-08-01 16:34:07.269589: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:07.277598: dc_authentication_pass:0
2025-08-01 16:34:07.285243: dc_read:0
2025-08-01 16:34:07.285243: data:31313131313100000000000000000000,coder:Std14443ACoder
2025-08-01 16:34:07.286238: parseRet：{"barCode":"111111"},decoder:14443AStd
2025-08-01 16:34:07.286238: 检测到二维码数据: 111111
2025-08-01 16:34:07.286238: 未知的二维码类型: 111111
2025-08-01 16:34:07.286238: 检测到读卡器数据: 1条
2025-08-01 16:34:07.286238: 读卡器数据认证：
2025-08-01 16:34:07.286238:   设备类型: 10
2025-08-01 16:34:07.286238:   条码: 111111
2025-08-01 16:34:07.287236:   标签UID: A577EB2D
2025-08-01 16:34:07.287236:   对应登录类型: AuthLoginType.readerCard
2025-08-01 16:34:07.287236:   根据读卡器类型10确定认证方式为: 读者证
2025-08-01 16:34:07.287236:   开始调用认证API: 111111
2025-08-01 16:34:07.287236: 正在认证用户: 111111, 方式: 读者证
2025-08-01 16:34:07.287236: 多认证管理器: 切换显示方式 人脸识别 -> 读者证
2025-08-01 16:34:07.287236: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:07.287236: 多认证管理器: 读者证获得认证请求锁
2025-08-01 16:34:07.287236: 63 CardType 值为空
2025-08-01 16:34:07.287236: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163407  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY1AZF01F
2025-08-01 16:34:07.288232: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:07.301199: 构建叠加认证区域，当前方式: AuthMethod.readerCard
2025-08-01 16:34:07.301199: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:07.301199: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:07.301199: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:07.302196: 构建读者证认证界面
2025-08-01 16:34:07.302196: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:07.302196: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:07.302196: === 构建多认证底部组件 ===
2025-08-01 16:34:07.302196: 显示底部组件: false
2025-08-01 16:34:07.302196: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:07.302196: 用户信息: null (null)
2025-08-01 16:34:07.302196: 底部组件被隐藏，返回空组件
2025-08-01 16:34:07.302196: Instance of 'SysConfigData'
2025-08-01 16:34:07.302196: Instance of 'SysConfigData'
2025-08-01 16:34:07.517758: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:07.517)
2025-08-01 16:34:07.517758: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:07.517)
2025-08-01 16:34:07.517758: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:07.537717: Rsp : 64              00120250801    163536000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY1AZDBBC
2025-08-01 16:34:07.558867: 63 CardType 值为空
2025-08-01 16:34:07.558867: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163407  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY2AZF01E
2025-08-01 16:34:07.564836: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163536, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 1AZDBBC}
2025-08-01 16:34:07.564836: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163536, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 1AZDBBC}
2025-08-01 16:34:07.564836: SIP2认证成功: 用户=gd, ID=111111
2025-08-01 16:34:07.564836:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-01 16:34:07.565834: 认证成功，继续监听下一个用户
2025-08-01 16:34:07.565834: 恢复读卡器扫描状态...
2025-08-01 16:34:07.565834: 读卡器扫描状态已恢复
2025-08-01 16:34:07.565834: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-01 16:34:07.565834: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:07.565834: 多认证管理器状态变更: authenticating
2025-08-01 16:34:07.566831: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:34:07.566831: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:07.566831: 多认证管理器状态变更: completed
2025-08-01 16:34:07.566831: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-01 16:34:07.566831: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:07.566831: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-01 16:34:07.566831: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:07.566831: 多认证管理器状态变更: authenticating
2025-08-01 16:34:07.566831: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:34:07.566831: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:07.566831: 多认证管理器状态变更: completed
2025-08-01 16:34:07.567828: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-01 16:34:07.567828: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:07.567828: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-01 16:34:07.567828: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:07.567828: 多认证管理器状态变更: authenticating
2025-08-01 16:34:07.567828: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:34:07.567828: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:07.567828: 多认证管理器状态变更: completed
2025-08-01 16:34:07.567828: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-01 16:34:07.567828: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:07.567828: 收到多认证结果: 读者证 - success
2025-08-01 16:34:07.567828: 认证成功，更新主显示方式为: 读者证
2025-08-01 16:34:07.568826: === 更新认证反馈状态 ===
2025-08-01 16:34:07.568826: 结果状态: AuthStatus.success
2025-08-01 16:34:07.568826: 用户姓名: gd
2025-08-01 16:34:07.568826: 用户ID: 111111
2025-08-01 16:34:07.568826: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:34:07.568826: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:07.568826: 用户信息: gd (111111)
2025-08-01 16:34:07.568826: 收到多认证结果: 读者证 - success
2025-08-01 16:34:07.568826: 认证成功，更新主显示方式为: 读者证
2025-08-01 16:34:07.568826: === 更新认证反馈状态 ===
2025-08-01 16:34:07.568826: 结果状态: AuthStatus.success
2025-08-01 16:34:07.569823: 用户姓名: gd
2025-08-01 16:34:07.569823: 用户ID: 111111
2025-08-01 16:34:07.569823: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:34:07.569823: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:07.569823: 用户信息: gd (111111)
2025-08-01 16:34:07.569823: 收到多认证结果: 读者证 - success
2025-08-01 16:34:07.569823: 认证成功，更新主显示方式为: 读者证
2025-08-01 16:34:07.569823: === 更新认证反馈状态 ===
2025-08-01 16:34:07.569823: 结果状态: AuthStatus.success
2025-08-01 16:34:07.569823: 用户姓名: gd
2025-08-01 16:34:07.570820: 用户ID: 111111
2025-08-01 16:34:07.570820: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:34:07.570820: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:07.570820: 用户信息: gd (111111)
2025-08-01 16:34:07.572816: 构建叠加认证区域，当前方式: AuthMethod.readerCard
2025-08-01 16:34:07.572816: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:07.572816: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:07.572816: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:07.573813: 构建读者证认证界面
2025-08-01 16:34:07.573813: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:07.573813: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:07.573813: === 构建多认证底部组件 ===
2025-08-01 16:34:07.573813: 显示底部组件: true
2025-08-01 16:34:07.573813: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:07.573813: 用户信息: gd (111111)
2025-08-01 16:34:07.573813: 创建AuthFeedbackWidget
2025-08-01 16:34:07.573813: 实际认证方式: AuthMethod.readerCard
2025-08-01 16:34:07.573813: Instance of 'SysConfigData'
2025-08-01 16:34:07.573813: Instance of 'SysConfigData'
2025-08-01 16:34:07.573813: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:07.573813: 当前状态: AuthFeedbackState.success
2025-08-01 16:34:07.574810: 返回成功组件 (详细信息)
2025-08-01 16:34:07.574810: === 构建成功组件（详细信息） ===
2025-08-01 16:34:07.574810: 用户: gd, ID: 111111
2025-08-01 16:34:07.574810: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:34:07.574810: 新状态: AuthFeedbackState.success
2025-08-01 16:34:07.574810: 用户信息: gd (111111)
2025-08-01 16:34:07.574810: 启动定时器，3秒后切换到通行模式
2025-08-01 16:34:07.582800: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:07.582800: 当前状态: AuthFeedbackState.success
2025-08-01 16:34:07.583787: 返回成功组件 (详细信息)
2025-08-01 16:34:07.583787: === 构建成功组件（详细信息） ===
2025-08-01 16:34:07.583787: 用户: gd, ID: 111111
2025-08-01 16:34:07.736538: Rsp : 64              00120250801    163536000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY2AZDBBB
2025-08-01 16:34:07.739588: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163536, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 2AZDBBB}
2025-08-01 16:34:07.739588: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163536, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 2AZDBBB}
2025-08-01 16:34:07.739588: 认证成功后清理完成，读卡器连接和监听器保持开启以供后续使用
2025-08-01 16:34:07.756572: 保存登录记录成功: 111111
2025-08-01 16:34:07.756572: 已通知认证成功状态变更
2025-08-01 16:34:07.772549: 保存认证记录到access_logs表成功
2025-08-01 16:34:07.773526: 认证成功: 读者姓名=gd, 读者编号=111111
2025-08-01 16:34:07.773526: 调用认证后处理服务: gd, 认证方式: 人脸认证
2025-08-01 16:34:07.773526: 执行认证后处理流程，共2个处理器
2025-08-01 16:34:07.773526: 执行处理器: DoorLockHandler
2025-08-01 16:34:07.773526: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸认证
2025-08-01 16:34:07.773526: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:34:07.773526: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:34:07.774522: 使用认证页面的门锁连接: COM1
2025-08-01 16:34:07.774522: 使用继电器通道: 1
2025-08-01 16:34:07.774522: 使用继电器通道: 1
2025-08-01 16:34:07.774522: 开锁前先关锁（不等待返回）...
2025-08-01 16:34:07.774522: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:34:07.774522: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:34:07.774522: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:34:07.774522: 等待100毫秒确保命令发送...
2025-08-01 16:34:07.876850: 正在打开绿灯（不等待返回）...
2025-08-01 16:34:07.877865: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:34:07.877865: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:34:07.877865: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:34:07.877865: 正在开门（不等待返回）...
2025-08-01 16:34:07.877865: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:34:07.877865: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:34:07.877865: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:34:07.877865: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:34:07.877865: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:34:07.878846: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:34:07.876012
2025-08-01 16:34:07.878846: 门锁操作完成，连接由认证页面管理
2025-08-01 16:34:07.878846: 自动开门成功 - 用户: gd
2025-08-01 16:34:07.878846: 执行处理器: WelcomeHandler
2025-08-01 16:34:07.878846: 显示欢迎信息给: gd
2025-08-01 16:34:07.878846: 欢迎用户: gd，认证方式: 人脸认证
2025-08-01 16:34:07.879828: 认证后处理流程执行完毕
2025-08-01 16:34:07.879828: 认证后处理流程执行完毕
2025-08-01 16:34:07.931481: iReadCardBas ret:4294967294
2025-08-01 16:34:07.932333: 无卡
2025-08-01 16:34:07.932333: 无卡
2025-08-01 16:34:07.932333: 检测到了卡 来暂停 ：1
2025-08-01 16:34:07.932333: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:07.933334: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:07.933334: dc_config_card:0
2025-08-01 16:34:08.029492: dc_card_n_hex:0,len:4
2025-08-01 16:34:08.029492: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:08.037492: dc_authentication_pass:0
2025-08-01 16:34:08.045449: dc_read:0
2025-08-01 16:34:08.045449: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:08.691967: iReadCardBas ret:4294967294
2025-08-01 16:34:08.691967: 无卡
2025-08-01 16:34:08.691967: 无卡
2025-08-01 16:34:08.691967: 检测到了卡 来暂停 ：1
2025-08-01 16:34:08.692960: 检测到二维码数据: 111111
2025-08-01 16:34:08.692960: 未知的二维码类型: 111111
2025-08-01 16:34:08.692960: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:08.692960: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:08.853264: dc_config_card:0
2025-08-01 16:34:08.869378: dc_card_n_hex:1,len:0
2025-08-01 16:34:08.869378: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:08.877356: 异步执行关锁动作（不等待返回）...
2025-08-01 16:34:08.877356: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:34:08.877356: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:34:08.878354: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:34:08.927613: 正在关闭绿灯...
2025-08-01 16:34:08.928445: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:34:08.928445: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:34:08.928445: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:34:08.928445: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:34:08.927613
2025-08-01 16:34:09.516279: iReadCardBas ret:4294967294
2025-08-01 16:34:09.517271: 无卡
2025-08-01 16:34:09.517271: 无卡
2025-08-01 16:34:09.517271: dc_config_card:0
2025-08-01 16:34:09.518247: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:09.518)
2025-08-01 16:34:09.518247: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:09.518)
2025-08-01 16:34:09.518247: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:09.533612: dc_card_n_hex:1,len:0
2025-08-01 16:34:09.534436: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:10.180716: iReadCardBas ret:4294967294
2025-08-01 16:34:10.181485: 无卡
2025-08-01 16:34:10.181485: 无卡
2025-08-01 16:34:10.189699: dc_config_card:0
2025-08-01 16:34:10.205672: dc_card_n_hex:1,len:0
2025-08-01 16:34:10.205672: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:10.575123: 定时器触发，切换到通行模式
2025-08-01 16:34:10.575123: 认证反馈状态切换到通行模式，启动2秒隐藏定时器
2025-08-01 16:34:10.584463: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:10.584463: 当前状态: AuthFeedbackState.passMode
2025-08-01 16:34:10.584463: 返回通行模式组件
2025-08-01 16:34:10.584463: === 构建通行模式组件 ===
2025-08-01 16:34:10.584463: 用户: gd, ID: 111111
2025-08-01 16:34:10.584463: 认证方式: AuthMethod.readerCard
2025-08-01 16:34:10.775579: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:34:10.775579: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:10.775579: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:10.776497: 开锁前关锁命令已发送
2025-08-01 16:34:10.852347: iReadCardBas ret:4294967294
2025-08-01 16:34:10.852347: 无卡
2025-08-01 16:34:10.852347: 无卡
2025-08-01 16:34:10.853346: dc_config_card:0
2025-08-01 16:34:10.869769: dc_card_n_hex:1,len:0
2025-08-01 16:34:10.869769: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:10.877747: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:34:10.877747: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:10.877747: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:10.877747: 绿灯命令已发送
2025-08-01 16:34:10.878747: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:34:10.878747: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:10.878747: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:10.878747: 开锁命令已发送
2025-08-01 16:34:11.516717: iReadCardBas ret:4294967294
2025-08-01 16:34:11.516717: 无卡
2025-08-01 16:34:11.516717: 无卡
2025-08-01 16:34:11.517714: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:11.517)
2025-08-01 16:34:11.517714: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:11.517)
2025-08-01 16:34:11.517714: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:11.517714: dc_config_card:0
2025-08-01 16:34:11.533670: dc_card_n_hex:1,len:0
2025-08-01 16:34:11.533670: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:11.877699: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:34:11.878695: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:11.878695: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:11.878695: 异步关锁命令已发送
2025-08-01 16:34:11.929603: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:34:11.929603: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:11.929603: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:11.930593: 绿灯关闭失败: 操作失败
2025-08-01 16:34:12.566989: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-01 16:34:12.566989: 多认证管理器: 还原到默认显示方式: 人脸识别
2025-08-01 16:34:12.566989: 检测到认证方式切换，重置认证反馈状态
2025-08-01 16:34:12.566989: 认证反馈状态已重置
2025-08-01 16:34:12.566989: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:34:12.585579: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:12.585579: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:12.585579: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:12.585579: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:12.585579: 构建读者证认证界面
2025-08-01 16:34:12.585579: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:12.585579: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:12.586551: === 构建多认证底部组件 ===
2025-08-01 16:34:12.586551: 显示底部组件: false
2025-08-01 16:34:12.586551: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:12.586551: 用户信息: null (null)
2025-08-01 16:34:12.586551: 底部组件被隐藏，返回空组件
2025-08-01 16:34:12.586551: Instance of 'SysConfigData'
2025-08-01 16:34:12.586551: Instance of 'SysConfigData'
2025-08-01 16:34:12.811246: iReadCardBas ret:-1
2025-08-01 16:34:12.812268: 卡复位失败
2025-08-01 16:34:12.812268: 卡复位失败
2025-08-01 16:34:12.813275: dc_config_card:0
2025-08-01 16:34:12.829229: dc_card_n_hex:1,len:0
2025-08-01 16:34:12.830198: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:12.900061: iReadCardBas ret:0
2025-08-01 16:34:12.900061: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:12.900061: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:12.901056: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:12.901056: data:330424198710132426,coder:NoParseCoder
2025-08-01 16:34:12.901056: parseRet：{"barCode":"330424198710132426"},decoder:不解析
2025-08-01 16:34:12.902054: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:12.902054: 验证通用二维码: 330424198710132426
2025-08-01 16:34:12.903050: 检测到读卡器数据: 1条
2025-08-01 16:34:12.903050: 读卡器数据认证：
2025-08-01 16:34:12.903050:   设备类型: 13
2025-08-01 16:34:12.903050:   条码: 330424198710132426
2025-08-01 16:34:12.903050:   标签UID: 330424198710132426
2025-08-01 16:34:12.903050:   对应登录类型: AuthLoginType.socailSecurityCard
2025-08-01 16:34:12.903050:   根据读卡器类型13确定认证方式为: 社保卡
2025-08-01 16:34:12.903050:   开始调用认证API: 330424198710132426
2025-08-01 16:34:12.904049: 正在认证用户: 330424198710132426, 方式: 社保卡
2025-08-01 16:34:12.904049: 多认证管理器: 切换显示方式 人脸识别 -> 社保卡
2025-08-01 16:34:12.904049: 多认证管理器状态变化，当前显示方式: 社保卡
2025-08-01 16:34:12.904049: 多认证管理器: 社保卡获得认证请求锁
2025-08-01 16:34:12.904049: 63 CardType 值为空
2025-08-01 16:34:12.905045: Req msgType：Sip2MsgType.patronInformation ,length:83， ret:  6300120250801    163412  Y       AOhlsp|AA330424198710132426|TY|BP1|BQ20|AY3AZEDAB
2025-08-01 16:34:12.905045: 检测到了卡 来暂停 ：1
2025-08-01 16:34:12.918011: 构建叠加认证区域，当前方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:12.919009: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:12.919009: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:12.919009: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:12.919009: 构建读者证认证界面
2025-08-01 16:34:12.919009: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:12.919009: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:12.919009: === 构建多认证底部组件 ===
2025-08-01 16:34:12.919009: 显示底部组件: false
2025-08-01 16:34:12.919009: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:12.919009: 用户信息: null (null)
2025-08-01 16:34:12.919009: 底部组件被隐藏，返回空组件
2025-08-01 16:34:12.920007: Instance of 'SysConfigData'
2025-08-01 16:34:12.920007: Instance of 'SysConfigData'
2025-08-01 16:34:12.973861: Rsp : 64YYYYYYYYYYYYYY00020250801    163542000000000000000000000000AOhlsp|AA330424198710132426|XO|AE330424198710132426|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY3AZBC82
2025-08-01 16:34:12.987958: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163542, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 3AZBC82}
2025-08-01 16:34:12.987958: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163542, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 3AZBC82}
2025-08-01 16:34:12.987958: SIP2认证失败: 读者未找到,请联系管理员寻求帮助
2025-08-01 16:34:12.987958: 认证失败，继续监听新的卡片扫描...
2025-08-01 16:34:12.988869:   认证结果: AuthStatus.failureNoMatch, 方式: 社保卡
2025-08-01 16:34:12.988869: 认证失败，继续监听下一个用户
2025-08-01 16:34:12.988869: 恢复读卡器扫描状态...
2025-08-01 16:34:12.988869: 读卡器扫描状态已恢复
2025-08-01 16:34:12.988869: 收到认证结果但当前状态已完成，忽略失败结果: 社保卡 - failureNoMatch
2025-08-01 16:34:12.988869: 收到认证结果但当前状态已完成，忽略失败结果: 社保卡 - failureNoMatch
2025-08-01 16:34:12.988869: 收到认证结果但当前状态已完成，忽略失败结果: 社保卡 - failureNoMatch
2025-08-01 16:34:12.988869: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:12.988869: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:12.993055: 63 CardType 值为空
2025-08-01 16:34:12.993055: Req msgType：Sip2MsgType.patronInformation ,length:83， ret:  6300120250801    163412  Y       AOhlsp|AA330424198710132426|TY|BP1|BQ20|AY4AZEDAA
2025-08-01 16:34:13.062864: Rsp : 64YYYYYYYYYYYYYY00020250801    163542000000000000000000000000AOhlsp|AA330424198710132426|XO|AE330424198710132426|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY4AZBC81
2025-08-01 16:34:13.086600: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163542, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 4AZBC81}
2025-08-01 16:34:13.086600: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163542, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 4AZBC81}
2025-08-01 16:34:13.103616: 保存认证失败记录到access_logs表成功: 俞佳, 330424198710132426
2025-08-01 16:34:13.350177: dc_config_card:0
2025-08-01 16:34:13.365336: dc_card_n_hex:1,len:0
2025-08-01 16:34:13.365336: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:13.436397: iReadCardBas ret:0
2025-08-01 16:34:13.436397: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:13.436397: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:13.437393: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:13.437393: 检测到了卡 来暂停 ：1
2025-08-01 16:34:13.437393: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:13.437393: 验证通用二维码: 330424198710132426
2025-08-01 16:34:13.438390: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:13.438390: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:13.517625: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:13.517)
2025-08-01 16:34:13.517625: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:13.517)
2025-08-01 16:34:13.517625: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:13.853962: dc_config_card:0
2025-08-01 16:34:13.869411: dc_card_n_hex:1,len:0
2025-08-01 16:34:13.869411: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:13.944719: iReadCardBas ret:0
2025-08-01 16:34:13.945510: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:13.945510: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:13.945510: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:13.945510: 检测到了卡 来暂停 ：1
2025-08-01 16:34:13.946518: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:13.946518: 验证通用二维码: 330424198710132426
2025-08-01 16:34:13.946518: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:13.946518: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:14.349544: dc_config_card:0
2025-08-01 16:34:14.365684: dc_card_n_hex:1,len:0
2025-08-01 16:34:14.365684: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:15.012262: iReadCardBas ret:4294967294
2025-08-01 16:34:15.012262: 无卡
2025-08-01 16:34:15.012262: 无卡
2025-08-01 16:34:15.013260: dc_config_card:0
2025-08-01 16:34:15.030216: dc_card_n_hex:1,len:0
2025-08-01 16:34:15.030216: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:15.518148: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:15.518)
2025-08-01 16:34:15.518148: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:15.518)
2025-08-01 16:34:15.518148: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:15.565680: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-01 16:34:15.566517: 多认证管理器状态变化，当前显示方式: 社保卡
2025-08-01 16:34:15.566517: 多认证管理器状态变更: listening
2025-08-01 16:34:15.566517: 🔍 检查人脸识别服务健康状态...
2025-08-01 16:34:15.566517: 🔄 重置人脸识别服务认证状态...
2025-08-01 16:34:15.568510: 构建叠加认证区域，当前方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:15.568510: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:15.568510: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:15.568510: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:15.569507: 构建读者证认证界面
2025-08-01 16:34:15.569507: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:15.569507: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:15.569507: === 构建多认证底部组件 ===
2025-08-01 16:34:15.569507: 显示底部组件: false
2025-08-01 16:34:15.569507: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:15.569507: 用户信息: null (null)
2025-08-01 16:34:15.569507: 底部组件被隐藏，返回空组件
2025-08-01 16:34:15.569507: Instance of 'SysConfigData'
2025-08-01 16:34:15.569507: Instance of 'SysConfigData'
2025-08-01 16:34:15.676430: iReadCardBas ret:4294967294
2025-08-01 16:34:15.677386: 无卡
2025-08-01 16:34:15.677386: 无卡
2025-08-01 16:34:15.685531: dc_config_card:0
2025-08-01 16:34:15.701725: dc_card_n_hex:1,len:0
2025-08-01 16:34:15.701725: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:16.347487: iReadCardBas ret:4294967294
2025-08-01 16:34:16.348490: 无卡
2025-08-01 16:34:16.348490: 无卡
2025-08-01 16:34:16.349486: dc_config_card:0
2025-08-01 16:34:16.365617: dc_card_n_hex:1,len:0
2025-08-01 16:34:16.365617: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:17.011440: iReadCardBas ret:4294967294
2025-08-01 16:34:17.012437: 无卡
2025-08-01 16:34:17.012437: 无卡
2025-08-01 16:34:17.013464: dc_config_card:0
2025-08-01 16:34:17.029392: dc_card_n_hex:1,len:0
2025-08-01 16:34:17.029392: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:17.517131: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:17.517)
2025-08-01 16:34:17.518011: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:17.517)
2025-08-01 16:34:17.518011: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:17.675517: iReadCardBas ret:4294967294
2025-08-01 16:34:17.676363: 无卡
2025-08-01 16:34:17.676363: 无卡
2025-08-01 16:34:17.677363: dc_config_card:0
2025-08-01 16:34:17.693983: dc_card_n_hex:1,len:0
2025-08-01 16:34:17.693983: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:17.764365: iReadCardBas ret:0
2025-08-01 16:34:17.764365: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:17.764365: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:17.764365: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:17.764365: data:330424198710132426,coder:NoParseCoder
2025-08-01 16:34:17.764365: parseRet：{"barCode":"330424198710132426"},decoder:不解析
2025-08-01 16:34:17.765364: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:17.765364: 验证通用二维码: 330424198710132426
2025-08-01 16:34:17.765364: 检测到读卡器数据: 1条
2025-08-01 16:34:17.765364: 读卡器数据认证：
2025-08-01 16:34:17.765364:   设备类型: 13
2025-08-01 16:34:17.765364:   条码: 330424198710132426
2025-08-01 16:34:17.766361:   标签UID: 330424198710132426
2025-08-01 16:34:17.766361:   对应登录类型: AuthLoginType.socailSecurityCard
2025-08-01 16:34:17.766361:   根据读卡器类型13确定认证方式为: 社保卡
2025-08-01 16:34:17.766361:   开始调用认证API: 330424198710132426
2025-08-01 16:34:17.766361: 正在认证用户: 330424198710132426, 方式: 社保卡
2025-08-01 16:34:17.766361: 多认证管理器: 认证请求被拒绝，社保卡认证正在进行中，拒绝重复请求
2025-08-01 16:34:17.766361: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:34:17.767358:   认证结果: AuthStatus.failureNoMatch, 方式: 社保卡
2025-08-01 16:34:17.767358: 认证失败，继续监听下一个用户
2025-08-01 16:34:17.767358: 恢复读卡器扫描状态...
2025-08-01 16:34:17.767358: 读卡器扫描状态已恢复
2025-08-01 16:34:17.767358: 多认证管理器: 收到认证结果: 社保卡 - failureNoMatch
2025-08-01 16:34:17.768355: 多认证管理器状态变化，当前显示方式: 社保卡
2025-08-01 16:34:17.768355: 多认证管理器状态变更: authenticating
2025-08-01 16:34:17.768355: 多认证管理器: 认证失败(无匹配): 社保卡，显示失败信息
2025-08-01 16:34:17.768355: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:17.768355: 多认证管理器: 收到认证结果: 社保卡 - failureNoMatch
2025-08-01 16:34:17.768355: 多认证管理器: 认证失败(无匹配): 社保卡，显示失败信息
2025-08-01 16:34:17.768355: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:17.768355: 多认证管理器: 收到认证结果: 社保卡 - failureNoMatch
2025-08-01 16:34:17.768355: 多认证管理器: 认证失败(无匹配): 社保卡，显示失败信息
2025-08-01 16:34:17.768355: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:17.768355: 63 CardType 值为空
2025-08-01 16:34:17.769353: Req msgType：Sip2MsgType.patronInformation ,length:83， ret:  6300120250801    163417  Y       AOhlsp|AA330424198710132426|TY|BP1|BQ20|AY5AZEDA4
2025-08-01 16:34:17.769353: 收到多认证结果: 社保卡 - failureNoMatch
2025-08-01 16:34:17.769353: === 更新认证反馈状态 ===
2025-08-01 16:34:17.769353: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:34:17.769353: 用户姓名: null
2025-08-01 16:34:17.769353: 用户ID: null
2025-08-01 16:34:17.769353: 设置反馈状态为failure，认证方式: 社保卡
2025-08-01 16:34:17.769353: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:34:17.769353: 显示底部组件: true
2025-08-01 16:34:17.769353: 收到多认证结果: 社保卡 - failureNoMatch
2025-08-01 16:34:17.770349: === 更新认证反馈状态 ===
2025-08-01 16:34:17.770349: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:34:17.770349: 用户姓名: null
2025-08-01 16:34:17.770349: 用户ID: null
2025-08-01 16:34:17.770349: 设置反馈状态为failure，认证方式: 社保卡
2025-08-01 16:34:17.770349: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:34:17.770349: 显示底部组件: true
2025-08-01 16:34:17.770349: 收到多认证结果: 社保卡 - failureNoMatch
2025-08-01 16:34:17.770349: === 更新认证反馈状态 ===
2025-08-01 16:34:17.770349: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:34:17.770349: 用户姓名: null
2025-08-01 16:34:17.771347: 用户ID: null
2025-08-01 16:34:17.771347: 设置反馈状态为failure，认证方式: 社保卡
2025-08-01 16:34:17.771347: 具体错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:34:17.771347: 显示底部组件: true
2025-08-01 16:34:17.771347: 检测到了卡 来暂停 ：1
2025-08-01 16:34:17.775339: 构建叠加认证区域，当前方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:17.775339: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:17.775339: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:17.775339: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:17.775339: 构建读者证认证界面
2025-08-01 16:34:17.775339: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:17.775339: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:17.776334: === 构建多认证底部组件 ===
2025-08-01 16:34:17.776334: 显示底部组件: true
2025-08-01 16:34:17.776334: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:34:17.776334: 用户信息: null (null)
2025-08-01 16:34:17.776334: 创建AuthFeedbackWidget
2025-08-01 16:34:17.776334: 实际认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:17.776334: Instance of 'SysConfigData'
2025-08-01 16:34:17.776334: Instance of 'SysConfigData'
2025-08-01 16:34:17.776334: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:17.776334: 当前状态: AuthFeedbackState.failure
2025-08-01 16:34:17.776334: 返回失败组件
2025-08-01 16:34:17.776334: === 构建失败组件 ===
2025-08-01 16:34:17.777331: 认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:17.777331: 错误信息: 当前有其他认证正在进行，请稍后再试
2025-08-01 16:34:17.777331: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:17.777331: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:17.853278: dc_config_card:0
2025-08-01 16:34:17.869209: dc_card_n_hex:1,len:0
2025-08-01 16:34:17.870206: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:17.938293: iReadCardBas ret:0
2025-08-01 16:34:17.938293: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:17.939286: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:17.939286: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:17.939286: 检测到了卡 来暂停 ：1
2025-08-01 16:34:17.939286: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:17.939286: 验证通用二维码: 330424198710132426
2025-08-01 16:34:17.940283: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:17.940283: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:18.156197: Rsp : 64YYYYYYYYYYYYYY00020250801    163547000000000000000000000000AOhlsp|AA330424198710132426|XO|AE330424198710132426|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY5AZBC7B
2025-08-01 16:34:18.168163: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163547, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 5AZBC7B}
2025-08-01 16:34:18.168163: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163547, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 5AZBC7B}
2025-08-01 16:34:18.184339: 保存认证失败记录到access_logs表成功: 俞佳, 330424198710132426
2025-08-01 16:34:18.349705: dc_config_card:0
2025-08-01 16:34:18.365759: dc_card_n_hex:1,len:0
2025-08-01 16:34:18.365759: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:18.435176: iReadCardBas ret:0
2025-08-01 16:34:18.435176: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:18.436116: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:18.436116: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:18.436116: 检测到了卡 来暂停 ：1
2025-08-01 16:34:18.436116: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:18.436116: 验证通用二维码: 330424198710132426
2025-08-01 16:34:18.438109: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:18.438109: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:18.853997: dc_config_card:0
2025-08-01 16:34:18.869207: dc_card_n_hex:1,len:0
2025-08-01 16:34:18.870176: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:19.516601: iReadCardBas ret:4294967294
2025-08-01 16:34:19.517488: 无卡
2025-08-01 16:34:19.517488: 无卡
2025-08-01 16:34:19.517488: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:19.517)
2025-08-01 16:34:19.517488: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:19.517)
2025-08-01 16:34:19.517488: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:19.525613: dc_config_card:0
2025-08-01 16:34:19.541629: dc_card_n_hex:1,len:0
2025-08-01 16:34:19.541629: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:20.187796: iReadCardBas ret:4294967294
2025-08-01 16:34:20.188589: 无卡
2025-08-01 16:34:20.188589: 无卡
2025-08-01 16:34:20.189726: dc_config_card:0
2025-08-01 16:34:20.205755: dc_card_n_hex:1,len:0
2025-08-01 16:34:20.205755: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:20.768826: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-01 16:34:20.768826: 多认证管理器状态变化，当前显示方式: 社保卡
2025-08-01 16:34:20.768826: 多认证管理器状态变更: listening
2025-08-01 16:34:20.772814: 多认证管理器: 还原到默认显示方式: 人脸识别
2025-08-01 16:34:20.772814: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:34:20.772814: 认证完成，还原到默认显示方式
2025-08-01 16:34:20.784809: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:20.784809: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:20.784809: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:20.784809: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:20.785780: 构建读者证认证界面
2025-08-01 16:34:20.785780: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:20.785780: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:20.785780: === 构建多认证底部组件 ===
2025-08-01 16:34:20.785780: 显示底部组件: false
2025-08-01 16:34:20.785780: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:20.785780: 用户信息: null (null)
2025-08-01 16:34:20.785780: 底部组件被隐藏，返回空组件
2025-08-01 16:34:20.785780: Instance of 'SysConfigData'
2025-08-01 16:34:20.785780: Instance of 'SysConfigData'
2025-08-01 16:34:20.851259: iReadCardBas ret:4294967294
2025-08-01 16:34:20.852227: 无卡
2025-08-01 16:34:20.852227: 无卡
2025-08-01 16:34:20.853224: dc_config_card:0
2025-08-01 16:34:20.869396: dc_card_n_hex:1,len:0
2025-08-01 16:34:20.869396: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:21.516292: iReadCardBas ret:4294967294
2025-08-01 16:34:21.517116: 无卡
2025-08-01 16:34:21.517116: 无卡
2025-08-01 16:34:21.518141: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:21.518)
2025-08-01 16:34:21.518141: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:21.518)
2025-08-01 16:34:21.518141: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:21.526091: dc_config_card:0
2025-08-01 16:34:21.541122: dc_card_n_hex:1,len:0
2025-08-01 16:34:21.542118: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:22.188042: iReadCardBas ret:4294967294
2025-08-01 16:34:22.188042: 无卡
2025-08-01 16:34:22.188042: 无卡
2025-08-01 16:34:22.190038: dc_config_card:0
2025-08-01 16:34:22.206196: dc_card_n_hex:1,len:0
2025-08-01 16:34:22.206196: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:22.769247: 多认证管理器: 认证请求锁已释放（之前为社保卡）
2025-08-01 16:34:22.852690: iReadCardBas ret:4294967294
2025-08-01 16:34:22.852690: 无卡
2025-08-01 16:34:22.852690: 无卡
2025-08-01 16:34:22.853697: dc_config_card:0
2025-08-01 16:34:22.869928: dc_card_n_hex:1,len:0
2025-08-01 16:34:22.869928: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:23.516374: iReadCardBas ret:4294967294
2025-08-01 16:34:23.516374: 无卡
2025-08-01 16:34:23.516374: 无卡
2025-08-01 16:34:23.517293: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:23.517)
2025-08-01 16:34:23.517293: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:23.517)
2025-08-01 16:34:23.517293: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:23.525502: dc_config_card:0
2025-08-01 16:34:23.613394: dc_card_n_hex:0,len:4
2025-08-01 16:34:23.614391: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-01 16:34:23.621461: dc_authentication_pass:0
2025-08-01 16:34:23.629615: dc_read:0
2025-08-01 16:34:23.629615: data:31313131313100000000000000000000,coder:Std14443ACoder
2025-08-01 16:34:23.629615: parseRet：{"barCode":"111111"},decoder:14443AStd
2025-08-01 16:34:23.630579: 检测到二维码数据: 111111
2025-08-01 16:34:23.630579: 未知的二维码类型: 111111
2025-08-01 16:34:23.630579: 检测到读卡器数据: 1条
2025-08-01 16:34:23.630579: 读卡器数据认证：
2025-08-01 16:34:23.630579:   设备类型: 10
2025-08-01 16:34:23.631576:   条码: 111111
2025-08-01 16:34:23.631576:   标签UID: A577EB2D
2025-08-01 16:34:23.631576:   对应登录类型: AuthLoginType.readerCard
2025-08-01 16:34:23.631576:   根据读卡器类型10确定认证方式为: 读者证
2025-08-01 16:34:23.631576:   开始调用认证API: 111111
2025-08-01 16:34:23.631576: 正在认证用户: 111111, 方式: 读者证
2025-08-01 16:34:23.631576: 多认证管理器: 切换显示方式 人脸识别 -> 读者证
2025-08-01 16:34:23.631576: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:23.631576: 多认证管理器: 读者证获得认证请求锁
2025-08-01 16:34:23.632586: 63 CardType 值为空
2025-08-01 16:34:23.632586: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163423  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY6AZF01C
2025-08-01 16:34:23.632586: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:23.634580: 构建叠加认证区域，当前方式: AuthMethod.readerCard
2025-08-01 16:34:23.634580: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:23.634580: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:23.634580: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:23.634580: 构建读者证认证界面
2025-08-01 16:34:23.635567: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:23.635567: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:23.635567: === 构建多认证底部组件 ===
2025-08-01 16:34:23.635567: 显示底部组件: false
2025-08-01 16:34:23.635567: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:23.635567: 用户信息: null (null)
2025-08-01 16:34:23.635567: 底部组件被隐藏，返回空组件
2025-08-01 16:34:23.635567: Instance of 'SysConfigData'
2025-08-01 16:34:23.635567: Instance of 'SysConfigData'
2025-08-01 16:34:23.829346: Rsp : 64              00120250801    163552000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY6AZDBB9
2025-08-01 16:34:23.831438: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163552, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 6AZDBB9}
2025-08-01 16:34:23.832352: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163552, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 6AZDBB9}
2025-08-01 16:34:23.832352: SIP2认证成功: 用户=gd, ID=111111
2025-08-01 16:34:23.832352:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-01 16:34:23.832352: 认证成功，继续监听下一个用户
2025-08-01 16:34:23.833334: 恢复读卡器扫描状态...
2025-08-01 16:34:23.833334: 读卡器扫描状态已恢复
2025-08-01 16:34:23.833334: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-01 16:34:23.833334: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:23.833334: 多认证管理器状态变更: authenticating
2025-08-01 16:34:23.833334: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:34:23.833334: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:23.834344: 多认证管理器状态变更: completed
2025-08-01 16:34:23.834344: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-01 16:34:23.834344: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:23.834344: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-01 16:34:23.834344: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:23.834344: 多认证管理器状态变更: authenticating
2025-08-01 16:34:23.835329: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:34:23.835329: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:23.835329: 多认证管理器状态变更: completed
2025-08-01 16:34:23.835329: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-01 16:34:23.835329: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:23.835329: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-01 16:34:23.835329: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:23.836326: 多认证管理器状态变更: authenticating
2025-08-01 16:34:23.836326: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:34:23.836326: 多认证管理器状态变化，当前显示方式: 读者证
2025-08-01 16:34:23.836326: 多认证管理器状态变更: completed
2025-08-01 16:34:23.836326: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-01 16:34:23.836326: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:23.836326: 收到多认证结果: 读者证 - success
2025-08-01 16:34:23.837325: 认证成功，更新主显示方式为: 读者证
2025-08-01 16:34:23.837325: === 更新认证反馈状态 ===
2025-08-01 16:34:23.837325: 结果状态: AuthStatus.success
2025-08-01 16:34:23.837325: 用户姓名: gd
2025-08-01 16:34:23.837325: 用户ID: 111111
2025-08-01 16:34:23.837325: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:34:23.837325: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:23.837325: 用户信息: gd (111111)
2025-08-01 16:34:23.838322: 收到多认证结果: 读者证 - success
2025-08-01 16:34:23.838322: 认证成功，更新主显示方式为: 读者证
2025-08-01 16:34:23.838322: === 更新认证反馈状态 ===
2025-08-01 16:34:23.838322: 结果状态: AuthStatus.success
2025-08-01 16:34:23.838322: 用户姓名: gd
2025-08-01 16:34:23.839319: 用户ID: 111111
2025-08-01 16:34:23.839319: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:34:23.839319: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:23.839319: 用户信息: gd (111111)
2025-08-01 16:34:23.839319: 收到多认证结果: 读者证 - success
2025-08-01 16:34:23.839319: 认证成功，更新主显示方式为: 读者证
2025-08-01 16:34:23.839319: === 更新认证反馈状态 ===
2025-08-01 16:34:23.840320: 结果状态: AuthStatus.success
2025-08-01 16:34:23.840320: 用户姓名: gd
2025-08-01 16:34:23.840320: 用户ID: 111111
2025-08-01 16:34:23.840320: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:34:23.840320: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:23.840320: 用户信息: gd (111111)
2025-08-01 16:34:23.842311: 构建叠加认证区域，当前方式: AuthMethod.readerCard
2025-08-01 16:34:23.843308: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:23.843308: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:23.843308: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:23.843308: 构建读者证认证界面
2025-08-01 16:34:23.843308: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:23.843308: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:23.843308: === 构建多认证底部组件 ===
2025-08-01 16:34:23.843308: 显示底部组件: true
2025-08-01 16:34:23.843308: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:23.843308: 用户信息: gd (111111)
2025-08-01 16:34:23.843308: 创建AuthFeedbackWidget
2025-08-01 16:34:23.844305: 实际认证方式: AuthMethod.readerCard
2025-08-01 16:34:23.844305: Instance of 'SysConfigData'
2025-08-01 16:34:23.844305: Instance of 'SysConfigData'
2025-08-01 16:34:23.844305: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:23.844305: 当前状态: AuthFeedbackState.success
2025-08-01 16:34:23.844305: 返回成功组件 (详细信息)
2025-08-01 16:34:23.844305: === 构建成功组件（详细信息） ===
2025-08-01 16:34:23.844305: 用户: gd, ID: 111111
2025-08-01 16:34:23.844305: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:34:23.844305: 新状态: AuthFeedbackState.success
2025-08-01 16:34:23.845306: 用户信息: gd (111111)
2025-08-01 16:34:23.845306: 启动定时器，3秒后切换到通行模式
2025-08-01 16:34:23.845306: 63 CardType 值为空
2025-08-01 16:34:23.846299: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163423  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY7AZF01B
2025-08-01 16:34:23.849457: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:23.850290: 当前状态: AuthFeedbackState.success
2025-08-01 16:34:23.850290: 返回成功组件 (详细信息)
2025-08-01 16:34:23.850290: === 构建成功组件（详细信息） ===
2025-08-01 16:34:23.850290: 用户: gd, ID: 111111
2025-08-01 16:34:24.031213: Rsp : 64              00120250801    163553000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY7AZDBB7
2025-08-01 16:34:24.050163: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163553, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 7AZDBB7}
2025-08-01 16:34:24.050163: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163553, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 7AZDBB7}
2025-08-01 16:34:24.050163: 认证成功后清理完成，读卡器连接和监听器保持开启以供后续使用
2025-08-01 16:34:24.065122: 保存登录记录成功: 111111
2025-08-01 16:34:24.065122: 已通知认证成功状态变更
2025-08-01 16:34:24.079084: 保存认证记录到access_logs表成功
2025-08-01 16:34:24.079084: 认证成功: 读者姓名=gd, 读者编号=111111
2025-08-01 16:34:24.079084: 调用认证后处理服务: gd, 认证方式: 人脸认证
2025-08-01 16:34:24.079084: 执行认证后处理流程，共2个处理器
2025-08-01 16:34:24.079084: 执行处理器: DoorLockHandler
2025-08-01 16:34:24.079084: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸认证
2025-08-01 16:34:24.079084: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:34:24.080082: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:34:24.080082: 使用认证页面的门锁连接: COM1
2025-08-01 16:34:24.080082: 使用继电器通道: 1
2025-08-01 16:34:24.080082: 使用继电器通道: 1
2025-08-01 16:34:24.080082: 开锁前先关锁（不等待返回）...
2025-08-01 16:34:24.080082: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:34:24.080082: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:34:24.080082: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:34:24.080082: 等待100毫秒确保命令发送...
2025-08-01 16:34:24.180934: 正在打开绿灯（不等待返回）...
2025-08-01 16:34:24.180934: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:34:24.180934: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:34:24.180934: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:34:24.180934: 正在开门（不等待返回）...
2025-08-01 16:34:24.181931: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:34:24.181931: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:34:24.181931: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:34:24.181931: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:34:24.181931: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:34:24.181931: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:34:24.180934
2025-08-01 16:34:24.181931: 门锁操作完成，连接由认证页面管理
2025-08-01 16:34:24.182927: 自动开门成功 - 用户: gd
2025-08-01 16:34:24.182927: 执行处理器: WelcomeHandler
2025-08-01 16:34:24.182927: 显示欢迎信息给: gd
2025-08-01 16:34:24.182927: 欢迎用户: gd，认证方式: 人脸认证
2025-08-01 16:34:24.182927: 认证后处理流程执行完毕
2025-08-01 16:34:24.182927: 认证后处理流程执行完毕
2025-08-01 16:34:24.275476: iReadCardBas ret:4294967294
2025-08-01 16:34:24.276330: 无卡
2025-08-01 16:34:24.276330: 无卡
2025-08-01 16:34:24.277328: 检测到了卡 来暂停 ：1
2025-08-01 16:34:24.277328: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:24.277328: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:24.279320: dc_config_card:0
2025-08-01 16:34:24.293770: dc_card_n_hex:1,len:0
2025-08-01 16:34:24.293770: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:24.939928: iReadCardBas ret:4294967294
2025-08-01 16:34:24.940748: 无卡
2025-08-01 16:34:24.940748: 无卡
2025-08-01 16:34:24.941745: dc_config_card:0
2025-08-01 16:34:24.957587: dc_card_n_hex:1,len:0
2025-08-01 16:34:24.957587: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:25.181889: 异步执行关锁动作（不等待返回）...
2025-08-01 16:34:25.181889: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:34:25.181889: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:34:25.181889: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:34:25.234573: 正在关闭绿灯...
2025-08-01 16:34:25.234573: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:34:25.234573: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:34:25.234573: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:34:25.234573: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:34:25.234573
2025-08-01 16:34:25.516761: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:25.516)
2025-08-01 16:34:25.517599: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:25.516)
2025-08-01 16:34:25.517599: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:25.604454: iReadCardBas ret:4294967294
2025-08-01 16:34:25.604454: 无卡
2025-08-01 16:34:25.604454: 无卡
2025-08-01 16:34:25.613611: dc_config_card:0
2025-08-01 16:34:25.629806: dc_card_n_hex:1,len:0
2025-08-01 16:34:25.629806: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:26.275401: iReadCardBas ret:4294967294
2025-08-01 16:34:26.275401: 无卡
2025-08-01 16:34:26.276396: 无卡
2025-08-01 16:34:26.277428: dc_config_card:0
2025-08-01 16:34:26.293589: dc_card_n_hex:1,len:0
2025-08-01 16:34:26.293589: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:26.845122: 定时器触发，切换到通行模式
2025-08-01 16:34:26.845122: 认证反馈状态切换到通行模式，启动2秒隐藏定时器
2025-08-01 16:34:26.851133: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:26.851133: 当前状态: AuthFeedbackState.passMode
2025-08-01 16:34:26.851133: 返回通行模式组件
2025-08-01 16:34:26.851133: === 构建通行模式组件 ===
2025-08-01 16:34:26.851133: 用户: gd, ID: 111111
2025-08-01 16:34:26.851133: 认证方式: AuthMethod.readerCard
2025-08-01 16:34:26.940663: iReadCardBas ret:4294967294
2025-08-01 16:34:26.940663: 无卡
2025-08-01 16:34:26.940663: 无卡
2025-08-01 16:34:26.949879: dc_config_card:0
2025-08-01 16:34:26.966094: dc_card_n_hex:1,len:0
2025-08-01 16:34:26.966094: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:27.035620: iReadCardBas ret:0
2025-08-01 16:34:27.035620: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:27.035620: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:27.035620: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:27.036605: data:330424198710132426,coder:NoParseCoder
2025-08-01 16:34:27.036605: parseRet：{"barCode":"330424198710132426"},decoder:不解析
2025-08-01 16:34:27.036605: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:27.036605: 验证通用二维码: 330424198710132426
2025-08-01 16:34:27.037602: 检测到读卡器数据: 1条
2025-08-01 16:34:27.037602: 读卡器数据认证：
2025-08-01 16:34:27.037602:   设备类型: 13
2025-08-01 16:34:27.037602:   条码: 330424198710132426
2025-08-01 16:34:27.037602:   标签UID: 330424198710132426
2025-08-01 16:34:27.037602:   对应登录类型: AuthLoginType.socailSecurityCard
2025-08-01 16:34:27.037602:   根据读卡器类型13确定认证方式为: 社保卡
2025-08-01 16:34:27.037602:   开始调用认证API: 330424198710132426
2025-08-01 16:34:27.037602: 正在认证用户: 330424198710132426, 方式: 社保卡
2025-08-01 16:34:27.037602: 多认证管理器: 认证请求被拒绝，当前正在进行读者证认证
2025-08-01 16:34:27.038613: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:34:27.038613:   认证结果: AuthStatus.failureNoMatch, 方式: 社保卡
2025-08-01 16:34:27.038613: 认证失败，继续监听下一个用户
2025-08-01 16:34:27.038613: 恢复读卡器扫描状态...
2025-08-01 16:34:27.038613: 读卡器扫描状态已恢复
2025-08-01 16:34:27.039599: 收到非当前认证方式的结果: 社保卡，当前认证方式: 读者证，忽略此结果
2025-08-01 16:34:27.039599: 收到非当前认证方式的结果: 社保卡，当前认证方式: 读者证，忽略此结果
2025-08-01 16:34:27.039599: 收到非当前认证方式的结果: 社保卡，当前认证方式: 读者证，忽略此结果
2025-08-01 16:34:27.039599: 63 CardType 值为空
2025-08-01 16:34:27.039599: Req msgType：Sip2MsgType.patronInformation ,length:83， ret:  6300120250801    163427  Y       AOhlsp|AA330424198710132426|TY|BP1|BQ20|AY8AZEDA0
2025-08-01 16:34:27.039599: 检测到了卡 来暂停 ：1
2025-08-01 16:34:27.040593: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:27.040593: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:27.082483: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:34:27.082483: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:27.082483: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:27.082483: 开锁前关锁命令已发送
2025-08-01 16:34:27.107304: Rsp : 64YYYYYYYYYYYYYY00020250801    163556000000000000000000000000AOhlsp|AA330424198710132426|XO|AE330424198710132426|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY8AZBC78
2025-08-01 16:34:27.123654: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163556, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 8AZBC78}
2025-08-01 16:34:27.123654: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250801    163556, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 330424198710132426, PersonName: 330424198710132426, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 8AZBC78}
2025-08-01 16:34:27.139859: 保存认证失败记录到access_logs表成功: 俞佳, 330424198710132426
2025-08-01 16:34:27.183454: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:34:27.184382: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:27.184382: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:27.184382: 绿灯命令已发送
2025-08-01 16:34:27.184382: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:34:27.184382: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:27.184382: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:27.184382: 开锁命令已发送
2025-08-01 16:34:27.350011: dc_config_card:0
2025-08-01 16:34:27.365480: dc_card_n_hex:1,len:0
2025-08-01 16:34:27.365480: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:27.435581: iReadCardBas ret:0
2025-08-01 16:34:27.435581: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:27.435581: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:27.436579: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:27.436579: 检测到了卡 来暂停 ：1
2025-08-01 16:34:27.436579: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:27.436579: 验证通用二维码: 330424198710132426
2025-08-01 16:34:27.436579: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:27.437575: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:27.517019: ✅ [FaceCapturePolling] 健康检查：轮询服务运行正常
2025-08-01 16:34:27.517852: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:27.517)
2025-08-01 16:34:27.517852: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:27.517)
2025-08-01 16:34:27.517852: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:27.853591: dc_config_card:0
2025-08-01 16:34:27.869548: dc_card_n_hex:1,len:0
2025-08-01 16:34:27.869548: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:27.939391: iReadCardBas ret:0
2025-08-01 16:34:27.939391: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:27.939391: *********|俞佳|330424198710132426|201703-202703|
2025-08-01 16:34:27.940401: {name: 俞佳, sex: , nation: , birth: , address: , idnum: 330424198710132426, department: , startDay: 201703, expireDay: 202703, english_name: , cardNum: *********, cdInfo: null}
2025-08-01 16:34:27.940401: 检测到了卡 来暂停 ：1
2025-08-01 16:34:27.940401: 检测到二维码数据: 330424198710132426
2025-08-01 16:34:27.940401: 验证通用二维码: 330424198710132426
2025-08-01 16:34:27.940401: subThread :ReaderCommand.resumeInventory
2025-08-01 16:34:27.940401: commandRsp:ReaderCommand.resumeInventory
2025-08-01 16:34:28.182927: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:34:28.182927: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:28.183898: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:28.183898: 异步关锁命令已发送
2025-08-01 16:34:28.235038: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:34:28.235038: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:28.235038: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:28.235038: 绿灯关闭失败: 操作失败
2025-08-01 16:34:28.350033: dc_config_card:0
2025-08-01 16:34:28.365692: dc_card_n_hex:1,len:0
2025-08-01 16:34:28.365692: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:28.835415: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-01 16:34:28.835415: 多认证管理器: 还原到默认显示方式: 人脸识别
2025-08-01 16:34:28.835415: 检测到认证方式切换，重置认证反馈状态
2025-08-01 16:34:28.835415: 认证反馈状态已重置
2025-08-01 16:34:28.835415: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:34:28.850592: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:28.851579: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:28.851579: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:28.851579: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:28.851579: 构建读者证认证界面
2025-08-01 16:34:28.851579: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:28.851579: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:28.851579: === 构建多认证底部组件 ===
2025-08-01 16:34:28.851579: 显示底部组件: false
2025-08-01 16:34:28.851579: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:28.852577: 用户信息: null (null)
2025-08-01 16:34:28.852577: 底部组件被隐藏，返回空组件
2025-08-01 16:34:28.852577: Instance of 'SysConfigData'
2025-08-01 16:34:28.852577: Instance of 'SysConfigData'
2025-08-01 16:34:29.012144: iReadCardBas ret:4294967294
2025-08-01 16:34:29.012144: 无卡
2025-08-01 16:34:29.013063: 无卡
2025-08-01 16:34:29.021177: dc_config_card:0
2025-08-01 16:34:29.037349: dc_card_n_hex:1,len:0
2025-08-01 16:34:29.038267: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:29.517203: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:29.517)
2025-08-01 16:34:29.517203: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:29.517)
2025-08-01 16:34:29.517203: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:29.684161: iReadCardBas ret:4294967294
2025-08-01 16:34:29.684161: 无卡
2025-08-01 16:34:29.684161: 无卡
2025-08-01 16:34:29.685126: dc_config_card:0
2025-08-01 16:34:29.701097: dc_card_n_hex:1,len:0
2025-08-01 16:34:29.702081: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:30.347776: iReadCardBas ret:4294967294
2025-08-01 16:34:30.347776: 无卡
2025-08-01 16:34:30.347776: 无卡
2025-08-01 16:34:30.349770: dc_config_card:0
2025-08-01 16:34:30.365503: dc_card_n_hex:1,len:0
2025-08-01 16:34:30.365503: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:31.012506: iReadCardBas ret:4294967294
2025-08-01 16:34:31.012506: 无卡
2025-08-01 16:34:31.012506: 无卡
2025-08-01 16:34:31.021977: dc_config_card:0
2025-08-01 16:34:31.038049: dc_card_n_hex:1,len:0
2025-08-01 16:34:31.038049: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:31.517741: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:31.517)
2025-08-01 16:34:31.518577: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 16:34:31.517)
2025-08-01 16:34:31.518577: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-08-01 16:34:31.684227: iReadCardBas ret:4294967294
2025-08-01 16:34:31.685225: 无卡
2025-08-01 16:34:31.685225: 无卡
2025-08-01 16:34:31.693203: dc_config_card:0
2025-08-01 16:34:31.709309: dc_card_n_hex:1,len:0
2025-08-01 16:34:31.710310: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:31.834758: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-01 16:34:31.834758: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:34:31.834758: 多认证管理器状态变更: listening
2025-08-01 16:34:31.834758: 🔍 检查人脸识别服务健康状态...
2025-08-01 16:34:31.834758: 🔄 重置人脸识别服务认证状态...
2025-08-01 16:34:31.851455: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:31.852433: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:31.852433: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:31.852433: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:31.852433: 构建读者证认证界面
2025-08-01 16:34:31.852433: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:31.852433: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:31.852433: === 构建多认证底部组件 ===
2025-08-01 16:34:31.852433: 显示底部组件: false
2025-08-01 16:34:31.853426: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:31.853426: 用户信息: null (null)
2025-08-01 16:34:31.853426: 底部组件被隐藏，返回空组件
2025-08-01 16:34:31.853426: Instance of 'SysConfigData'
2025-08-01 16:34:31.853426: Instance of 'SysConfigData'
2025-08-01 16:34:32.357901: iReadCardBas ret:4294967294
2025-08-01 16:34:32.358898: 无卡
2025-08-01 16:34:32.358898: 无卡
2025-08-01 16:34:32.365413: dc_config_card:0
2025-08-01 16:34:32.382091: dc_card_n_hex:1,len:0
2025-08-01 16:34:32.382091: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:32.677309: AuthView: 检测到 1 个人脸
2025-08-01 16:34:32.758218: AuthView: 检测到 1 个人脸
2025-08-01 16:34:32.837115: AuthView: 检测到 1 个人脸
2025-08-01 16:34:32.917345: AuthView: 检测到 1 个人脸
2025-08-01 16:34:32.997896: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.028311: iReadCardBas ret:4294967294
2025-08-01 16:34:33.029116: 无卡
2025-08-01 16:34:33.029116: 无卡
2025-08-01 16:34:33.038092: dc_config_card:0
2025-08-01 16:34:33.053054: dc_card_n_hex:1,len:0
2025-08-01 16:34:33.054050: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:33.077440: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.157968: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.237373: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.317297: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.397937: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.476980: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.518407: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:33.518)
2025-08-01 16:34:33.519310: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:34:33.518)
2025-08-01 16:34:33.519310: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:34:33.519310: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:34:33.519310: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:34:33.519310: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 149.043701171875, 数据大小: 0 bytes
2025-08-01 16:34:33.519310: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:34:33.519310: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:34:33.519310: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3102 bytes, 实际置信度: 149.043701171875
2025-08-01 16:34:33.519310: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:34:33.519310: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:34:33.520308: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:34:33.520308: ⏹️ 停止人脸捕获轮询
2025-08-01 16:34:33.520308: 暂停人脸轮询，开始认证流程
2025-08-01 16:34:33.646968: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:34:33.646968: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "36a3249cee0b591328cd66187b757fa1",
		"log_id" : "1754037273647",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 71.11,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:34:33.646968: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:34:33.646968: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=71.11
2025-08-01 16:34:33.647973: 人脸识别成功，得分: 71.11，用户ID: 111111
2025-08-01 16:34:33.647973: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:34:33.647973: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:33.648962: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:33.648962: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:34:33.648962: 🔄 开始重启人脸识别服务...
2025-08-01 16:34:33.648962: 轮询未在运行
2025-08-01 16:34:33.648962: 轮询未在运行
2025-08-01 16:34:33.648962: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:34:33.648962: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:34:33.648962: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:34:33.648962: 多认证管理器: 人脸识别获得认证请求锁
2025-08-01 16:34:33.649960: 63 CardType 值为空
2025-08-01 16:34:33.649960: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250801    163433  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY9AZF018
2025-08-01 16:34:33.649960: 多认证管理器: 收到认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:34:33.649960: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:34:33.649960: 多认证管理器状态变更: authenticating
2025-08-01 16:34:33.649960: 多认证管理器: 认证失败(无匹配): 人脸识别，显示失败信息
2025-08-01 16:34:33.649960: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:33.650957: 收到多认证结果: 人脸识别 - failureNoMatch
2025-08-01 16:34:33.650957: === 更新认证反馈状态 ===
2025-08-01 16:34:33.650957: 结果状态: AuthStatus.failureNoMatch
2025-08-01 16:34:33.650957: 用户姓名: null
2025-08-01 16:34:33.650957: 用户ID: null
2025-08-01 16:34:33.650957: 设置反馈状态为failure，认证方式: 人脸识别
2025-08-01 16:34:33.650957: 具体错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:33.650957: 显示底部组件: true
2025-08-01 16:34:33.652952: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.667912: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:33.668909: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:33.668909: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:33.668909: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:33.668909: 构建读者证认证界面
2025-08-01 16:34:33.668909: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:33.668909: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:33.668909: === 构建多认证底部组件 ===
2025-08-01 16:34:33.668909: 显示底部组件: true
2025-08-01 16:34:33.668909: 反馈状态: AuthFeedbackState.failure
2025-08-01 16:34:33.668909: 用户信息: null (null)
2025-08-01 16:34:33.668909: 创建AuthFeedbackWidget
2025-08-01 16:34:33.668909: 实际认证方式: AuthMethod.face
2025-08-01 16:34:33.669907: Instance of 'SysConfigData'
2025-08-01 16:34:33.669907: Instance of 'SysConfigData'
2025-08-01 16:34:33.669907: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:33.669907: 当前状态: AuthFeedbackState.failure
2025-08-01 16:34:33.669907: 返回失败组件
2025-08-01 16:34:33.669907: === 构建失败组件 ===
2025-08-01 16:34:33.669907: 认证方式: AuthMethod.face
2025-08-01 16:34:33.669907: 错误信息: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:33.700017: iReadCardBas ret:4294967294
2025-08-01 16:34:33.700017: 无卡
2025-08-01 16:34:33.700017: 无卡
2025-08-01 16:34:33.701999: dc_config_card:0
2025-08-01 16:34:33.716981: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.717955: dc_card_n_hex:1,len:0
2025-08-01 16:34:33.717955: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:33.797968: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.876740: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.950101: Rsp : 64              00120250801    163603000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|**********|JF0.0|BE|AF|AG|AY9AZDBB9
2025-08-01 16:34:33.957067: AuthView: 检测到 1 个人脸
2025-08-01 16:34:33.966044: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163603, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 9AZDBB9}
2025-08-01 16:34:33.966044: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250801    163603, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 9AZDBB9}
2025-08-01 16:34:33.966044: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-08-01 16:34:33.966044: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-08-01 16:34:33.966044: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-08-01 16:34:33.966044: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-01 16:34:33.967041: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-08-01 16:34:33.967041: 多认证管理器状态变更: completed
2025-08-01 16:34:33.967041: 多认证管理器: 调用认证后处理服务 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:34:33.967041: 执行认证后处理流程，共2个处理器
2025-08-01 16:34:33.967041: 执行处理器: DoorLockHandler
2025-08-01 16:34:33.967041: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸识别
2025-08-01 16:34:33.967041: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-01 16:34:33.968038: 收到多认证结果: 人脸识别 - success
2025-08-01 16:34:33.968038: 认证成功，更新主显示方式为: 人脸识别
2025-08-01 16:34:33.968038: === 更新认证反馈状态 ===
2025-08-01 16:34:33.968038: 结果状态: AuthStatus.success
2025-08-01 16:34:33.968038: 用户姓名: gd
2025-08-01 16:34:33.968038: 用户ID: 111111
2025-08-01 16:34:33.968038: 设置反馈状态为success，显示底部组件: true
2025-08-01 16:34:33.968038: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:33.968038: 用户信息: gd (111111)
2025-08-01 16:34:33.968038: 找到启用的门锁配置: 主门锁设备
2025-08-01 16:34:33.968038: 使用门锁配置: 主门锁设备 (COM1)
2025-08-01 16:34:33.969035: 使用认证页面的门锁连接: COM1
2025-08-01 16:34:33.969035: 使用继电器通道: 1
2025-08-01 16:34:33.969035: 使用继电器通道: 1
2025-08-01 16:34:33.969035: 开锁前先关锁（不等待返回）...
2025-08-01 16:34:33.969035: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:34:33.969035: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:34:33.969035: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:34:33.969035: 等待100毫秒确保命令发送...
2025-08-01 16:34:33.971030: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:33.972029: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:33.972029: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:33.972029: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:33.972029: 构建读者证认证界面
2025-08-01 16:34:33.972029: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:33.972029: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:33.972029: === 构建多认证底部组件 ===
2025-08-01 16:34:33.973025: 显示底部组件: true
2025-08-01 16:34:33.973025: 反馈状态: AuthFeedbackState.success
2025-08-01 16:34:33.973025: 用户信息: gd (111111)
2025-08-01 16:34:33.973025: 创建AuthFeedbackWidget
2025-08-01 16:34:33.973025: 实际认证方式: AuthMethod.face
2025-08-01 16:34:33.973025: Instance of 'SysConfigData'
2025-08-01 16:34:33.973025: Instance of 'SysConfigData'
2025-08-01 16:34:33.973025: === AuthFeedbackWidget 状态变化 ===
2025-08-01 16:34:33.973025: 新状态: AuthFeedbackState.success
2025-08-01 16:34:33.974022: 用户信息: gd (111111)
2025-08-01 16:34:33.974022: 启动定时器，3秒后切换到通行模式
2025-08-01 16:34:33.974022: === AuthFeedbackWidget 构建内容 ===
2025-08-01 16:34:33.974022: 当前状态: AuthFeedbackState.success
2025-08-01 16:34:33.974022: 返回成功组件 (详细信息)
2025-08-01 16:34:33.974022: === 构建成功组件（详细信息） ===
2025-08-01 16:34:33.974022: 用户: gd, ID: 111111
2025-08-01 16:34:34.037578: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.069064: 正在打开绿灯（不等待返回）...
2025-08-01 16:34:34.069064: 准备发送命令 [GLED打开], 命令键: 31_37
2025-08-01 16:34:34.069064: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-08-01 16:34:34.069064: 等待响应 [GLED打开], 超时时间: 3000ms
2025-08-01 16:34:34.070003: 正在开门（不等待返回）...
2025-08-01 16:34:34.070003: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-08-01 16:34:34.070003: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-08-01 16:34:34.070003: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-08-01 16:34:34.070003: 开锁命令已发送，将在 1000 毫秒后异步关闭
2025-08-01 16:34:34.070003: 开始异步关门倒计时，等待 1000 毫秒...
2025-08-01 16:34:34.070509: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 成功, 时间: 2025-08-01T16:34:34.069064
2025-08-01 16:34:34.070509: 门锁操作完成，连接由认证页面管理
2025-08-01 16:34:34.070509: 自动开门成功 - 用户: gd
2025-08-01 16:34:34.070509: 执行处理器: WelcomeHandler
2025-08-01 16:34:34.070509: 显示欢迎信息给: gd
2025-08-01 16:34:34.070509: 欢迎用户: gd，认证方式: 人脸识别
2025-08-01 16:34:34.070509: 认证后处理流程执行完毕
2025-08-01 16:34:34.071510: 多认证管理器: 认证后处理流程执行完毕
2025-08-01 16:34:34.119306: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.150236: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:34:34.150236: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:34:34.150236: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:34:34.151220: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:34:34.151220: ✅ 人脸识别服务重启完成
2025-08-01 16:34:34.197724: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.278198: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.358358: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.363951: iReadCardBas ret:4294967294
2025-08-01 16:34:34.363951: 无卡
2025-08-01 16:34:34.363951: 无卡
2025-08-01 16:34:34.365951: dc_config_card:0
2025-08-01 16:34:34.381409: dc_card_n_hex:1,len:0
2025-08-01 16:34:34.382236: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:34.438223: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.519131: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.598205: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.677954: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.758694: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.837922: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.918982: AuthView: 检测到 1 个人脸
2025-08-01 16:34:34.998629: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.028508: iReadCardBas ret:4294967294
2025-08-01 16:34:35.028508: 无卡
2025-08-01 16:34:35.029505: 无卡
2025-08-01 16:34:35.037482: dc_config_card:0
2025-08-01 16:34:35.053791: dc_card_n_hex:1,len:0
2025-08-01 16:34:35.053791: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:35.071438: 异步执行关锁动作（不等待返回）...
2025-08-01 16:34:35.071438: 准备发送命令 [继电器1关闭], 命令键: 32_31
2025-08-01 16:34:35.071438: 发送命令 [继电器1关闭]: A0 A3 00 02 32 31 A2 0A
2025-08-01 16:34:35.071438: 等待响应 [继电器1关闭], 超时时间: 3000ms
2025-08-01 16:34:35.077408: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.122643: 正在关闭绿灯...
2025-08-01 16:34:35.122643: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-08-01 16:34:35.123481: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-08-01 16:34:35.123481: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-08-01 16:34:35.123481: 自动开门操作 - 用户: gd (111111), 设备: 主门锁设备, 通道: 1, 结果: 失败, 时间: 2025-08-01T16:34:35.122643
2025-08-01 16:34:35.156627: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.238605: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.318944: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.397510: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.478530: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.558582: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.637731: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.701199: iReadCardBas ret:4294967294
2025-08-01 16:34:35.701199: 无卡
2025-08-01 16:34:35.701199: 无卡
2025-08-01 16:34:35.709733: dc_config_card:0
2025-08-01 16:34:35.719527: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.725512: dc_card_n_hex:1,len:0
2025-08-01 16:34:35.725512: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:35.798517: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.878002: AuthView: 检测到 1 个人脸
2025-08-01 16:34:35.958220: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.036726: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.151122: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:36.151)
2025-08-01 16:34:36.152087: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:34:36.151)
2025-08-01 16:34:36.152087: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:34:36.152087: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:34:36.152087: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:34:36.152087: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 129.17156982421875, 数据大小: 0 bytes
2025-08-01 16:34:36.152087: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:34:36.152087: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:34:36.153084: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 5334 bytes, 实际置信度: 129.17156982421875
2025-08-01 16:34:36.153084: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:34:36.153084: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:34:36.153084: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:34:36.153084: ⏹️ 停止人脸捕获轮询
2025-08-01 16:34:36.153084: 暂停人脸轮询，开始认证流程
2025-08-01 16:34:36.281629: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:34:36.281629: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "40c2cc53873763b272abf6d86f9f122a",
		"log_id" : "1754037276281",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 91.89,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:34:36.281629: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:34:36.281629: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=91.89
2025-08-01 16:34:36.282625: 人脸识别成功，得分: 91.89，用户ID: 111111
2025-08-01 16:34:36.282625: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:34:36.282625: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:36.282625: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:36.282625: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:34:36.282625: 🔄 开始重启人脸识别服务...
2025-08-01 16:34:36.282625: 轮询未在运行
2025-08-01 16:34:36.282625: 轮询未在运行
2025-08-01 16:34:36.282625: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:34:36.283623: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:34:36.283623: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:34:36.283623: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:34:36.283623: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:34:36.283623: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:34:36.283623: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:34:36.285618: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.357569: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.371545: iReadCardBas ret:4294967294
2025-08-01 16:34:36.371545: 无卡
2025-08-01 16:34:36.371545: 无卡
2025-08-01 16:34:36.373551: dc_config_card:0
2025-08-01 16:34:36.389937: dc_card_n_hex:1,len:0
2025-08-01 16:34:36.389937: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:36.437277: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.518771: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.598064: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.652852: 认证完成，还原到默认显示方式
2025-08-01 16:34:36.668810: 构建叠加认证区域，当前方式: AuthMethod.face
2025-08-01 16:34:36.668810: 构建认证区域，认证方式: AuthMethod.face
2025-08-01 16:34:36.669807: 构建认证区域，认证方式: AuthMethod.wechatScanQRCode
2025-08-01 16:34:36.669807: 构建认证区域，认证方式: AuthMethod.readerCard
2025-08-01 16:34:36.669807: 构建读者证认证界面
2025-08-01 16:34:36.669807: 构建认证区域，认证方式: AuthMethod.socialSecurityCard
2025-08-01 16:34:36.669807: 构建认证区域，认证方式: AuthMethod.eletricSocialSecurityCard
2025-08-01 16:34:36.669807: === 构建多认证底部组件 ===
2025-08-01 16:34:36.669807: 显示底部组件: false
2025-08-01 16:34:36.669807: 反馈状态: AuthFeedbackState.idle
2025-08-01 16:34:36.670805: 用户信息: null (null)
2025-08-01 16:34:36.670805: 底部组件被隐藏，返回空组件
2025-08-01 16:34:36.670805: Instance of 'SysConfigData'
2025-08-01 16:34:36.670805: Instance of 'SysConfigData'
2025-08-01 16:34:36.676789: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.758727: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.783707: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:34:36.783707: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:34:36.783707: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:34:36.783707: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:34:36.783707: ✅ 人脸识别服务重启完成
2025-08-01 16:34:36.836789: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.910787: 发送心跳
2025-08-01 16:34:36.910787: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY1AZFCA0
2025-08-01 16:34:36.918795: AuthView: 检测到 1 个人脸
2025-08-01 16:34:36.929739: Rsp : 98YYYNNN00500320250801    1636062.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY1AZD523
2025-08-01 16:34:36.969168: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 3
2025-08-01 16:34:36.969975: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:36.969975: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:36.969975: 开锁前关锁命令已发送
2025-08-01 16:34:36.998298: AuthView: 检测到 1 个人脸
2025-08-01 16:34:37.035683: iReadCardBas ret:4294967294
2025-08-01 16:34:37.036680: 无卡
2025-08-01 16:34:37.036680: 无卡
2025-08-01 16:34:37.037830: dc_config_card:0
2025-08-01 16:34:37.053870: dc_card_n_hex:1,len:0
2025-08-01 16:34:37.053870: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:37.070997: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 2
2025-08-01 16:34:37.070997: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:37.071824: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-08-01 16:34:37.071824: 绿灯命令已发送
2025-08-01 16:34:37.071824: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 1
2025-08-01 16:34:37.071824: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:37.071824: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-08-01 16:34:37.071824: 开锁命令已发送
2025-08-01 16:34:37.077656: AuthView: 检测到 1 个人脸
2025-08-01 16:34:37.156739: AuthView: 检测到 1 个人脸
2025-08-01 16:34:37.477580: AuthView: 检测到 1 个人脸
2025-08-01 16:34:37.558559: AuthView: 检测到 1 个人脸
2025-08-01 16:34:37.701069: iReadCardBas ret:4294967294
2025-08-01 16:34:37.701069: 无卡
2025-08-01 16:34:37.701069: 无卡
2025-08-01 16:34:37.709036: dc_config_card:0
2025-08-01 16:34:37.725991: dc_card_n_hex:1,len:0
2025-08-01 16:34:37.725991: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:38.073350: 命令响应超时 [继电器1关闭], 命令键: 32_31, 当前等待命令数: 1
2025-08-01 16:34:38.073350: 发送命令失败 [继电器1关闭]: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:38.074197: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1关闭
2025-08-01 16:34:38.074197: 异步关锁命令已发送
2025-08-01 16:34:38.124248: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-08-01 16:34:38.124248: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:38.124248: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-08-01 16:34:38.124248: 绿灯关闭失败: 操作失败
2025-08-01 16:34:38.372127: iReadCardBas ret:4294967294
2025-08-01 16:34:38.372127: 无卡
2025-08-01 16:34:38.372127: 无卡
2025-08-01 16:34:38.373123: dc_config_card:0
2025-08-01 16:34:38.389741: dc_card_n_hex:1,len:0
2025-08-01 16:34:38.389741: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:38.784701: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 16:34:38.784)
2025-08-01 16:34:38.785670: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 16:34:38.784)
2025-08-01 16:34:38.785670: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-08-01 16:34:38.785670: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-08-01 16:34:38.785670: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-08-01 16:34:38.785670: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 120.89237213134766, 数据大小: 0 bytes
2025-08-01 16:34:38.785670: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-08-01 16:34:38.785670: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-08-01 16:34:38.785670: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 4418 bytes, 实际置信度: 120.89237213134766
2025-08-01 16:34:38.785670: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-08-01 16:34:38.785670: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-08-01 16:34:38.786668: 🎯 检测到新的人脸数据，开始获取...
2025-08-01 16:34:38.786668: ⏹️ 停止人脸捕获轮询
2025-08-01 16:34:38.786668: 暂停人脸轮询，开始认证流程
2025-08-01 16:34:38.931093: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-08-01 16:34:38.932064: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "567e5358ff4a2a0d5ae09b0f7e0e9852",
		"log_id" : "1754037278931",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 87.58,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-08-01 16:34:38.932064: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-08-01 16:34:38.932064: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=87.58
2025-08-01 16:34:38.932064: 人脸识别成功，得分: 87.58，用户ID: 111111
2025-08-01 16:34:38.933057: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-08-01 16:34:38.933057: 人脸识别失败: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:38.933057: ⚠️ 检测到百度SDK错误，准备重启人脸识别服务: Unsupported operation: Cannot remove from a fixed-length list
2025-08-01 16:34:38.933057: 🔄 执行人脸识别服务重启（任何错误都重启策略）...
2025-08-01 16:34:38.933057: 🔄 开始重启人脸识别服务...
2025-08-01 16:34:38.933057: 轮询未在运行
2025-08-01 16:34:38.933057: 轮询未在运行
2025-08-01 16:34:38.934056: 🔄 人脸捕获轮询状态已强制重置
2025-08-01 16:34:38.934056: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-08-01 16:34:38.934056: 🔍 开始请求读者信息: 用户ID=111111
2025-08-01 16:34:38.934056: 多认证管理器: 认证请求被拒绝，人脸识别认证正在进行中，拒绝重复请求
2025-08-01 16:34:38.934056: 无法获取认证请求锁，当前有其他认证正在进行
2025-08-01 16:34:38.934056: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:34:38.934056: 收到认证结果但当前状态已完成，忽略失败结果: 人脸识别 - failureNoMatch
2025-08-01 16:34:38.968168: 多认证管理器: 认证请求锁已释放（之前为人脸识别）
2025-08-01 16:34:39.036714: iReadCardBas ret:4294967294
2025-08-01 16:34:39.036714: 无卡
2025-08-01 16:34:39.036714: 无卡
2025-08-01 16:34:39.045790: dc_config_card:0
2025-08-01 16:34:39.061675: dc_card_n_hex:1,len:0
2025-08-01 16:34:39.061675: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:39.434318: 🔄 重新初始化人脸捕获轮询服务...
2025-08-01 16:34:39.434318: ✅ 人脸捕获轮询服务初始化成功
2025-08-01 16:34:39.434318: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-08-01 16:34:39.434318: 🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次
2025-08-01 16:34:39.434318: ✅ 人脸识别服务重启完成
2025-08-01 16:34:39.707760: iReadCardBas ret:4294967294
2025-08-01 16:34:39.708768: 无卡
2025-08-01 16:34:39.708768: 无卡
2025-08-01 16:34:39.709755: dc_config_card:0
2025-08-01 16:34:39.725903: dc_card_n_hex:1,len:0
2025-08-01 16:34:39.725903: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:40.371202: iReadCardBas ret:4294967294
2025-08-01 16:34:40.372200: 无卡
2025-08-01 16:34:40.372200: 无卡
2025-08-01 16:34:40.373207: dc_config_card:0
2025-08-01 16:34:40.389300: dc_card_n_hex:1,len:0
2025-08-01 16:34:40.389300: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:40.437409: CameraPreviewWidget: 开始清理资源...
2025-08-01 16:34:40.437409: CameraPreviewWidget: 停止帧捕获定时器
2025-08-01 16:34:40.437409: CameraPreviewWidget: 停止人脸识别服务...
2025-08-01 16:34:40.437409: 🛑 停止人脸识别监听...
2025-08-01 16:34:40.437409: ⏹️ 停止人脸捕获轮询
2025-08-01 16:34:40.437409: ✅ 人脸识别监听已停止
2025-08-01 16:34:40.437409: CameraPreviewWidget: 资源清理完成
2025-08-01 16:34:40.437409: 认证监听未在运行中
2025-08-01 16:34:40.437409: 开始清理门锁连接...
2025-08-01 16:34:40.437409: 门锁继电器已断开连接
2025-08-01 16:34:40.438408: 门锁连接已断开: COM1
2025-08-01 16:34:40.438408: 门锁连接清理完成
2025-08-01 16:34:40.438408: 认证成功后清理完成，读卡器连接和监听器保持开启以供后续使用
2025-08-01 16:34:40.438408: CameraPreviewWidget: 人脸识别服务已清理
2025-08-01 16:34:40.438408: 串口数据监听结束
2025-08-01 16:34:41.035459: iReadCardBas ret:4294967294
2025-08-01 16:34:41.036457: 无卡
2025-08-01 16:34:41.036457: 无卡
2025-08-01 16:34:41.045433: dc_config_card:0
2025-08-01 16:34:41.061389: dc_card_n_hex:1,len:0
2025-08-01 16:34:41.061389: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:41.707314: iReadCardBas ret:4294967294
2025-08-01 16:34:41.708313: 无卡
2025-08-01 16:34:41.708313: 无卡
2025-08-01 16:34:41.709310: dc_config_card:0
2025-08-01 16:34:41.725266: dc_card_n_hex:1,len:0
2025-08-01 16:34:41.726264: iReadCardBas: start ,isOpenComPort:false
2025-08-01 16:34:41.966913: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-01 16:34:41.966913: 多认证管理器状态变更: listening
2025-08-01 16:34:41.966913: 🔍 检查人脸识别服务健康状态...
2025-08-01 16:34:41.966913: 🔄 重置人脸识别服务认证状态...
2025-08-01 16:34:42.371880: iReadCardBas ret:4294967294
2025-08-01 16:34:42.371880: 无卡
2025-08-01 16:34:42.371880: 无卡
2025-08-01 16:34:42.373874: dc_config_card:0
2025-08-01 16:34:42.389883: dc_card_n_hex:1,len:0
2025-08-01 16:34:42.389883: iReadCardBas: start ,isOpenComPort:false
