import 'package:flutter/material.dart';

class SysTimeoutButton extends StatelessWidget {
  final VoidCallback backAction;

  const SysTimeoutButton({
    Key? key,
    required this.backAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: backAction,
      backgroundColor: Colors.white.withOpacity(0.2),
      child: const Icon(
        Icons.arrow_back,
        color: Colors.white,
      ),
    );
  }
} 