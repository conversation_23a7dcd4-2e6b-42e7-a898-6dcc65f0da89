import 'dart:async';
import 'dart:ffi';
import 'dart:typed_data';
import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';

class FaceCapturePolling {
  static const String _dllName = 'libface_detector.dll';
  late final DynamicLibrary _lib;
  
  // FFI函数指针
  late final bool Function() _hasNewFaceCapture;
  late final bool Function(Pointer<Float>, Pointer<Int32>) _getFaceCaptureInfo;
  late final bool Function(Pointer<Pointer<Uint8>>, Pointer<Int32>, Pointer<Float>) _getLatestFaceCapture;
  late final void Function() _clearFaceCapture;
  late final void Function(Pointer<Uint8>) _freeImage;
  
  Timer? _pollingTimer;
  Timer? _healthCheckTimer; // 新增：健康检查定时器
  bool _isPolling = false;
  DateTime? _lastSuccessfulCheck; // 新增：记录最后一次成功检查的时间

  // 回调函数
  Function(Uint8List imageData, double confidence)? onFaceCaptured;
  Function()? onNoFaceDetected; // 新增：没有人脸时的回调
  Function(String error)? onError;
  

  
  FaceCapturePolling() {
    _initializeDLL();
  }
  
  void _initializeDLL() {
    try {
      _lib = DynamicLibrary.open(_dllName);
      
      // 绑定函数
      _hasNewFaceCapture = _lib
          .lookup<NativeFunction<Bool Function()>>('has_new_face_capture')
          .asFunction();
          
      _getFaceCaptureInfo = _lib
          .lookup<NativeFunction<Bool Function(Pointer<Float>, Pointer<Int32>)>>('get_face_capture_info')
          .asFunction();
          
      _getLatestFaceCapture = _lib
          .lookup<NativeFunction<Bool Function(Pointer<Pointer<Uint8>>, Pointer<Int32>, Pointer<Float>)>>('get_latest_face_capture')
          .asFunction();
          
      _clearFaceCapture = _lib
          .lookup<NativeFunction<Void Function()>>('clear_face_capture')
          .asFunction();
          

          
      _freeImage = _lib
          .lookup<NativeFunction<Void Function(Pointer<Uint8>)>>('free_image')
          .asFunction();
          
      print('✅ 人脸捕获轮询服务初始化成功');
    } catch (e) {
      print('❌ 人脸捕获轮询服务初始化失败: $e');
      rethrow;
    }
  }
  
  // 开始轮询
  void startPolling({Duration interval = const Duration(seconds: 1)}) {
    if (_isPolling) {
      print('轮询已在运行中');
      return;
    }
    
    print('🔄 开始人脸捕获轮询，间隔: ${interval.inMilliseconds}ms');
    _isPolling = true;
    _lastSuccessfulCheck = DateTime.now();

    _pollingTimer = Timer.periodic(interval, (timer) {
      _checkForNewFace();
    });

    // 启动健康检查定时器，每30秒检查一次
    _startHealthCheck();
  }
  
  // 停止轮询
  void stopPolling() {
    if (!_isPolling) {
      print('轮询未在运行');
      return;
    }
    
    print('⏹️ 停止人脸捕获轮询');
    _isPolling = false;
    _pollingTimer?.cancel();
    _pollingTimer = null;

    // 停止健康检查
    _stopHealthCheck();
  }
  
  // 检查新人脸数据
  void _checkForNewFace() {
    try {
      final checkTime = DateTime.now().toString().substring(11, 23);
      print('🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: $checkTime)');

      // 更新最后成功检查时间
      _lastSuccessfulCheck = DateTime.now();

      // 1. 检查是否有新数据
      bool hasNew = _hasNewFaceCapture();
      print('🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: ${hasNew ? "有新数据" : "无新数据"} (时间: $checkTime)');

      if (!hasNew) {
        // 没有新人脸时，通知UI清除显示
        print('📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调');
        if (onNoFaceDetected != null) {
          onNoFaceDetected!();
        }
        return;
      }

      print('✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...');
      
      // 2. 获取数据信息
      final confidencePtr = malloc<Float>();
      final dataSizePtr = malloc<Int32>();
      
      try {
        print('📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...');
        bool infoSuccess = _getFaceCaptureInfo(confidencePtr, dataSizePtr);
        print('📞 [FaceCapturePolling] get_face_capture_info返回结果: ${infoSuccess ? "成功" : "失败"}');

        if (!infoSuccess) {
          print('❌ [FaceCapturePolling] 获取人脸信息失败 - 可能是时序问题，数据已被清理');
          print('🔍 [FaceCapturePolling] 建议：检查C++层数据缓存机制');
          return;
        }

        double confidence = confidencePtr.value;
        int dataSize = dataSizePtr.value;
        print('📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: $confidence, 数据大小: $dataSize bytes');


        // 3. 获取实际图像数据
        print('📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...');
        final imageDataPtr = malloc<Pointer<Uint8>>();
        final imageSizePtr = malloc<Int32>();
        final confPtr = malloc<Float>();
        
        try {
          bool dataSuccess = _getLatestFaceCapture(imageDataPtr, imageSizePtr, confPtr);
          print('📞 [FaceCapturePolling] get_latest_face_capture返回结果: ${dataSuccess ? "成功" : "失败"}');

          if (!dataSuccess) {
            print('❌ [FaceCapturePolling] 获取图像数据失败');
            return;
          }

          int actualSize = imageSizePtr.value;
          double actualConfidence = confPtr.value;
          print('📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: $actualSize bytes, 实际置信度: $actualConfidence');

          // 复制图像数据到Dart
          Pointer<Uint8> imageBuffer = imageDataPtr.value;
          Uint8List imageData = Uint8List.fromList(
            imageBuffer.asTypedList(actualSize)
          );
          print('✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调');

          // 释放C++分配的内存
          _freeImage(imageBuffer);

          // 注意：C++端在get_latest_face_capture()中已自动清理缓存，无需手动清理

          // 4. 触发回调 - 增加异常捕获
          if (onFaceCaptured != null) {
            print('🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度');
            try {
              onFaceCaptured!(imageData, actualConfidence);
              print('✅ [FaceCapturePolling] onFaceCaptured回调调用完成');
            } catch (callbackError) {
              print('❌ [FaceCapturePolling] onFaceCaptured回调执行出错: $callbackError');
              // 回调错误也需要触发重启机制
              if (onError != null) {
                onError!('回调执行出错: $callbackError');
              }
              // 强制重启轮询服务
              print('🔄 [FaceCapturePolling] 回调错误，强制重启轮询服务...');
              _attemptRestart();
              return; // 立即返回，避免继续执行
            }
          } else {
            print('⚠️ [FaceCapturePolling] onFaceCaptured回调为null，无法传递人脸数据');
          }
          
        } finally {
          malloc.free(imageDataPtr);
          malloc.free(imageSizePtr);
          malloc.free(confPtr);
        }
        
      } finally {
        malloc.free(confidencePtr);
        malloc.free(dataSizePtr);
      }
      
    } catch (e) {
      print('❌ [FaceCapturePolling] 轮询检查出错: $e');

      // 🚨 重要修改：任何错误都触发重启，不再区分错误类型
      print('⚠️ [FaceCapturePolling] 检测到错误，准备重启轮询服务（确保服务稳定性）');

      if (onError != null) {
        onError!('轮询检查出错: $e');
      }

      // 🔄 强制重启轮询服务 - 确保任何错误都能自动恢复
      print('🔄 [FaceCapturePolling] 自动重启轮询服务...');
      _attemptRestart();
    }
  }
  

  
  // 手动检查一次
  void checkOnce() {
    if (!_isPolling) {
      _checkForNewFace();
    }
  }
  
  // 获取轮询状态
  bool get isPolling => _isPolling;

  // 获取当前状态
  Map<String, dynamic> getStatus() {
    return {
      'isPolling': _isPolling,
      'pollingInterval': _pollingTimer?.isActive == true ? '1秒' : 'stopped',
    };
  }
  
  // 强制重置底层状态
  void forceReset() {
    try {
      // 先停止轮询
      stopPolling();

      // 多次调用底层清理函数，确保C++状态被完全重置
      for (int i = 0; i < 3; i++) {
        _clearFaceCapture();
      }

      // 短暂延迟，让底层有时间完成清理
      Future.delayed(const Duration(milliseconds: 50), () {
        // 再次清理，确保状态干净
        _clearFaceCapture();
      });

      print('🔄 人脸捕获轮询状态已强制重置');
    } catch (e) {
      print('❌ 强制重置人脸捕获状态时出错: $e');
    }
  }

  /// 尝试重启轮询服务 - 增强版，确保重启成功
  void _attemptRestart() {
    try {
      print('🔄 [FaceCapturePolling] 开始重启轮询服务...');

      // 1. 停止当前轮询
      final wasPolling = _isPolling;
      stopPolling();

      // 2. 强制重置状态
      forceReset();

      // 3. 短暂延迟后重新启动，增加重试机制
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (wasPolling) {
          _restartWithRetry(maxRetries: 3);
        }
      });

    } catch (e) {
      print('❌ [FaceCapturePolling] 重启轮询服务时出错: $e');
      if (onError != null) {
        onError!('重启轮询服务失败: $e');
      }
      // 即使重启失败，也要尝试简单重启
      _restartWithRetry(maxRetries: 1);
    }
  }

  /// 带重试机制的重启方法
  void _restartWithRetry({int maxRetries = 3, int currentRetry = 0}) {
    try {
      startPolling(interval: const Duration(seconds: 2));
      print('✅ [FaceCapturePolling] 轮询服务重启成功 (尝试 ${currentRetry + 1}/$maxRetries)');
    } catch (e) {
      print('❌ [FaceCapturePolling] 轮询服务重启失败 (尝试 ${currentRetry + 1}/$maxRetries): $e');

      if (currentRetry < maxRetries - 1) {
        // 还有重试机会，延迟后重试
        final retryDelay = Duration(milliseconds: 1000 * (currentRetry + 1)); // 递增延迟
        print('🔄 [FaceCapturePolling] ${retryDelay.inMilliseconds}ms后进行第${currentRetry + 2}次重启尝试...');

        Future.delayed(retryDelay, () {
          _restartWithRetry(maxRetries: maxRetries, currentRetry: currentRetry + 1);
        });
      } else {
        // 所有重试都失败了
        print('❌ [FaceCapturePolling] 所有重启尝试都失败，轮询服务无法恢复');
        if (onError != null) {
          onError!('轮询服务重启失败，已尝试$maxRetries次: $e');
        }
      }
    }
  }

  /// 启动健康检查定时器
  void _startHealthCheck() {
    _stopHealthCheck(); // 先停止之前的健康检查

    _healthCheckTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _performHealthCheck();
    });

    print('🏥 [FaceCapturePolling] 健康检查定时器已启动，每30秒检查一次');
  }

  /// 停止健康检查定时器
  void _stopHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }

  /// 执行健康检查
  void _performHealthCheck() {
    try {
      final now = DateTime.now();

      // 检查轮询状态
      if (!_isPolling) {
        print('⚠️ [FaceCapturePolling] 健康检查：轮询已停止，跳过检查');
        return;
      }

      // 检查最后成功检查时间
      if (_lastSuccessfulCheck != null) {
        final timeSinceLastCheck = now.difference(_lastSuccessfulCheck!);

        if (timeSinceLastCheck.inSeconds > 60) {
          // 超过60秒没有成功检查，可能出现问题
          print('🚨 [FaceCapturePolling] 健康检查：检测到轮询可能卡死（${timeSinceLastCheck.inSeconds}秒无响应），触发重启');

          if (onError != null) {
            onError!('轮询服务健康检查失败：${timeSinceLastCheck.inSeconds}秒无响应');
          }

          // 触发重启
          _attemptRestart();
          return;
        }
      }

      print('✅ [FaceCapturePolling] 健康检查：轮询服务运行正常');

    } catch (e) {
      print('❌ [FaceCapturePolling] 健康检查出错: $e');
      if (onError != null) {
        onError!('健康检查出错: $e');
      }
    }
  }

  // 清理资源
  void dispose() {
    _stopHealthCheck(); // 停止健康检查
    forceReset();
  }
}