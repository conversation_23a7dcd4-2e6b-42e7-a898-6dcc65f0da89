# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\soft\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\gdwork\\code\\a3g" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\soft\\flutter"
  "PROJECT_DIR=D:\\gdwork\\code\\a3g"
  "FLUTTER_ROOT=D:\\soft\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\gdwork\\code\\a3g\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\gdwork\\code\\a3g"
  "FLUTTER_TARGET=lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=true"
  "PACKAGE_CONFIG=D:\\gdwork\\code\\a3g\\.dart_tool\\package_config.json"
)
