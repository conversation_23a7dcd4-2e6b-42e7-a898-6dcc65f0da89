import 'package:flutter/material.dart';

import '../../../../core/widgets/loading_tip.dart';

class LoadingDialog extends StatelessWidget {
  static const _dialogStyle = DialogTheme(
    backgroundColor: Colors.transparent,
  );

  final Widget content;

  const LoadingDialog({
    super.key, 
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Theme(
        data: Theme.of(context).copyWith(dialogTheme: _dialogStyle),
        child: Dialog(
          insetPadding: EdgeInsets.zero,
          child: LoadingTipWidget(
            content: content,
          ),
        ),
      ),
    );
  }
}