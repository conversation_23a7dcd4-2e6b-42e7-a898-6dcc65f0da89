import 'dart:math';

import 'package:flutter/material.dart';

import '../utils/window_util.dart';
import 'rotating_gradient_ring.dart';

class LoadingTipWidget extends StatelessWidget {
  static const _decoration = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment(-0.8, -1),
      end: Alignment(0.8, 1),
      colors: [
        Color.fromRGBO(84, 160, 255, 0.6),
        Color.fromRGBO(63, 226, 200, 0.6),
      ],
    ),
    borderRadius: BorderRadius.all(Radius.circular(32)),
    border: GradientBoxBorder(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Color(0xFF54A0FF),
          Color(0xFF3FE2C8),
        ],
      ),
      width: 3,
    ),
  );

  static const _bottomWidget = Expanded(
    child: Align(
      alignment: Alignment.topCenter,
      child: RotatingGradientRing(),
    ),
  );

  final Widget content;

  const LoadingTipWidget({
    super.key,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: DecoratedBox(
        decoration: _decoration,
        child: SizedBox(
          width: 810.p,
          height: 680.p,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: _ContentWrapper(content: content),
              ),
              _bottomWidget,
            ],
          ),
        ),
      ),
    );
  }
}

class _ContentWrapper extends StatelessWidget {
  final Widget content;

  const _ContentWrapper({required this.content});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.p),
        child: content,
      ),
    );
  }
}

// 渐变边框实现
class GradientBoxBorder extends BoxBorder {
  final LinearGradient gradient;
  final double width;

  const GradientBoxBorder({
    required this.gradient,
    required this.width,
  });

  @override
  BorderSide get top => BorderSide.none;
  @override
  BorderSide get bottom => BorderSide.none;
  @override
  BorderSide get left => BorderSide.none;
  @override
  BorderSide get right => BorderSide.none;
  @override
  bool get isUniform => true;

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.all(width);

  @override
  void paint(
      Canvas canvas,
      Rect rect, {
        TextDirection? textDirection,
        BoxShape shape = BoxShape.rectangle,
        BorderRadius? borderRadius,
      }) {
    final Paint paint = Paint()
      ..strokeWidth = width
      ..style = PaintingStyle.stroke;

    if (borderRadius != null) {
      final RRect rrect = RRect.fromRectAndCorners(
        rect.deflate(width / 2),
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );

      paint.shader = gradient.createShader(rect);
      canvas.drawRRect(rrect, paint);
    } else {
      paint.shader = gradient.createShader(rect);
      canvas.drawRect(rect.deflate(width / 2), paint);
    }
  }

  @override
  bool equals(BoxBorder? other) => other is GradientBoxBorder &&
      other.gradient == gradient &&
      other.width == width;

  @override
  ShapeBorder scale(double t) {
    // TODO: implement scale
    throw UnimplementedError();
  }
}

// 进度环画笔
class _LoadingRingPainter extends CustomPainter {
  final double progress;
  final double strokeWidth;
  final Color backgroundColor;
  final Color progressColor;
  final Color progressEndColor;

  _LoadingRingPainter({
    required this.progress,
    required this.strokeWidth,
    required this.backgroundColor,
    required this.progressColor,
    required this.progressEndColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 绘制背景圆环
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制进度圆环
    final progressPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final gradient = SweepGradient(
      colors: [progressColor, progressEndColor],
      tileMode: TileMode.clamp,
      startAngle: -pi / 2,
      endAngle: 3 * pi / 2,
    );

    progressPaint.shader = gradient.createShader(
      Rect.fromCircle(center: center, radius: radius),
    );

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      2 * pi * progress,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}