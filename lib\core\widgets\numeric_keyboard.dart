// 显示底部数字键盘
import 'package:flutter/material.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';
import 'custom_button.dart';
import 'keyboard_button.dart';

class NumericKeyboard extends StatefulWidget {
  final Function(String) onValueChanged;
  final Function() onExit;
  final Function() onConfirm;
  final String? initialValue;
  final Widget? content;

  const NumericKeyboard({
    super.key,
    required this.onValueChanged,
    required this.onExit,
    required this.onConfirm,
    this.initialValue, this.content,
  });

  @override
  State<NumericKeyboard> createState() => _NumericKeyboardState();
}

class _NumericKeyboardState extends State<NumericKeyboard> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  final List<int> _list1 = [1, 2, 3, 4, 5, 6];
  final List<int> _list2 = [7, 8, 9, 0];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onDelete() {
    _focusNode.requestFocus();

    final text = _controller.text;
    final selection = _controller.selection;

    if (text.isEmpty) return;

    if (selection.start != selection.end) {
      final newText = text.replaceRange(selection.start, selection.end, '');
      _controller.text = newText;
      _controller.selection = TextSelection.collapsed(offset: selection.start);
    } else if (selection.start > 0) {
      final newText =
          text.replaceRange(selection.start - 1, selection.start, '');
      _controller.text = newText;
      _controller.selection =
          TextSelection.collapsed(offset: selection.start - 1);
    } else if (selection.start < text.length) {
      final newText =
          text.replaceRange(selection.start, selection.start + 1, '');
      _controller.text = newText;
      _controller.selection = TextSelection.collapsed(offset: selection.start);
    }

    widget.onValueChanged(_controller.text);
  }

  void _onKeyPressed(String value) {
    _focusNode.requestFocus();

    final text = _controller.text;
    final selection = _controller.selection;

    final newText = text.replaceRange(selection.start, selection.end, value);
    final newSelection =
        TextSelection.collapsed(offset: selection.start + value.length);

    _controller.text = newText;
    _controller.selection = newSelection;

    widget.onValueChanged(_controller.text);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding:
          EdgeInsets.only(left: 80.p, right: 80.p, top: 50.p, bottom: 35.p),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(-0.8, -0.6),
          end: Alignment(0.8, 0.6),
          colors: [
            Color(0xFFB3D5FF),
            Color(0xFFE6FCF8),
          ],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.content?? const SizedBox(),
          SizedBox(height: 30.p),
          Container(
            height: 110.p,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment(-0.8, -0.6),
                end: Alignment(0.8, 0.6),
                colors: [
                  Color.fromRGBO(255, 255, 255, 0.7),
                  Color.fromRGBO(218, 234, 255, 0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(30.p),
              border: Border.all(
                color: Colors.white,
                width: 2.p,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    autofocus: true,
                    style: TextStyle(
                      fontSize: 64.p,
                      color: const Color(0xFF222222),
                      fontWeight: FontWeight.w500,
                      letterSpacing: 5.p,
                      height: 1,
                    ),
                    cursorColor: const Color(0xFF54A0FF),
                    cursorWidth: 2,
                    textAlignVertical: TextAlignVertical.center,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 30.p),
                      isDense: true,
                      isCollapsed: true,
                    ),
                  ),
                ),
                ClearButton(
                  controller: _controller,
                  focusNode: _focusNode,
                  onValueChanged: widget.onValueChanged,
                ),
              ],
            ),
          ),
          _buildKeyboard(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomButton.outline(
                width: 300.p,
                text: '退出',
                onTap: widget.onExit,
              ),
              CustomButton.filled(
                width: 300.p,
                text: '确定',
                onTap: widget.onConfirm,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKeyboard() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 60.p, vertical: 60.p),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(_list1.length, (index) {
              return Padding(
                padding: EdgeInsets.only(right: 15.p),
                child: _buildKeyboardButton(_list1[index]),
              );
            }),
          ),
          SizedBox(height: 20.p),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...List.generate(_list2.length, (index) {
                return Padding(
                  padding: EdgeInsets.only(right: 15.p),
                  child: _buildKeyboardButton(_list2[index]),
                );
              }),
              KeyboardButton(
                text: Image.asset(
                  AssetUtil.fullPath('delete_icon.png'),
                  width: 58.p,
                  height: 44.p,
                ),
                onPressed: _onDelete,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKeyboardButton(int number) {
    return KeyboardButton(
      text: Text(
        number.toString(),
        style: TextStyle(
          color: Colors.white,
          fontSize: 48.p,
          fontWeight: FontWeight.w500,
          height: 1,
        ),
      ),
      onPressed: () {
        _onKeyPressed(number.toString());
      },
    );
  }
}

class ClearButton extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final Function(String) onValueChanged;

  const ClearButton({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onValueChanged,
  });

  @override
  State<ClearButton> createState() => _ClearButtonState();
}

class _ClearButtonState extends State<ClearButton> {
  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (widget.controller.text.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(right: 30.p),
      child: GestureDetector(
        onTap: () {
          widget.controller.clear();
          widget.onValueChanged('');
          widget.focusNode.requestFocus();
        },
        child: Image.asset(AssetUtil.fullPath('close.png'),width: 48.p,height: 48.p,),
      ),
    );
  }
}
