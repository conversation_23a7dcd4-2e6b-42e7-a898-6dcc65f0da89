import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 日志拦截器
/// 用于记录请求和响应的详细信息
class LoggingInterceptor extends Interceptor {
  /// 是否打印请求体
  final bool requestBody;
  
  /// 是否打印响应体
  final bool responseBody;
  
  /// 是否打印请求头
  final bool requestHeader;
  
  /// 是否打印错误消息
  final bool error;
  
  /// 输出前缀
  final String logPrefixText;

  LoggingInterceptor({
    this.requestBody = true,
    this.responseBody = true,
    this.requestHeader = true,
    this.error = true,
    this.logPrefixText = '### HTTP',
  });

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      _printRequestInfo(options);
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      _printResponseInfo(response);
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode && error) {
      _printError(err);
    }
    super.onError(err, handler);
  }

  /// 打印请求信息
  /// [options] 请求选项
  void _printRequestInfo(RequestOptions options) {
    final Uri uri = options.uri;
    final String method = options.method;
    
    print('$logPrefixText Request: $method $uri');
    
    if (requestHeader) {
      print('$logPrefixText Headers:');
      options.headers.forEach((key, value) {
        print('$logPrefixText   $key: $value');
      });
    }
    
    if (requestBody && options.data != null) {
      print('$logPrefixText Body: ${options.data}');
    }
    
    print('$logPrefixText END REQUEST');
  }

  /// 打印响应信息
  /// [response] 响应对象
  void _printResponseInfo(Response response) {
    final RequestOptions options = response.requestOptions;
    final int? statusCode = response.statusCode;
    final Uri uri = options.uri;
    final String method = options.method;
    
    print('$logPrefixText Response: [$statusCode] $method $uri');
    
    if (responseBody) {
      print('$logPrefixText Body: ${response.data}');
    }
    
    print('$logPrefixText END RESPONSE');
  }

  /// 打印错误信息
  /// [err] 错误对象
  void _printError(DioException err) {
    final RequestOptions options = err.requestOptions;
    final Uri uri = options.uri;
    final String method = options.method;
    
    print('$logPrefixText Error: [${err.type}] $method $uri');
    print('$logPrefixText   ${err.message}');
    
    if (err.response != null) {
      print('$logPrefixText Response: [${err.response?.statusCode}]');
      if (responseBody) {
        print('$logPrefixText Body: ${err.response?.data}');
      }
    }
    
    print('$logPrefixText END ERROR');
  }
} 