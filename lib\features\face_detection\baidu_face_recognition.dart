import 'dart:io';
import 'dart:ffi';
import 'dart:typed_data';
import 'dart:convert';
import 'package:ffi/ffi.dart';

// FFI类型定义
typedef BaiduSdkInitNative = Int32 Function(Pointer<Utf8> modelPath);
typedef BaiduSdkInit = int Function(Pointer<Utf8> modelPath);

typedef BaiduIsAuthNative = Int32 Function();
typedef BaiduIsAuth = int Function();

typedef BaiduGetSdkStatusNative = Pointer<Utf8> Function();
typedef BaiduGetSdkStatus = Pointer<Utf8> Function();

typedef BaiduLoadDbFaceNative = Int32 Function();
typedef BaiduLoadDbFace = int Function();

typedef BaiduGetFaceCountNative = Int32 Function();
typedef BaiduGetFaceCount = int Function();

typedef BaiduDetectFacesNative = Pointer<Utf8> Function(Pointer<Uint8>, Int32, Int32, Int32);
typedef BaiduDetectFaces = Pointer<Utf8> Function(Pointer<Uint8>, int, int, int);

typedef BaiduIdentifyWithAllNative = Pointer<Utf8> Function(Pointer<Uint8>, Int32, Int32, Int32);
typedef BaiduIdentifyWithAll = Pointer<Utf8> Function(Pointer<Uint8>, int, int, int);

typedef BaiduUserAddNative = Pointer<Utf8> Function(Pointer<Uint8>, Int32, Int32, Int32, Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>);
typedef BaiduUserAdd = Pointer<Utf8> Function(Pointer<Uint8>, int, int, int, Pointer<Utf8>, Pointer<Utf8>, Pointer<Utf8>);

typedef BaiduUserDeleteNative = Pointer<Utf8> Function(Pointer<Utf8>, Pointer<Utf8>);
typedef BaiduUserDelete = Pointer<Utf8> Function(Pointer<Utf8>, Pointer<Utf8>);

typedef BaiduGroupAddNative = Pointer<Utf8> Function(Pointer<Utf8>);
typedef BaiduGroupAdd = Pointer<Utf8> Function(Pointer<Utf8>);

typedef BaiduGroupDeleteNative = Pointer<Utf8> Function(Pointer<Utf8>);
typedef BaiduGroupDelete = Pointer<Utf8> Function(Pointer<Utf8>);

typedef BaiduGetUserListNative = Pointer<Utf8> Function(Pointer<Utf8>);
typedef BaiduGetUserList = Pointer<Utf8> Function(Pointer<Utf8>);

typedef BaiduFreeStringNative = Void Function(Pointer<Utf8>);
typedef BaiduFreeString = void Function(Pointer<Utf8>);

typedef BaiduCleanupNative = Int32 Function();
typedef BaiduCleanup = int Function();

// SDK路径管理相关类型定义
typedef BaiduSetSdkPathNative = Int32 Function(Pointer<Utf8> sdkPath);
typedef BaiduSetSdkPath = int Function(Pointer<Utf8> sdkPath);

typedef BaiduValidateSdkPathNative = Pointer<Utf8> Function(Pointer<Utf8> sdkPath);
typedef BaiduValidateSdkPath = Pointer<Utf8> Function(Pointer<Utf8> sdkPath);

// 数据类定义
class FaceRecognitionResult {
  final String userId;
  final String groupId;
  final double score;
  final String? userInfo;
  
  FaceRecognitionResult({
    required this.userId,
    required this.groupId,
    required this.score,
    this.userInfo,
  });
  
  factory FaceRecognitionResult.fromJson(Map<String, dynamic> json) {
    return FaceRecognitionResult(
      userId: json['user_id'] ?? '',
      groupId: json['group_id'] ?? '',
      score: (json['score'] ?? 0.0).toDouble(),
      userInfo: json['user_info'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'group_id': groupId,
      'score': score,
      'user_info': userInfo,
    };
  }
}

class FaceDetectionResult {
  final double confidence;
  final int left;
  final int top;
  final int width;
  final int height;
  
  FaceDetectionResult({
    required this.confidence,
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });
  
  factory FaceDetectionResult.fromJson(Map<String, dynamic> json) {
    final location = json['location'] ?? {};
    return FaceDetectionResult(
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      left: location['left'] ?? 0,
      top: location['top'] ?? 0,
      width: location['width'] ?? 0,
      height: location['height'] ?? 0,
    );
  }
}

class BaiduFaceRecognition {
  static DynamicLibrary? _dylib;
  static bool _initialized = false;
  
  // 检查是否已初始化
  static bool isInitialized() {
    return _initialized;
  }
  
  // FFI函数绑定
  static late BaiduSdkInit _sdkInit;
  static late BaiduIsAuth _isAuth;
  static late BaiduGetSdkStatus _getSdkStatus;
  static late BaiduLoadDbFace _loadDbFace;
  static late BaiduGetFaceCount _getFaceCount;
  static late BaiduDetectFaces _detectFaces;
  static late BaiduIdentifyWithAll _identifyWithAll;
  static late BaiduUserAdd _userAdd;
  static late BaiduUserDelete _userDelete;
  static late BaiduGroupAdd _groupAdd;
  static late BaiduGroupDelete _groupDelete;
  static late BaiduGetUserList _getUserList;
  static late BaiduFreeString _freeString;
  static late BaiduCleanup _cleanup;
  
  // SDK路径管理函数绑定
  static late BaiduSetSdkPath _setSdkPath;
  static late BaiduValidateSdkPath _validateSdkPath;
  
  // 初始化SDK
  static Future<bool> initialize({String? modelPath}) async {
    if (_initialized) {
      return true;
    }
    
    try {
      // 加载动态库
      if (Platform.isWindows) {
        _dylib = DynamicLibrary.open('a3g.exe');
      } else {
        throw UnsupportedError('百度人脸识别SDK仅支持Windows平台');
      }
      
      // 绑定函数
      _sdkInit = _dylib!.lookupFunction<BaiduSdkInitNative, BaiduSdkInit>('baidu_sdk_init');
      _isAuth = _dylib!.lookupFunction<BaiduIsAuthNative, BaiduIsAuth>('baidu_is_auth');
      _getSdkStatus = _dylib!.lookupFunction<BaiduGetSdkStatusNative, BaiduGetSdkStatus>('baidu_get_sdk_status');
      _loadDbFace = _dylib!.lookupFunction<BaiduLoadDbFaceNative, BaiduLoadDbFace>('baidu_load_db_face');
      _getFaceCount = _dylib!.lookupFunction<BaiduGetFaceCountNative, BaiduGetFaceCount>('baidu_get_face_count');
      _detectFaces = _dylib!.lookupFunction<BaiduDetectFacesNative, BaiduDetectFaces>('baidu_detect_faces');
      _identifyWithAll = _dylib!.lookupFunction<BaiduIdentifyWithAllNative, BaiduIdentifyWithAll>('baidu_identify_with_all');
      _userAdd = _dylib!.lookupFunction<BaiduUserAddNative, BaiduUserAdd>('baidu_user_add');
      _userDelete = _dylib!.lookupFunction<BaiduUserDeleteNative, BaiduUserDelete>('baidu_user_delete');
      _groupAdd = _dylib!.lookupFunction<BaiduGroupAddNative, BaiduGroupAdd>('baidu_group_add');
      _groupDelete = _dylib!.lookupFunction<BaiduGroupDeleteNative, BaiduGroupDelete>('baidu_group_delete');
      _getUserList = _dylib!.lookupFunction<BaiduGetUserListNative, BaiduGetUserList>('baidu_get_user_list');
      _freeString = _dylib!.lookupFunction<BaiduFreeStringNative, BaiduFreeString>('baidu_free_string');
      _cleanup = _dylib!.lookupFunction<BaiduCleanupNative, BaiduCleanup>('baidu_cleanup');
      
      // 绑定SDK路径管理函数
      _setSdkPath = _dylib!.lookupFunction<BaiduSetSdkPathNative, BaiduSetSdkPath>('baidu_set_sdk_path');
      _validateSdkPath = _dylib!.lookupFunction<BaiduValidateSdkPathNative, BaiduValidateSdkPath>('baidu_validate_sdk_path');
      
      // 初始化SDK
      final modelPathPtr = modelPath?.toNativeUtf8() ?? nullptr;
      final result = _sdkInit(modelPathPtr);
      
      if (modelPathPtr != nullptr) {
        malloc.free(modelPathPtr);
      }
      
      if (result == 0) {
        _initialized = true;
        
        // 加载人脸数据库
        final loadResult = _loadDbFace();
        print('百度SDK初始化成功, 人脸数据库加载结果: $loadResult');
        
        return true;
      } else {
        print('百度SDK初始化失败, 错误码: $result');
        return false;
      }
    } catch (e) {
      print('百度SDK初始化异常: $e');
      return false;
    }
  }
  
  // 检查授权状态
  static bool isAuthorized() {
    if (!_initialized) return false;
    try {
      return _isAuth() == 1;
    } catch (e) {
      print('检查授权状态失败: $e');
      return false;
    }
  }
  
  // 获取人脸数量
  static int getFaceCount() {
    if (!_initialized) return 0;
    try {
      return _getFaceCount();
    } catch (e) {
      print('获取人脸数量失败: $e');
      return 0;
    }
  }
  
  // 人脸检测
  static Future<List<FaceDetectionResult>> detectFaces(Uint8List imageData, {int width = 0, int height = 0}) async {
    if (!_initialized) {
      throw StateError('SDK未初始化');
    }
    
    try {
      final imagePtr = malloc<Uint8>(imageData.length);
      imagePtr.asTypedList(imageData.length).setAll(0, imageData);
      
      final resultPtr = _detectFaces(imagePtr, imageData.length, width, height);
      final resultStr = resultPtr.toDartString();
      
      _freeString(resultPtr);
      malloc.free(imagePtr);
      
      final jsonResult = jsonDecode(resultStr);
      if (jsonResult['error'] != null) {
        throw Exception('人脸检测失败: ${jsonResult['error']}');
      }
      
      final faces = jsonResult['faces'] as List? ?? [];
      return faces.map((face) => FaceDetectionResult.fromJson(face)).toList();
    } catch (e) {
      print('人脸检测异常: $e');
      rethrow;
    }
  }
  
  // 1:N人脸识别
  static Future<List<FaceRecognitionResult>> identifyWithImageData(
    Uint8List imageData, {
    int width = 0, 
    int height = 0,
    bool isJpeg = true
  }) async {
    if (!_initialized) {
      throw StateError('SDK未初始化');
    }
    
    try {
      final imagePtr = malloc<Uint8>(imageData.length);
      imagePtr.asTypedList(imageData.length).setAll(0, imageData);
      
      final resultPtr = _identifyWithAll(imagePtr, imageData.length, width, height);
      final resultStr = resultPtr.toDartString();
      
      _freeString(resultPtr);
      malloc.free(imagePtr);
      
      final jsonResult = jsonDecode(resultStr);
      
      // 打印完整的识别结果用于调试
      print('🔍 百度SDK识别结果: $resultStr');
      
      // 检查是否有错误
      if (jsonResult['error'] != null) {
        throw Exception('人脸识别失败: ${jsonResult['error']}');
      }
      
      // 检查errno字段（百度SDK特有格式）
      if (jsonResult['errno'] != null && jsonResult['errno'] != 0) {
        final errno = jsonResult['errno'];
        final errorMsg = jsonResult['msg'] ?? '未知错误';
        print('❌ 百度SDK返回错误: errno=$errno, msg=$errorMsg');
        
        // 将错误抛出，让上层处理并显示给用户
        String userFriendlyMessage;
        switch (errno) {
          case -1008:
            userFriendlyMessage = '人脸特征提取失败，请确保图像中有清晰的人脸';
            break;
          case -1009:
            userFriendlyMessage = '未检测到人脸，请正对摄像头';
            break;
          case -1001:
            userFriendlyMessage = 'SDK授权过期，请联系管理员';
            break;
          case -1002:
            userFriendlyMessage = '人脸数据库为空，请先注册人脸';
            break;
          case -1003:
            userFriendlyMessage = '图像格式错误，请检查图像数据';
            break;
          case -1004:
            userFriendlyMessage = '图像质量过低，请在光线充足的环境下拍摄';
            break;
          case -1005:
            userFriendlyMessage = '人脸过小，请靠近摄像头';
            break;
          case -1006:
            userFriendlyMessage = '人脸角度过大，请正对摄像头';
            break;
          case -1007:
            userFriendlyMessage = '人脸被遮挡，请露出完整的面部';
            break;
          case -1010:
            userFriendlyMessage = '人脸识别库异常，请重试';
            break;
          case -1011:
            userFriendlyMessage = '参数错误，请检查输入参数';
            break;
          case -1012:
            userFriendlyMessage = '内存不足，请重启应用';
            break;
          case -1013:
            userFriendlyMessage = '网络错误，请检查网络连接';
            break;
          case -1014:
            userFriendlyMessage = '授权文件损坏，请联系管理员';
            break;
          case -1015:
            userFriendlyMessage = '模型文件加载失败，请重启应用';
            break;
          default:
            userFriendlyMessage = '人脸识别失败: $errorMsg (错误码: $errno)';
        }
        
        throw Exception(userFriendlyMessage);
      }
      
      // 兼容不同的JSON格式
      List results = [];
      if (jsonResult['data'] != null && jsonResult['data']['result'] != null) {
        // 百度SDK格式: {"data": {"result": [...]}}
        results = jsonResult['data']['result'] as List;
        print('✅ 使用百度SDK格式解析，找到 ${results.length} 个识别结果');
      } else if (jsonResult['result'] != null) {
        // 标准格式: {"result": [...]}
        results = jsonResult['result'] as List;
        print('✅ 使用标准格式解析，找到 ${results.length} 个识别结果');
      } else {
        print('⚠️ 未找到result字段，返回空结果');
        return [];
      }
      
      // 打印每个识别结果的详细信息
      for (var i = 0; i < results.length; i++) {
        final result = results[i];
        print('🎯 识别结果 ${i+1}: 用户ID=${result['user_id']}, 分组=${result['group_id']}, 得分=${result['score']}');
      }
      
      return results.map((result) => FaceRecognitionResult.fromJson(result)).toList();
    } catch (e) {
      print('人脸识别异常: $e');
      // 🚨 重要修改：重新抛出异常，让上层的错误处理和重启机制能够正常工作
      // 这样可以确保errno=-1008等严重错误能够触发服务重启
      rethrow;
    }
  }
  
  // 便捷方法：直接使用图片数据进行识别
  static Future<List<FaceRecognitionResult>> identifyFace(Uint8List imageData) async {
    return identifyWithImageData(imageData, isJpeg: true);
  }
  
  // 用户注册
  static Future<bool> addUser(
    Uint8List imageData,
    String userId,
    String groupId, [
    String? userInfo,
  ]) async {
    if (!_initialized) {
      throw StateError('SDK未初始化');
    }
    
    try {
      final imagePtr = malloc<Uint8>(imageData.length);
      imagePtr.asTypedList(imageData.length).setAll(0, imageData);
      
      final userIdPtr = userId.toNativeUtf8();
      final groupIdPtr = groupId.toNativeUtf8();
      final userInfoPtr = userInfo?.toNativeUtf8() ?? nullptr;
      
      final resultPtr = _userAdd(imagePtr, imageData.length, 0, 0, userIdPtr, groupIdPtr, userInfoPtr);
      final resultStr = resultPtr.toDartString();
      
      _freeString(resultPtr);
      malloc.free(imagePtr);
      malloc.free(userIdPtr);
      malloc.free(groupIdPtr);
      if (userInfoPtr != nullptr) {
        malloc.free(userInfoPtr);
      }
      
      final jsonResult = jsonDecode(resultStr);
      if (jsonResult['error'] != null) {
        throw Exception('用户注册失败: ${jsonResult['error']}');
      }
      
      return jsonResult['error_code'] == 0;
    } catch (e) {
      print('用户注册异常: $e');
      rethrow;
    }
  }
  
  // 用户删除
  static Future<bool> deleteUser(String userId, String groupId) async {
    if (!_initialized) {
      throw StateError('SDK未初始化');
    }
    
    try {
      final userIdPtr = userId.toNativeUtf8();
      final groupIdPtr = groupId.toNativeUtf8();
      
      final resultPtr = _userDelete(userIdPtr, groupIdPtr);
      final resultStr = resultPtr.toDartString();
      
      _freeString(resultPtr);
      malloc.free(userIdPtr);
      malloc.free(groupIdPtr);
      
      final jsonResult = jsonDecode(resultStr);
      return jsonResult['error_code'] == 0;
    } catch (e) {
      print('用户删除异常: $e');
      return false;
    }
  }
  
  // 创建用户组
  static Future<bool> createGroup(String groupId) async {
    if (!_initialized) {
      throw StateError('SDK未初始化');
    }
    
    try {
      final groupIdPtr = groupId.toNativeUtf8();
      
      final resultPtr = _groupAdd(groupIdPtr);
      final resultStr = resultPtr.toDartString();
      
      _freeString(resultPtr);
      malloc.free(groupIdPtr);
      
      final jsonResult = jsonDecode(resultStr);
      return jsonResult['error_code'] == 0;
    } catch (e) {
      print('创建用户组异常: $e');
      return false;
    }
  }
  
  // 删除用户组
  static Future<bool> deleteGroup(String groupId) async {
    if (!_initialized) {
      throw StateError('SDK未初始化');
    }
    
    try {
      final groupIdPtr = groupId.toNativeUtf8();
      
      final resultPtr = _groupDelete(groupIdPtr);
      final resultStr = resultPtr.toDartString();
      
      _freeString(resultPtr);
      malloc.free(groupIdPtr);
      
      final jsonResult = jsonDecode(resultStr);
      return jsonResult['error_code'] == 0;
    } catch (e) {
      print('删除用户组异常: $e');
      return false;
    }
  }
  
  // 获取用户列表
  static Future<List<String>> getUserList(String groupId) async {
    if (!_initialized) {
      throw StateError('SDK未初始化');
    }
    
    try {
      final groupIdPtr = groupId.toNativeUtf8();
      
      final resultPtr = _getUserList(groupIdPtr);
      final resultStr = resultPtr.toDartString();
      
      _freeString(resultPtr);
      malloc.free(groupIdPtr);
      
      final jsonResult = jsonDecode(resultStr);
      if (jsonResult['error_code'] == 0) {
        final userList = jsonResult['result']['user_id_list'] as List? ?? [];
        return userList.cast<String>();
      }
      return [];
    } catch (e) {
      print('获取用户列表异常: $e');
      return [];
    }
  }
  
  // 获取SDK详细状态
  static Map<String, dynamic> getSdkStatus() {
    if (!_initialized) {
      return {'initialized': false, 'api_instance': false};
    }
    
    try {
      final statusPtr = _getSdkStatus();
      final statusStr = statusPtr.toDartString();
      _freeString(statusPtr);
      
      // 解析JSON字符串
      final Map<String, dynamic> status = json.decode(statusStr);
      return status;
    } catch (e) {
      print('获取SDK状态失败: $e');
      return {'error': e.toString()};
    }
  }
  
  // 清理资源
  static void dispose() {
    if (_initialized) {
      try {
        _cleanup();
      } catch (e) {
        print('清理资源异常: $e');
      }
      _initialized = false;
    }
  }
  
  // ========== SDK路径管理相关方法 ==========
  
  /// 设置SDK路径
  /// 
  /// [sdkPath] SDK根目录路径，例如 'D:\gdwork\文档\FaceOfflineSdk'
  /// 返回 0 表示成功，其他值表示失败
  static int setSdkPath(String sdkPath) {
    if (!_initialized) {
      print('SDK未初始化，无法设置路径');
      return -100;
    }
    
    try {
      final pathPtr = sdkPath.toNativeUtf8();
      final result = _setSdkPath(pathPtr);
      malloc.free(pathPtr);
      
      if (result == 0) {
        print('SDK路径设置成功: $sdkPath');
      } else {
        print('SDK路径设置失败，错误码: $result');
      }
      
      return result;
    } catch (e) {
      print('设置SDK路径异常: $e');
      return -101;
    }
  }
  
  /// 验证SDK路径是否有效
  /// 
  /// [sdkPath] 要验证的SDK路径
  /// 返回验证结果的JSON字符串
  static Map<String, dynamic> validateSdkPath(String sdkPath) {
    if (!_initialized) {
      return {
        'valid': false,
        'error': 'SDK未初始化'
      };
    }
    
    try {
      final pathPtr = sdkPath.toNativeUtf8();
      final resultPtr = _validateSdkPath(pathPtr);
      malloc.free(pathPtr);
      
      final resultStr = resultPtr.toDartString();
      _freeString(resultPtr);
      
      // 解析JSON结果
      final Map<String, dynamic> result = json.decode(resultStr);
      return result;
    } catch (e) {
      print('验证SDK路径异常: $e');
      return {
        'valid': false,
        'error': '验证过程异常: $e'
      };
    }
  }
  
  /// 获取当前SDK状态（增强版）
  /// 
  /// 包含SDK路径、DLL加载状态等详细信息
  static Map<String, dynamic> getDetailedSdkStatus() {
    if (!_initialized) {
      return {
        'initialized': false,
        'error': 'SDK未初始化'
      };
    }
    
    try {
      final statusPtr = _getSdkStatus();
      final statusStr = statusPtr.toDartString();
      _freeString(statusPtr);
      
      // 解析JSON结果
      final Map<String, dynamic> status = json.decode(statusStr);
      return status;
    } catch (e) {
      print('获取详细SDK状态异常: $e');
      return {
        'error': '获取状态失败: $e'
      };
    }
  }
} 