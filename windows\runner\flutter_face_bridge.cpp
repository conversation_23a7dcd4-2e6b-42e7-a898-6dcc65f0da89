﻿#include "flutter_face_bridge.h"
#include <iostream>
#include <memory>
#include <opencv2/opencv.hpp>
#include <windows.h>
#include <string>
#include <functional>
#include <vector>

// 使用函数指针方式调用百度SDK，避免直接包含头文件
typedef void (*IdentifyWithAllFunc)(std::string& res, const cv::Mat* img, int type);

// 全局函数指针
IdentifyWithAllFunc g_identifyWithAll = nullptr;

// 尝试从多个位置加载DLL
HMODULE loadDllFromMultiplePaths(const std::vector<std::string>& paths, const std::string& dllName) {
    HMODULE hModule = NULL;
    
    // 首先尝试直接加载（从系统路径）
    hModule = LoadLibraryA(dllName.c_str());
    if (hModule) {
        std::cout << "Loaded " << dllName << " from system path" << std::endl;
        return hModule;
    }
    
    // 尝试从指定路径加载
    for (const auto& path : paths) {
        std::string fullPath = path + "\\" + dllName;
        std::cout << "Trying to load " << fullPath << std::endl;
        
        hModule = LoadLibraryA(fullPath.c_str());
        if (hModule) {
            std::cout << "Successfully loaded " << fullPath << std::endl;
            return hModule;
        } else {
            std::cerr << "Failed to load " << fullPath << ", error code: " << GetLastError() << std::endl;
        }
    }
    
    return NULL;
}

// 初始化SDK函数指针
bool initBaiduSDK() {
    static bool initialized = false;
    
    if (initialized) {
        return true;
    }
    
    // 定义可能的DLL路径
    std::vector<std::string> possiblePaths = {
        ".",                                   // 当前目录
        ".\\data",                             // 当前目录下的data子目录
        "..\\sdk\\FaceOfflineSdk\\x64"         // 项目内的SDK目录
    };
    
    // 加载DLL
    HMODULE hModule = loadDllFromMultiplePaths(possiblePaths, "BaiduFaceApi.dll");
    if (!hModule) {
        std::cerr << "Failed to load BaiduFaceApi.dll from any location" << std::endl;
        return false;
    }
    
    // 获取函数地址
    g_identifyWithAll = (IdentifyWithAllFunc)GetProcAddress(hModule, "identify_with_all");
    if (!g_identifyWithAll) {
        std::cerr << "Failed to get identify_with_all function, error code: " << GetLastError() << std::endl;
        FreeLibrary(hModule);
        return false;
    }
    
    initialized = true;
    return true;
}

extern "C" {

// 将Flutter图像数据转换为OpenCV Mat并调用百度SDK进行人脸识别
void identify_with_image_data(
    char** result,
    const unsigned char* data,
    int data_length,
    int width,
    int height,
    int channels,
    int is_jpeg,
    int type
) {
    try {
        // 初始化SDK函数指针
        if (!initBaiduSDK()) {
            std::string error_str = "{\"error\":\"Failed to initialize Baidu SDK\"}";
            *result = (char*)malloc(error_str.length() + 1);
            strcpy(*result, error_str.c_str());
            return;
        }
        
        cv::Mat img;
        
        // 根据数据类型进行转换
        if (is_jpeg) {
            // 解码JPEG数据
            std::vector<uchar> buffer(data, data + data_length);
            img = cv::imdecode(buffer, cv::IMREAD_COLOR);
            
            if (img.empty()) {
                // 设置错误结果
                std::string error_str = "{\"error\":\"Failed to decode JPEG data\"}";
                *result = (char*)malloc(error_str.length() + 1);
                strcpy(*result, error_str.c_str());
                return;
            }
        } else {
            // 原始图像数据
            if (channels == 3) {
                img = cv::Mat(height, width, CV_8UC3, (void*)data);
            } else if (channels == 4) {
                img = cv::Mat(height, width, CV_8UC4, (void*)data);
            } else if (channels == 1) {
                img = cv::Mat(height, width, CV_8UC1, (void*)data);
            } else {
                // 设置错误结果
                std::string error_str = "{\"error\":\"Unsupported number of channels\"}";
                *result = (char*)malloc(error_str.length() + 1);
                strcpy(*result, error_str.c_str());
                return;
            }
            
            // 为了确保安全，创建一个副本
            img = img.clone();
        }
        
        // 调用百度SDK进行人脸识别
        std::string res;
        g_identifyWithAll(res, &img, type);
        
        // 分配内存并复制结果
        *result = (char*)malloc(res.length() + 1);
        strcpy(*result, res.c_str());
        
    } catch (const std::exception& e) {
        // 处理异常
        std::string error_str = "{\"error\":\"" + std::string(e.what()) + "\"}";
        *result = (char*)malloc(error_str.length() + 1);
        strcpy(*result, error_str.c_str());
    }
}

// 释放结果字符串
void free_result_string(char* result) {
    if (result) {
        free(result);
    }
}

} // extern "C" 