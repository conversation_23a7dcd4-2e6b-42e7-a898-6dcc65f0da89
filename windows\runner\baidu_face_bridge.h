#ifndef BAIDU_FACE_BRIDGE_H
#define BAIDU_FACE_BRIDGE_H

#include <windows.h>

#ifdef __cplusplus
extern "C" {
#endif

// 导出函数声明
// SDK路径管理
__declspec(dllexport) int baidu_set_sdk_path(const char* sdk_path);
__declspec(dllexport) char* baidu_validate_sdk_path(const char* sdk_path);
__declspec(dllexport) char* baidu_get_sdk_status();

// SDK初始化和基本功能
__declspec(dllexport) int baidu_sdk_init(const char* model_path);
__declspec(dllexport) int baidu_is_auth();
__declspec(dllexport) int baidu_load_db_face();
__declspec(dllexport) int baidu_get_face_count();

// 人脸检测和识别
__declspec(dllexport) char* baidu_detect_faces(unsigned char* image_data, int image_length, int width, int height);
__declspec(dllexport) char* baidu_identify_with_all(unsigned char* image_data, int image_length, int width, int height);

// 用户管理
__declspec(dllexport) char* baidu_user_add(unsigned char* image_data, int image_length, int width, int height, 
                                          const char* user_id, const char* group_id, const char* user_info);
__declspec(dllexport) char* baidu_user_delete(const char* user_id, const char* group_id);
__declspec(dllexport) char* baidu_group_add(const char* group_id);
__declspec(dllexport) char* baidu_group_delete(const char* group_id);
__declspec(dllexport) char* baidu_get_user_list(const char* group_id);

// 内存管理和清理
__declspec(dllexport) void baidu_free_string(char* str);
__declspec(dllexport) int baidu_cleanup();

#ifdef __cplusplus
}
#endif

#endif // BAIDU_FACE_BRIDGE_H 