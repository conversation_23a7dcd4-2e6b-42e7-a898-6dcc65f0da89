import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:base_package/base_package.dart';
import 'package:seasetting/seasetting.dart';
import 'interfaces/db_interface.dart';
import 'implementations/mysql_db.dart';
import 'implementations/sqlite_db.dart';

class DBManager {
  static DBInterface? _instance;
  
  /// 获取数据库实例
  static Future<DBInterface> getDBInstance() async {
    if (_instance != null) {
      return _instance!;
    }

    DBType? dbType = Get.context?.read<DBProvider>().dbType;
    _instance = dbType == DBType.mysql 
        ? await MysqlDB.instance()
        : await SqliteDB.instance();
        
    return _instance!;
  }

  /// 删除并重置数据库
  static Future<DBResult<void>> resetDatabase() async {
    try {
      // 1. 关闭现有连接
      await closeDB();

      // 2. 获取新实例并删除数据库
      final db = await getDBInstance();
      final result = await db.deleteDatabase();

      // 3. 再次关闭连接，强制重新初始化
      await closeDB();

      return result;
    } catch (e) {
      return DBResult.error('重置数据库失败: $e');
    }
  }
  
  /// 关闭数据库连接
  static Future<void> closeDB() async {
    await _instance?.close();
    _instance = null;
  }
} 