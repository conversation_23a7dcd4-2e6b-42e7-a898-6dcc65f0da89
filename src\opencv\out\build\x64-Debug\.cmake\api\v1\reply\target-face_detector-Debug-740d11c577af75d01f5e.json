{"artifacts": [{"path": "bin/libface_detector.dll"}, {"path": "lib/libface_detector.lib"}, {"path": "bin/libface_detector.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 18, "parent": 0}, {"command": 1, "file": 0, "line": 41, "parent": 0}, {"command": 2, "file": 0, "line": 25, "parent": 0}, {"command": 3, "file": 0, "line": 6, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"fragment": "/MDd"}], "defines": [{"backtrace": 3, "define": "FACE_DETECTOR_EXPORTS"}, {"define": "face_detector_EXPORTS"}], "includes": [{"backtrace": 4, "isSystem": true, "path": "D:/Users/<USER>/Downloads/opencv/build/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "11"}, "sourceIndexes": [0]}], "id": "face_detector::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL /NODEFAULTLIB:libcmtd.lib", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Users\\Administrator\\Downloads\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "face_detector", "nameOnDisk": "libface_detector.dll", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "face_detection.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}