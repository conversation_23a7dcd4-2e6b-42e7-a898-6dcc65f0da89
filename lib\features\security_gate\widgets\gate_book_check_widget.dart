import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import 'package:base_package/base_package.dart';

/// 闸机书籍检查组件
class GateBookCheckWidget extends StatefulWidget {
  final List<Map<String, dynamic>> books;
  final VoidCallback? onClose;
  
  const GateBookCheckWidget({
    Key? key,
    required this.books,
    this.onClose,
  }) : super(key: key);

  @override
  State<GateBookCheckWidget> createState() => _GateBookCheckWidgetState();
}

class _GateBookCheckWidgetState extends State<GateBookCheckWidget>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  List<Map<String, dynamic>> _borrowedBooks = [];
  List<Map<String, dynamic>> _unborrowedBooks = [];
  bool _hasUnborrowedBooks = false;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _processBooks();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 400),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));
  }
  
  void _processBooks() {
    for (final book in widget.books) {
      final isBorrowed = book['is_borrowed'] as bool? ?? false;
      if (isBorrowed) {
        _borrowedBooks.add(book);
      } else {
        _unborrowedBooks.add(book);
      }
    }
    _hasUnborrowedBooks = _unborrowedBooks.isNotEmpty;
  }
  
  void _startAnimations() {
    _fadeController.forward();
    _slideController.forward();
  }
  
  void _closeOverlay() async {
    await _fadeController.reverse();
    await _slideController.reverse();
    widget.onClose?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: Listenable.merge([_fadeAnimation, _slideAnimation]),
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withOpacity(0.8),
              child: Center(
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Container(
                    width: 800.p,
                    height: 600.p,
                    margin: EdgeInsets.all(40.p),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20.p),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 20,
                          offset: Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // 标题栏
                        _buildHeader(),
                        
                        // 检查结果内容
                        Expanded(
                          child: _buildCheckContent(),
                        ),
                        
                        // 底部按钮
                        _buildBottomActions(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
  
  /// 构建标题栏
  Widget _buildHeader() {
    final headerColor = _hasUnborrowedBooks ? Colors.red : Colors.green;
    final headerIcon = _hasUnborrowedBooks ? Icons.error : Icons.check_circle;
    final headerText = _hasUnborrowedBooks ? '检测到未借书籍' : '检查通过';
    
    return Container(
      padding: EdgeInsets.all(20.p),
      decoration: BoxDecoration(
        color: headerColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.p),
          topRight: Radius.circular(20.p),
        ),
      ),
      child: Row(
        children: [
          Icon(
            headerIcon,
            color: Colors.white,
            size: 32.p,
          ),
          SizedBox(width: 15.p),
          Expanded(
            child: Text(
              headerText,
              style: TextStyle(
                color: Colors.white,
                fontSize: 24.p,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Text(
            '共 ${widget.books.length} 本',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.p,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建检查内容
  Widget _buildCheckContent() {
    if (widget.books.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 80.p,
              color: Colors.grey[400],
            ),
            SizedBox(height: 20.p),
            Text(
              '未检测到书籍',
              style: TextStyle(
                fontSize: 24.p,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }
    
    return Padding(
      padding: EdgeInsets.all(20.p),
      child: Column(
        children: [
          // 统计信息
          _buildSummary(),
          
          SizedBox(height: 20.p),
          
          // 书籍列表
          Expanded(
            child: _buildBooksList(),
          ),
        ],
      ),
    );
  }
  
  /// 构建统计信息
  Widget _buildSummary() {
    return Container(
      padding: EdgeInsets.all(20.p),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(15.p),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryItem(
              '已借书籍',
              _borrowedBooks.length.toString(),
              Colors.green,
              Icons.check_circle,
            ),
          ),
          Container(
            width: 1,
            height: 40.p,
            color: Colors.grey[300],
          ),
          Expanded(
            child: _buildSummaryItem(
              '未借书籍',
              _unborrowedBooks.length.toString(),
              Colors.red,
              Icons.error,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建统计项
  Widget _buildSummaryItem(String label, String count, Color color, IconData icon) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20.p),
            SizedBox(width: 8.p),
            Text(
              count,
              style: TextStyle(
                fontSize: 24.p,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        SizedBox(height: 5.p),
        Text(
          label,
          style: TextStyle(
            fontSize: 14.p,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
  
  /// 构建书籍列表
  Widget _buildBooksList() {
    return ListView.builder(
      itemCount: widget.books.length,
      itemBuilder: (context, index) {
        final book = widget.books[index];
        final isBorrowed = book['is_borrowed'] as bool? ?? false;
        
        return Container(
          margin: EdgeInsets.only(bottom: 10.p),
          padding: EdgeInsets.all(15.p),
          decoration: BoxDecoration(
            color: isBorrowed ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10.p),
            border: Border.all(
              color: isBorrowed ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // 状态图标
              Container(
                width: 40.p,
                height: 40.p,
                decoration: BoxDecoration(
                  color: isBorrowed ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isBorrowed ? Icons.check : Icons.close,
                  color: Colors.white,
                  size: 20.p,
                ),
              ),
              
              SizedBox(width: 15.p),
              
              // 书籍信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      book['barcode'] as String? ?? '未知条码',
                      style: TextStyle(
                        fontSize: 16.p,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 5.p),
                    Text(
                      book['book_name'] as String? ?? '未知书籍',
                      style: TextStyle(
                        fontSize: 14.p,
                        color: Colors.grey[600],
                      ),
                    ),
                    if (book['author'] != null) ...[
                      SizedBox(height: 3.p),
                      Text(
                        '作者: ${book['author']}',
                        style: TextStyle(
                          fontSize: 12.p,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // 状态标签
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.p, vertical: 6.p),
                decoration: BoxDecoration(
                  color: isBorrowed ? Colors.green : Colors.red,
                  borderRadius: BorderRadius.circular(15.p),
                ),
                child: Text(
                  isBorrowed ? '已借' : '未借',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.p,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Container(
      padding: EdgeInsets.all(20.p),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20.p),
          bottomRight: Radius.circular(20.p),
        ),
      ),
      child: Row(
        children: [
          if (_hasUnborrowedBooks) ...[
            Expanded(
              child: Text(
                '请退出通道处理未借书籍',
                style: TextStyle(
                  fontSize: 16.p,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ] else ...[
            Expanded(
              child: Text(
                '所有书籍均已借阅，可以通过',
                style: TextStyle(
                  fontSize: 16.p,
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
          ElevatedButton(
            onPressed: _closeOverlay,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[600],
              padding: EdgeInsets.symmetric(horizontal: 30.p, vertical: 15.p),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25.p),
              ),
            ),
            child: Text(
              '确定',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.p,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }
}
