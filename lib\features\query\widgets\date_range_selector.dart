import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../core/utils/window_util.dart';
import '../view_models/query_view_model.dart';

class DateRangeSelector extends StatelessWidget {
  const DateRangeSelector({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<QueryViewModel>(context);
    final dateFormat = DateFormat('yyyy-MM-dd');
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 50.p, vertical: 20.p),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '时间：',
            style: TextStyle(
              fontSize: 26.p,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF3568FC),
            ),
          ),
          SizedBox(width: 10.p),
          _buildDateSelector(
            context,
            dateText: dateFormat.format(viewModel.startDate),
            onTap: () => _selectDate(context, true),
          ),
          SizedBox(width: 10.p),
          Text(
            '至',
            style: TextStyle(
              fontSize: 26.p,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF12215F),
            ),
          ),
          SizedBox(width: 10.p),
          _buildDateSelector(
            context,
            dateText: dateFormat.format(viewModel.endDate),
            onTap: () => _selectDate(context, false),
          ),
          SizedBox(width: 30.p),
          _buildQueryButton(context),
        ],
      ),
    );
  }

  // 日期选择器
  Widget _buildDateSelector(
    BuildContext context, {
    required String dateText,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.p, vertical: 8.p),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFF3568FC)),
          borderRadius: BorderRadius.circular(33.p),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              dateText,
              style: TextStyle(
                fontSize: 26.p,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF12215F),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 查询按钮
  Widget _buildQueryButton(BuildContext context) {
    final viewModel = Provider.of<QueryViewModel>(context);
    
    return Container(
      // 固定宽高以避免状态变化导致的尺寸变化
      width: 150.p, 
      height: 50.p,
      child: ElevatedButton(
        onPressed: viewModel.isLoading ? null : viewModel.fetchRecords,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF3568FC),
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(45.p),
          ),
        ),
        child: Center(
          child: viewModel.isLoading
            ? SizedBox(
                width: 42.p,
                height: 42.p,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                '查询',
                style: TextStyle(
                  fontSize: 26.p,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
        ),
      ),
    );
  }

  // 日期选择器
  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final viewModel = Provider.of<QueryViewModel>(context, listen: false);
    final initialDate = isStartDate ? viewModel.startDate : viewModel.endDate;
    
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF3568FC),
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      if (isStartDate) {
        viewModel.setStartDate(picked);
      } else {
        viewModel.setEndDate(picked);
      }
    }
  }
}