

import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';

import '../../shared/utils/asset_util.dart';

class BookAreaSelector extends StatelessWidget {
  final List<AreaOption> options;
  final String currentValue;
  final Function(String) onValueChanged;
  final double height;
  final Color backgroundColor;
  final Color activeTextColor;
  final Color inactiveTextColor;
  final Color activeIndicatorColor;
  final String? iconAssetPath;

  const BookAreaSelector({
    Key? key,
    required this.options,
    required this.currentValue,
    required this.onValueChanged,
    this.height = 100,
    this.backgroundColor = const Color(0xFFD8E9FF),
    this.activeTextColor = const Color(0xFF2A88FF),
    this.inactiveTextColor = const Color(0xFF212121),
    this.activeIndicatorColor = const Color(0xFF2A88FF),
    this.iconAssetPath,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: height.p,
      margin: EdgeInsets.symmetric(horizontal: 8.p),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(24.p),
      ),
      child: Row(
        children: options.map((option) => _buildTabItem(option)).toList(),
      ),
    );
  }

  Widget _buildTabItem(AreaOption option) {
    final bool isSelected = option.value == currentValue;
    return Expanded(
      child: InkWell(
        onTap: () => onValueChanged(option.value),
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12.p, vertical: 0.p),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 图标和文字容器
              Expanded(
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if(isSelected && iconAssetPath != null) Image.asset(
                        AssetUtil.fullPath(iconAssetPath!),
                        width: 48.p,
                        height: 48.p,
                        fit: BoxFit.contain,
                      ),
                      if(isSelected && iconAssetPath != null) SizedBox(width: 11.p),
                      Text(
                        option.label,
                        style: TextStyle(
                            color: isSelected ? activeTextColor : inactiveTextColor,
                            fontSize: 32.p,
                            fontWeight: FontWeight.w500,
                            height: 32/36
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // 下方指示条
              Container(
                height: 6.p,
                width: 80.p,
                decoration: BoxDecoration(
                  color: isSelected ? activeIndicatorColor : Colors.transparent,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(6.p),
                    topRight: Radius.circular(6.p),
                    bottomLeft: const Radius.circular(0),
                    bottomRight: const Radius.circular(0),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 区域选项类
class AreaOption {
  final String label; // 显示的标签
  final String value; // 选项的值
  const AreaOption({required this.label, required this.value});
}