import 'dart:async';
import 'package:flutter/material.dart';
import '../../core/utils/window_util.dart';
import '../utils/asset_util.dart';

class TimeDisplay extends StatelessWidget {
  final DateTime? dateTime;
  final double? width;

  const TimeDisplay({
    Key? key,
    this.dateTime,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final DateTime displayTime = dateTime ?? DateTime.now();
    final String date =
        "${displayTime.year}-${displayTime.month.toString().padLeft(2, '0')}-${displayTime.day.toString().padLeft(2, '0')}";
    final String time =
        "${displayTime.hour.toString().padLeft(2, '0')}:${displayTime.minute.toString().padLeft(2, '0')}:${displayTime.second.toString().padLeft(2, '0')}";

    return Container(
      width: width,
      padding: EdgeInsets.symmetric(horizontal: 10.p, vertical: 5.p),
      decoration: BoxDecoration(
        // color: const Color(0xFF5076D7),
        borderRadius: BorderRadius.circular(20.p),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(AssetUtil.fullPath('clock'),
                  width: 26.p, height: 26.p),
              SizedBox(width: 10.p),
              Text(
                date,
                style: TextStyle(
                    color: const Color(0xFF12215F),
                    fontSize: 26.p,
                    fontWeight: FontWeight.w500,
                    height: 40 / 26),
              ),
            ],
          ),
          SizedBox(height: 4.p),
          Container(
            width: 180.p,
            height: 38.p,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: const Color.fromRGBO(18, 33, 95, 0.3),
              borderRadius: BorderRadius.circular(100.p),
            ),
            child: Text(
              time,
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 28.p,
                  fontWeight: FontWeight.bold,
                  height: 40 / 28),
            ),
          ),
        ],
      ),
    );
  }
}

// 添加一个可以自动刷新时间的组件
class LiveTimeDisplay extends StatefulWidget {
  final bool showUpdateIcon;
  final double? width;

  const LiveTimeDisplay({
    Key? key,
    this.showUpdateIcon = true,
    this.width,
  }) : super(key: key);

  @override
  State<LiveTimeDisplay> createState() => _LiveTimeDisplayState();
}

class _LiveTimeDisplayState extends State<LiveTimeDisplay> {
  late DateTime _currentTime;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();
    // 每秒更新一次时间
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _currentTime = DateTime.now();
        });
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TimeDisplay(
      dateTime: _currentTime,
      width: widget.width,
    );
  }
}
