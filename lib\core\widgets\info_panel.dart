import 'package:flutter/material.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';


class InfoPanel extends StatelessWidget {
  final Widget child;
  final bool showCloseIcon;
  const InfoPanel({super.key, required this.child,  this.showCloseIcon = true});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          padding:
          EdgeInsets.only(left: 80.p, right: 80.p, top: 60.p, bottom: 35.p),
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(-0.8, -0.6),
              end: Alignment(0.8, 0.6),
              colors: [
                Color(0xFFB3D5FF),
                Color(0xFFE6FCF8),
              ],
            ),
          ),
          child: child,
        ),
        if (showCloseIcon) Positioned(
            top: 40.p,
            right: 40.p,
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Image.asset(
                AssetUtil.fullPath('close.png'),
                width: 48.p,
                height: 48.p,
              ),
            ))
      ],
    );
  }
}



