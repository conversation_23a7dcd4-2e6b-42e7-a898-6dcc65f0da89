import 'package:flutter/material.dart';
import '../database/db_setting_interface.dart';

class DBProvider extends ChangeNotifier implements DBSettingInterface {
  String _dbType = '';
  
  String get dbType => _dbType;

  Future<void> initDBType() async {
    // TODO: 从配置文件或其他地方获取数据库类型
    _dbType = 'sqlite';
    notifyListeners();
  }

  @override
  Future<List<Map<String, dynamic>>> querySettingBy(String type) async {
    // TODO: 实现查询设置
    return [];
  }

  @override
  Future<void> insertSetting(String type, Map<String, dynamic> data) async {
    // TODO: 实现插入设置
  }

  @override
  Future<void> updateSetting(String type, Map<String, dynamic> data) async {
    // TODO: 实现更新设置
  }

  @override
  Future<void> deleteSetting(String type) async {
    // TODO: 实现删除设置
  }
} 