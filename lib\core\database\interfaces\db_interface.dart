import 'package:base_package/base_package.dart';
import 'package:seasetting/seasetting.dart' as setting;
import 'package:seasetting/setting/mini_smart_library/models/cabinet.dart';
import '../../../features/admin/models/error.dart';
import '../../models/book.dart';
import '../../models/slot.dart';
import '../models/db_result.dart';

/// 数据库接口
abstract class DBInterface extends setting.IExternalDatabase{

  //
  // /// 按书架查询图书
  // Future<DBResult<List<BookDBData>>> queryBooksBy(List<String> doors, String table);
  /// 关闭数据库连接
  Future<void> close();
  // /// 按 书架号 查询图书 查询 slots 表
  Future<setting.DBResult<List<Book>>> queryBooksByShelfNo(String shelfNo);
  //
  // 查询正常状态的书柜
  Future<setting.DBResult<List<Cabinet>>> getActiveCabinets();
  // /// 插入测试数据
  // Future<setting.DBResult<void>> insertTestData();
  // /// 删除测试数据
  // Future<setting.DBResult<void>> deleteTestData();
  /// 删除数据库文件
  Future<setting.DBResult<void>> deleteDatabase();

  Future<setting.DBResult<void>> setBookBoxCount( int count);
  Future<setting.DBResult<int>> getBookBoxCount();

  Future<setting.DBResult<void>> updateErrorRecordStatus({
    required int id,
    required String status,
    String? operator,
  });
  Future<setting.DBResult<List<Map<String, dynamic>>>> getErrorRecords({String? status});
  Future<setting.DBResult<void>> addErrorRecord(ErrorRecord record);
  Future<setting.DBResult<List<Slot>>> getSlotsByStatusAndType({
    required String cabinetNo,  // 添加书柜参数
    String? status,
    String? slotType,
  });
  Future<setting.DBResult<void>> saveBookAndUpdateSlot({
    required String barcode,
    required String slotNo,
    required String status,
    required Book bookInfo,
  });
  Future<setting.DBResult<void>> delBookAndUpdateSlot({
    required String slotNo,
    required String barcode,
  });

  Future<setting.DBResult<List<Map<String, dynamic>>>> querySlotsByCabinetAndStatus(
      String cabinetNo, String status, String? slotType);

  Future<setting.DBResult<Map<String, dynamic>>> querySlot(String slotNo);

  Future<setting.DBResult<List<Map<String, dynamic>>>> querySlotsByBarcode(String barcode);

  Future<setting.DBResult<List<Map<String, dynamic>?>>> queryEmptySlot({String type = 'rfid'});

  Future<setting.DBResult<String>> setSystemType(String systemType);
  Future<setting.DBResult<String>> getSystemType();

  /// 获取书柜排序方式
  Future<setting.DBResult<int?>> getCabinetOrderMode();

  /// 设置书柜排序方式
  Future<setting.DBResult<void>> setCabinetOrderMode(int value);

  /// 添加认证记录
  Future<setting.DBResult<void>> addAccessLog({
    required String name,
    required String readerCardNo,
    required DateTime openTime,
    String? authMethod,
    String status,
    String? remark,
  });

  /// 根据时间范围查询认证记录
  Future<setting.DBResult<List<Map<String, dynamic>>>> queryAccessLogs({
    required DateTime startDate,
    required DateTime endDate,
    int? limit, 
    int? offset,
  });

  /// 删除指定日期前的认证记录
  Future<setting.DBResult<int>> cleanupOldAccessLogs(DateTime beforeDate);
} 