import 'dart:async';

import 'package:flutter/material.dart';

class CountdownProvider extends ChangeNotifier {
  Timer? _timer;
  int _seconds = 60;
  int _initialDuration = 60;
  VoidCallback? _onComplete;

  int get seconds => _seconds;

  void start({
    int duration = 60,
    VoidCallback? onComplete,
  }) {
    _initialDuration = duration;
    _seconds = duration;
    _onComplete = onComplete;
    _startTimer();
  }

  void stop() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _timer?.cancel();
      _seconds = 60;  // 设置为 00
      notifyListeners();
    });

  }

  void reset([int? duration]) {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _timer?.cancel();
    _seconds = duration ?? _initialDuration;  // 使用传入的值或默认值
    notifyListeners();
    _startTimer();
  });
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_seconds > 0) {
        _seconds--;
        notifyListeners();
      } else {
        _timer?.cancel();
        _onComplete?.call();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}