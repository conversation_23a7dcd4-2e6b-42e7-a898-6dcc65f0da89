import 'package:flutter/material.dart';
import 'package:a3g/core/widgets/warning_info_tip.dart';

import '../../../../core/utils/window_util.dart';

class AdminReaderCardLoginView extends StatelessWidget {
  const AdminReaderCardLoginView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 800.p,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(35.p),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding:  EdgeInsets.symmetric(horizontal: 100.p),
                child: const WarningInfoTip(message: '请将您的读者证放于指定区域'),
              ),
              SizedBox(height: 80.p),
              Image.asset(
                'images/common/刷读者证.gif',
                width: 760.p,
                height: 500.p,
              ),
            ],
          ),
        ),
        // SizedBox(height: 40.p),
      ],
    );
  }
} 