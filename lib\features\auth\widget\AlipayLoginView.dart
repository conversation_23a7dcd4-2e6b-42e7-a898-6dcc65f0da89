// ignore_for_file: must_be_immutable

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:seasetting/seasetting.dart';
import '../../../generated/l10n.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';

class AlipayLoginView extends StatelessWidget {
  AlipayLoginView(this.data, {Key? key}) : super(key: key);
  String data;

  @override
  Widget build(BuildContext context) {
    return Consumer2<CurrentLayoutProvider, SettingProvider>(
        builder: (context, layoutProvider, settingProvider, child) {
      if (settingProvider.getTheme() == ThemeType.ximalaya) {
        return AlipayLoginXmlyView(data);
      } else if (settingProvider.getTheme() == ThemeType.child) {
        return AlipayLoginChildView(data);
      } else {
        return AlipayLoginBlueView(data);
      }
    });
  }
}

class AlipayLoginBlueView extends StatelessWidget {
  AlipayLoginBlueView(this.data, {Key? key}) : super(key: key);
  String data;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.alipayQRCode == AuthLoginTypeMap[element.type]).firstOrNull;

    return ListView(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title:S.current.alipay_login,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: BPUtils.c_ff12215F,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 70.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 480.p,
              padding: EdgeInsets.only(
                right: 40.p,
                left: 40.p,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(40.p),
                border: Border.all(
                  color: BPUtils.c_FFB7CDFF,
                  width: 5.p,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 10.p),
                  QrImageView(
                    data: data,
                    size: 400.p,
                    version: QrVersions.auto,
                    backgroundColor: Colors.white,
                  ),
                  SizedBox(height: 10.p),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.p,
                    ),
                    child: Row(
                      children: [
                        Image.asset(
                          AssetUtil.fullPath('alipay_scan'),
                          width: 68.p,
                          height: 68.p,
                          key: const Key('alipay_scan'),
                        ),
                        SizedBox(width: 18.p),
                        Expanded(
                            child: Text(
                              (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle: S.current.alipay_scan_guide,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 30.p,
                            color: BPUtils.c_FF7A8499,
                          ),
                          textAlign: TextAlign.start,
                        ))
                      ],
                    ),
                  ),
                  SizedBox(height: 40.p),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class AlipayLoginChildView extends StatelessWidget {
  AlipayLoginChildView(this.data, {Key? key}) : super(key: key);
  String data;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.alipayQRCode == AuthLoginTypeMap[element.type]).firstOrNull;

  return ListView(
      shrinkWrap: true,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title:S.current.alipay_login,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 34.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ChildThemeYellowBorder(
              child: Container(
                width: 480.p,
                padding: EdgeInsets.only(
                  right: 40.p,
                  left: 40.p,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.circular(40.p),
                ),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    SizedBox(height: 10.p),
                    QrImageView(
                      data: data,
                      size: 400.p,
                      version: QrVersions.auto,
                      backgroundColor: Colors.white,
                    ),
                    SizedBox(height: 10.p),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 15.p,
                      ),
                      child: Row(
                        children: [
                          Image.asset(
                            AssetUtil.fullPath('alipay_scan'),
                            width: 68.p,
                            height: 68.p,
                            key: const Key('alipay_scan'),
                          ),
                          SizedBox(width: 18.p),
                          Expanded(
                              child: Text(
                                (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:S.current.alipay_scan_guide,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 30.p,
                              color: BPUtils.c_FF848383,
                            ),
                            textAlign: TextAlign.start,
                          ))
                        ],
                      ),
                    ),
                    SizedBox(height: 46.p),
                  ],
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 40.p),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 150.p),
          decoration: BoxDecoration(
              color: BPUtils.c_FFCCEFFF,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.p),
                topRight: Radius.circular(40.p),
                bottomRight: Radius.circular(40.p),
                bottomLeft: Radius.circular(10.p),
              )),
          child: Padding(
            padding: EdgeInsets.symmetric(
                vertical: 25.p,
                horizontal: 40.p),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.current.borrow_with_alipay_credit,
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.normal,
                    fontSize: 32.p,
                  ),
                ),
                SizedBox(height: 10.p),
                Text(
                  S.current.auth_fail_advice,
                  style: TextStyle(
                    color: BPUtils.c_FF1D62FD,
                    fontWeight: FontWeight.normal,
                    fontSize: 22.p,
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 40.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 80.p,
              decoration: BoxDecoration(
                color: BPUtils.c_FF36B6F3,
                borderRadius: BorderRadius.circular(
                  40.p,
                ),
              ),
              alignment: Alignment.center,
              padding:
                  EdgeInsets.symmetric(horizontal: 50.p),
              child: Row(
                children: [
                  Image.asset(
                    AssetUtil.fullPath('refreshIcon'),
                    width: 38.p,
                    height: 38.p,
                    key: const Key('refreshIcon'),
                  ),
                  SizedBox(width: 10.p),
                  Text(
                    S.current.regenerate,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 36.p,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 40.p),
      ],
    );
  }
}

class AlipayLoginXmlyView extends StatelessWidget {
  AlipayLoginXmlyView(this.data, {Key? key}) : super(key: key);
  String data;

  @override
  Widget build(BuildContext context) {
    ReaderAuthTitleConfig? titleData = Get.context?.read<SettingProvider>().readerConfigData?.authTitleConfig.where((element) => AuthLoginType.alipayQRCode == AuthLoginTypeMap[element.type]).firstOrNull;

    return ListView(
      shrinkWrap: true,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                (titleData?.title.isNotEmpty??false) ? titleData!.title:S.current.alipay_login,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: BPUtils.c_ff12215F,
                  fontWeight: FontWeight.w600,
                  fontSize: 48.p,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 70.p),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 480.p,
              padding: EdgeInsets.only(
                right: 40.p,
                left: 40.p,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(40.p),
                border: Border.all(
                  color: BPUtils.c_FFB7CDFF,
                  width: 5.p,
                ),
              ),
              child: ListView(
                shrinkWrap: true,
                children: [
                  SizedBox(height: 10.p),
                  QrImageView(
                    data: data,
                    size: 400.p,
                    version: QrVersions.auto,
                    backgroundColor: Colors.white,
                  ),
                  SizedBox(height: 10.p),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.p,
                    ),
                    child: Row(
                      children: [
                        Image.asset(
                          AssetUtil.fullPath('alipay_scan'),
                          width: 68.p,
                          height: 68.p,
                          key: const Key('alipay_scan'),
                        ),
                        SizedBox(width: 18.p),
                        Expanded(
                            child: Text(
                              (titleData?.subTitle.isNotEmpty??false) ? titleData!.subTitle:S.current.alipay_scan_guide,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 30.p,
                            color: BPUtils.c_FF7A8499,
                          ),
                          textAlign: TextAlign.start,
                        ))
                      ],
                    ),
                  ),
                  SizedBox(height: 40.p),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class ChildThemeYellowBorder extends StatelessWidget {
  ChildThemeYellowBorder({this.child,Key? key}) : super(key: key);
  Widget? child;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: BPUtils.c_FFFEC72D,
        borderRadius: BorderRadius.circular(40.p),
      ),
      padding: EdgeInsets.all(8.p),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(40.p),
        ),
        clipBehavior: Clip.antiAlias,
        child: child,
      ),
    );
  }
}
