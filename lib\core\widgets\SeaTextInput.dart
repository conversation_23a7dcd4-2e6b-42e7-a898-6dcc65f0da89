import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/window_util.dart';
import '../../shared/utils/asset_util.dart';
import 'SeaKeyboard.dart';

class SeaTextInput extends StatefulWidget {
  SeaTextInput(
      {this.focusNode,
      this.controller,
      this.style,
      this.textAlign = TextAlign.start,
      this.enabled,
      this.keyboardType,
      this.onChanged,
      this.onSubmitted,
      this.hintText,
      this.onTap,
      this.needObscure = false,
      this.icon,
      this.height,
      this.borderRadius,
      this.backgroundColor,
      this.padding,
      Key? key})
      : super(key: key);
  String? icon;
  double? height;
  double? borderRadius;
  Color? backgroundColor;
  EdgeInsets? padding;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final TextStyle? style;
  final TextAlign textAlign;
  final bool? enabled;
  TextInputType? keyboardType;
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final GestureTapCallback? onTap;
  bool needObscure;

  @override
  State<SeaTextInput> createState() => _SeaTextInputState();
}

class _SeaTextInputState extends State<SeaTextInput> {
  RxBool hasFocus = false.obs;
  RxBool obscureText = false.obs;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    widget.focusNode?.addListener(focusListener);
    obscureText.value = widget.needObscure;
  }

  focusListener() {
    hasFocus.value = widget.focusNode?.hasFocus ?? false;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    widget.focusNode?.removeListener(focusListener);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
          height: widget.height ?? 100.p,
          padding: widget.padding ?? EdgeInsets.symmetric(horizontal: 50.p),
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? BPUtils.c_FFF6F7FA,
            borderRadius: BorderRadius.circular(
                widget.borderRadius ?? 50.p),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Padding(
              //   padding: EdgeInsets.only(
              //     right: 20.p,
              //   ),
              //   child: (widget.icon?.isEmpty ?? true)
              //       ? Container()
              //       : Image.asset(
              //           AssetUtil.fullPath(widget.icon!),
              //           width: 48.p,
              //           height: 48.p,
              //           fit: BoxFit.fill,
              //         ),
              // ),
              Expanded(
                child: TextField(
                  // readOnly: true,
                  obscureText: obscureText.value,
                  showCursor: true,
                  controller: widget.controller,
                  focusNode: widget.focusNode,
                  onTap: () {
                    SeaKeyboard.show(
                      type: SeaKeyboardType.number,
                      controller: widget.controller,
                      focus: widget.focusNode,
                      onSubmitted: (ctr){
                        widget.onSubmitted?.call(widget.controller?.text ?? '');
                      }
                    );
                  },
                  textAlign: TextAlign.start,
                  textAlignVertical: TextAlignVertical.center,
                  style: TextStyle(
                    color: BPUtils.c_FF212121,
                    fontSize: 32.p,
                  ),
                  decoration: InputDecoration(
                    hintText: widget.hintText,
                    hintStyle: TextStyle(
                      color: BPUtils.c_FF7A8499,
                      fontSize: 32.p,
                    ),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide.none,
                      gapPadding: 0,
                    ),
                    // fillColor: Colors.green,
                    filled: true,
                    fillColor: Colors.transparent,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                // child: SizedBox(
                //   height: WindowUtill.scaleSize(100),
                //   child: ,
                // ),
              ),
              Row(
                children: [
                  Visibility(
                    visible: widget.needObscure,
                    child: InkWell(
                      onTap: () {
                        obscureText.value = !(obscureText.value);
                        obscureText.refresh();
                        print(obscureText.value);
                      },
                      child: Padding(
                        padding:
                            EdgeInsets.only(right: 25.p),
                        child: Image.asset(
                          AssetUtil.fullPath(obscureText.value ? 'hide_icon' : 'show_icon'),
                          width: 44.p,
                          height: 44.p,
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      widget.controller?.text = '';
                      setState(() {});
                    },
                    child: Padding(
                      padding: EdgeInsets.all(0.p),
                      child: Image.asset(
                        AssetUtil.fullPath('清除'),
                        width: 44.p,
                        height: 44.p,
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ));
  }
}
