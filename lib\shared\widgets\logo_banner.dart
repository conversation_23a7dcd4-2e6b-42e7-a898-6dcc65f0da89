import 'package:flutter/material.dart';

import '../../core/utils/window_util.dart';
import '../utils/asset_util.dart';

class LogoBanner extends StatelessWidget {
  final Widget tailWidget;
  final Widget? title;

  const LogoBanner({
    super.key, 
    this.tailWidget = const SizedBox(),
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1000.p,
      height: 140.p,
      decoration:  BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AssetUtil.fullPath('logo_banner_bg.png')),
        )
      ),
        child: Padding(
          padding:  EdgeInsets.symmetric(horizontal: 40.p),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  title ??  Text(
                    '自助借还书机',
                    style: TextStyle(
                      fontSize: 40.p,
                      color: const Color(0xFF222222),
                      fontWeight: FontWeight.bold,
                      height: 1
                    ),
                  ),
                   SizedBox(height: 15.p),
                   Text('SELF-CHECK IN/OUT MACHINE',style: TextStyle(
                      fontSize: 22.p,
                      color: const Color(0xFF222222),
                      fontWeight: FontWeight.w500,
                      height: 1
                  )),
                ],
              ),
              tailWidget,
            ],
          ),
        ),
    );
  }
}
