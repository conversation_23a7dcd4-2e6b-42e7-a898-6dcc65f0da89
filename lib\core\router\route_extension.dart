import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';


// lib/core/router/route_extension.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:seasetting/seasetting.dart';

import '../../features/login/models/operation_type.dart';
import 'app_router.dart';

extension RouterExtension on BuildContext {
  /// 基本导航方法
  void push(String location) => go(location);

  /// 带命名的导航方法
  // void pushNamed(
  //     String name, {
  //       Map<String, String> pathParameters = const <String, String>{},
  //       Map<String, dynamic> queryParameters = const <String, dynamic>{},
  //       Object? extra,
  //     }) {
  //   goNamed(
  //     name,
  //     pathParameters: pathParameters,
  //     queryParameters: queryParameters,
  //     extra: extra,
  //   );
  // }

  /// 替换当前路由
  void replaceTo(String location) => go(location);

  /// 替换当前路由（命名路由）
  void replaceNamed(
      String name, {
        Map<String, String> pathParameters = const <String, String>{},
        Map<String, dynamic> queryParameters = const <String, dynamic>{},
        Object? extra,
      }) {
    goNamed(
      name,
      pathParameters: pathParameters,
      queryParameters: queryParameters,
      extra: extra,
    );
  }

  /// 返回上一页
  void pop<T extends Object?>([T? result]) => Navigator.pop(this, result);

  /// 返回到首页
  void popToHome() => go('/home');

  /// 可以返回时才返回，否则去首页
  void popOrHome() => canPop ? pop() : go('/');

  /// 获取当前路由名称
  String? get currentRouteName => GoRouterState.of(this).name;

  /// 获取当前路由路径
  String get currentPath => GoRouterState.of(this).uri.toString();

  /// 获取路由参数
  Map<String, String> get pathParameters => GoRouterState.of(this).pathParameters;

  /// 获取查询参数
  Map<String, String> get queryParameters => GoRouterState.of(this).uri.queryParameters;

  /// 获取额外数据
  Object? get extra => GoRouterState.of(this).extra;

  /// 检查是否可以返回
  bool get canPop => Navigator.canPop(this);

  /// 返回到指定路由
  void popUntil(String routeName) {
    if (!canPop) return;
    Navigator.popUntil(this, (route) {
      return route.settings.name == routeName;
    });
  }

  /// 返回指定次数
  void popTimes(int times) {
    if (!canPop) return;
    int count = 0;
    Navigator.popUntil(this, (route) {
      return count++ == times;
    });
  }

  /// 刷新当前路由
  void refresh() => go(currentPath);

  /// 带结果返回
  void popWithResult<T extends Object?>(T result) => pop<T>(result);

  /// 带确认的返回
  Future<bool> maybePop<T extends Object?>([T? result]) async {
    return Navigator.maybePop(this, result);
  }



  void pushLoginType(OperationType type) {
    pushNamed('loginType', extra: type);
  }
  void pushPreview() {
    pushNamed('preview');
  }
  void pushTestPage() {
    pushNamed('test');
  }
  void pushAuth({
    required OperationType operationType,
    required AuthLoginType authLoginType,
  }) {
    pushNamed(
      'auth',
      extra: {
        'operationType': operationType,
        'authLoginType': authLoginType,
      },
    );
  }

  int getPopCountToHome() {
    final router = GoRouter.of(this);
    final configuration = router.routerDelegate.currentConfiguration;
    final matches = configuration.matches;
    int homeIndex = -1;
    for (int i = 0; i < matches.length; i++) {
      if (matches[i].matchedLocation == '/home') {
        homeIndex = i;
        break;
      }
    }
    if (homeIndex >= 0) {
      return matches.length - homeIndex - 1;
    }
    return 0;

  }
  // 如模态框弹出 modalPopCount > 0, 一层模态框弹出 modalPopCount = 1
  void backToHome({int modalPopCount = 0}) {
    int popCount = getPopCountToHome() + modalPopCount;
    if (popCount > 0) {
      for (int i = 0; i < popCount; i++) {
        pop();
      }
    }
  }
}