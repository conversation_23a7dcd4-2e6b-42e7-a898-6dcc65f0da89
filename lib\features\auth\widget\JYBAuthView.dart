import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';

/// 借阅宝认证组件
class JYBAuthView extends StatelessWidget {
  final AuthLoginType authLoginType;
  final VoidCallback? onTap;

  const JYBAuthView({
    super.key,
    required this.authLoginType,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingProvider>(
      builder: (context, settingProvider, child) {
        // 获取动态标题配置
        String title = _getDynamicTitle(settingProvider) ?? '请使用借阅宝';
        
        return Center(
          child: GestureDetector(
            onTap: onTap,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 借阅宝图标
                Container(
                  width: 180.p,
                  height: 180.p,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.p),
                    gradient: const LinearGradient(
                      colors: [Color(0xFF4F83FC), Color(0xFF6C9DFF)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF4F83FC).withOpacity(0.3),
                        blurRadius: 20.p,
                        offset: Offset(0, 10.p),
                      ),
                    ],
                  ),
                  child: Image.asset(
                    AssetUtil.fullPath('borrow_treasure_auth.png'),
                    width: 180.p,
                    height: 180.p,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.book,
                        size: 80.p,
                        color: Colors.white,
                      );
                    },
                  ),
                ),
                SizedBox(height: 50.p),
                
                // 主标题
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 52.p,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF222222),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 20.p),
                
                // 副标题
                Text(
                  '借阅宝认证',
                  style: TextStyle(
                    fontSize: 36.p,
                    color: const Color(0xFF666666),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 40.p),
                
                // 提示信息
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 80.p, vertical: 30.p),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(12.p),
                    border: Border.all(
                      color: const Color(0xFFE9ECEF),
                      width: 1.p,
                    ),
                  ),
                  child: Text(
                    '请打开借阅宝APP，使用扫码功能扫描屏幕或出示借阅码',
                    style: TextStyle(
                      fontSize: 28.p,
                      color: const Color(0xFF666666),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 获取动态标题配置
  String? _getDynamicTitle(SettingProvider settingProvider) {
    try {
      final readerConfig = settingProvider.readerConfigData;
      final authTitleConfig = readerConfig?.authTitleConfig;
      
      if (authTitleConfig != null && authTitleConfig.isNotEmpty) {
        ReaderAuthTitleConfig? titleData;
        try {
          titleData = authTitleConfig.firstWhere(
            (element) => authLoginType == AuthLoginTypeMap[element.type],
          );
        } catch (e) {
          titleData = null;
        }
        
        if (titleData != null && titleData.title?.isNotEmpty == true) {
          return titleData.title!;
        }
      }
    } catch (e) {
      print('获取借阅宝认证页面动态标题失败: $e');
    }
    
    return null;
  }
} 