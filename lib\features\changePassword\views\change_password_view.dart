import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';

import '../../../core/router/app_router.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/widgets/base_page2.dart';
import '../../../core/widgets/countdown_timer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/footer.dart';
import '../../../shared/widgets/logo_banner.dart';
import '../../home/<USER>/home_view.dart';
import 'widgets/ChangePasswordVPage.dart';

class ChangePasswordView extends StatelessWidget {
  const ChangePasswordView({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage2(
      topWrapper: _buildHeader(),
      mainWrapper: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _buildMainContent(),
      ),
    );
  }
    Widget _buildHeader() {
    return const HeaderWidget();
  }


  Widget _buildMainContent() {
    return const ChangePasswordContent();
  }
}
