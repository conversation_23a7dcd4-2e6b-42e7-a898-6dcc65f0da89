import 'package:flutter/foundation.dart';
import 'package:sea_socket/sea_socket.dart';

/// 认证成功后操作处理器接口
abstract class PostAuthHandler {
  /// 优先级 - 数字越小越先执行
  int get priority;
  
  /// 处理认证成功事件
  /// 返回true继续执行链，false中断执行
  Future<bool> handle(Sip2PatronInfoData readerInfo, String authMethod);
}

/// 认证成功后服务
class PostAuthService {
  PostAuthService._();
  static final instance = PostAuthService._();
  
  final List<PostAuthHandler> _handlers = [];
  
  /// 注册处理器
  void registerHandler(PostAuthHandler handler) {
    _handlers.add(handler);
    // 按优先级排序
    _handlers.sort((a, b) => a.priority.compareTo(b.priority));
    debugPrint('注册认证后处理器: ${handler.runtimeType}，当前共有${_handlers.length}个处理器');
  }
  
  /// 移除处理器
  void removeHandler(PostAuthHandler handler) {
    _handlers.remove(handler);
  }
  
  /// 执行所有处理器
  Future<void> executeHandlers(Sip2PatronInfoData readerInfo, String authMethod) async {
    debugPrint('执行认证后处理流程，共${_handlers.length}个处理器');
    
    for (final handler in _handlers) {
      debugPrint('执行处理器: ${handler.runtimeType}');
      try {
        bool continueChain = await handler.handle(readerInfo, authMethod);
        if (!continueChain) {
          debugPrint('处理器${handler.runtimeType}中断了处理链');
          break;
        }
      } catch (e) {
        debugPrint('处理器${handler.runtimeType}执行出错: $e');
        // 继续执行其他处理器，不中断整个链
      }
    }
    
    debugPrint('认证后处理流程执行完毕');
  }
} 