import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import 'api_exception.dart';
import 'api_response.dart';
import 'interceptors/error_interceptor.dart';
import 'interceptors/log_interceptor.dart';
import 'interceptors/header_interceptor.dart';

/// Dio客户端封装
/// 提供统一的网络请求接口
class DioClient {
  /// 单例模式
  static final DioClient _instance = DioClient._internal();
  factory DioClient() => _instance;

  late Dio _dio;
  
  /// 基础URL
  String baseUrl = '';
  
  /// 连接超时时间（毫秒）
  int connectTimeout = 15000;
  
  /// 接收超时时间（毫秒）
  int receiveTimeout = 15000;
  
  /// 是否显示日志
  bool showLog = kDebugMode;

  DioClient._internal() {
    final options = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: Duration(milliseconds: connectTimeout),
      receiveTimeout: Duration(milliseconds: receiveTimeout),
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
    );

    _dio = Dio(options);
    
    // 添加拦截器
    _dio.interceptors.add(HeaderInterceptor());
    _dio.interceptors.add(ErrorInterceptor());
    
    if (showLog) {
      _dio.interceptors.add(LoggingInterceptor());
    }
  }

  /// 初始化Dio客户端
  /// [baseUrl] 基础URL
  /// [connectTimeout] 连接超时时间（毫秒）
  /// [receiveTimeout] 接收超时时间（毫秒）
  /// [showLog] 是否显示日志
  void init({
    required String baseUrl,
    int? connectTimeout,
    int? receiveTimeout,
    bool? showLog,
    Map<String, dynamic>? headers,
  }) {
    this.baseUrl = baseUrl;
    this.connectTimeout = connectTimeout ?? this.connectTimeout;
    this.receiveTimeout = receiveTimeout ?? this.receiveTimeout;
    this.showLog = showLog ?? this.showLog;

    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = Duration(milliseconds: this.connectTimeout);
    _dio.options.receiveTimeout = Duration(milliseconds: this.receiveTimeout);
    
    if (headers != null) {
      _dio.options.headers.addAll(headers);
    }
  }

  /// 获取Dio实例
  Dio get dio => _dio;

  /// GET请求
  /// [path] 请求路径
  /// [queryParameters] 查询参数
  /// [options] 请求选项
  /// [cancelToken] 取消令牌
  /// [fromJsonT] 从JSON映射转换为T类型的函数
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T? Function(dynamic)? fromJsonT,
  }) async {
    try {
      final response = await _dio.get<Map<String, dynamic>>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      
      if (response.statusCode == 200 && response.data != null) {
        return ApiResponse<T>.fromJson(
          response.data!, 
          fromJsonT ?? ((dynamic json) => json as T?),
        );
      }
      
      return ApiResponse<T>(
        errorCode: response.statusCode ?? -1,
        message: '请求失败: ${response.statusCode}',
      );
    } on DioException catch (e) {
      final error = ApiException.fromDioError(e);
      return ApiResponse<T>(
        errorCode: -1,
        message: error.message,
      );
    } catch (e) {
      return ApiResponse<T>(
        errorCode: -1,
        message: e.toString(),
      );
    }
  }

  /// POST请求
  /// [path] 请求路径
  /// [data] 请求数据
  /// [queryParameters] 查询参数
  /// [options] 请求选项
  /// [cancelToken] 取消令牌
  /// [fromJsonT] 从JSON映射转换为T类型的函数
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T? Function(dynamic)? fromJsonT,
  }) async {
    try {
      final response = await _dio.post<Map<String, dynamic>>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      
      if (response.statusCode == 200 && response.data != null) {
        return ApiResponse<T>.fromJson(
          response.data!, 
          fromJsonT ?? ((dynamic json) => json as T?),
        );
      }
      
      return ApiResponse<T>(
        errorCode: response.statusCode ?? -1,
        message: '请求失败: ${response.statusCode}',
      );
    } on DioException catch (e) {
      final error = ApiException.fromDioError(e);
      return ApiResponse<T>(
        errorCode: -1,
        message: error.message,
      );
    } catch (e) {
      return ApiResponse<T>(
        errorCode: -1,
        message: e.toString(),
      );
    }
  }

  /// PUT请求
  /// [path] 请求路径
  /// [data] 请求数据
  /// [queryParameters] 查询参数
  /// [options] 请求选项
  /// [cancelToken] 取消令牌
  /// [fromJsonT] 从JSON映射转换为T类型的函数
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T? Function(dynamic)? fromJsonT,
  }) async {
    try {
      final response = await _dio.put<Map<String, dynamic>>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      
      if (response.statusCode == 200 && response.data != null) {
        return ApiResponse<T>.fromJson(
          response.data!, 
          fromJsonT ?? ((dynamic json) => json as T?),
        );
      }
      
      return ApiResponse<T>(
        errorCode: response.statusCode ?? -1,
        message: '请求失败: ${response.statusCode}',
      );
    } on DioException catch (e) {
      final error = ApiException.fromDioError(e);
      return ApiResponse<T>(
        errorCode: -1,
        message: error.message,
      );
    } catch (e) {
      return ApiResponse<T>(
        errorCode: -1,
        message: e.toString(),
      );
    }
  }

  /// DELETE请求
  /// [path] 请求路径
  /// [data] 请求数据
  /// [queryParameters] 查询参数
  /// [options] 请求选项
  /// [cancelToken] 取消令牌
  /// [fromJsonT] 从JSON映射转换为T类型的函数
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T? Function(dynamic)? fromJsonT,
  }) async {
    try {
      final response = await _dio.delete<Map<String, dynamic>>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      
      if (response.statusCode == 200 && response.data != null) {
        return ApiResponse<T>.fromJson(
          response.data!, 
          fromJsonT ?? ((dynamic json) => json as T?),
        );
      }
      
      return ApiResponse<T>(
        errorCode: response.statusCode ?? -1,
        message: '请求失败: ${response.statusCode}',
      );
    } on DioException catch (e) {
      final error = ApiException.fromDioError(e);
      return ApiResponse<T>(
        errorCode: -1,
        message: error.message,
      );
    } catch (e) {
      return ApiResponse<T>(
        errorCode: -1,
        message: e.toString(),
      );
    }
  }
} 