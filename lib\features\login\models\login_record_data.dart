class LoginRecordData {
  final String barcode;
  final String uid;
  final String readerName;
  final String result;
  final String readerId;
  final String reason;
  final int timestamp;

  LoginRecordData({
    required this.barcode,
    required this.uid,
    required this.readerName,
    required this.result,
    required this.readerId,
    required this.reason,
    required this.timestamp,
  });
}