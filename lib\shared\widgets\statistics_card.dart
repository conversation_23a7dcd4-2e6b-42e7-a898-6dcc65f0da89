import 'package:flutter/material.dart';

import '../../core/utils/window_util.dart';

class StatisticsCard extends StatelessWidget {
  final int borrowCount;
  final int availableCount;
  final int overdueCount;
  final double deposit;
  final double debt;
  final double balance;

  // 自定义选项
  final List<Color>? gradientColors;
  final String? topLeftLabel;
  final String? topMiddleLabel;
  final String? topRightLabel;
  final String? bottomLeftLabel;
  final String? bottomMiddleLabel;
  final String? bottomRightLabel;
  final double borderRadius;
  final EdgeInsets margin;
  final EdgeInsets padding;

  const StatisticsCard({
    Key? key,
    required this.borrowCount,
    required this.availableCount,
    required this.overdueCount,
    required this.deposit,
    required this.debt,
    required this.balance,
    this.gradientColors,
    this.topLeftLabel,
    this.topMiddleLabel,
    this.topRightLabel,
    this.bottomLeftLabel,
    this.bottomMiddleLabel,
    this.bottomRightLabel,
    this.borderRadius = 16.0,
    this.margin = const EdgeInsets.all(10.0),
    this.padding = const EdgeInsets.symmetric(vertical: 0.0, horizontal: 0.0),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final defaultGradientColors = [
      const Color(0xFF4B97FF),
      const Color(0xFF7AB6FF),
    ];

    return Container(
      height: 280.p,
      margin:  EdgeInsets.symmetric(vertical: 0.0, horizontal: 40.p),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/card_bg.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // 上半部分数据
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(borrowCount.toString(), topLeftLabel ?? '现借总数'),
              _buildStatItem(
                  availableCount.toString(), topMiddleLabel ?? '可借总数'),
              _buildStatItem(
                  overdueCount.toString(), topRightLabel ?? '超期文献数'),
            ],
          ),

          // 下半部分数据
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(
                  deposit.toStringAsFixed(2), bottomLeftLabel ?? '押金'),
              _buildStatItem(
                  debt.toStringAsFixed(1), bottomMiddleLabel ?? '欠款'),
              _buildStatItem(
                  balance.toStringAsFixed(1), bottomRightLabel ?? '预存费余额'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Expanded(
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
                color: Colors.white,
                fontSize: 48.p,
                fontWeight: FontWeight.w500,
                height: 1),
          ),
           SizedBox(height: 16.p),
          Text(
            label,
            style: TextStyle(
                color: Colors.white,
                fontSize: 26.p,
                fontWeight: FontWeight.w500,
                height: 1),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
