import 'package:dio/dio.dart';

/// 错误拦截器
/// 用于处理请求和响应错误
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 处理错误，例如针对特定状态码的处理
    switch (err.type) {
      case DioExceptionType.badResponse:
        if (err.response?.statusCode == 401) {
          // 可以在这里处理401未授权错误，例如刷新token或重定向到登录页
          // 如果有必要的话，可以在此修改err或创建新的DioError
        }
        break;
      default:
        break;
    }
    
    // 继续错误处理
    super.onError(err, handler);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 处理特定的响应情况
    // 例如检查自定义API错误代码
    if (response.data is Map<String, dynamic>) {
      final Map<String, dynamic> data = response.data as Map<String, dynamic>;
      
      // 示例：检查API返回的错误码
      if (data.containsKey('errorCode') && data['errorCode'] != 0) {
        final String errorMsg = data['message'] as String? ?? '未知错误';
        
        // 创建一个新的DioError并通过handler.reject传递
        handler.reject(
          DioException(
            requestOptions: response.requestOptions,
            response: response,
            error: errorMsg,
            type: DioExceptionType.badResponse,
          ),
        );
        return;
      }
    }
    
    // 正常响应，继续处理
    super.onResponse(response, handler);
  }
} 