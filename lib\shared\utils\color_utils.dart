import 'package:flutter/material.dart';

class ColorUtils {
  /// 从十六进制字符串创建颜色
  /// 支持格式：
  /// - #RGB
  /// - #RGBA
  /// - #RRGGBB
  /// - #RRGGBBAA
  static Color fromHex(String hexString) {
    // 移除可能存在的#号
    final hex = hexString.replaceAll('#', '');
    
    // 处理不同长度的十六进制
    if (hex.length == 3) {
      // #RGB -> #RRGGBB
      final r = hex[0];
      final g = hex[1];
      final b = hex[2];
      return Color(int.parse('FF$r$r$g$g$b$b', radix: 16));
    }
    
    if (hex.length == 4) {
      // #RGBA -> #RRGGBBAA
      final r = hex[0];
      final g = hex[1];
      final b = hex[2];
      final a = hex[3];
      return Color(int.parse('$a$a$r$r$g$g$b$b', radix: 16));
    }
    
    if (hex.length == 6) {
      // #RRGGBB
      return Color(int.parse('FF$hex', radix: 16));
    }
    
    if (hex.length == 8) {
      // #RRGGBBAA
      return Color(int.parse(hex, radix: 16));
    }
    
    throw ArgumentError('Invalid hex color format: $hexString');
  }

  /// 转换颜色为十六进制字符串
  static String toHex(Color color, {bool withAlpha = true, bool withHash = true}) {
    final prefix = withHash ? '#' : '';
    if (withAlpha) {
      return '$prefix${color.value.toRadixString(16).padLeft(8, '0')}';
    }
    return '$prefix${color.value.toRadixString(16).padLeft(6, '0').substring(2)}';
  }

  /// 调整颜色亮度
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// 调整颜色暗度
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// 混合两种颜色
  static Color blend(Color color1, Color color2, [double weight = 0.5]) {
    assert(weight >= 0 && weight <= 1);
    final w1 = weight;
    final w2 = 1 - weight;
    return Color.fromARGB(
      (color1.alpha * w1 + color2.alpha * w2).round(),
      (color1.red * w1 + color2.red * w2).round(),
      (color1.green * w1 + color2.green * w2).round(),
      (color1.blue * w1 + color2.blue * w2).round(),
    );
  }

  /// 判断颜色是否为亮色
  static bool isLight(Color color) {
    return color.computeLuminance() > 0.5;
  }

  /// 获取对比色
  static Color getContrastColor(Color color) {
    return isLight(color) ? Colors.black : Colors.white;
  }
}