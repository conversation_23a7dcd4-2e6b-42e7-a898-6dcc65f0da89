import 'package:flutter/material.dart';

import '../utils/window_util.dart';

class LibraryCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget imageWidget;
  final BoxDecoration decoration;
  final VoidCallback? onTap;

  const LibraryCard({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.imageWidget,
    required this.decoration ,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: decoration,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding:  EdgeInsets.only(left: 60.p, right: 60.p, top: 66.p),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style:  TextStyle(
                    fontSize: 64.p,
                    color: const Color(0xFF222222),
                    fontWeight: FontWeight.bold,
                    height: 1,
                  )),
                  SizedBox(height: 25.p),
                  Text(subtitle, style:  TextStyle(
                    fontSize: 30.p,
                    color: const Color(0xFF222222),
                    fontWeight: FontWeight.w500,
                    height: 1,
                  )),
                ],
              ),
            ),
            imageWidget
          ],
        ),
      ),
    );
  }
}