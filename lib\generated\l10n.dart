// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Renewal in progress...`
  String get renewInProgress {
    return Intl.message(
      'Renewal in progress...',
      name: 'renewInProgress',
      desc: '',
      args: [],
    );
  }

  /// `The card has been used up and the reader's card cannot be processed temporarily. Please contact the staff！`
  String get noCardTip {
    return Intl.message(
      'The card has been used up and the reader\'s card cannot be processed temporarily. Please contact the staff！',
      name: 'noCardTip',
      desc: '',
      args: [],
    );
  }

  /// `Remaining cards`
  String get cardLefts {
    return Intl.message(
      'Remaining cards',
      name: 'cardLefts',
      desc: '',
      args: [],
    );
  }

  /// `Sending...`
  String get Sending {
    return Intl.message(
      'Sending...',
      name: 'Sending',
      desc: '',
      args: [],
    );
  }

  /// `s`
  String get resend {
    return Intl.message(
      's',
      name: 'resend',
      desc: '',
      args: [],
    );
  }

  /// `Account/Password`
  String get AccountPassword {
    return Intl.message(
      'Account/Password',
      name: 'AccountPassword',
      desc: '',
      args: [],
    );
  }

  /// `Reader Card Verification`
  String get ReaderCardVerification {
    return Intl.message(
      'Reader Card Verification',
      name: 'ReaderCardVerification',
      desc: '',
      args: [],
    );
  }

  /// `Scan QR Code Authentication`
  String get ScanAuth {
    return Intl.message(
      'Scan QR Code Authentication',
      name: 'ScanAuth',
      desc: '',
      args: [],
    );
  }

  /// `WeChat Scan QR Code Authentication`
  String get wechatScanAuth {
    return Intl.message(
      'WeChat Scan QR Code Authentication',
      name: 'wechatScanAuth',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect password`
  String get IncorrectPassword {
    return Intl.message(
      'Incorrect password',
      name: 'IncorrectPassword',
      desc: '',
      args: [],
    );
  }

  /// `Account Login`
  String get accountLogin {
    return Intl.message(
      'Account Login',
      name: 'accountLogin',
      desc: '',
      args: [],
    );
  }

  /// `Enter patron ID or ID card number`
  String get accountInput {
    return Intl.message(
      'Enter patron ID or ID card number',
      name: 'accountInput',
      desc: '',
      args: [],
    );
  }

  /// `Enter Password`
  String get passwordInput {
    return Intl.message(
      'Enter Password',
      name: 'passwordInput',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get loginButton {
    return Intl.message(
      'Login',
      name: 'loginButton',
      desc: '',
      args: [],
    );
  }

  /// ` Login`
  String get spaceLogin {
    return Intl.message(
      ' Login',
      name: 'spaceLogin',
      desc: '',
      args: [],
    );
  }

  /// `Account cannot be empty`
  String get accountRequired {
    return Intl.message(
      'Account cannot be empty',
      name: 'accountRequired',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect password length`
  String get passwordLengthError {
    return Intl.message(
      'Incorrect password length',
      name: 'passwordLengthError',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect password format`
  String get passwordFormatError {
    return Intl.message(
      'Incorrect password format',
      name: 'passwordFormatError',
      desc: '',
      args: [],
    );
  }

  /// `Requesting reader information`
  String get requestingInfo {
    return Intl.message(
      'Requesting reader information',
      name: 'requestingInfo',
      desc: '',
      args: [],
    );
  }

  /// `Reader ID does not exist or incorrect password`
  String get invalidCredentials {
    return Intl.message(
      'Reader ID does not exist or incorrect password',
      name: 'invalidCredentials',
      desc: '',
      args: [],
    );
  }

  /// `Reader initialization failed`
  String get readerFailure {
    return Intl.message(
      'Reader initialization failed',
      name: 'readerFailure',
      desc: '',
      args: [],
    );
  }

  /// `Barcode parsing failed`
  String get barcodeError {
    return Intl.message(
      'Barcode parsing failed',
      name: 'barcodeError',
      desc: '',
      args: [],
    );
  }

  /// `Card creation failed`
  String get cardCreationFail {
    return Intl.message(
      'Card creation failed',
      name: 'cardCreationFail',
      desc: '',
      args: [],
    );
  }

  /// `Waiting for result`
  String get waitingResult {
    return Intl.message(
      'Waiting for result',
      name: 'waitingResult',
      desc: '',
      args: [],
    );
  }

  /// `Processing reader card, please wait...`
  String get processingCard {
    return Intl.message(
      'Processing reader card, please wait...',
      name: 'processingCard',
      desc: '',
      args: [],
    );
  }

  /// `Failed to retrieve reader card ID`
  String get cardIdFail {
    return Intl.message(
      'Failed to retrieve reader card ID',
      name: 'cardIdFail',
      desc: '',
      args: [],
    );
  }

  /// `Failed to retrieve reader card ID, please contact library staff`
  String get cardIdFailContact {
    return Intl.message(
      'Failed to retrieve reader card ID, please contact library staff',
      name: 'cardIdFailContact',
      desc: '',
      args: [],
    );
  }

  /// `Card issuance error`
  String get cardIssuanceError {
    return Intl.message(
      'Card issuance error',
      name: 'cardIssuanceError',
      desc: '',
      args: [],
    );
  }

  /// `Card machine error, please contact library staff`
  String get cardMachineError {
    return Intl.message(
      'Card machine error, please contact library staff',
      name: 'cardMachineError',
      desc: '',
      args: [],
    );
  }

  /// `All reader card IDs have been used`
  String get cardIdExhausted {
    return Intl.message(
      'All reader card IDs have been used',
      name: 'cardIdExhausted',
      desc: '',
      args: [],
    );
  }

  /// `All reader card IDs have been used, please contact library staff`
  String get cardIdExhaustedContact {
    return Intl.message(
      'All reader card IDs have been used, please contact library staff',
      name: 'cardIdExhaustedContact',
      desc: '',
      args: [],
    );
  }

  /// `Unsupported card type`
  String get unsupportedCardType {
    return Intl.message(
      'Unsupported card type',
      name: 'unsupportedCardType',
      desc: '',
      args: [],
    );
  }

  /// `Unsupported card type, please contact library staff`
  String get unsupportedCardTypeContact {
    return Intl.message(
      'Unsupported card type, please contact library staff',
      name: 'unsupportedCardTypeContact',
      desc: '',
      args: [],
    );
  }

  /// `Deposit not supported`
  String get depositNotSupported {
    return Intl.message(
      'Deposit not supported',
      name: 'depositNotSupported',
      desc: '',
      args: [],
    );
  }

  /// `Card creation successful`
  String get cardCreationSuccess {
    return Intl.message(
      'Card creation successful',
      name: 'cardCreationSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Failed to process reader table`
  String get readerTableFail {
    return Intl.message(
      'Failed to process reader table',
      name: 'readerTableFail',
      desc: '',
      args: [],
    );
  }

  /// `Failed to process business system table`
  String get businessTableFail {
    return Intl.message(
      'Failed to process business system table',
      name: 'businessTableFail',
      desc: '',
      args: [],
    );
  }

  /// `Failed to request card creation`
  String get cardRequestFail {
    return Intl.message(
      'Failed to request card creation',
      name: 'cardRequestFail',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get nameInput {
    return Intl.message(
      'Name',
      name: 'nameInput',
      desc: '',
      args: [],
    );
  }

  /// `Gender`
  String get genderInput {
    return Intl.message(
      'Gender',
      name: 'genderInput',
      desc: '',
      args: [],
    );
  }

  /// `Ethnicity`
  String get ethnicityInput {
    return Intl.message(
      'Ethnicity',
      name: 'ethnicityInput',
      desc: '',
      args: [],
    );
  }

  /// `Date of Birth`
  String get birthDateInput {
    return Intl.message(
      'Date of Birth',
      name: 'birthDateInput',
      desc: '',
      args: [],
    );
  }

  /// `Issuing Authority`
  String get issuingAuthInput {
    return Intl.message(
      'Issuing Authority',
      name: 'issuingAuthInput',
      desc: '',
      args: [],
    );
  }

  /// `ID Number`
  String get idNumberInput {
    return Intl.message(
      'ID Number',
      name: 'idNumberInput',
      desc: '',
      args: [],
    );
  }

  /// `Expiration Date`
  String get expirationInput {
    return Intl.message(
      'Expiration Date',
      name: 'expirationInput',
      desc: '',
      args: [],
    );
  }

  /// `Address`
  String get addressInput {
    return Intl.message(
      'Address',
      name: 'addressInput',
      desc: '',
      args: [],
    );
  }

  /// `Captcha cannot be empty`
  String get captchaRequired {
    return Intl.message(
      'Captcha cannot be empty',
      name: 'captchaRequired',
      desc: '',
      args: [],
    );
  }

  /// `Invalid phone number`
  String get phoneError {
    return Intl.message(
      'Invalid phone number',
      name: 'phoneError',
      desc: '',
      args: [],
    );
  }

  /// `Deposit`
  String get depositInput {
    return Intl.message(
      'Deposit',
      name: 'depositInput',
      desc: '',
      args: [],
    );
  }

  /// `Yuan`
  String get currencyUnit {
    return Intl.message(
      'Yuan',
      name: 'currencyUnit',
      desc: '',
      args: [],
    );
  }

  /// `For better service, please enter your phone number`
  String get phoneForService {
    return Intl.message(
      'For better service, please enter your phone number',
      name: 'phoneForService',
      desc: '',
      args: [],
    );
  }

  /// `Enter your phone number`
  String get phoneInput {
    return Intl.message(
      'Enter your phone number',
      name: 'phoneInput',
      desc: '',
      args: [],
    );
  }

  /// `Enter correct captcha`
  String get validCaptcha {
    return Intl.message(
      'Enter correct captcha',
      name: 'validCaptcha',
      desc: '',
      args: [],
    );
  }

  /// `Enter captcha`
  String get captchaInput {
    return Intl.message(
      'Enter captcha',
      name: 'captchaInput',
      desc: '',
      args: [],
    );
  }

  /// `Phone number or captcha cannot be empty`
  String get phoneOrCaptchaRequired {
    return Intl.message(
      'Phone number or captcha cannot be empty',
      name: 'phoneOrCaptchaRequired',
      desc: '',
      args: [],
    );
  }

  /// `Enter valid phone number`
  String get validPhoneInput {
    return Intl.message(
      'Enter valid phone number',
      name: 'validPhoneInput',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirmButton {
    return Intl.message(
      'Confirm',
      name: 'confirmButton',
      desc: '',
      args: [],
    );
  }

  /// `Self-service card issuance notice`
  String get serviceNotice {
    return Intl.message(
      'Self-service card issuance notice',
      name: 'serviceNotice',
      desc: '',
      args: [],
    );
  }

  /// `I have read and agreed to the above`
  String get agreement {
    return Intl.message(
      'I have read and agreed to the above',
      name: 'agreement',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueButton {
    return Intl.message(
      'Continue',
      name: 'continueButton',
      desc: '',
      args: [],
    );
  }

  /// `You have successfully applied for a reader card`
  String get readerCardAppliedSuccessfully {
    return Intl.message(
      'You have successfully applied for a reader card',
      name: 'readerCardAppliedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Please collect your reader card from the dispenser`
  String get collectReaderCard {
    return Intl.message(
      'Please collect your reader card from the dispenser',
      name: 'collectReaderCard',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirm {
    return Intl.message(
      'Confirm',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `Social Security Card`
  String get socialSecurityCard {
    return Intl.message(
      'Social Security Card',
      name: 'socialSecurityCard',
      desc: '',
      args: [],
    );
  }

  /// `ID Card`
  String get identityCard {
    return Intl.message(
      'ID Card',
      name: 'identityCard',
      desc: '',
      args: [],
    );
  }

  /// ` Register`
  String get register {
    return Intl.message(
      ' Register',
      name: 'register',
      desc: '',
      args: [],
    );
  }

  /// `Scan your electronic social security card QR code`
  String get scanESCBarcode {
    return Intl.message(
      'Scan your electronic social security card QR code',
      name: 'scanESCBarcode',
      desc: '',
      args: [],
    );
  }

  /// `Place`
  String get place {
    return Intl.message(
      'Place',
      name: 'place',
      desc: '',
      args: [],
    );
  }

  /// `at`
  String get at {
    return Intl.message(
      'at',
      name: 'at',
      desc: '',
      args: [],
    );
  }

  /// `Sensing Area`
  String get sensingArea {
    return Intl.message(
      'Sensing Area',
      name: 'sensingArea',
      desc: '',
      args: [],
    );
  }

  /// `Electronic Social Security Card`
  String get electronicSocialSecurityCard {
    return Intl.message(
      'Electronic Social Security Card',
      name: 'electronicSocialSecurityCard',
      desc: '',
      args: [],
    );
  }

  /// `Scan your`
  String get scanYour {
    return Intl.message(
      'Scan your',
      name: 'scanYour',
      desc: '',
      args: [],
    );
  }

  /// ` QR Code`
  String get qrCode {
    return Intl.message(
      ' QR Code',
      name: 'qrCode',
      desc: '',
      args: [],
    );
  }

  /// `Select Registration Method`
  String get selectRegistrationMethod {
    return Intl.message(
      'Select Registration Method',
      name: 'selectRegistrationMethod',
      desc: '',
      args: [],
    );
  }

  /// `Reader not configured`
  String get readerNotFound {
    return Intl.message(
      'Reader not configured',
      name: 'readerNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Social Security Card Registration`
  String get sscRegistration {
    return Intl.message(
      'Social Security Card Registration',
      name: 'sscRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Second Generation ID Card Registration`
  String get secondGenIDCardRegistration {
    return Intl.message(
      'Second Generation ID Card Registration',
      name: 'secondGenIDCardRegistration',
      desc: '',
      args: [],
    );
  }

  /// `You have already registered a reader card, please do not register again`
  String get duplicateRegistration {
    return Intl.message(
      'You have already registered a reader card, please do not register again',
      name: 'duplicateRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Electronic Social Security Card Registration`
  String get eSSCRegistration {
    return Intl.message(
      'Electronic Social Security Card Registration',
      name: 'eSSCRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Unsupported type`
  String get unsupportedType {
    return Intl.message(
      'Unsupported type',
      name: 'unsupportedType',
      desc: '',
      args: [],
    );
  }

  /// `Deposit-free`
  String get noDeposit {
    return Intl.message(
      'Deposit-free',
      name: 'noDeposit',
      desc: '',
      args: [],
    );
  }

  /// `Select Reader Card Type`
  String get selectReaderCardType {
    return Intl.message(
      'Select Reader Card Type',
      name: 'selectReaderCardType',
      desc: '',
      args: [],
    );
  }

  /// `Age Ineligible`
  String get ineligibleAge {
    return Intl.message(
      'Age Ineligible',
      name: 'ineligibleAge',
      desc: '',
      args: [],
    );
  }

  /// `Area Ineligible`
  String get ineligibleArea {
    return Intl.message(
      'Area Ineligible',
      name: 'ineligibleArea',
      desc: '',
      args: [],
    );
  }

  /// `Place Document on Sensing Area`
  String get placeDocument {
    return Intl.message(
      'Place Document on Sensing Area',
      name: 'placeDocument',
      desc: '',
      args: [],
    );
  }

  /// `Success`
  String get success {
    return Intl.message(
      'Success',
      name: 'success',
      desc: '',
      args: [],
    );
  }

  /// `items`
  String get copy {
    return Intl.message(
      'items',
      name: 'copy',
      desc: '',
      args: [],
    );
  }

  /// `Failure`
  String get failure {
    return Intl.message(
      'Failure',
      name: 'failure',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get sequenceNumber {
    return Intl.message(
      'No',
      name: 'sequenceNumber',
      desc: '',
      args: [],
    );
  }

  /// `Barcode`
  String get barcode {
    return Intl.message(
      'Barcode',
      name: 'barcode',
      desc: '',
      args: [],
    );
  }

  /// `Book Title`
  String get bookTitle {
    return Intl.message(
      'Book Title',
      name: 'bookTitle',
      desc: '',
      args: [],
    );
  }

  /// `Due Date`
  String get dueDate {
    return Intl.message(
      'Due Date',
      name: 'dueDate',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get note {
    return Intl.message(
      'Note',
      name: 'note',
      desc: '',
      args: [],
    );
  }

  /// `Books to Return`
  String get booksToReturn {
    return Intl.message(
      'Books to Return',
      name: 'booksToReturn',
      desc: '',
      args: [],
    );
  }

  /// `In Library`
  String get availableInLibrary {
    return Intl.message(
      'In Library',
      name: 'availableInLibrary',
      desc: '',
      args: [],
    );
  }

  /// `Borrow Success`
  String get borrowSuccess {
    return Intl.message(
      'Borrow Success',
      name: 'borrowSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Title`
  String get bookTitleShort {
    return Intl.message(
      'Title',
      name: 'bookTitleShort',
      desc: '',
      args: [],
    );
  }

  /// `Call Number`
  String get callNumber {
    return Intl.message(
      'Call Number',
      name: 'callNumber',
      desc: '',
      args: [],
    );
  }

  /// `Borrow Failure`
  String get borrowFailure {
    return Intl.message(
      'Borrow Failure',
      name: 'borrowFailure',
      desc: '',
      args: [],
    );
  }

  /// `Continue Borrowing`
  String get continueBorrowing {
    return Intl.message(
      'Continue Borrowing',
      name: 'continueBorrowing',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Borrow`
  String get confirmBorrow {
    return Intl.message(
      'Confirm Borrow',
      name: 'confirmBorrow',
      desc: '',
      args: [],
    );
  }

  /// `Reader Card ID`
  String get readerCardID {
    return Intl.message(
      'Reader Card ID',
      name: 'readerCardID',
      desc: '',
      args: [],
    );
  }

  /// `Available Copies`
  String get borrowableCopies {
    return Intl.message(
      'Available Copies',
      name: 'borrowableCopies',
      desc: '',
      args: [],
    );
  }

  /// `Borrowed Copies`
  String get borrowedCopies {
    return Intl.message(
      'Borrowed Copies',
      name: 'borrowedCopies',
      desc: '',
      args: [],
    );
  }

  /// `Default Password`
  String get defaultPassword {
    return Intl.message(
      'Default Password',
      name: 'defaultPassword',
      desc: '',
      args: [],
    );
  }

  /// `Prepaid (Yuan)`
  String get Predeposit {
    return Intl.message(
      'Prepaid (Yuan)',
      name: 'Predeposit',
      desc: '',
      args: [],
    );
  }

  /// `Returning in Progress, Do Not Repeat`
  String get returningInProgress {
    return Intl.message(
      'Returning in Progress, Do Not Repeat',
      name: 'returningInProgress',
      desc: '',
      args: [],
    );
  }

  /// `No Books Available`
  String get noBooksAvailable {
    return Intl.message(
      'No Books Available',
      name: 'noBooksAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Borrowing in Progress`
  String get borrowingInProgress {
    return Intl.message(
      'Borrowing in Progress',
      name: 'borrowingInProgress',
      desc: '',
      args: [],
    );
  }

  /// `Borrow Request Failed`
  String get borrowRequestFailed {
    return Intl.message(
      'Borrow Request Failed',
      name: 'borrowRequestFailed',
      desc: '',
      args: [],
    );
  }

  /// `Borrow Request Failed`
  String get borrow_fail_request {
    return Intl.message(
      'Borrow Request Failed',
      name: 'borrow_fail_request',
      desc: '',
      args: [],
    );
  }

  /// `Borrow Failed, Security Tag Rewrite Failed`
  String get borrow_fail_rewrite_security {
    return Intl.message(
      'Borrow Failed, Security Tag Rewrite Failed',
      name: 'borrow_fail_rewrite_security',
      desc: '',
      args: [],
    );
  }

  /// `Certificate Number`
  String get cert_number {
    return Intl.message(
      'Certificate Number',
      name: 'cert_number',
      desc: '',
      args: [],
    );
  }

  /// `Document Successfully Borrowed`
  String get borrow_success {
    return Intl.message(
      'Document Successfully Borrowed',
      name: 'borrow_success',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get status {
    return Intl.message(
      'Status',
      name: 'status',
      desc: '',
      args: [],
    );
  }

  /// `Return Date`
  String get return_date {
    return Intl.message(
      'Return Date',
      name: 'return_date',
      desc: '',
      args: [],
    );
  }

  /// `Please Take Your Reader Card and Borrowed Books`
  String get take_away_card_books {
    return Intl.message(
      'Please Take Your Reader Card and Borrowed Books',
      name: 'take_away_card_books',
      desc: '',
      args: [],
    );
  }

  /// `Free Listening`
  String get free_listening {
    return Intl.message(
      'Free Listening',
      name: 'free_listening',
      desc: '',
      args: [],
    );
  }

  /// `Scan with WeChat`
  String get wechat_scan {
    return Intl.message(
      'Scan with WeChat',
      name: 'wechat_scan',
      desc: '',
      args: [],
    );
  }

  /// `Reader Card`
  String get reader_card {
    return Intl.message(
      'Reader Card',
      name: 'reader_card',
      desc: '',
      args: [],
    );
  }

  /// `Please Scan Your Electronic Social Security Card`
  String get scan_e_social_card {
    return Intl.message(
      'Please Scan Your Electronic Social Security Card',
      name: 'scan_e_social_card',
      desc: '',
      args: [],
    );
  }

  /// `WeChat QR Code`
  String get wechat_qr_code {
    return Intl.message(
      'WeChat QR Code',
      name: 'wechat_qr_code',
      desc: '',
      args: [],
    );
  }

  /// `HuiWen QR Code`
  String get huiwen_qr_code {
    return Intl.message(
      'HuiWen QR Code',
      name: 'huiwen_qr_code',
      desc: '',
      args: [],
    );
  }

  /// `Please Scan Your WeChat QR Code`
  String get scan_wechat_qr {
    return Intl.message(
      'Please Scan Your WeChat QR Code',
      name: 'scan_wechat_qr',
      desc: '',
      args: [],
    );
  }

  /// `Please Scan Your HuiWen QR Code`
  String get scan_huiwen_qr {
    return Intl.message(
      'Please Scan Your HuiWen QR Code',
      name: 'scan_huiwen_qr',
      desc: '',
      args: [],
    );
  }

  /// `Login with Alipay`
  String get alipay_login {
    return Intl.message(
      'Login with Alipay',
      name: 'alipay_login',
      desc: '',
      args: [],
    );
  }

  /// `Alipay`
  String get alipay {
    return Intl.message(
      'Alipay',
      name: 'alipay',
      desc: '',
      args: [],
    );
  }

  /// `Open Alipay App and Click Scan Icon in Top Left Corner`
  String get alipay_scan_guide {
    return Intl.message(
      'Open Alipay App and Click Scan Icon in Top Left Corner',
      name: 'alipay_scan_guide',
      desc: '',
      args: [],
    );
  }

  /// `With a Sesame Credit Score of 550 or above, scan the QR code on the screen to borrow books`
  String get borrow_with_alipay_credit {
    return Intl.message(
      'With a Sesame Credit Score of 550 or above, scan the QR code on the screen to borrow books',
      name: 'borrow_with_alipay_credit',
      desc: '',
      args: [],
    );
  }

  /// `If authorization fails, please check the failure reason on the page or contact Sesame Credit customer service`
  String get auth_fail_advice {
    return Intl.message(
      'If authorization fails, please check the failure reason on the page or contact Sesame Credit customer service',
      name: 'auth_fail_advice',
      desc: '',
      args: [],
    );
  }

  /// `Regenerate`
  String get regenerate {
    return Intl.message(
      'Regenerate',
      name: 'regenerate',
      desc: '',
      args: [],
    );
  }

  /// `Login with Face Recognition`
  String get face_recognition_login {
    return Intl.message(
      'Login with Face Recognition',
      name: 'face_recognition_login',
      desc: '',
      args: [],
    );
  }

  /// `Face Recognition`
  String get face_recognition {
    return Intl.message(
      'Face Recognition',
      name: 'face_recognition',
      desc: '',
      args: [],
    );
  }

  /// `Please Face the Camera`
  String get face_to_camera {
    return Intl.message(
      'Please Face the Camera',
      name: 'face_to_camera',
      desc: '',
      args: [],
    );
  }

  /// `Login Method`
  String get login_method {
    return Intl.message(
      'Login Method',
      name: 'login_method',
      desc: '',
      args: [],
    );
  }

  /// `Reader Card Does Not Exist`
  String get card_not_exist {
    return Intl.message(
      'Reader Card Does Not Exist',
      name: 'card_not_exist',
      desc: '',
      args: [],
    );
  }

  /// `Reader is Closing, Please Try Again Later`
  String get reader_closing {
    return Intl.message(
      'Reader is Closing, Please Try Again Later',
      name: 'reader_closing',
      desc: '',
      args: [],
    );
  }

  /// `Detailed Information`
  String get detail_info {
    return Intl.message(
      'Detailed Information',
      name: 'detail_info',
      desc: '',
      args: [],
    );
  }

  /// `Borrowing`
  String get borrowing {
    return Intl.message(
      'Borrowing',
      name: 'borrowing',
      desc: '',
      args: [],
    );
  }

  /// `Overdue`
  String get overdue {
    return Intl.message(
      'Overdue',
      name: 'overdue',
      desc: '',
      args: [],
    );
  }

  /// `Renewal Success`
  String get renew_success {
    return Intl.message(
      'Renewal Success',
      name: 'renew_success',
      desc: '',
      args: [],
    );
  }

  /// `Renewal Failed`
  String get renew_fail {
    return Intl.message(
      'Renewal Failed',
      name: 'renew_fail',
      desc: '',
      args: [],
    );
  }

  /// `Barcode Number`
  String get barcode_number {
    return Intl.message(
      'Barcode Number',
      name: 'barcode_number',
      desc: '',
      args: [],
    );
  }

  /// `Renew`
  String get renew {
    return Intl.message(
      'Renew',
      name: 'renew',
      desc: '',
      args: [],
    );
  }

  /// `Debt Amount (Yuan)`
  String get debt_amount {
    return Intl.message(
      'Debt Amount (Yuan)',
      name: 'debt_amount',
      desc: '',
      args: [],
    );
  }

  /// `Details`
  String get details {
    return Intl.message(
      'Details',
      name: 'details',
      desc: '',
      args: [],
    );
  }

  /// `Current Borrowing Count`
  String get current_borrow_count {
    return Intl.message(
      'Current Borrowing Count',
      name: 'current_borrow_count',
      desc: '',
      args: [],
    );
  }

  /// `Available Borrowing Count`
  String get available_borrow_count {
    return Intl.message(
      'Available Borrowing Count',
      name: 'available_borrow_count',
      desc: '',
      args: [],
    );
  }

  /// `Borrowed`
  String get borrowed {
    return Intl.message(
      'Borrowed',
      name: 'borrowed',
      desc: '',
      args: [],
    );
  }

  /// `Overdue Document Count`
  String get overdue_count {
    return Intl.message(
      'Overdue Document Count',
      name: 'overdue_count',
      desc: '',
      args: [],
    );
  }

  /// `Deposit`
  String get deposit {
    return Intl.message(
      'Deposit',
      name: 'deposit',
      desc: '',
      args: [],
    );
  }

  /// `Debt`
  String get debt {
    return Intl.message(
      'Debt',
      name: 'debt',
      desc: '',
      args: [],
    );
  }

  /// `Prepaid Balance`
  String get prepaid_balance {
    return Intl.message(
      'Prepaid Balance',
      name: 'prepaid_balance',
      desc: '',
      args: [],
    );
  }

  /// `Select All`
  String get select_all {
    return Intl.message(
      'Select All',
      name: 'select_all',
      desc: '',
      args: [],
    );
  }

  /// `View Details`
  String get view_details {
    return Intl.message(
      'View Details',
      name: 'view_details',
      desc: '',
      args: [],
    );
  }

  /// `Available for Borrowing`
  String get available_for_borrow {
    return Intl.message(
      'Available for Borrowing',
      name: 'available_for_borrow',
      desc: '',
      args: [],
    );
  }

  /// `Return Success`
  String get return_success {
    return Intl.message(
      'Return Success',
      name: 'return_success',
      desc: '',
      args: [],
    );
  }

  /// `Recorded Return Date`
  String get return_date_recorded {
    return Intl.message(
      'Recorded Return Date',
      name: 'return_date_recorded',
      desc: '',
      args: [],
    );
  }

  /// `Return Failed`
  String get return_fail {
    return Intl.message(
      'Return Failed',
      name: 'return_fail',
      desc: '',
      args: [],
    );
  }

  /// `Continue Returning`
  String get continue_returning {
    return Intl.message(
      'Continue Returning',
      name: 'continue_returning',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Return`
  String get confirm_return {
    return Intl.message(
      'Confirm Return',
      name: 'confirm_return',
      desc: '',
      args: [],
    );
  }

  /// `This Item is Overdue`
  String get book_overdue {
    return Intl.message(
      'This Item is Overdue',
      name: 'book_overdue',
      desc: '',
      args: [],
    );
  }

  /// `No Books to Return`
  String get no_books_to_return {
    return Intl.message(
      'No Books to Return',
      name: 'no_books_to_return',
      desc: '',
      args: [],
    );
  }

  /// `Returning in progress...`
  String get returningStatus {
    return Intl.message(
      'Returning in progress...',
      name: 'returningStatus',
      desc: '',
      args: [],
    );
  }

  /// `Return Failed, Request Failed`
  String get returnFailure {
    return Intl.message(
      'Return Failed, Request Failed',
      name: 'returnFailure',
      desc: '',
      args: [],
    );
  }

  /// `Return Failed, Security Tag Rewrite Failed`
  String get returnFailureRewrite {
    return Intl.message(
      'Return Failed, Security Tag Rewrite Failed',
      name: 'returnFailureRewrite',
      desc: '',
      args: [],
    );
  }

  /// `Please place the document to be returned on the sensor area`
  String get placeBookOnSensor {
    return Intl.message(
      'Please place the document to be returned on the sensor area',
      name: 'placeBookOnSensor',
      desc: '',
      args: [],
    );
  }

  /// `Document Successfully Returned`
  String get returnSuccess {
    return Intl.message(
      'Document Successfully Returned',
      name: 'returnSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Please take your reader card and borrowed books, and verify the printed receipt!`
  String get takeCardAndBooks {
    return Intl.message(
      'Please take your reader card and borrowed books, and verify the printed receipt!',
      name: 'takeCardAndBooks',
      desc: '',
      args: [],
    );
  }

  /// `Return Date`
  String get returnDate {
    return Intl.message(
      'Return Date',
      name: 'returnDate',
      desc: '',
      args: [],
    );
  }

  /// `Recommended Books for You`
  String get bookRecommendations {
    return Intl.message(
      'Recommended Books for You',
      name: 'bookRecommendations',
      desc: '',
      args: [],
    );
  }

  /// `Self-Service Borrowing and Returning Machine`
  String get selfServiceMachine {
    return Intl.message(
      'Self-Service Borrowing and Returning Machine',
      name: 'selfServiceMachine',
      desc: '',
      args: [],
    );
  }

  /// `Send Verification Code`
  String get sendVerificationCode {
    return Intl.message(
      'Send Verification Code',
      name: 'sendVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to print a receipt?`
  String get printReceipt {
    return Intl.message(
      'Do you want to print a receipt?',
      name: 'printReceipt',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get no {
    return Intl.message(
      'No',
      name: 'no',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get yes {
    return Intl.message(
      'Yes',
      name: 'yes',
      desc: '',
      args: [],
    );
  }

  /// `Clear`
  String get clear {
    return Intl.message(
      'Clear',
      name: 'clear',
      desc: '',
      args: [],
    );
  }

  /// `Characters`
  String get characters {
    return Intl.message(
      'Characters',
      name: 'characters',
      desc: '',
      args: [],
    );
  }

  /// `Quit`
  String get quit {
    return Intl.message(
      'Quit',
      name: 'quit',
      desc: '',
      args: [],
    );
  }

  /// `Book Recommendations`
  String get bookRecommendationsTitle {
    return Intl.message(
      'Book Recommendations',
      name: 'bookRecommendationsTitle',
      desc: '',
      args: [],
    );
  }

  /// `Scan to Listen`
  String get scanToListen {
    return Intl.message(
      'Scan to Listen',
      name: 'scanToListen',
      desc: '',
      args: [],
    );
  }

  /// `Home Page`
  String get homePage {
    return Intl.message(
      'Home Page',
      name: 'homePage',
      desc: '',
      args: [],
    );
  }

  /// `Borrow Book`
  String get borrowBook {
    return Intl.message(
      'Borrow Book',
      name: 'borrowBook',
      desc: '',
      args: [],
    );
  }

  /// `Return Book`
  String get returnBook {
    return Intl.message(
      'Return Book',
      name: 'returnBook',
      desc: '',
      args: [],
    );
  }

  /// `Renew Book`
  String get renewBook {
    return Intl.message(
      'Renew Book',
      name: 'renewBook',
      desc: '',
      args: [],
    );
  }

  /// `Apply for Card`
  String get applyCard {
    return Intl.message(
      'Apply for Card',
      name: 'applyCard',
      desc: '',
      args: [],
    );
  }

  /// `Renew/Query`
  String get renewOrQuery {
    return Intl.message(
      'Renew/Query',
      name: 'renewOrQuery',
      desc: '',
      args: [],
    );
  }

  /// `Query`
  String get query {
    return Intl.message(
      'Query',
      name: 'query',
      desc: '',
      args: [],
    );
  }

  /// `Remaining Time`
  String get remainingTime {
    return Intl.message(
      'Remaining Time',
      name: 'remainingTime',
      desc: '',
      args: [],
    );
  }

  /// `Select Operation Type`
  String get selectOperationType {
    return Intl.message(
      'Select Operation Type',
      name: 'selectOperationType',
      desc: '',
      args: [],
    );
  }

  /// `QR Code Login`
  String get qrCodeLogin {
    return Intl.message(
      'QR Code Login',
      name: 'qrCodeLogin',
      desc: '',
      args: [],
    );
  }

  /// `WeChat Login`
  String get wechatLogin {
    return Intl.message(
      'WeChat Login',
      name: 'wechatLogin',
      desc: '',
      args: [],
    );
  }

  /// `Reader Card Login`
  String get cardLogin {
    return Intl.message(
      'Reader Card Login',
      name: 'cardLogin',
      desc: '',
      args: [],
    );
  }

  /// `Manual Input Login`
  String get manualInputLogin {
    return Intl.message(
      'Manual Input Login',
      name: 'manualInputLogin',
      desc: '',
      args: [],
    );
  }

  /// `Manual Input`
  String get manualInput {
    return Intl.message(
      'Manual Input',
      name: 'manualInput',
      desc: '',
      args: [],
    );
  }

  /// `ID Card Login`
  String get idCardLogin {
    return Intl.message(
      'ID Card Login',
      name: 'idCardLogin',
      desc: '',
      args: [],
    );
  }

  /// `Social Security Card Login`
  String get socialSecurityCardLogin {
    return Intl.message(
      'Social Security Card Login',
      name: 'socialSecurityCardLogin',
      desc: '',
      args: [],
    );
  }

  /// `Electronic Social Security Card Login`
  String get eSocialSecurityCardLogin {
    return Intl.message(
      'Electronic Social Security Card Login',
      name: 'eSocialSecurityCardLogin',
      desc: '',
      args: [],
    );
  }

  /// `Citizen Card Login`
  String get citizenCardLogin {
    return Intl.message(
      'Citizen Card Login',
      name: 'citizenCardLogin',
      desc: '',
      args: [],
    );
  }

  /// `Exit`
  String get exit {
    return Intl.message(
      'Exit',
      name: 'exit',
      desc: '',
      args: [],
    );
  }

  /// `Please place the reader card on the sensor area`
  String get placeCardOnSensor {
    return Intl.message(
      'Please place the reader card on the sensor area',
      name: 'placeCardOnSensor',
      desc: '',
      args: [],
    );
  }

  /// `Please place the ID card on the sensor area`
  String get placeIdCardOnSensor {
    return Intl.message(
      'Please place the ID card on the sensor area',
      name: 'placeIdCardOnSensor',
      desc: '',
      args: [],
    );
  }

  /// `Tencent E-Pass Registration`
  String get tencentEPassRegister {
    return Intl.message(
      'Tencent E-Pass Registration',
      name: 'tencentEPassRegister',
      desc: '',
      args: [],
    );
  }

  /// `QR Code Display Registration`
  String get qrCodeRegistration {
    return Intl.message(
      'QR Code Display Registration',
      name: 'qrCodeRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Alipay Credit Score Registration`
  String get alipayCreditRegister {
    return Intl.message(
      'Alipay Credit Score Registration',
      name: 'alipayCreditRegister',
      desc: '',
      args: [],
    );
  }

  /// `Face Matching Registration`
  String get faceMatchRegister {
    return Intl.message(
      'Face Matching Registration',
      name: 'faceMatchRegister',
      desc: '',
      args: [],
    );
  }

  /// `Scan to Pay Registration`
  String get scanPayRegister {
    return Intl.message(
      'Scan to Pay Registration',
      name: 'scanPayRegister',
      desc: '',
      args: [],
    );
  }

  /// `External Card Registration (for Card Application)`
  String get externalCardRegistration {
    return Intl.message(
      'External Card Registration (for Card Application)',
      name: 'externalCardRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Payment Code Configuration`
  String get paymentCodeConfig {
    return Intl.message(
      'Payment Code Configuration',
      name: 'paymentCodeConfig',
      desc: '',
      args: [],
    );
  }

  /// `Manual Input Registration`
  String get manualInputRegistration {
    return Intl.message(
      'Manual Input Registration',
      name: 'manualInputRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Shanghai ShenShen Code Registration`
  String get shanghaiShenShenCodeRegister {
    return Intl.message(
      'Shanghai ShenShen Code Registration',
      name: 'shanghaiShenShenCodeRegister',
      desc: '',
      args: [],
    );
  }

  /// `Please place the card on the sensor area`
  String get placeCardOnSensorForCard {
    return Intl.message(
      'Please place the card on the sensor area',
      name: 'placeCardOnSensorForCard',
      desc: '',
      args: [],
    );
  }

  /// `For better service, please enter your mobile number`
  String get enterPhoneNumber {
    return Intl.message(
      'For better service, please enter your mobile number',
      name: 'enterPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter Mobile Number`
  String get inputPhoneNumber {
    return Intl.message(
      'Enter Mobile Number',
      name: 'inputPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Your Phone Number`
  String get yourPhoneNumber {
    return Intl.message(
      'Your Phone Number',
      name: 'yourPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Please select the button below to apply for the corresponding type of library card`
  String get selectCardType {
    return Intl.message(
      'Please select the button below to apply for the corresponding type of library card',
      name: 'selectCardType',
      desc: '',
      args: [],
    );
  }

  /// `Submitting data, please wait...`
  String get submittingData {
    return Intl.message(
      'Submitting data, please wait...',
      name: 'submittingData',
      desc: '',
      args: [],
    );
  }

  /// `Please insert the banknote into the slot`
  String get insertMoney {
    return Intl.message(
      'Please insert the banknote into the slot',
      name: 'insertMoney',
      desc: '',
      args: [],
    );
  }

  /// `This machine only accepts RMB 10, 20, 50, and 100 yuan. No change is provided. Any remaining balance will be credited to your prepaid account.`
  String get moneyAcceptanceInfo {
    return Intl.message(
      'This machine only accepts RMB 10, 20, 50, and 100 yuan. No change is provided. Any remaining balance will be credited to your prepaid account.',
      name: 'moneyAcceptanceInfo',
      desc: '',
      args: [],
    );
  }

  /// `Account Balance`
  String get accountBalance {
    return Intl.message(
      'Account Balance',
      name: 'accountBalance',
      desc: '',
      args: [],
    );
  }

  /// `Current Deposit`
  String get currentDeposit {
    return Intl.message(
      'Current Deposit',
      name: 'currentDeposit',
      desc: '',
      args: [],
    );
  }

  /// `You have successfully applied for a reader card`
  String get cardApplicationSuccess {
    return Intl.message(
      'You have successfully applied for a reader card',
      name: 'cardApplicationSuccess',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'zh'),
      Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hant'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
