@echo off
echo Starting Release build process...

REM Set paths
set OpenCV_DIR=D:\Users\Administrator\Downloads\opencv\build
echo Using OpenCV at %OpenCV_DIR%

REM Check for CMake
where cmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo CMake not found in PATH, trying to use Visual Studio's CMake...
    if exist "D:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" (
        set CMAKE_PATH="D:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"
        echo Using CMake from Visual Studio at %CMAKE_PATH%
    ) else (
        echo CMake not found. Please install CMake or add it to your PATH.
        echo You can download CMake from https://cmake.org/download/
        goto :error
    )
) else (
    set CMAKE_PATH=cmake
    echo Using CMake from PATH
)

REM Create and enter build directory
echo Creating build directory...
if exist "src\opencv\build" (
    echo Cleaning existing build directory...
    rd /s /q "src\opencv\build"
)
mkdir "src\opencv\build"
cd "src\opencv\build"

REM Run CMake to generate project files
echo Running CMake with Release configuration...
%CMAKE_PATH% .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_PREFIX_PATH=%OpenCV_DIR% -DCMAKE_BUILD_TYPE=Release

REM Build the project
echo Building Release version...
%CMAKE_PATH% --build . --config Release

REM Install to specified location
echo Installing...
%CMAKE_PATH% --install . --config Release

REM Return to original directory
cd ..\..\..

REM Create destination directories
echo Creating destination directories...
if not exist "windows\runner\Release\data" (
    mkdir "windows\runner\Release\data"
    echo Created directory: windows\runner\Release\data
)
if not exist "windows\runner\Release\data\models" (
    mkdir "windows\runner\Release\data\models"
    echo Created directory: windows\runner\Release\data\models
)
if not exist "build\windows\x64\runner\Release\data" (
    mkdir "build\windows\x64\runner\Release\data"
    echo Created directory: build\windows\x64\runner\Release\data
)
if not exist "build\windows\x64\runner\Release\data\models" (
    mkdir "build\windows\x64\runner\Release\data\models"
    echo Created directory: build\windows\x64\runner\Release\data\models
)
if not exist "build\windows\runner\Release\data" (
    mkdir "build\windows\runner\Release\data"
    echo Created directory: build\windows\runner\Release\data
)
if not exist "build\windows\runner\Release\data\models" (
    mkdir "build\windows\runner\Release\data\models"
    echo Created directory: build\windows\runner\Release\data\models
)

REM Clean up previous OpenCV DLLs from Release directories
echo Cleaning up previous OpenCV DLLs from Release directories...
if exist "windows\runner\Release\opencv_world*.dll" del /Q "windows\runner\Release\opencv_world*.dll"
if exist "build\windows\x64\runner\Release\opencv_world*.dll" del /Q "build\windows\x64\runner\Release\opencv_world*.dll"
if exist "build\windows\runner\Release\opencv_world*.dll" del /Q "build\windows\runner\Release\opencv_world*.dll"
echo Cleanup done.

REM Copy DLL files
echo Copying DLL files...
echo Checking for DLL at: src\opencv\build\bin\Release\libface_detector.dll

REM Check if DLL exists
if not exist "src\opencv\build\bin\Release\libface_detector.dll" (
    echo Warning: DLL file not found at src\opencv\build\bin\Release\libface_detector.dll
    echo This may be because the build failed or the output path is different.
    echo Continuing with other copy operations...
) else (
    REM Copy DLL to various required locations
    echo Copying DLL to multiple locations...
    
    REM 1. Copy to windows runner directory
    copy /Y "src\opencv\build\bin\Release\libface_detector.dll" "windows\runner\Release\libface_detector.dll"
    
    REM 2. Copy to flutter app build directory (with x64 path)
    copy /Y "src\opencv\build\bin\Release\libface_detector.dll" "build\windows\x64\runner\Release\libface_detector.dll"
    
    REM 3. Copy to actual Flutter build directory
    copy /Y "src\opencv\build\bin\Release\libface_detector.dll" "build\windows\runner\Release\libface_detector.dll"
    
    REM Verify all copied files
    echo Verifying all DLL locations...
    if exist "windows\runner\Release\libface_detector.dll" echo - Found in windows\runner\Release
    if exist "build\windows\x64\runner\Release\libface_detector.dll" echo - Found in build\windows\x64\runner\Release
    if exist "build\windows\runner\Release\libface_detector.dll" echo - Found in build\windows\runner\Release
)

REM Copy OpenCV Release DLLs (only non-debug versions)
echo Copying OpenCV Release DLLs to directories...
copy /Y "%OpenCV_DIR%\x64\vc16\bin\opencv_world4110.dll" "windows\runner\Release\"
echo Verifying OpenCV copy in windows\runner\Release:
dir "windows\runner\Release\opencv_world4110.dll" 2>nul || echo File not found.

copy /Y "%OpenCV_DIR%\x64\vc16\bin\opencv_world4110.dll" "build\windows\x64\runner\Release\"
echo Verifying OpenCV copy in build\windows\x64\runner\Release:
dir "build\windows\x64\runner\Release\opencv_world4110.dll" 2>nul || echo File not found.

copy /Y "%OpenCV_DIR%\x64\vc16\bin\opencv_world4110.dll" "build\windows\runner\Release\"
echo Verifying OpenCV copy in build\windows\runner\Release:
dir "build\windows\runner\Release\opencv_world4110.dll" 2>nul || echo File not found.

REM Include only release DLLs
echo Filtering for release DLLs (those that do not end with d.dll)...
for %%f in ("%OpenCV_DIR%\x64\vc16\bin\opencv_world*.dll") do (
    echo Checking file: %%f
    echo %%~nxf | findstr /I /E /C:"d.dll" >nul
    if errorlevel 1 (
        REM No "d.dll" at the end, so it's a release DLL
        echo Copying Release DLL: %%f (filename: %%~nxf)
        copy /Y "%%f" "windows\runner\Release\"
        copy /Y "%%f" "build\windows\x64\runner\Release\"
        copy /Y "%%f" "build\windows\runner\Release\"
        goto :skip_debug_dll_handling_in_loop
    )
    REM If we are here, it means errorlevel was 0 (debug DLL)
    echo Skipping Debug DLL: %%f (filename: %%~nxf)
    :skip_debug_dll_handling_in_loop
)

REM Copy model files
echo Copying model files...
if exist "models\face_detection_yunet_2023mar.onnx" (
    copy /Y "models\face_detection_yunet_2023mar.onnx" "windows\runner\Release\data\models"
    copy /Y "models\face_detection_yunet_2023mar.onnx" "build\windows\x64\runner\Release\data\models"
    copy /Y "models\face_detection_yunet_2023mar.onnx" "build\windows\runner\Release\data\models"
) else (
    echo Warning: Model file 'models\face_detection_yunet_2023mar.onnx' not found!
)

REM Verify DLLs existence
echo Checking main OpenCV DLL in runner directory:
if exist "windows\runner\Release\opencv_world4110.dll" (
    echo - opencv_world4110.dll found in runner directory
) else (
    echo - opencv_world4110.dll NOT found in runner directory - copy likely failed!
)

echo Checking main OpenCV DLL in Flutter build directory (x64):
if exist "build\windows\x64\runner\Release\opencv_world4110.dll" (
    echo - opencv_world4110.dll found in Flutter build directory (x64)
) else (
    echo - opencv_world4110.dll NOT found in Flutter build directory (x64) - copy likely failed!
)

echo Checking main OpenCV DLL in Flutter build directory:
if exist "build\windows\runner\Release\opencv_world4110.dll" (
    echo - opencv_world4110.dll found in Flutter build directory
) else (
    echo - opencv_world4110.dll NOT found in Flutter build directory - copy likely failed!
)

echo Release build and file copying completed!
goto :end

:error
echo Build process failed with errors.
exit /b 1

:end 