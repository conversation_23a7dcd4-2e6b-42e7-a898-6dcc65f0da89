import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/router/app_router.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/widgets/base_page2.dart';
import '../../../core/widgets/countdown_timer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/footer.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/widgets/logo_banner.dart';
import '../view_models/query_view_model.dart';
import '../widgets/date_range_selector.dart';
import '../widgets/records_table.dart';

class QueryView extends StatelessWidget {
  const QueryView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => QueryViewModel(),
      child: const QueryViewContent(),
    );
  }
}

class QueryViewContent extends StatefulWidget {
  const QueryViewContent({Key? key}) : super(key: key);

  @override
  State<QueryViewContent> createState() => _QueryViewContentState();
}

class _QueryViewContentState extends State<QueryViewContent> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<QueryViewModel>().init();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BasePage2(
      topWrapper: SizedBox(height: 200.p,child: Padding(
          padding: EdgeInsets.symmetric(vertical: 20.p),
          child: Text(
            '认证记录查询',
            style: TextStyle(
              fontSize: 32.p,
              fontWeight: FontWeight.bold,
              color:  Colors.white,
            ),
          ),
        ),),
      mainWrapper: _buildMainContent(),

    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        // 标题
        // Padding(
        //   padding: EdgeInsets.symmetric(vertical: 20.p),
        //   child: Text(
        //     '认证记录查询',
        //     style: TextStyle(
        //       fontSize: 24.p,
        //       fontWeight: FontWeight.bold,
        //       color: const Color(0xFF333333),
        //     ),
        //   ),
        // ),
        SizedBox(height: 20.p,),
        
        // 日期选择器
        const DateRangeSelector(),
        
        // 记录表格
        const Expanded(
          child: RecordsTable(),
        ),
      ],
    );
  }
}