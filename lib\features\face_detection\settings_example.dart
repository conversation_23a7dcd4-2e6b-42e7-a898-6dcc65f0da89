import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'face_detector.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String _photoSaveDir = '';
  String _videoSaveDir = '';
  
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }
  
  Future<void> _loadSettings() async {
    // 获取当前设置的照片和视频保存目录
    String photoDir = FaceDetector.getPhotoSaveDir();
    String videoDir = FaceDetector.getVideoSaveDir();
    
    // 如果目录为空，设置默认值
    if (photoDir.isEmpty) {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      photoDir = '${appDocDir.path}/Photos';
      await FaceDetector.selectPhotoSaveDir(photoDir);
    }
    
    if (videoDir.isEmpty) {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      videoDir = '${appDocDir.path}/Videos';
      await FaceDetector.selectVideoSaveDir(videoDir);
    }
    
    setState(() {
      _photoSaveDir = photoDir;
      _videoSaveDir = videoDir;
    });
  }
  
  Future<void> _selectPhotoDirectory() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      if (selectedDirectory != null) {
        final dir = Directory(selectedDirectory);
        if (!dir.existsSync()) {
          await dir.create(recursive: true);
        }
        
        await FaceDetector.selectPhotoSaveDir(selectedDirectory);
        setState(() {
          _photoSaveDir = selectedDirectory;
        });
      }
    } catch (e) {
      debugPrint('选择照片保存目录失败: $e');
    }
  }
  
  Future<void> _selectVideoDirectory() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      if (selectedDirectory != null) {
        final dir = Directory(selectedDirectory);
        if (!dir.existsSync()) {
          await dir.create(recursive: true);
        }
        
        await FaceDetector.selectVideoSaveDir(selectedDirectory);
        setState(() {
          _videoSaveDir = selectedDirectory;
        });
      }
    } catch (e) {
      debugPrint('选择视频保存目录失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '保存位置设置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // 照片保存目录设置
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('照片保存目录'),
                      const SizedBox(height: 4),
                      Text(
                        _photoSaveDir.isEmpty ? '未设置' : _photoSaveDir,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: _selectPhotoDirectory,
                  child: const Text('选择目录'),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 视频保存目录设置
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('视频保存目录'),
                      const SizedBox(height: 4),
                      Text(
                        _videoSaveDir.isEmpty ? '未设置' : _videoSaveDir,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: _selectVideoDirectory,
                  child: const Text('选择目录'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}