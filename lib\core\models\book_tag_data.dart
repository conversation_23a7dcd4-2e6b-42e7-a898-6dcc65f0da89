

// ignore_for_file: non_constant_identifier_names

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:hardware/hardware.dart';
import 'package:sea_socket/sea_socket.dart';
import '';




// class BookTagData {
//   HWTagData tag;          /// 阅读器读出来的 标签信息
//   Sip2BookData? bookData; /// 根据条码查出来的书籍信息
//   Sip2ReturnBookRspData? returnBookRspData;   /// 还书页面  还书返回消息
//   Sip2BorrowBookRspData? borrowBookRspData;   /// 借书页面  借书返回的消息体
//
//
//   BookTagData(this.tag, {this.bookData,this.returnBookRspData});
// }



// class BookData {
//   int? index;
//   bool? isSelected;
//   String? barcode; // 条码号
//   String? bookName;  // 书名
//   String? shelfNo;   // 层架号
//   String? queryIndex;// 索引号
//   int state;  //  状态  0：不在架  1：在架
//
//   String get stateString {
//     return state == 0 ? '不在架':'在架';
//   }
//
//   Color get stateColor {
//     return state == 0 ? BPUtils.c_FFFF725B : BPUtils.c_FF212121;
//   }
//
//   BookData({this.index, this.isSelected, this.barcode, this.bookName,
//     this.shelfNo, this.queryIndex,this.state = 0});
//
//
// }