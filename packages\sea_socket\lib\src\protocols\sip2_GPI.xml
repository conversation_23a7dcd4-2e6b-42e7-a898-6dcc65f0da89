<sip type="SIP_GPI" desc="SIP_GPI_GPI版本SIP2协议">
	<message id="93" name="Login" type="Req" desc="读者登录">
		<field value = ""				length = "2"	must="Y"	flag=""		default=""	force = "TC"   brief ="透传消息标识"/>
		<field value = "UIDAlgorithm"	length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="读者ID加密算法"/>
		<field value = "PWDAlgorithm"	length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="读者密码加密算法"/>
		<field value = "UserId"			length = "-1"	must="Y"	flag="CN"	default=""		force = ""   brief ="用户名(操作员代码)"/>
		<field value = "Password"  		length = "-1" 	must="Y"  	flag="CO" 	default="" 		force = ""   brief ="密码"/>
		<field value = "LocationCode"  	length = "-1" 	must="N"  	flag="CP" 	default=""		force = ""   brief ="馆藏地"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  		length = "-1" 	must="Y"  	flag="AY" 	default=""		force = ""   brief ="消息序列标识"/>
	</message>
	<message id="94" name="Login" type="Ans" desc="读者登录">
		<field value = "OK"   			length = "1"  	must="Y"  	flag="" 	default="" 	force = ""   brief ="是否登录成功，1成功0失败"/>
		<field value = "MsgSeqId"  		length = "-1" 	must="Y"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="35" name="Logout" type="Req" desc="结束读者事务">
		<field value = ""				length = "2"	must="Y"	flag=""	default=""		force = "TC"   brief ="透传消息标识"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"   	length = "-1"	must="Y"	flag="AO" 	default="" 	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"   length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "TerminalPwd"		length = "-1"	must="N"	flag="AC"	default=""	force = ""   brief ="终端机密码"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"	default=""	force = ""   brief ="读者密码"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="36" name="Logout" type="Ans" desc="结束读者事务">
		<field value = "EndSession"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="结束标志，Y:成功结束 ; N:未成功结束"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"   	length = "-1"	must="Y"	flag="AO" 	default="" 	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"   length = "-1"	must="Y"	flag="AA"	default=""	force = ""   brief ="读者证号"/>
		<field value = "ScreenMessage"   	length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"   		length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义打印信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>		
	<message id="99" name="SCStatus" type="Req" desc="SC 状态">
		<field value = ""				length = "2"	must="Y"	flag=""	default=""		force = "TC"   brief ="透传消息标识"/>
		<field value = "StatusCode"			length = "1"	must="Y"	flag=""		default="0"		force = ""   brief ="状态码，0:SC正常，1:SC打印机缺纸,2:SC即将关机"/>
		<field value = "MaxprintWidth"		length = "3"	must="Y"	flag=""		default="052"	force = ""   brief ="最大打印宽度，默认052"/>
		<field value = "ProtocolVersion"	length = "4"	must="Y"	flag=""		default="2.00"	force = ""   brief ="协议版本，默认2.00"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="|CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 	default=""		force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="98" name="SCStatus" type="Ans" desc="SC 状态">
		<field value = "OnLineStatus"		length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="在线状态，Y:在线 ，N:即将下线"/>
		<field value = "CheckInOk"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC还书：Y:允许 or N:不允许"/>
		<field value = "CheckOutOk"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC借书：Y or N"/>
		<field value = "ACSRenewalPolicy"	length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC续借：Y or N"/>
		<field value = "StatusUpdateOk"		length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否允许SC修改读者状态：Y or N"/>
		<field value = "OfflineOk"			length = "1"	must="Y"	flag=""		default=""	force = ""   brief ="是否支持离线业务：Y or N"/>
		<field value = "TimeoutPeriod"		length = "3"	must="Y"	flag=""		default=""	force = ""   brief ="应答超时时间"/>
		<field value = "RetriesAllowed"		length = "3"	must="Y"	flag=""		default=""	force = ""   brief ="应答重试次数"/>
		<field value = "DatetimeSync"		length = "18"	must="Y"	flag=""		default=""	force = ""   brief ="同步时间"/>
		<field value = "ProtocolVersion"	length = "4"	must="Y"	flag=""		default=""	force = ""   brief ="协议版本"/>
		<field value = "InstitutionId"		length = "-1"	must="N"	flag="AO"	default=""	force = ""   brief ="图书馆ID"/>
		<field value = "LibraryName"		length = "-1"	must="N"	flag="AM"	default=""	force = ""   brief ="图书馆名"/>
		<field value = "SupportedMessages"	length = "-1"	must="N"	flag="BX"	default=""	force = ""   brief ="支持的消息命令"/>
		<field value = "TerminalLocation"	length = "-1"	must="N"	flag="AN"	default=""	force = ""   brief ="终端名，由ACS描述SC位置"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"	default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	default=""	force = ""   brief ="ACS自定义打印信息"/>
		<field value = "MsgSeqId"  	    	length = "-1" 	must="N"  	flag="AY" 	default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="91" name="CheckRepeat" type="Req" desc="查重">
		<field value = ""					length = "2"	must="Y"	flag=""	default=""		force = "TC"   brief ="透传消息标识"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier1"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonalIdentifier"	length = "-1"	must="N"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="XK"		default=""	force = ""   brief ="操作验证方式，0:图书证，1:读者证"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="92" name="CheckRepeat" type="Ans" desc="查重">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>		
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonalIdentifier"	length = "-1"	must="N"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="XK"		default=""	force = ""   brief ="操作方式"/>
		<field value = "OK"					length = "-1"	must="N"	flag="OK"		default=""	force = ""   brief ="操作是否成功 0:成功，1:失败"/>
		<field value = "CardStatus"			length = "-1"	must="N"	flag="ST"		default=""	force = ""   brief ="证状态"/>
		<field value = "Result"				length = "1"	must="N"	flag="AC"		default=""	force = ""   brief ="验证结果：0:不存在记录，1:存在记录"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"		default=""	force = ""   brief ="ACS自定义信息"/>
	</message>
	<message id="81" name="CreateUser" type="Req" desc="创建读者">
		<field value = ""						length = "2"	must="Y"	flag=""	default=""		force = "TC"   brief ="透传消息标识"/>
		<field value = "TransactionDate"		length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"		length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"			length = "-1"	must="Y"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "PersonName"				length = "-1"	must="Y"	flag="AE"		default=""	force = ""   brief ="读者姓名"/>
		<field value = "LibraryName"			length = "-1"	must="Y"	flag="AM"		default=""	force = ""   brief ="图书馆名"/>
		<field value = "Telephone"				length = "-1"	must="N"	flag="BP"		default=""	force = ""   brief ="电话号码"/>
		<field value = "MobilePhone"			length = "-1"	must="N"	flag="MP"		default=""	force = ""   brief ="手机号码"/>
		<field value = "EmailAddress"			length = "-1"	must="N"	flag="BE"		default=""	force = ""   brief ="Email地址"/>
		<field value = "HomeAddress"			length = "-1"	must="N"	flag="BD"		default=""	force = ""   brief ="住址"/>
		<field value = "PersonalIdentifier"		length = "-1"	must="Y"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "PatronTypeCode"			length = "-1"	must="Y"	flag="XT"		default=""	force = ""   brief ="读者类型"/>
		<field value = "FeeAmount"		        length = "-1"	must="Y"	flag="BV"		default=""	force = ""   brief ="办证押金"/>
		<field value = "PatronRegisterDate"		length = "-1"	must="N"	flag="XB"		default=""	force = ""   brief ="读者证办证日期"/>
		<field value = "BirthDate"				length = "-1"	must="N"	flag="XH"		default=""	force = ""   brief ="出生日期"/>
		<field value = "Nation"					length = "-1"	must="N"	flag="XN"		default=""	force = ""   brief ="民族"/>
		<field value = "NativePlace"			length = "-1"	must="N"	flag="XP"		default=""	force = ""   brief ="籍贯"/>
		<field value = "Remark"					length = "-1"	must="N"	flag="XF"		default=""	force = ""   brief ="备注"/>
		<field value = "PatronValidDate"		length = "-1"	must="N"	flag="XD"		default=""	force = ""   brief ="读者证有效日期"/>
		<field value = "PatronStartDate"		length = "-1"	must="N"	flag="XE"		default=""	force = ""   brief ="读者证启用日期"/>		
		<field value = "PatronSex"				length = "-1"	must="N"	flag="XM"		default=""	force = ""   brief ="读者性别"/>		
		<field value = "NewPatronIdentifier"	length = "-1"	must=""		flag="XA"		default=""	force = ""   brief ="新读者证号"/>
		<field value = "OperationType"			length = "-1"	must="Y"	flag="XK"		default=""	force = ""   brief ="操作方式"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  				length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="82" name="CreateUser" type="Ans" desc="创建读者">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "OK"					length = "-1"	must="Y"	flag="OK"		default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "OperationType"		length = "-1"	must="N"	flag="XK"		default=""	force = ""   brief ="操作方式"/>
		<field value = "FiscalIdentifier"	length = "-1"	must="N"	flag="JK"		default=""	force = ""   brief ="财经记录流水号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
	</message>
	<message id="77" name="PhotoData" type="" desc="上传头像">
		<field value = ""					length = "2"	must="Y"	flag=""	default=""		force = "TC"   brief ="透传消息标识"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPhotoLength"	length = "-1"	must="Y"	flag="LE"		default=""	force = ""   brief ="头像数据长度"/>
		<field value = "PhtronPhotoData"	length = "-1"	must="Y"	flag="DA"		default=""	force = ""   brief ="头像数据"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="78" name="PhotoData" type="" desc="上传头像">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "OK"					length = "1"	must="Y"	flag="OK"		default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>	
	</message>
	<message id="45" name="QueryUser" type="Req" desc="查询读者信息">
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonalIdentifier"	length = "-1"	must="N"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "OperationType"		length = "-1"	must=""		flag="XK"		default=""	force = ""   brief ="操作方式"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="46" name="QueryUser" type="Ans" desc="查询读者信息">
		<field value = "TransactionDate"		length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"			length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>		
		<field value = "PatronIdentifier"		length = "-1"	must="N"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronIdentifierInfo"	length = "-1"	must="N"	flag="OX"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"			length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "LibraryName"			length = "-1"	must="N"	flag="AM"		default=""	force = ""   brief ="图书馆名(读者开户馆)"/>
		<field value = "Telephone"				length = "-1"	must="N"	flag="BP"		default=""	force = ""   brief ="电话号码"/>
		<field value = "MobilePhone"			length = "-1"	must="N"	flag="MP"		default=""	force = ""   brief ="手机号码"/>
		<field value = "EmailAddress"			length = "-1"	must="N"	flag="BE"		default=""	force = ""   brief ="Email地址"/>
		<field value = "HomeAddress"			length = "-1"	must="N"	flag="BD"		default=""	force = ""   brief ="住址"/>
		<field value = "FeeAmount"		        length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="办证押金"/>
		<field value = "ValidPatron"			length = "1"	must="N"	flag="BL"	default=""	force = ""   brief ="读者是否存在"/>
		<field value = "ChargeAmount"			length = "-1"	must="N"	flag="JE"		default=""	force = ""   brief ="读者预付款"/>
		<field value = "PersonalIdentifier"		length = "-1"	must="N"	flag="XO"		default=""	force = ""   brief ="身份证号码"/>
		<field value = "PatronTypeCode"			length = "-1"	must="N"	flag="XT"		default=""	force = ""   brief ="读者类型"/>
		<field value = "PatronRegisterDate"		length = "-1"	must="N"	flag="XB"		default=""	force = ""   brief ="读者证办证日期"/>
		<field value = "BirthDate"				length = "-1"	must="N"	flag="XH"		default=""	force = ""   brief ="出生日期"/>
		<field value = "Nation"					length = "-1"	must="N"	flag="XN"		default=""	force = ""   brief ="民族"/>
		<field value = "NativePlace"			length = "-1"	must="N"	flag="XP"		default=""	force = ""   brief ="籍贯"/>
		<field value = "Remark"					length = "-1"	must="N"	flag="XF"		default=""	force = ""   brief ="备注"/>
		<field value = "PatronValidDate"		length = "-1"	must="N"	flag="XD"		default=""	force = ""   brief ="读者证有效日期"/>
		<field value = "PatronStartDate"		length = "-1"	must="N"	flag="XE"		default=""	force = ""   brief ="读者证启用日期"/>		
		<field value = "PatronSex"				length = "-1"	must="N"	flag="XM"		default=""	force = ""   brief ="读者性别"/>	
		<field value = "OK"						length = "1"	must="N"	flag="OK"		default="1"	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "ScreenMessage"			length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>	
	</message>
	<message id="53" name="QueryImprestFine" type="Req" desc="查询预付款金额">
		<field value = ""					length = "2"	must="Y"	flag=""	default=""		force = "TC"   brief ="透传消息标识"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "OperationType"		length = "-1"	must=""		flag="XK"		default=""	force = ""   brief ="操作方式"/>	
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="54" name="QueryImprestFine" type="Ans" desc="查询预付款金额">
		<field value = "OK"					length = "1"	must="Y"	flag=""			default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "TransactionDate"	length = "18"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "InstitutionId"		length = "-1"	must="Y"	flag="AO"		default=""	force = ""   brief ="图书馆ID"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"			length = "-1"	must="Y"	flag="AE"		default=""	force = ""   brief ="读者姓名"/>
		<field value = "ChargeAmount"		length = "-1"	must="N"	flag="JE"		default=""	force = ""   brief ="读者预付款"/>
		<field value = "PatronTotalFine"	length = "-1"	must="N"	flag="JF"		default=""	force = ""   brief ="读者欠费总金额"/>
		<field value = "PatronFineRecord"	length = "-1"	must="N"	flag="CH"		default=""	force = ""   brief ="读者欠费记录"/>
		<field value = "ValidActionFee"		length = "-1"	must="N"	flag="JA"		default=""	force = ""   brief ="读者证验证费用"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="17" name="PatronInformation" type="Req" desc="查询读者证信息">
		<field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/> 
		<field value = "FeGeFu"				length = "14"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/> 
		<field value = "PatronIdentifier"	length = "1"	must="Y"	flag="AA"		default="N"	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="18" name="PatronInformation" type="Ans" desc="查询读者证信息">
		<field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PersonName"			length = "-1"	must="N"	flag="AE"		default=""	force = ""   brief ="读者姓名"/>
		<field value = "HoldItemsLimit"		length = "4"	must="N"	flag="BZ"		default=""	force = ""   brief ="可借图书数量"/>
		<field value = "ChargedItems"		length = "-1"	must="N"	flag="AS"		default=""	force = ""   brief ="预借图书"/>
		<field value = "HoldItemsCount"		length = "-1"	must="Y"	flag="CB"		default=""	force = ""   brief ="当前借书数量"/>
		<field value = "HoldItems"			length = "-1"	must="N"	flag="AU"		default=""	force = ""   brief ="已借图书"/>
		<field value = "ChargedItemsCount"	length = "4"	must="Y"	flag="XB"		default=""	force = ""   brief ="预约借书数量"/>
		<field value = "OverdueItemCount"	length = "4"	must="Y"	flag="XA"		default=""	force = ""   brief ="超期书数量"/>
		<field value = "ValidPatron"		length = "1"	must="N"	flag="BL"		default=""	force = ""   brief ="读者状态"/>
		<field value = "ValidPatronPassword"	length = "1"	must="N"	flag="CQ"	default=""	force = ""   brief ="读者密码是否正确"/>
		<field value = "PatronTotalFine"	length = "-1"	must="N"	flag="BV"		default=""	force = ""   brief ="读者欠款"/>
		<field value = "FeeLimit"			length = "-1"	must="N"	flag="CC"		default=""	force = ""   brief ="最大欠费金额"/>	
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>      
        <field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>		
	</message>	
	<message id="19" name="ItemInformation" type="Req" desc="图书查询">
		<field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/> 
		<field value = "FeGeFu"				length = "14"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/> 
		<field value = "QueryType"			length = "2"	must="Y"	flag="DC"		default="00"	force = ""   brief ="查询方式"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TagTID"				length = "-1"	must="Y"	flag="UA"		default=""	force = ""   brief ="标签唯一标识TID"/>
		<field value = "LayerTID"			length = "-1"	must="Y"	flag="WA"		default=""	force = ""   brief ="层架标签TID"/>
		<field value = "Subject"			length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="主题"/>
		<field value = "OID_Owner"	length = "-1"	must="Y"	flag="WC"		default=""	force = ""   brief ="题名"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="20" name="ItemInformation" type="Ans" desc="图书查询">
		<field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "Collection"			length = "-1"	must="N"	flag="AQ"		default=""	force = ""   brief ="馆藏地"/>
		<field value = "branchlibraries"	length = "-1"	must="N"	flag="WB"		default=""	force = ""   brief ="分馆"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "OID_Owner"			length = "-1"	must="Y"	flag="WC"		default=""	force = ""   brief ="著者"/>
		<field value = "CallNo"				length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="索书号"/>
        <field value = "DueDate"			length = "-1"	must="N"	flag="AH"	    default=""	force = ""   brief ="截止日期"/>
		<field value = "CirculationStatus"	length = "2"	must="Y"	flag="CL"		default=""	force = ""   brief ="图书流通状态"/>
		<field value = "LayerTID"			length = "-1"	must="Y"	flag="UH"		default=""	force = ""   brief ="层架TID"/>
        <field value = "registDate"			length = "-1"	must="N"	flag="RD"	    default=""	force = ""   brief ="注册日期"/>
		<field value = "DayCheckOutIn"		length = "-1"	must="N"	flag="BD"	    default=""	force = ""   brief ="借书日期"/>
		<field value = "LayerNumber"		length = "2"	must="Y"	flag="LN"		default=""	force = ""   brief ="层架号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>      
        <field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="21" name="CheckOut" type="Req" desc="借书">
        <field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>     
		<field value = "FeGeFu"				length = "14"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/> 
		<field value = "PatronIdentifier"	length = "1"	must="Y"	flag="AA"		default="N"	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者密码"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TagTID"				length = "-1"	must="Y"	flag="UA"		default=""	force = ""   brief ="标签唯一标识TID"/>
		<field value = "OID_Owner"	length = "-1"	must="Y"	flag="WC"		default=""	force = ""   brief ="题名"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="22" name="CheckOut" type="Ans" desc="借书">
        <field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "CallNo"				length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="索书号"/>
		<field value = "OK"		            length = "1"	must="Y"	flag="DB"		default=""	force = ""   brief ="结果 0 失败，1 成功"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TagTID"				length = "-1"	must="Y"	flag="UA"		default=""	force = ""   brief ="标签唯一标识TID"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
        <field value = "DueDate"			length = "-1"	must="N"	flag="AH"	    default=""	force = ""   brief ="操作日期"/>
		<field value = "PatronTotalFine"    length = "1"	must="Y"	flag="BV"		default=""	force = ""   brief ="读者欠款"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>        
        <field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="23" name="CheckIn" type="Req" desc="还书">
        <field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/> 
		<field value = "FeGeFu"				length = "14"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/> 
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TagTID"				length = "-1"	must="Y"	flag="UA"		default=""	force = ""   brief ="标签唯一标识TID"/>
		<field value = "OID_Owner"			length = "-1"	must="Y"	flag="WC"		default=""	force = ""   brief ="题名"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="24" name="CheckIn" type="Ans" desc="还书">
        <field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "CallNo"				length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="索书号"/>
		<field value = "OK"		            length = "1"	must="Y"	flag="DB"		default=""	force = ""   brief ="结果 0 失败，1 成功"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
		<field value = "Collection"			length = "-1"	must="N"	flag="AQ"		default=""	force = ""   brief ="馆藏地"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>        
        <field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
    <message id="25" name="Renew" type="Req" desc="图书续借">
        <field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>   
		<field value = "FeGeFu"				length = "14"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/>   
		<field value = "PatronIdentifier"	length = "1"	must="Y"	flag="AA"		default="N"	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"		length = "-1"	must="N"	flag="AD"		default=""	force = ""   brief ="读者密码"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TagTID"				length = "-1"	must="Y"	flag="UA"		default=""	force = ""   brief ="标签唯一标识TID"/>
		<field value = "OID_Owner"			length = "-1"	must="Y"	flag="WC"		default=""	force = ""   brief ="题名"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="26" name="Renew" type="Ans" desc="图书续借">
        <field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "CallNo"				length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="索书号"/>
		<field value = "OK"		            length = "1"	must="Y"	flag="DB"		default=""	force = ""   brief ="结果 0 失败，1 成功"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "ItemIdentifier"		length = "-1"	must="Y"	flag="AB"		default=""	force = ""   brief ="图书条码"/>
		<field value = "TagTID"				length = "-1"	must="Y"	flag="UA"		default=""	force = ""   brief ="标签唯一标识TID"/>
		<field value = "TitleIdentifier"	length = "-1"	must="Y"	flag="AJ"		default=""	force = ""   brief ="图书题名"/>
        <field value = "DueDate"			length = "-1"	must="N"	flag="AH"	    default=""	force = ""   brief ="操作日期"/>
		<field value = "PatronTotalFine"	length = "1"	must="Y"	flag="BV"		default=""	force = ""   brief ="读者欠款"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>        
        <field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="41" name="DeductImprest" type="Req" desc="预付款扣费">
		<field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronTotalFine"	length = "-1"	must="Y"	flag="JF"		default=""	force = ""   brief ="读者欠费金额"/>
		<field value = "PatronPassword"		length = "-1"	must="Y"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "OperationType"		length = "-1"	must="N"	flag="XK"		default=""	force = ""   brief ="操作方式，00:指定记录号扣费，01:所有记录号扣费"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>	
	</message>
	<message id="42" name="DeductImprest" type="Ans" desc="预付款扣费">
		<field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "OK"					length = "-1"	must="Y"	flag="DB"			default=""	force = ""   brief ="是否处理成功 0：未成功，1：成功"/>
		<field value = "FiscalIdentifier"	length = "-1"	must="N"	flag="JK"		default=""	force = ""   brief ="财经记录流水号"/>		
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "PrintLine"			length = "-1"	must="N"	flag="AG"	    default=""	force = ""   brief =""/>
		<field value = "MsgSeqId"  			length = "-1" 	must="Y"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>	
	<message id="43" name="ModifyPasswd" type="Req" desc="读者密码修改">
		<field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "FeGeFu"				length = "14"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"		length = "-1"	must="Y"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "NewPatronPassword"	length = "-1"	must="Y"	flag="AN"		default=""	force = ""   brief ="读者新密码"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="OP"		default=""	force = ""   brief ="密码操作标识 1 第一次刷卡  2密码校验 3 修改密码"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"           length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="44" name="ModifyPasswd" type="Ans" desc="读者密码修改">
		<field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "CallNo"				length = "-1"	must="N"	flag="CH"	    default=""	force = ""   brief ="索书号"/>
		<field value = "OK"		            length = "1"	must="Y"	flag="DB"		default=""	force = ""   brief ="结果 0 失败，1 成功"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="OP"		default=""	force = ""   brief ="密码操作标识"/>
		<field value = "GPI_FirstLogin"			length = "-1"	must="Y"	flag="FL"		default=""	force = ""   brief ="是否第一次登录 1 第一次刷卡登入"/>
		<field value = "GPI_PasswdOpen"			length = "-1"	must="Y"	flag="NP"		default=""	force = ""   brief ="是否开启密码校验 0 开启密码"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="51" name="Getstudentidcardnum" type="Req" desc="获取读者学号">
		<field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "FeGeFu"				length = "1"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/> 
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "PatronPassword"		length = "-1"	must="Y"	flag="AD"		default=""	force = ""   brief ="读者证密码"/>
		<field value = "CardType"			length = "-1"	must="Y"	flag="TY"		default=""	force = ""   brief ="登录类型"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"           length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="52" name="Getstudentidcardnum" type="Ans" desc="获取读者学号">
		<field value = "TransactionDate"	length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "FeGeFu"				length = "14"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/> 
		<field value = "OK"		            length = "1"	must="Y"	flag="DB"		default=""	force = ""   brief ="结果 0 失败，1 成功"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="57" name="GetPatronByANetwork" type="Req" desc="a网账号和读者证号">
		<field value = "NbDueDate"			length = "14"	must="Y"	flag=""			default=""	force = ""   brief ="事务发生时间"/>
		<field value = "FeGeFu"				length = "1"	must="Y"	flag=""			default="|"	force = ""   brief ="分隔符"/> 
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "aNetwork"			length = "-1"	must="Y"	flag="AC"		default=""	force = ""   brief ="a网账号"/>
		<field value = "OperationType"		length = "-1"	must="Y"	flag="OP"		default=""	force = ""   brief ="操作类型"/>
		<field value = "DeviceInfo"			length = "-1"	must="Y"	flag="CP"		default=""	force = ""   brief ="设备编号与物理地址"/>
		<field value = "MsgSeqId"           length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
	<message id="58" name="GetPatronByANetwork" type="Ans" desc="a网账号和读者证号">
		<field value = "OK"		            length = "-1"	must="Y"	flag="DB"		default=""	force = ""   brief ="结果 0 失败，1 成功"/>
		<field value = "PatronIdentifier"	length = "-1"	must="Y"	flag="AA"		default=""	force = ""   brief ="读者证号"/>
		<field value = "aNetwork"			length = "-1"	must="Y"	flag="AC"		default=""	force = ""   brief ="a网账号"/>
		<field value = "ScreenMessage"		length = "-1"	must="N"	flag="AF"		default=""	force = ""   brief ="ACS自定义信息"/>
		<field value = "MsgSeqId"  			length = "-1" 	must="N"  	flag="AY" 		default=""	force = ""   brief ="消息序列标识"/>
	</message>
</sip>