class ErrorRecord {
  final int? id;
  final String? slotNo;
  final String? bookTitle;
  final String? errorReason;
  final String? remark;
  final String? status;
  final String? operator;
  final String? createdAt;
  final String? updatedAt;

  ErrorRecord({
    this.id,
    this.slotNo,
    this.bookTitle,
    this.errorReason,
    this.remark,
    this.status,
    this.operator,
    this.createdAt,
    this.updatedAt,
  });

  // 从 JSON 创建对象
  factory ErrorRecord.fromJson(Map<String, dynamic> json) {
    return ErrorRecord(
      id: json['id'] as int?,
      slotNo: json['slot_no'] as String?,
      bookTitle: json['book_title'] as String?,
      errorReason: json['error_reason'] as String?,
      remark: json['remark'] as String?,
      status: json['status'] as String?,
      operator: json['operator'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );
  }

  // 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'slot_no': slotNo,
      'book_title': bookTitle,
      'error_reason': errorReason,
      'remark': remark,
      'status': status,
      'operator': operator,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  // 复制对象并修改部分属性
  ErrorRecord copyWith({
    int? id,
    String? slotNo,
    String? bookTitle,
    String? errorReason,
    String? remark,
    String? status,
    String? operator,
    String? createdAt,
    String? updatedAt,
  }) {
    return ErrorRecord(
      id: id ?? this.id,
      slotNo: slotNo ?? this.slotNo,
      bookTitle: bookTitle ?? this.bookTitle,
      errorReason: errorReason ?? this.errorReason,
      remark: remark ?? this.remark,
      status: status ?? this.status,
      operator: operator ?? this.operator,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // 状态常量
  static const String statusUnhandled = '0';  // 未处理
  static const String statusHandled = '1';    // 已处理

  // 判断是否已处理
  bool get isHandled => status == statusHandled;

  // 获取状态描述
  String get statusText => status == statusUnhandled ? '未处理' : '已处理';

  @override
  String toString() {
    return 'ErrorLog(id: $id, slotNo: $slotNo, bookTitle: $bookTitle, status: $statusText)';
  }
}