// import 'package:base_package/base_package.dart';
// import 'package:flutter/material.dart';
// import 'package:seasetting/seasetting.dart';
// import 'login_item_data.dart';
//
// class LoginItemModel {
//   final String title;
//   final String? subtitle;
//   final String? imagePath;
//   final AuthLoginType? type;
//   final Color? backgroundColor;
//   final Color? textColor;
//
//   const LoginItemModel({
//     required this.title,
//     this.subtitle,
//     this.imagePath,
//     this.type,
//     this.backgroundColor,
//     this.textColor,
//   });
//
//   factory LoginItemModel.fromLoginItemData(LoginItemData data) {
//     return LoginItemModel(
//       title: data.title ?? '',
//       subtitle: data.subtitle,
//       imagePath: data.imagePath,
//       type: data.type,
//       backgroundColor: data.backgroundColor,
//       textColor: data.textColor,
//     );
//   }
//
//   LoginItemModel copyWith({
//     String? title,
//     String? subtitle,
//     String? imagePath,
//     AuthLoginType? type,
//     Color? backgroundColor,
//     Color? textColor,
//   }) {
//     return LoginItemModel(
//       title: title ?? this.title,
//       subtitle: subtitle ?? this.subtitle,
//       imagePath: imagePath ?? this.imagePath,
//       type: type ?? this.type,
//       backgroundColor: backgroundColor ?? this.backgroundColor,
//       textColor: textColor ?? this.textColor,
//     );
//   }
//
//   @override
//   bool operator ==(Object other) =>
//       identical(this, other) ||
//       other is LoginItemModel &&
//           runtimeType == other.runtimeType &&
//           title == other.title &&
//           subtitle == other.subtitle &&
//           imagePath == other.imagePath &&
//           type == other.type;
//
//   @override
//   int get hashCode =>
//       title.hashCode ^
//       subtitle.hashCode ^
//       imagePath.hashCode ^
//       type.hashCode;
// }