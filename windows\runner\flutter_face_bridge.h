﻿#ifndef FLUTTER_FACE_BRIDGE_H_
#define FLUTTER_FACE_BRIDGE_H_

#include <string>
#include <vector>
#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

// 将Flutter图像数据转换为OpenCV Mat并调用百度SDK进行人脸识别
// 参数:
//   result: 输出参数，存储识别结果的字符串
//   data: 输入的图像数据
//   data_length: 数据长度
//   width: 图像宽度
//   height: 图像高度
//   channels: 图像通道数
//   is_jpeg: 是否为JPEG压缩数据 (1:是, 0:否)
//   type: 识别类型
void identify_with_image_data(
    char** result,
    const unsigned char* data,
    int data_length,
    int width,
    int height,
    int channels,
    int is_jpeg,
    int type
);

// 释放结果字符串
void free_result_string(char* result);

#ifdef __cplusplus
}
#endif

#endif  // FLUTTER_FACE_BRIDGE_H_ 