import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/router/route_extension.dart';
import '../../../../core/utils/window_util.dart';
import '../../../../core/widgets/library_grid_card.dart';
import '../../../../shared/utils/asset_util.dart';
import '../../../login/models/operation_type.dart';
import '../../../login/views/login_type_page.dart';

class HomeOptions extends StatelessWidget {
  const HomeOptions({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      padding: EdgeInsets.only(top: 104.p, left: 40.p, right: 40.p),
      mainAxisSpacing: 40.p,
      crossAxisSpacing: 40.p,
      childAspectRatio: 480.p / 586.p,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        // LibraryCard(
        //   title: '借书',
        //   subtitle: 'CheckOut',
        //   imageWidget:
        //   Image.asset(AssetUtil.fullPath('check_out'), fit: BoxFit.cover),
        //   decoration: BoxDecoration(
        //     gradient: const LinearGradient(
        //       begin: Alignment.topCenter,
        //       end: Alignment.bottomCenter,
        //       colors: [
        //         Color(0xFFB3D9FE),
        //         Color(0xFFE5F9FF),
        //       ],
        //     ),
        //     borderRadius: BorderRadius.circular(50.p),
        //     boxShadow: [
        //       BoxShadow(
        //         color: const Color.fromRGBO(29, 98, 253, 0.2),
        //         offset: Offset(0, 6.p),
        //         blurRadius: 20.p,
        //         spreadRadius: 0,
        //       )
        //     ],
        //   ),
        //   onTap: ()=>AppNavigator.toLoginType(OperationType.borrow),
        // ),
        // LibraryCard(
        //   title: '还书',
        //   subtitle: 'CheckIn',
        //   imageWidget: Expanded(
        //       child: Image.asset(AssetUtil.fullPath('check_in'),
        //           fit: BoxFit.contain)),
        //   decoration: BoxDecoration(
        //     gradient: const LinearGradient(
        //       begin: Alignment(-0.8, -0.7),
        //       end: Alignment(0.8, 0.7),
        //       colors: [
        //         Color(0xFF63E2CE),
        //         Color(0xFFE4FEF9),
        //       ],
        //     ),
        //     borderRadius: BorderRadius.circular(50.p),
        //     boxShadow: [
        //       BoxShadow(
        //         color: const Color.fromRGBO(29, 253, 201, 0.2),
        //         offset: Offset(0, 6.p),
        //         blurRadius: 20.p,
        //         spreadRadius: 0,
        //       )
        //     ],
        //   ),
        //   onTap: () => AppNavigator.toReturnBook(),
        // ),
        // LibraryCard(
        //   title: '查询/续借',
        //   subtitle: 'Query/Renewal',
        //   imageWidget: Expanded(
        //       child: Image.asset(AssetUtil.fullPath('query_renewal'),
        //           fit: BoxFit.contain)),
        //   decoration: BoxDecoration(
        //     gradient: const LinearGradient(
        //       begin: Alignment.topCenter,
        //       end: Alignment.bottomCenter,
        //       colors: [
        //         Color(0xFFF7B66D),
        //         Color(0xFFFDF2E2),
        //       ],
        //     ),
        //     borderRadius: BorderRadius.circular(50.p),
        //     boxShadow: [
        //       BoxShadow(
        //         color: const Color.fromRGBO(253, 160, 29, 0.2),
        //         offset: Offset(0, 6.p),
        //         blurRadius: 20.p,
        //         spreadRadius: 0,
        //       )
        //     ],
        //   ),
        //   onTap: () => AppNavigator.toLoginType(OperationType.renew),
        // ),
        // LibraryCard(
        //   title: '书架预览',
        //   subtitle: 'Bookshelf Preview',
        //   imageWidget:
        //   Image.asset(AssetUtil.fullPath('bookshelf'), fit: BoxFit.cover),
        //   decoration: BoxDecoration(
        //     gradient: const LinearGradient(
        //       begin: Alignment.topCenter,
        //       end: Alignment.bottomCenter,
        //       colors: [
        //         Color(0xFF80B8FF),
        //         Color(0xFFE5F9FF),
        //       ],
        //     ),
        //     borderRadius: BorderRadius.circular(50.p),
        //     boxShadow: [
        //       BoxShadow(
        //         color: const Color.fromRGBO(29, 98, 253, 0.2),
        //         offset: Offset(0, 6.p),
        //         blurRadius: 20.p,
        //         spreadRadius: 0,
        //       )
        //     ],
        //   ),
        //   onTap: () => AppNavigator.toPreview(),
        // ),
        LibraryCard(
          title: '智能认证系统',
          subtitle: 'Authentication',
          imageWidget: Expanded(
            child: Icon(
              Icons.verified_user, 
              size: 80.p,
              color: Colors.white,
            ),
          ),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF9575CD),
                Color(0xFFE1BEE7),
              ],
            ),
            borderRadius: BorderRadius.circular(50.p),
            boxShadow: [
              BoxShadow(
                color: const Color.fromRGBO(142, 36, 170, 0.2),
                offset: Offset(0, 6.p),
                blurRadius: 20.p,
                spreadRadius: 0,
              )
            ],
          ),
          onTap: () => AppNavigator.toFaceDetection(),
        ),
      ],
    );
  }
}
