import 'package:flutter/material.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';

class KeyboardButton extends StatefulWidget {
  final Widget text;
  final VoidCallback? onPressed;

  const KeyboardButton({
    super.key,
    required this.text,
    this.onPressed,
  });

  @override
  State<KeyboardButton> createState() => _KeyboardButtonState();
}

class _KeyboardButtonState extends State<KeyboardButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.7,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        _controller.forward();
      },
      onTapUp: (_) async {
        await Future.delayed(const Duration(milliseconds: 50)); // 短暂延迟
        _controller.reverse();
        widget.onPressed?.call();
      },
      onTapCancel: () {
        _controller.reverse();
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: Container(
                width: 120.p,
                height: 90.p,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.p),
                  image: DecorationImage(
                    image: AssetImage(AssetUtil.fullPath('keyboard_button_bg.png')),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Center(
                  child: widget.text,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
