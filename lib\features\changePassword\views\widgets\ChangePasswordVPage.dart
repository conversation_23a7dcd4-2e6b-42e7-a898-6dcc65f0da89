// ignore_for_file: prefer_const_constructors

import 'package:base_package/base_package.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:seasetting/seasetting.dart';
import '../../../../../../core/utils/window_util.dart';
import '../../../../core/widgets/SeaTextInput.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/gradient_border_container.dart';
import '../../../auth/widget/gradientView.dart';

class ChangePasswordContent extends StatefulWidget {
  const ChangePasswordContent( {Key? key}) : super(key: key);

  @override
  State<ChangePasswordContent> createState() => _ChangePasswordContentState();
}

class _ChangePasswordContentState extends SEContentState<ChangePasswordContent> {
  TextEditingController ctr1 = TextEditingController();
  TextEditingController ctr2 = TextEditingController();
  TextEditingController ctr3 = TextEditingController();
  FocusNode node1 = FocusNode();
  FocusNode node2 = FocusNode();
  FocusNode node3 = FocusNode();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    // widget.controller.addListener(onTap);
  }

  onTap() {
    // String method = widget.controller.method;
    // dynamic args = widget.controller.args;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();

    // widget.controller.removeListener(onTap);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return containerView();
  }

  Widget containerView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 40.p),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: 94.p,
              bottom: 108.p,
            ),
            child: Text(
              '修改密码',
              style: TextStyle(
                fontSize: 48.p,
                fontWeight: FontWeight.w500,
                color: BPUtils.c_FF212121,
              ),
            ),
          ),
          SeaTextInput(
            icon: '密码图标',
            hintText: '请输入原密码',
            controller: ctr1,
            focusNode: node1,
            needObscure: true,
          ),
          SizedBox(height: 44.p),
          SeaTextInput(
            icon: '密码图标',
            hintText: '请输入新密码',
            controller: ctr2,
            focusNode: node2,
            needObscure: true,
          ),
          SizedBox(height: 44.p),
          SeaTextInput(
            icon: '密码图标',
            hintText: '请再次输入新密码',
            controller: ctr3,
            focusNode: node3,
            needObscure: true,
          ),
          SizedBox(height: 140.p),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // InkWell(
              //   onTap: () {
              //     Get.back();
              //   },
              //   child: Container(
              //     alignment: Alignment.center,
              //     decoration: BoxDecoration(
              //       color: BPUtils.c_FFEBF1FF,
              //       borderRadius: BorderRadius.circular(40),
              //     ),
              //     height: 80.p,
              //     width: 300.p,
              //     child: Text(
              //       '取消',
              //       style: TextStyle(
              //         color: BPUtils.c_FF1D62FD,
              //         fontSize: 36.p,
              //         fontWeight: FontWeight.w500,
              //       ),
              //     ),
              //   ),
              // ),
              CustomButton.outline(text: '取消',onTap: () {
                Get.back();
              },width: 300.p,height: 80.p,),
              CustomButton.filled(text: '提交',onTap: onSubmit,width: 300.p,height: 80.p,)

              // InkWell(
              //   onTap: onSubmit,
              //   child: Container(
              //     alignment: Alignment.center,
              //     decoration: BoxDecoration(
              //       color: BPUtils.c_FF1D62FD,
              //       borderRadius: BorderRadius.circular(40),
              //     ),
              //     height: 80.p,
              //     width: 300.p,
              //     child: Text(
              //       '提交',
              //       style: TextStyle(
              //         color: Colors.white,
              //         fontSize: 36.p,
              //         fontWeight: FontWeight.w500,
              //       ),
              //     ),
              //   ),
              // )
            ],
          ),
        ],
      ),
    );
  }

  onSubmit() {
    if (check(ctr1.text, ctr2.text, confirmPsw: ctr3.text)) {
      DBSettingManager.getDBInstance().then((db) {
        db.verifyUserBy(UserData.adminUser, ctr1.text).then((value) {
          if (value != null) {
            db.insertUser(0, UserData.adminUser, ctr2.text).then((value) {
              showToast('操作成功');
              Get.back();
            });
          } else {
            showToast('原密码错误');
          }
        });
      });
    }
  }

  bool check(String oldPsw, String newPsw,
      {String? confirmPsw, bool show = true}) {
    if (oldPsw.isEmpty || newPsw.isEmpty) {
      if (show) {
        showToast('账号或密码不能为空');
      }
      return false;
    }

    if (confirmPsw != null) {
      if (confirmPsw.isEmpty) {
        if (show) {
          showToast('账号或密码不能为空');
        }
        return false;
      }
      if (newPsw != confirmPsw) {
        if (show) {
          showToast('前后密码不一致');
        }
        return false;
      }
    }
    return true;
  }
}
