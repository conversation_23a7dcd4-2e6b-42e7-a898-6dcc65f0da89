import 'package:flutter/material.dart';

class ThemeConfig {
  static late ThemeData currentThemeData;
  // 默认字体配置
  static String _fontFamily = 'SOURCEHANSANSCN';

  // 获取当前设置的字体
  static String get fontFamily => _fontFamily;
  
  // 设置字体方法
  static void setFontFamily(String fontFamily) {
    _fontFamily = fontFamily;
    // 如果已经初始化了主题，重新应用当前主题以更新字体
    if (currentThemeData != null) {
      final currentTheme = ThemeStore.themes.firstWhere(
          (theme) => theme.primaryColor == currentThemeData.primaryColor,
          orElse: () => ThemeStore.themes[0]);
      initTheme(currentTheme);
    }
  }

  static void initTheme(ThemeItem theme) {
    currentThemeData = _getThemeData(theme);
  }

  static ThemeData _getThemeData(ThemeItem theme) {
    return ThemeData(
      // 在这里设置全局字体
      fontFamily: _fontFamily,
      primaryColor: theme.primaryColor,
      scaffoldBackgroundColor: theme.scaffoldBackgroundColor,
      appBarTheme: AppBarTheme(
        backgroundColor: theme.appBarBackgroundColor,
        foregroundColor: theme.appBarForegroundColor,
        elevation: 0,
      ),
      cardTheme: CardTheme(
        color: theme.cardBackgroundColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      textTheme: TextTheme(
        titleLarge: TextStyle(
          fontFamily: _fontFamily, // 明确指定标题字体
          color: theme.primaryTextColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        bodyLarge: TextStyle(
          fontFamily: _fontFamily, // 明确指定正文字体
          color: theme.primaryTextColor,
          fontSize: 16,
        ),
        bodyMedium: TextStyle(
          fontFamily: _fontFamily, // 明确指定次要文本字体
          color: theme.secondaryTextColor,
          fontSize: 14,
        ),
      ),
      iconTheme: IconThemeData(
        color: theme.iconColor,
        size: 24,
      ),
      buttonTheme: ButtonThemeData(
        buttonColor: theme.buttonBackgroundColor,
        textTheme: ButtonTextTheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      colorScheme: ColorScheme(
        primary: theme.primaryColor,
        secondary: theme.accentColor,
        surface: theme.surfaceColor,
        background: theme.backgroundColor,
        error: theme.errorColor,
        onPrimary: theme.onPrimaryColor,
        onSecondary: theme.onSecondaryColor,
        onSurface: theme.onSurfaceColor,
        onBackground: theme.onBackgroundColor,
        onError: theme.onErrorColor,
        brightness: theme.brightness,
      ),
    );
  }
}

class ThemeItem {
  final Color primaryColor;
  final Color accentColor;
  final Color backgroundColor;
  final Color surfaceColor;
  final Color errorColor;
  final Color scaffoldBackgroundColor;
  final Color appBarBackgroundColor;
  final Color appBarForegroundColor;
  final Color cardBackgroundColor;
  final Color primaryTextColor;
  final Color secondaryTextColor;
  final Color iconColor;
  final Color buttonBackgroundColor;
  final Color onPrimaryColor;
  final Color onSecondaryColor;
  final Color onSurfaceColor;
  final Color onBackgroundColor;
  final Color onErrorColor;
  final Brightness brightness;

  const ThemeItem({
    required this.primaryColor,
    required this.accentColor,
    required this.backgroundColor,
    required this.surfaceColor,
    required this.errorColor,
    required this.scaffoldBackgroundColor,
    required this.appBarBackgroundColor,
    required this.appBarForegroundColor,
    required this.cardBackgroundColor,
    required this.primaryTextColor,
    required this.secondaryTextColor,
    required this.iconColor,
    required this.buttonBackgroundColor,
    required this.onPrimaryColor,
    required this.onSecondaryColor,
    required this.onSurfaceColor,
    required this.onBackgroundColor,
    required this.onErrorColor,
    required this.brightness,
  });
}

class ThemeStore {
  static final List<ThemeItem> themes = [
    // 浅色主题
    ThemeItem(
      primaryColor: const Color(0xFF2196F3),
      accentColor: const Color(0xFF03A9F4),
      backgroundColor: const Color(0xFFF5F5F5),
      surfaceColor: Colors.white,
      errorColor: const Color(0xFFD32F2F),
      scaffoldBackgroundColor: const Color(0xFFF5F5F5),
      appBarBackgroundColor: const Color(0xFF2196F3),
      appBarForegroundColor: Colors.white,
      cardBackgroundColor: Colors.white,
      primaryTextColor: const Color(0xFF212121),
      secondaryTextColor: const Color(0xFF757575),
      iconColor: const Color(0xFF212121),
      buttonBackgroundColor: const Color(0xFF2196F3),
      onPrimaryColor: Colors.white,
      onSecondaryColor: Colors.white,
      onSurfaceColor: const Color(0xFF212121),
      onBackgroundColor: const Color(0xFF212121),
      onErrorColor: Colors.white,
      brightness: Brightness.light,
    ),
    // 深色主题
    ThemeItem(
      primaryColor: const Color(0xFF1976D2),
      accentColor: const Color(0xFF03A9F4),
      backgroundColor: const Color(0xFF121212),
      surfaceColor: const Color(0xFF1E1E1E),
      errorColor: const Color(0xFFCF6679),
      scaffoldBackgroundColor: const Color(0xFF121212),
      appBarBackgroundColor: const Color(0xFF1E1E1E),
      appBarForegroundColor: Colors.white,
      cardBackgroundColor: const Color(0xFF1E1E1E),
      primaryTextColor: Colors.white,
      secondaryTextColor: const Color(0xFFB3B3B3),
      iconColor: Colors.white,
      buttonBackgroundColor: const Color(0xFF1976D2),
      onPrimaryColor: Colors.white,
      onSecondaryColor: Colors.white,
      onSurfaceColor: Colors.white,
      onBackgroundColor: Colors.white,
      onErrorColor: const Color(0xFF121212),
      brightness: Brightness.dark,
    ),
  ];
} 