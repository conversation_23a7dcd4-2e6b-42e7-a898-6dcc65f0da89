import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:libserialport/libserialport.dart';

import '../models/gate_command.dart';

/// 闸机串口通信服务
class GateSerialService {
  static GateSerialService? _instance;
  static GateSerialService get instance => _instance ??= GateSerialService._();
  GateSerialService._();
  
  // 串口相关
  SerialPort? _serialPort;
  SerialPortReader? _reader;
  StreamSubscription? _subscription;
  
  // 状态标志
  bool _isInitialized = false;
  bool _isListening = false;
  
  // 配置参数
  String _portName = 'COM3';
  int _baudRate = 9600;
  
  // 事件流
  final StreamController<GateCommand> _commandController = 
      StreamController<GateCommand>.broadcast();
  Stream<GateCommand> get commandStream => _commandController.stream;
  
  // 错误流
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;
  
  /// 初始化串口服务
  Future<void> initialize({
    String? portName,
    int? baudRate,
  }) async {
    if (_isInitialized) {
      debugPrint('闸机串口服务已经初始化');
      return;
    }
    
    _portName = portName ?? _portName;
    _baudRate = baudRate ?? _baudRate;
    
    try {
      await _openSerialPort();
      _isInitialized = true;
      debugPrint('闸机串口服务初始化成功: $_portName');
    } catch (e) {
      final errorMsg = '闸机串口服务初始化失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }
  
  /// 开始监听串口数据
  Future<void> startListening() async {
    if (!_isInitialized) {
      throw Exception('串口服务未初始化');
    }
    
    if (_isListening) {
      debugPrint('串口监听已经启动');
      return;
    }
    
    try {
      _startReading();
      _isListening = true;
      debugPrint('开始监听闸机串口命令');
    } catch (e) {
      final errorMsg = '启动串口监听失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }
  
  /// 停止监听串口数据
  Future<void> stopListening() async {
    if (!_isListening) return;
    
    try {
      await _subscription?.cancel();
      _subscription = null;
      _reader = null;
      _isListening = false;
      debugPrint('停止监听闸机串口命令');
    } catch (e) {
      debugPrint('停止串口监听失败: $e');
    }
  }
  
  /// 发送命令到闸机
  Future<bool> sendCommand(String commandType) async {
    if (!_isInitialized || _serialPort == null || !_serialPort!.isOpen) {
      debugPrint('串口未初始化或未打开，无法发送命令');
      return false;
    }
    
    final commandData = GateCommand.getSendCommandData(commandType);
    if (commandData == null) {
      debugPrint('未知的发送命令类型: $commandType');
      return false;
    }
    
    try {
      final bytesToSend = Uint8List.fromList(commandData);
      final bytesWritten = _serialPort!.write(bytesToSend);
      
      if (bytesWritten == commandData.length) {
        debugPrint('成功发送闸机命令: $commandType (${commandData.length} bytes)');
        return true;
      } else {
        debugPrint('发送闸机命令不完整: $commandType ($bytesWritten/${commandData.length} bytes)');
        return false;
      }
    } catch (e) {
      final errorMsg = '发送闸机命令失败: $commandType, 错误: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return false;
    }
  }
  
  /// 打开串口
  Future<void> _openSerialPort() async {
    try {
      // 检查串口是否存在
      final availablePorts = SerialPort.availablePorts;
      if (!availablePorts.contains(_portName)) {
        throw Exception('串口 $_portName 不存在。可用串口: ${availablePorts.join(", ")}');
      }
      
      _serialPort = SerialPort(_portName);
      
      // 配置串口参数
      final config = SerialPortConfig();
      config.baudRate = _baudRate;
      config.bits = 8;
      config.stopBits = 1;
      config.parity = SerialPortParity.none;
      config.setFlowControl(SerialPortFlowControl.none);
      
      _serialPort!.config = config;
      
      // 打开串口
      if (!_serialPort!.openReadWrite()) {
        final error = SerialPort.lastError;
        throw Exception('无法打开串口 $_portName: ${error?.message ?? "未知错误"}');
      }
      
      debugPrint('串口 $_portName 打开成功 (波特率: $_baudRate)');
    } catch (e) {
      _serialPort?.close();
      _serialPort = null;
      throw Exception('打开串口失败: $e');
    }
  }
  
  /// 开始读取串口数据
  void _startReading() {
    if (_serialPort == null || !_serialPort!.isOpen) {
      throw Exception('串口未打开');
    }
    
    try {
      _reader = SerialPortReader(_serialPort!);
      
      _subscription = _reader!.stream.listen(
        _onDataReceived,
        onError: (error) {
          final errorMsg = '串口读取错误: $error';
          debugPrint(errorMsg);
          _errorController.add(errorMsg);
        },
        onDone: () {
          debugPrint('串口读取完成');
        },
      );
      
      debugPrint('开始监听串口数据');
    } catch (e) {
      throw Exception('开始读取串口数据失败: $e');
    }
  }
  
  /// 处理接收到的数据
  void _onDataReceived(List<int> data) {
    if (!_isListening || data.isEmpty) return;
    
    try {
      debugPrint('接收到串口数据: ${data.map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}').join(' ')}');
      
      final commandType = GateCommand.parseCommandType(data);
      if (commandType != null) {
        final command = GateCommand(type: commandType, data: data);
        debugPrint('解析到闸机命令: ${command.type} (${command.displayName})');
        _commandController.add(command);
      } else {
        debugPrint('未识别的串口数据: ${data.map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}').join(' ')}');
      }
    } catch (e) {
      final errorMsg = '解析闸机命令失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }
  
  /// 获取串口状态信息
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'listening': _isListening,
      'port_name': _portName,
      'baud_rate': _baudRate,
      'port_open': _serialPort?.isOpen ?? false,
      'available_ports': SerialPort.availablePorts,
    };
  }
  
  /// 重新连接串口
  Future<bool> reconnect() async {
    try {
      debugPrint('尝试重新连接串口...');
      
      // 停止监听
      await stopListening();
      
      // 关闭串口
      _serialPort?.close();
      _serialPort = null;
      _isInitialized = false;
      
      // 重新初始化
      await initialize(portName: _portName, baudRate: _baudRate);
      await startListening();
      
      debugPrint('串口重新连接成功');
      return true;
    } catch (e) {
      final errorMsg = '串口重新连接失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return false;
    }
  }
  
  /// 检查串口连接状态
  bool get isConnected {
    return _isInitialized && _serialPort != null && _serialPort!.isOpen;
  }
  
  /// 释放资源
  void dispose() {
    debugPrint('释放闸机串口服务资源');
    
    stopListening();
    _serialPort?.close();
    _serialPort = null;
    _reader = null;
    
    _commandController.close();
    _errorController.close();
    
    _isInitialized = false;
    _isListening = false;
    
    debugPrint('闸机串口服务已释放');
  }
}
