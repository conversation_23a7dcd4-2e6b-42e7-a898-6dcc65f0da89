import 'dart:async';
import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';
import 'package:ffi/ffi.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

// FaceRect结构体定义
base class FaceRect extends Struct {
  @Int32()
  external int x;

  @Int32()
  external int y;

  @Int32()
  external int width;

  @Int32()
  external int height;
}

// 函数指针类型定义
typedef DetectFacesNative = Bool Function(
    Pointer<Uint8> imageData,
    Int32 width,
    Int32 height,
    Int32 channels,
    Pointer<FaceRect> faces,
    Int32 maxFaces,
    Pointer<Int32> numFaces);

typedef DetectFacesDart = bool Function(
    Pointer<Uint8> imageData,
    int width,
    int height,
    int channels,
    Pointer<FaceRect> faces,
    int maxFaces,
    Pointer<Int32> numFaces);

typedef ProcessImageNative = Pointer<Uint8> Function(
    Pointer<Uint8> imageData, Int32 width, Int32 height, Int32 channels);

typedef ProcessImageDart = Pointer<Uint8> Function(
    Pointer<Uint8> imageData, int width, int height, int channels);

typedef FreeImageNative = Void Function(Pointer<Uint8> imageData);
typedef FreeImageDart = void Function(Pointer<Uint8> imageData);

// 摄像头操作相关的函数指针类型定义
typedef StartCameraNative = Bool Function();
typedef StartCameraDart = bool Function();

typedef StartCameraWithIDNative = Bool Function(Int32 cameraId);
typedef StartCameraWithIDDart = bool Function(int cameraId);

typedef StopCameraNative = Void Function();
typedef StopCameraDart = void Function();

typedef TakePhotoNative = Bool Function(Pointer<Utf8> savePath);
typedef TakePhotoDart = bool Function(Pointer<Utf8> savePath);

typedef StartRecordingNative = Bool Function(Pointer<Utf8> savePath);
typedef StartRecordingDart = bool Function(Pointer<Utf8> savePath);

typedef StopRecordingNative = Bool Function();
typedef StopRecordingDart = bool Function();

typedef IsRecordingNative = Bool Function();
typedef IsRecordingDart = bool Function();

// 获取帧信息和数据的函数指针
typedef GetFrameInfoNative = Bool Function(
    Pointer<Int32> width, Pointer<Int32> height, Pointer<Int32> channels);
typedef GetFrameInfoDart = bool Function(
    Pointer<Int32> width, Pointer<Int32> height, Pointer<Int32> channels);

typedef GetFrameDataNative = Pointer<Uint8> Function(
    Pointer<Int32> width, Pointer<Int32> height, Pointer<Int32> channels);
typedef GetFrameDataDart = Pointer<Uint8> Function(
    Pointer<Int32> width, Pointer<Int32> height, Pointer<Int32> channels);

// 获取JPEG图像数据
typedef GetJpegDataNative = Bool Function(
    Pointer<Pointer<Uint8>> output, Pointer<Int32> length);
typedef GetJpegDataDart = bool Function(
    Pointer<Pointer<Uint8>> output, Pointer<Int32> length);

// 获取原始帧尺寸
typedef GetOriginalFrameSizeNative = Void Function(
    Pointer<Int32> width, Pointer<Int32> height);
typedef GetOriginalFrameSizeDart = void Function(
    Pointer<Int32> width, Pointer<Int32> height);

// 获取当前JPEG质量
typedef GetJpegQualityNative = Int32 Function();
typedef GetJpegQualityDart = int Function();

// 设置JPEG质量
typedef SetJpegQualityNative = Void Function(Int32 quality);
typedef SetJpegQualityDart = void Function(int quality);

// 添加设置和获取使用Haar检测器的函数
typedef SetUseHaarDetectorNative = Void Function(Bool useHaar);
typedef SetUseHaarDetectorDart = void Function(bool useHaar);

typedef GetUseHaarDetectorNative = Bool Function();
typedef GetUseHaarDetectorDart = bool Function();

// 添加新的分离式人脸检测函数类型定义
typedef DetectFacesOnlyNative = Bool Function(
    Pointer<Void> facesArray, Int32 maxFaces, Pointer<Int32> numFaces);
typedef DetectFacesOnlyDart = bool Function(
    Pointer<Void> facesArray, int maxFaces, Pointer<Int32> numFaces);

// 添加平滑因子相关的函数指针类型定义
typedef SetSmoothingFactorNative = Void Function(Double factor);
typedef SetSmoothingFactorDart = void Function(double factor);

typedef GetSmoothingFactorNative = Double Function();
typedef GetSmoothingFactorDart = double Function();

// 系统负载相关的函数指针
typedef GetSystemLoadNative = Int32 Function();
typedef GetSystemLoadDart = int Function();

// 处理间隔相关的函数指针
typedef SetProcessingIntervalNative = Void Function(Int32 interval);
typedef SetProcessingIntervalDart = void Function(int interval);

typedef GetProcessingIntervalNative = Int32 Function();
typedef GetProcessingIntervalDart = int Function();

// 添加性能统计相关的函数类型定义
typedef GetDetectionFpsNative = Int32 Function();
typedef GetDetectionFpsDart = int Function();

typedef GetLastDetectionTimeNative = Int32 Function();
typedef GetLastDetectionTimeDart = int Function();

typedef GetAvgDetectionTimeNative = Int32 Function();
typedef GetAvgDetectionTimeDart = int Function();

typedef SetShowFpsNative = Void Function(Bool show);
typedef SetShowFpsDart = void Function(bool show);

typedef GetShowFpsNative = Bool Function();
typedef GetShowFpsDart = bool Function();

// 添加控制人脸框显示的函数类型定义
typedef SetShowFaceBoxesNative = Void Function(Bool show);
typedef SetShowFaceBoxesDart = void Function(bool show);

typedef GetShowFaceBoxesNative = Bool Function();
typedef GetShowFaceBoxesDart = bool Function();

class FaceDetector {
  static DynamicLibrary? _dylib;
  static DetectFacesDart? _detectFaces;
  static ProcessImageDart? _processImage;
  static FreeImageDart? _freeImage;

  // 摄像头相关的函数
  static StartCameraDart? _startCamera;
  static StartCameraWithIDDart? _startCameraWithID;
  static StopCameraDart? _stopCamera;
  static GetFrameInfoDart? _getFrameInfo;
  static GetFrameDataDart? _getFrameData;
  static GetJpegDataDart? _getJpegData;
  static GetOriginalFrameSizeDart? _getOriginalFrameSize;
  static GetJpegQualityDart? _getJpegQuality;
  static SetJpegQualityDart? _setJpegQuality;
  
  // 新增拍照和录像函数
  static TakePhotoDart? _takePhoto;
  static StartRecordingDart? _startRecording;
  static StopRecordingDart? _stopRecording;
  static IsRecordingDart? _isRecording;

  // Haar检测器控制函数 - 保留API但不使用
  static SetUseHaarDetectorDart? _setUseHaarDetector;
  static GetUseHaarDetectorDart? _getUseHaarDetector;

  // 摄像头帧的宽高
  static int _frameWidth = 320;
  static int _frameHeight = 240;
  static int _originalWidth = 640;
  static int _originalHeight = 480;
  static final int _frameChannels = 3; // 使用BGR格式

  static bool _isInitialized = false;
  static final int _maxFaces = 10; // 最多检测10个人脸

  // 添加公共方法检查初始化状态
  static bool get isInitialized => _isInitialized;

  // 添加新的分离式人脸检测函数指针
  static DetectFacesOnlyDart? _detectFacesOnly;

  // 平滑因子函数
  static SetSmoothingFactorDart? _setSmoothingFactor;
  static GetSmoothingFactorDart? _getSmoothingFactor;
  
  // 默认平滑因子
  static double _smoothingFactor = 0.7;

  // 系统负载和处理间隔函数
  static GetSystemLoadDart? _getSystemLoad;
  static SetProcessingIntervalDart? _setProcessingInterval;
  static GetProcessingIntervalDart? _getProcessingInterval;
  
  // 默认处理间隔
  static int _processingInterval = 30;

  // 检测相关的函数
  static GetDetectionFpsDart? _getDetectionFps;
  static GetLastDetectionTimeDart? _getLastDetectionTime;
  static GetAvgDetectionTimeDart? _getAvgDetectionTime;
  static SetShowFpsDart? _setShowFps;
  static GetShowFpsDart? _getShowFps;
  
  // 人脸框显示控制
  static SetShowFaceBoxesDart? _setShowFaceBoxes;
  static GetShowFaceBoxesDart? _getShowFaceBoxes;
  
  // 新增：默认拍照和录像保存路径
  static String _defaultPhotoSavePath = "";
  static String _defaultVideoSavePath = "";
  
  // 获取和设置默认拍照保存路径
  static String get defaultPhotoSavePath => _defaultPhotoSavePath;
  static set defaultPhotoSavePath(String path) {
    _defaultPhotoSavePath = path;
  }
  
  // 获取和设置默认录像保存路径
  static String get defaultVideoSavePath => _defaultVideoSavePath;
  static set defaultVideoSavePath(String path) {
    _defaultVideoSavePath = path;
  }

  static Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // 根据平台加载不同的动态库
      if (Platform.isWindows) {
        // 对于Windows，尝试从多个可能的位置加载库
        final List<String> possiblePaths = [
          // 尝试直接从与可执行文件相同的目录加载
          path.join(Directory.current.path, 'windows', 'runner', 'Debug', 'libface_detector.dll'),
          // 原有的data子目录路径
          path.join(Directory.current.path, 'windows', 'runner', 'Debug', 'data', 'libface_detector.dll'),
          // 其他可能的路径
          path.join(File(Platform.resolvedExecutable).parent.path, 'libface_detector.dll'),
          path.join(File(Platform.resolvedExecutable).parent.path, 'data', 'libface_detector.dll'),
          // 绝对路径格式
          '${Directory.current.path}\\windows\\runner\\Debug\\libface_detector.dll',
          '${Directory.current.path}\\windows\\runner\\Debug\\data\\libface_detector.dll',
          // build目录下的路径
          path.join(Directory.current.path, 'build', 'windows', 'runner', 'Debug', 'libface_detector.dll'),
          path.join(Directory.current.path, 'build', 'windows', 'runner', 'Debug', 'data', 'libface_detector.dll'),
        ];

        // 尝试加载每个可能的路径
        bool loaded = false;
        Exception? lastError;

        for (final libraryPath in possiblePaths) {
          debugPrint('尝试加载库: $libraryPath');
          final file = File(libraryPath);
          if (file.existsSync()) {
            try {
              _dylib = DynamicLibrary.open(libraryPath);
              loaded = true;
              debugPrint('成功加载库: $libraryPath');
              break;
            } catch (e) {
              lastError = Exception('加载失败: $libraryPath - $e');
              debugPrint('加载失败: $libraryPath - $e');
            }
          } else {
            debugPrint('文件不存在: $libraryPath');
          }
        }

        if (!loaded) {
          throw lastError ?? Exception('无法找到libface_detector.dll，请确认文件位置');
        }
      } else if (Platform.isMacOS) {
        // 对于macOS，尝试从多个可能的位置加载库
        final List<String> possiblePaths = [
          path.join(Directory.current.path, 'macos', 'build',
              'libface_detector.dylib'),
          path.join(
              Directory.current.path,
              'build',
              'macos',
              'Build',
              'Products',
              'Debug',
              'face_recognition.app',
              'Contents',
              'Frameworks',
              'libface_detector.dylib'),
          path.join(File(Platform.resolvedExecutable).parent.parent.path,
              'Frameworks', 'libface_detector.dylib'),
          path.join(File(Platform.resolvedExecutable).parent.path, 'Frameworks',
              'libface_detector.dylib'),
          path.join((await getApplicationSupportDirectory()).path,
              'libface_detector.dylib'),
        ];

        // 尝试加载每个可能的路径
        bool loaded = false;
        Exception? lastError;

        for (final libraryPath in possiblePaths) {
          if (File(libraryPath).existsSync()) {
            try {
              _dylib = DynamicLibrary.open(libraryPath);
              loaded = true;
              break;
            } catch (e) {
              lastError = Exception('加载失败: $libraryPath - $e');
            }
          }
        }

        if (!loaded) {
          throw lastError ?? Exception('无法找到libface_detector.dylib');
        }
      } else {
        throw Exception('不支持当前平台');
      }

      // 查找函数
      _detectFaces = _dylib!
          .lookupFunction<DetectFacesNative, DetectFacesDart>('detect_faces');
      _processImage = _dylib!
          .lookupFunction<ProcessImageNative, ProcessImageDart>('process_image');
      _freeImage =
          _dylib!.lookupFunction<FreeImageNative, FreeImageDart>('free_image');

      // 尝试查找摄像头相关函数
      try {
        _startCamera = _dylib!
            .lookupFunction<StartCameraNative, StartCameraDart>('start_camera');
        _stopCamera = _dylib!
            .lookupFunction<StopCameraNative, StopCameraDart>('stop_camera');
            
        // 加载新函数指针
        try {
          _startCameraWithID = _dylib!
              .lookupFunction<StartCameraWithIDNative, StartCameraWithIDDart>('start_camera_with_id');
          _takePhoto = _dylib!
              .lookupFunction<TakePhotoNative, TakePhotoDart>('take_photo');
          _startRecording = _dylib!
              .lookupFunction<StartRecordingNative, StartRecordingDart>('start_recording');
          _stopRecording = _dylib!
              .lookupFunction<StopRecordingNative, StopRecordingDart>('stop_recording');
          _isRecording = _dylib!
              .lookupFunction<IsRecordingNative, IsRecordingDart>('is_recording');
        } catch (e) {
          debugPrint('加载扩展功能失败: $e');
        }
            
        if (Platform.isWindows) {
          // Windows版本使用get_frame_info和get_frame_data代替get_frame
          _getFrameInfo = _dylib!
              .lookupFunction<GetFrameInfoNative, GetFrameInfoDart>('get_frame_info');
          _getFrameData = _dylib!
              .lookupFunction<GetFrameDataNative, GetFrameDataDart>('get_frame_data');
              
          _getJpegData = _dylib!
              .lookupFunction<GetJpegDataNative, GetJpegDataDart>('get_jpeg_data');
        }

        _getOriginalFrameSize = _dylib!.lookupFunction<
            GetOriginalFrameSizeNative,
            GetOriginalFrameSizeDart>('get_original_frame_size');
        _getJpegQuality = _dylib!
            .lookupFunction<GetJpegQualityNative, GetJpegQualityDart>('get_jpeg_quality');
        _setJpegQuality = _dylib!
            .lookupFunction<SetJpegQualityNative, SetJpegQualityDart>('set_jpeg_quality');

        // 加载Haar检测器控制函数
        _setUseHaarDetector = _dylib!
            .lookupFunction<SetUseHaarDetectorNative, SetUseHaarDetectorDart>('set_use_haar_detector');
        _getUseHaarDetector = _dylib!
            .lookupFunction<GetUseHaarDetectorNative, GetUseHaarDetectorDart>('get_use_haar_detector');

        // 初始化新的分离式人脸检测函数
        _detectFacesOnly = _dylib!
            .lookupFunction<DetectFacesOnlyNative, DetectFacesOnlyDart>('detect_faces_only');
      } catch (e) {
        // 摄像头函数加载失败不影响其他功能
        debugPrint('部分功能加载失败: $e');
      }

      // 查找新增的平滑因子函数
      try {
        _setSmoothingFactor = _dylib!.lookupFunction<SetSmoothingFactorNative, 
            SetSmoothingFactorDart>('set_smoothing_factor');
        _getSmoothingFactor = _dylib!.lookupFunction<GetSmoothingFactorNative, 
            GetSmoothingFactorDart>('get_smoothing_factor');
            
        // 获取当前平滑因子
        if (_getSmoothingFactor != null) {
          _smoothingFactor = _getSmoothingFactor!();
        }
      } catch (e) {
        debugPrint('平滑因子函数加载失败: $e');
      }

      // 查找新增的系统负载和处理间隔函数
      try {
        _getSystemLoad = _dylib!.lookupFunction<GetSystemLoadNative, 
            GetSystemLoadDart>('get_system_load');
            
        _setProcessingInterval = _dylib!.lookupFunction<SetProcessingIntervalNative, 
            SetProcessingIntervalDart>('set_processing_interval');
            
        _getProcessingInterval = _dylib!.lookupFunction<GetProcessingIntervalNative, 
            GetProcessingIntervalDart>('get_processing_interval');
            
        // 获取当前处理间隔
        if (_getProcessingInterval != null) {
          _processingInterval = _getProcessingInterval!();
        }
        
        debugPrint('成功加载系统负载和处理间隔函数');
      } catch (e) {
        debugPrint('系统负载和处理间隔函数加载失败: $e');
        _getSystemLoad = null;
        _setProcessingInterval = null;
        _getProcessingInterval = null;
      }

      // 加载性能监控和控制函数
      try {
        _getDetectionFps = _dylib!
            .lookupFunction<GetDetectionFpsNative, GetDetectionFpsDart>('get_detection_fps');
        _getLastDetectionTime = _dylib!
            .lookupFunction<GetLastDetectionTimeNative, GetLastDetectionTimeDart>('get_last_detection_time');
        _getAvgDetectionTime = _dylib!
            .lookupFunction<GetAvgDetectionTimeNative, GetAvgDetectionTimeDart>('get_avg_detection_time');
        _setShowFps = _dylib!
            .lookupFunction<SetShowFpsNative, SetShowFpsDart>('set_show_fps');
        _getShowFps = _dylib!
            .lookupFunction<GetShowFpsNative, GetShowFpsDart>('get_show_fps');
      } catch (e) {
        print('加载性能监控函数失败: $e');
        // 这不是关键功能，可以继续
      }
      
      // 加载人脸框显示控制函数
      try {
        _setShowFaceBoxes = _dylib!
            .lookupFunction<SetShowFaceBoxesNative, SetShowFaceBoxesDart>('set_show_face_boxes');
        _getShowFaceBoxes = _dylib!
            .lookupFunction<GetShowFaceBoxesNative, GetShowFaceBoxesDart>('get_show_face_boxes');
      } catch (e) {
        print('加载人脸框显示控制函数失败: $e');
        // 这不是关键功能，可以继续
      }

      _isInitialized = true;
    } catch (e) {
      debugPrint('人脸检测器初始化失败: $e');
      return false;
    }
    return true;
  }

  // 启动摄像头
  static Future<bool> startCamera() async {
    if (_startCamera == null) {
      return false;
    }

    try {
      return _startCamera!();
    } catch (e) {
      debugPrint('启动摄像头出错: $e');
      return false;
    }
  }

  // 启动指定ID的摄像头
  static Future<bool> startCameraWithID(int cameraId) async {
    if (_startCameraWithID == null) {
      debugPrint('指定摄像头ID功能不可用，尝试使用默认摄像头');
      return startCamera();
    }

    try {
      return _startCameraWithID!(cameraId);
    } catch (e) {
      debugPrint('启动指定摄像头出错: $e');
      return false;
    }
  }

  // 停止摄像头
  static Future<void> stopCamera() async {
    if (_stopCamera == null) return;

    try {
      _stopCamera!();
    } catch (e) {
      debugPrint('停止摄像头出错: $e');
    }
  }

  /// 获取最后的错误信息
  static String getLastError() {
    if (_dylib == null) return '库未初始化';

    try {
      final getLastErrorBinding = _dylib!.lookupFunction<
          Pointer<Utf8> Function(),
          Pointer<Utf8> Function()>('get_last_error');
      final errorPtr = getLastErrorBinding();
      return errorPtr.toDartString();
    } catch (e) {
      return '获取错误信息失败: $e';
    }
  }

  /// 获取当前使用的摄像头后端
  static String getCurrentBackend() {
    if (_dylib == null) return '库未初始化';

    try {
      final getCurrentBackendBinding = _dylib!.lookupFunction<
          Pointer<Utf8> Function(),
          Pointer<Utf8> Function()>('get_current_backend');
      final backendPtr = getCurrentBackendBinding();
      return backendPtr.toDartString();
    } catch (e) {
      return '获取后端信息失败: $e';
    }
  }

  /// 检测可用的摄像头设备
  static List<int> detectAvailableCameras() {
    if (_dylib == null) return [];

    try {
      final detectCamerasBinding = _dylib!.lookupFunction<
          Int32 Function(Pointer<Int32>, Int32),
          int Function(Pointer<Int32>, int)>('detect_available_cameras');

      // 分配内存来存储摄像头ID
      final cameraIdsPtr = calloc<Int32>(10); // 最多检测10个摄像头
      final count = detectCamerasBinding(cameraIdsPtr, 10);

      final List<int> cameraIds = [];
      for (int i = 0; i < count; i++) {
        cameraIds.add(cameraIdsPtr[i]);
      }

      calloc.free(cameraIdsPtr);
      return cameraIds;
    } catch (e) {
      debugPrint('检测摄像头设备失败: $e');
      return [];
    }
  }

  // 获取当前帧 - 为Windows版本实现
  static Future<Uint8List?> getCurrentFrame() async {
    // 针对Windows平台的实现
    if (Platform.isWindows) {
      if (_getFrameInfo == null || _getFrameData == null) {
        debugPrint('获取帧函数不可用');
        return null;
      }

      final width = calloc<Int32>(1);
      final height = calloc<Int32>(1);
      final channels = calloc<Int32>(1);

      try {
        // 先获取帧信息
        final hasFrame = _getFrameInfo!(width, height, channels);
        
        if (!hasFrame) {
          return null;
        }
        
        // 更新帧尺寸
        _frameWidth = width.value;
        _frameHeight = height.value;
        
        // 同时更新原始帧尺寸
        if (_getOriginalFrameSize != null) {
          final origWidth = calloc<Int32>(1);
          final origHeight = calloc<Int32>(1);
          _getOriginalFrameSize!(origWidth, origHeight);
          _originalWidth = origWidth.value;
          _originalHeight = origHeight.value;
          calloc.free(origWidth);
          calloc.free(origHeight);
        }
        
        // 然后获取帧数据
        final framePtr = _getFrameData!(width, height, channels);
        if (framePtr == nullptr) {
          return null;
        }

        // 创建帧数据
        final frameSize = _frameWidth * _frameHeight * channels.value;
        final frame = Uint8List(frameSize);

        frame.setRange(0, frameSize, framePtr.asTypedList(frameSize));

        // 释放C++分配的内存
        if (_freeImage != null) {
          _freeImage!(framePtr);
        }

        return frame;
      } catch (e) {
        debugPrint('获取帧出错: $e');
        return null;
      } finally {
        calloc.free(width);
        calloc.free(height);
        calloc.free(channels);
      }
    }
    
    // 非Windows平台的实现将保持不变
    return null;
  }

  // 获取帧宽度 (检测尺寸)
  static int getFrameWidth() {
    return _frameWidth;
  }

  // 获取帧高度 (检测尺寸)
  static int getFrameHeight() {
    return _frameHeight;
  }

  // 获取原始帧宽度
  static int getOriginalWidth() {
    return _originalWidth;
  }

  // 获取原始帧高度
  static int getOriginalHeight() {
    return _originalHeight;
  }

  // 获取帧通道数
  static int getFrameChannels() {
    return _frameChannels;
  }

  // 获取当前JPEG质量
  static int getJpegQuality() {
    if (_getJpegQuality == null) {
      return 80; // 默认值
    }
    return _getJpegQuality!();
  }

  // 设置JPEG质量
  static void setJpegQuality(int quality) {
    if (_setJpegQuality == null) return;
    _setJpegQuality!(quality);
  }

  // 设置是否使用Haar级联分类器 - 保留API兼容性但始终使用YuNet
  static void setUseHaarDetector(bool useHaar) {
    if (_isInitialized && _setUseHaarDetector != null) {
      try {
        _setUseHaarDetector!(false); // 始终使用DNN (YuNet)检测器，忽略输入参数
      } catch (e) {
        debugPrint('设置检测器类型失败: $e');
      }
    }
  }

  // 获取是否使用Haar级联分类器 - 保留API兼容性但始终返回false
  static bool getUseHaarDetector() {
    if (_isInitialized && _getUseHaarDetector != null) {
      try {
        return false; // 始终返回false，表示使用YuNet检测器
      } catch (e) {
        debugPrint('获取检测器类型失败: $e');
      }
    }
    return false; // 默认返回false
  }

  static List<Map<String, int>> detectFaces(
      Uint8List imageData, int width, int height,
      {int channels = 3}) {
    if (_detectFaces == null) {
      throw Exception('FaceDetector未初始化');
    }

    // 分配内存
    final data = calloc<Uint8>(imageData.length);
    final faces = calloc<FaceRect>(_maxFaces);
    final numFaces = calloc<Int32>(1);

    // 复制图像数据 - 使用更高效的方法
    final Uint8List nativeInputView = data.asTypedList(imageData.length);
    nativeInputView.setRange(0, imageData.length, imageData);

    try {
      final result = _detectFaces!(
          data, width, height, channels, faces, _maxFaces, numFaces);

      if (!result) {
        return [];
      }

      // 收集结果
      final faceList = <Map<String, int>>[];
      for (var i = 0; i < numFaces.value; i++) {
        faceList.add({
          'x': faces[i].x,
          'y': faces[i].y,
          'width': faces[i].width,
          'height': faces[i].height,
        });
      }

      return faceList;
    } finally {
      // 释放内存
      calloc.free(data);
      calloc.free(faces);
      calloc.free(numFaces);
    }
  }

  static Uint8List? processImage(Uint8List imageData, int width, int height,
      {int channels = 4}) {
    if (_processImage == null || _freeImage == null) {
      throw Exception('FaceDetector未初始化');
    }

    // 分配内存并复制图像数据 - 使用更高效的方法
    final data = calloc<Uint8>(imageData.length);
    final Uint8List nativeInputView = data.asTypedList(imageData.length);
    nativeInputView.setRange(0, imageData.length, imageData);

    try {
      final resultPtr = _processImage!(data, width, height, channels);

      if (resultPtr == nullptr) {
        return null;
      }

      // 创建结果数据
      final resultSize = width * height * channels;
      final result = Uint8List(resultSize);
      result.setRange(0, resultSize, resultPtr.asTypedList(resultSize));

      // 释放C++分配的内存
      _freeImage!(resultPtr);

      return result;
    } finally {
      // 释放Dart分配的内存
      calloc.free(data);
    }
  }

  // 获取JPEG图像数据 - 为Windows版本实现
  static Future<Uint8List?> getJpegImageData() async {
    if (_getJpegData == null) return null;

    final outputPtr = calloc<Pointer<Uint8>>();
    final lengthPtr = calloc<Int32>();

    try {
      final success = _getJpegData!(outputPtr, lengthPtr);
      if (!success || outputPtr.value == nullptr) {
        return null;
      }
      
      final dataSize = lengthPtr.value;
      final result = Uint8List(dataSize);
      result.setRange(0, dataSize, outputPtr.value.asTypedList(dataSize));

      // 释放原始数据
      if (_freeImage != null) {
        _freeImage!(outputPtr.value);
      }

      return result;
    } catch (e) {
      debugPrint('获取JPEG数据失败: $e');
      return null;
    } finally {
      calloc.free(outputPtr);
      calloc.free(lengthPtr);
    }
  }

  // 添加新的只执行人脸检测的方法，不需要传入图像数据
  static List<Map<dynamic, dynamic>> detectFacesOnly() {
    if (_detectFacesOnly == null) {
      debugPrint('警告: detectFacesOnly函数不可用');
      return [];
    }

    try {
      final faces = calloc<FaceRect>(_maxFaces);
      final numFacesPtr = calloc<Int32>(1);

      final success = _detectFacesOnly!(faces.cast(), _maxFaces, numFacesPtr);

      if (success) {
        final numFaces = numFacesPtr.value;
        final List<Map<dynamic, dynamic>> facesList = [];

        for (int i = 0; i < numFaces; i++) {
          facesList.add({
            'x': faces[i].x,
            'y': faces[i].y,
            'width': faces[i].width,
            'height': faces[i].height,
          });
        }

        calloc.free(faces);
        calloc.free(numFacesPtr);
        return facesList;
      }

      calloc.free(faces);
      calloc.free(numFacesPtr);
      return [];
    } catch (e) {
      debugPrint('人脸检测错误: $e');
      return [];
    }
  }

  // 获取当前平滑因子
  static double getSmoothingFactor() {
    if (!_isInitialized || _getSmoothingFactor == null) {
      return _smoothingFactor;
    }
    try {
      return _getSmoothingFactor!();
    } catch (e) {
      debugPrint('获取平滑因子失败: $e');
      return _smoothingFactor;
    }
  }

  // 设置平滑因子 (0.0-1.0)
  static void setSmoothingFactor(double factor) {
    if (!_isInitialized || _setSmoothingFactor == null) {
      _smoothingFactor = factor;
      return;
    }
    try {
      _setSmoothingFactor!(factor);
      _smoothingFactor = factor;
    } catch (e) {
      debugPrint('设置平滑因子失败: $e');
    }
  }
  
  // 获取系统负载
  static int getSystemLoad() {
    try {
      if (_getSystemLoad != null) {
        return _getSystemLoad!();
      }
      return 0; // 如果函数指针未初始化，返回默认值0
    } catch (e) {
      debugPrint('获取系统负载失败: $e');
      return 0; // 发生错误时返回默认值0
    }
  }
  
  // 设置处理间隔（毫秒）
  static void setProcessingInterval(int intervalMs) {
    if (!_isInitialized || _setProcessingInterval == null) {
      _processingInterval = intervalMs;
      return;
    }
    try {
      _setProcessingInterval!(intervalMs);
      _processingInterval = intervalMs;
    } catch (e) {
      debugPrint('设置处理间隔失败: $e');
    }
  }
  
  // 获取当前处理间隔（毫秒）
  static int getProcessingInterval() {
    if (!_isInitialized || _getProcessingInterval == null) {
      return _processingInterval;
    }
    try {
      return _getProcessingInterval!();
    } catch (e) {
      debugPrint('获取处理间隔失败: $e');
      return _processingInterval;
    }
  }

  // 拍照并保存到指定路径（路径应该是目录）
  static Future<bool> takePhoto(String directoryPath) async {
    if (_takePhoto == null) {
      debugPrint('拍照功能不可用');
      return false;
    }

    try {
      // 使用提供的目录路径，如果为空则使用默认路径
      final String finalPath = directoryPath.isNotEmpty ? directoryPath : _defaultPhotoSavePath;
      
      // 如果最终路径仍为空，则返回错误
      if (finalPath.isEmpty) {
        debugPrint('错误：未指定拍照保存目录，且未设置默认目录');
        return false;
      }
      
      debugPrint('拍照保存目录: $finalPath');
      
      // 确保路径使用正斜杠，避免转义问题
      String normalizedPath = finalPath.replaceAll('\\', '/');
      debugPrint('规范化后目录: $normalizedPath');
      
      // 确保目录存在
      final directory = Directory(normalizedPath);
      if (!directory.existsSync()) {
        try {
          directory.createSync(recursive: true);
          debugPrint('已创建目录: $normalizedPath');
        } catch (e) {
          debugPrint('创建目录失败: $e');
          return false;
        }
      }
      
      // 将路径转换为Pointer<Utf8>
      final pathPointer = normalizedPath.toNativeUtf8();
      
      try {
        // 调用本地函数，由C++端负责生成文件名
        final result = _takePhoto!(pathPointer);
        return result;
      } finally {
        // 释放内存
        calloc.free(pathPointer);
      }
    } catch (e) {
      debugPrint('拍照出错: $e');
      return false;
    }
  }
  
  // 开始录像（路径应该是目录）
  static Future<bool> startRecording(String directoryPath) async {
    if (_startRecording == null) {
      debugPrint('录像功能不可用');
      return false;
    }

    try {
      // 使用提供的目录路径，如果为空则使用默认路径
      final String finalPath = directoryPath.isNotEmpty ? directoryPath : _defaultVideoSavePath;
      
      // 如果最终路径仍为空，则返回错误
      if (finalPath.isEmpty) {
        debugPrint('错误：未指定录像保存目录，且未设置默认目录');
        return false;
      }
      
      debugPrint('录像保存目录: $finalPath');
      
      // 确保路径使用正斜杠，避免转义问题
      String normalizedPath = finalPath.replaceAll('\\', '/');
      debugPrint('规范化后目录: $normalizedPath');
      
      // 确保目录存在
      final directory = Directory(normalizedPath);
      if (!directory.existsSync()) {
        try {
          directory.createSync(recursive: true);
          debugPrint('已创建目录: $normalizedPath');
        } catch (e) {
          debugPrint('创建目录失败: $e');
          return false;
        }
      }
      
      // 将路径转换为Pointer<Utf8>
      final pathPointer = normalizedPath.toNativeUtf8();
      
      try {
        // 调用本地函数，由C++端负责生成文件名
        final result = _startRecording!(pathPointer);
        return result;
      } finally {
        // 释放内存
        calloc.free(pathPointer);
      }
    } catch (e) {
      debugPrint('开始录像出错: $e');
      return false;
    }
  }
  
  // 停止录像
  static Future<bool> stopRecording() async {
    if (_stopRecording == null) {
      debugPrint('停止录像功能不可用');
      return false;
    }
    
    try {
      return _stopRecording!();
    } catch (e) {
      debugPrint('停止录像出错: $e');
      return false;
    }
  }
  
  // 检查是否正在录像
  static Future<bool> isRecording() async {
    if (_isRecording == null) {
      return false;
    }
    
    try {
      return _isRecording!();
    } catch (e) {
      debugPrint('检查录像状态出错: $e');
      return false;
    }
  }

  // 设置是否显示人脸框
  static bool setShowFaceBoxes(bool show) {
    if (_setShowFaceBoxes == null) {
      debugPrint('警告: setShowFaceBoxes函数不可用');
      return false;
    }
    
    _setShowFaceBoxes!(show);
    return true;
  }
  
  // 获取是否显示人脸框
  static bool getShowFaceBoxes() {
    if (_getShowFaceBoxes == null) {
      debugPrint('警告: getShowFaceBoxes函数不可用');
      return true; // 默认显示
    }
    
    return _getShowFaceBoxes!();
  }

  // 使用默认路径拍照
  static Future<bool> takePhotoWithDefaultPath() async {
    return takePhoto(_defaultPhotoSavePath);
  }
  
  // 使用默认路径录像
  static Future<bool> startRecordingWithDefaultPath() async {
    return startRecording(_defaultVideoSavePath);
  }
  
  // 获取当前照片保存目录
  static String getPhotoSaveDir() {
    return _defaultPhotoSavePath;
  }
  
  // 获取当前视频保存目录
  static String getVideoSaveDir() {
    return _defaultVideoSavePath;
  }
  
  // 选择照片保存目录 (需要结合平台特定的目录选择器使用)
  static Future<bool> selectPhotoSaveDir(String dirPath) async {
    if (dirPath.isNotEmpty) {
      _defaultPhotoSavePath = dirPath;
      debugPrint('设置照片保存目录: $_defaultPhotoSavePath');
      return true;
    }
    return false;
  }
  
  // 选择视频保存目录 (需要结合平台特定的目录选择器使用)
  static Future<bool> selectVideoSaveDir(String dirPath) async {
    if (dirPath.isNotEmpty) {
      _defaultVideoSavePath = dirPath;
      debugPrint('设置视频保存目录: $_defaultVideoSavePath');
      return true;
    }
    return false;
  }
}