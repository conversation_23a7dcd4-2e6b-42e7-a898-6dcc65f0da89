设备通信协议文档
1. 通信格式
1.1 PC -> Device (发送)
格式: 帧头 + 地址 + 长度 + 命令 + 命令参数 + 数据 + 校验 + 帧尾

1.2 Device -> PC (回复)
格式: 帧头 + 地址 + 长度 + 状态 + 命令 + 命令参数 + 数据 + 校验 + 帧尾

1.3 长度计算
长度 = 状态byte + 命令byte + 命令参数byte + 数据长度bytes

2. 命令列表
序列号	状态	作用	发送命令	回复命令
1	发送	查询状态	A0 A3 00 02 30 30 A1 0A	
回复	返回状态		A0 A3 00 10 59 30 30 30 30 30 30 30 30 30 30 30 30 30 30 DA 0A
2	发送	打开继电器1	a0 A3 00 02 31 31 A1 0a	
回复	回应结果		A0 A3 03 00 59 31 31 F9 0A
3	发送	打开继电器2	a0 A3 00 02 31 32 A2 0a	
回复	回应结果		A0 A3 03 00 59 31 32 FA 0A
4	发送	打开继电器3	a0 A3 00 02 31 33 A3 0a	
回复	回应结果		A0 A3 03 00 59 31 33 FB 0A
5	发送	打开继电器4	a0 A3 00 02 31 34 A4 0a	
回复	回应结果		A0 A3 03 00 59 31 34 FC 0A
6	发送	打开继电器5	a0 A3 00 02 31 35 A5 0a	
回复	回应结果		A0 A3 03 00 59 31 35 FD 0A
7	发送	打开R_LED指示灯	a0 A3 00 02 31 36 A6 0a	
回复	回应结果		A0 A3 03 00 59 31 36 FE 0A
8	发送	打开G_LED指示灯	a0 A3 00 02 31 37 A7 0a	
回复	回应结果		A0 A3 03 00 59 31 37 FF 0A
9	发送	打开B_LED指示灯	a0 A3 00 02 31 38 A8 0a	
回复	回应结果		A0 A3 03 00 59 31 38 F0 0A
10	发送	打开RF_LED指示灯	a0 A3 00 02 31 39 A9 0a	
回复	回应结果		A0 A3 03 00 59 31 39 F1 0A
11	发送	关闭继电器1	A0 A3 00 02 32 31 A2 0a	
回复	回应结果		A0 A3 03 00 59 32 31 FA 0A
12	发送	关闭继电器2	A0 A3 00 02 32 32 A1 0a	
回复	回应结果		A0 A3 03 00 59 32 32 F9 0A
13	发送	关闭继电器3	A0 A3 00 02 32 33 A0 0a	
回复	回应结果		A0 A3 03 00 59 32 33 F8 0A
14	发送	关闭继电器4	A0 A3 00 02 32 34 A7 0a	
回复	回应结果		A0 A3 03 00 59 32 34 FF 0A
15	发送	关闭继电器5	A0 A3 00 02 32 35 A6 0a	
回复	回应结果		A0 A3 03 00 59 32 35 FE 0A
16	发送	关闭R_LED指示灯	A0 A3 00 02 32 36 A5 0a	
回复	回应结果		A0 A3 03 00 59 32 36 FD 0A
17	发送	关闭G_LED指示灯	A0 A3 00 02 32 37 A4 0a	
回复	回应结果		A0 A3 03 00 59 32 37 FC 0A
18	发送	关闭B_LED指示灯	A0 A3 00 02 32 38 AB 0a	
回复	回应结果		A0 A3 03 00 59 32 38 F3 0A
19	发送	关闭RF_LED指示灯	A0 A3 00 02 32 39 AA 0a	
回复	回应结果		A0 A3 03 00 59 32 39 F2 0A
20	发送	设置定时输出时间	a0 a3 00 0c a5 30 00 64 00 00 00 64 00 64 00 00 5E 0A	
回复	回应结果		A0 A3 03 00 59 A6 30 6F 0A
3. 参数解析
命令	作用	命令参数	作用	数据	数据说明
0x30	查询IO输入输出状态	0x30	/	NULL	/
0x31	使能输出端	0x31	继电器1	NULL	/
0x32	继电器2	NULL	/
0x33	继电器3	NULL	/
0x34	继电器4	NULL	/
0x35	继电器5	NULL	/
0x36	RGB_R_LED	NULL	/
0x37	RGB_G_LED	NULL	/
0x38	RGB_B_LED	NULL	/
0x39	RF_LED	NULL	/
0x32	失能输出端	0x31	继电器1	NULL	/
0x32	继电器2	NULL	/
0x33	继电器3	NULL	/
0x34	继电器4	NULL	/
0x35	继电器5	NULL	/
0x36	RGB_R_LED	NULL	/
0x37	RGB_G_LED	NULL	/
0x38	RGB_B_LED	NULL	/
0x39	RF_LED	NULL	/
0x33	输入端状态反馈	0x30	/	info	info[0:3]: 第一组1-2-3-4路输入<br>info[4:7]: 第二组1-2-3-4路输入<br>info[8]: 雷达输入端
0xa5	设置继电器工作时间	0x30	/	info	继电器工作时间倒计时：单位10ms<br>info[0:1]: 继电器1工作时间倒计时<br>info[2:3]: 继电器2工作时间倒计时
