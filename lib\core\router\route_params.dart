import 'package:go_router/go_router.dart';

class RouteParams {
  /// 路由参数（如 /user/:id 中的 id）
  final Map<String, String> pathParams;

  /// 查询参数（如 ?type=book）
  final Map<String, String> queryParams;

  /// 额外数据（通过 extra 传递的数据）
  final dynamic extra;

  const RouteParams({
    this.pathParams = const {},
    this.queryParams = const {},
    this.extra,
  });

  /// 从 GoRouterState 创建 RouteParams
  factory RouteParams.fromState(GoRouterState state) {
    return RouteParams(
      pathParams: state.pathParameters,
      queryParams: state.uri.queryParameters,
      extra: state.extra,
    );
  }

  /// 获取路径参数
  String? getPath(String key) => pathParams[key];

  /// 获取查询参数
  String? getQuery(String key) => queryParams[key];

  /// 获取类型安全的额外数据
  T? getExtra<T>() => extra as T?;
}