import 'package:flutter/material.dart';

import '../../shared/utils/asset_util.dart';
import '../utils/window_util.dart';

class AuthItem extends StatelessWidget {
  final String title;
  final String iconPath;
  final VoidCallback? onTap;

  const AuthItem({
    Key? key,
    required this.title,
    required this.iconPath,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 180.p,
        margin: EdgeInsets.symmetric(vertical: 25.p),
        padding: EdgeInsets.only(left: 32.p, right: 43.p),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment(-0.8, -0.7), // 136度角
            end: Alignment(0.8, 0.7),
            colors: [
              Color(0xB3FFFFFF), // rgba(255,255,255,0.7)
              Color(0x99DAEAFF), // rgba(218,234,255,0.6)
            ],
          ),
          borderRadius: BorderRadius.circular(48.p),
          border: Border.all(width: 2.p, color: const Color(0xFFFFFFFF)),
        ),
        child: Row(
          children: [
            Image.asset(
              iconPath,
              width: 148.p,
              height: 148.p,
              fit: BoxFit.contain,
            ),
            SizedBox(width: 40.p),
            Expanded(
              child: Text(
                title,
                style:  TextStyle(
                  fontSize: 54.p,
                  color: const Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  height: 54/60
                ),
              ),
            ),
            Image.asset(
              AssetUtil.fullPath('arrow_right.png'),
              width: 21.p,
              height: 42.p,
              fit: BoxFit.contain,
            ),
          ],
        ),
      ),
    );
  }
}
