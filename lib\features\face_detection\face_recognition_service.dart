import 'dart:async';
import 'dart:typed_data';
import 'dart:ffi';
import 'package:flutter/material.dart';
import 'face_capture_polling.dart';
import 'baidu_face_recognition.dart';
import 'face_detector.dart';
import 'sdk_config_manager.dart';

/// 人脸识别服务 - 整合现有人脸检测系统与百度1:N人脸检索
class FaceRecognitionService {
  // 单例模式
  static final FaceRecognitionService _instance = FaceRecognitionService._internal();
  factory FaceRecognitionService() => _instance;
  FaceRecognitionService._internal();
  
  // 是否已初始化
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  
  // 是否正在进行人脸识别
  bool _isProcessingRecognition = false;
  
  // 人脸捕获轮询器
  late FaceCapturePolling _faceCapture;
  
  // 事件流控制器
  final _recognitionController = StreamController<RecognitionEvent>.broadcast();
  Stream<RecognitionEvent> get recognitionStream => _recognitionController.stream;
  
  /// 初始化服务
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      // 从配置管理器获取SDK路径
      final configManager = SdkConfigManager.instance;
      final sdkPath = await configManager.getSdkPath();
      
      // 验证SDK路径
      final validation = await configManager.validateSdkPath(sdkPath);
      if (!validation.isValid) {
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.error,
            message: 'SDK路径无效: ${validation.error}',
          )
        );
        print('SDK路径验证失败: ${validation.error}');
        return false;
      }
      
      // 初始化百度人脸识别SDK
      final baiduInitialized = await BaiduFaceRecognition.initialize(modelPath: sdkPath);
      if (!baiduInitialized) {
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.error,
            message: '百度人脸识别SDK初始化失败',
          )
        );
        return false;
      }
      
      // 初始化人脸捕获轮询器
      _faceCapture = FaceCapturePolling();
      _faceCapture.onFaceCaptured = _onFaceCaptured;
    _faceCapture.onNoFaceDetected = _onNoFaceDetected;
      _faceCapture.onError = (error) {
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.error,
            message: '人脸捕获错误: $error',
          )
        );
      };
      
      _isInitialized = true;
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.info,
          message: '人脸识别服务初始化成功',
        )
      );
      
      return true;
    } catch (e) {
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别服务初始化错误: $e',
        )
      );
      return false;
    }
  }
  
  /// 处理没有检测到人脸的情况
  void _onNoFaceDetected() {
    // 发送清除事件
    _recognitionController.add(
      RecognitionEvent(
        type: RecognitionEventType.noFaceDetected,
        message: '未检测到人脸，清除显示',
      )
    );
    print('🧹 人脸识别服务：未检测到人脸，已清除');
  }
  
  /// 处理捕获到的人脸
  void _onFaceCaptured(Uint8List imageData, double confidence) async {
    if (_isProcessingRecognition) {
      print('正在处理人脸识别，跳过当前帧');
      return;
    }
    
    _isProcessingRecognition = true;
    
    try {
      // 通知捕获到人脸
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.faceCaptured,
          message: '捕获到人脸，置信度: $confidence',
          faceImage: imageData,
          confidence: confidence,
        )
      );
      
      // 执行1:N人脸识别
      print('执行1:N人脸识别，图像大小: ${imageData.length} 字节');
      
      // 使用新的方法进行人脸识别
      final results = await recognizeWithCapturedFace(imageData);
      
      if (results.isEmpty) {
        // 未找到匹配的人脸
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.noMatch,
            message: '未找到匹配的人脸',
            faceImage: imageData,
          )
        );
      } else {
        // 找到匹配的人脸
        final bestMatch = results.first;
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.matchFound,
            message: '找到匹配的人脸: ${bestMatch.userId}',
            faceImage: imageData,
            recognitionResult: bestMatch,
          )
        );
      }
    } catch (e) {
      final errorMessage = e.toString().replaceFirst('Exception: ', '');
      print('❌ 人脸识别服务错误: $errorMessage');
      
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: errorMessage,
        )
      );
    } finally {
      _isProcessingRecognition = false;
    }
  }
  
  /// 使用捕获的人脸图像进行识别
  /// 
  /// 这个方法处理FaceCapturePolling获取的Uint8List图像数据
  /// 并将其传递给C++层进行处理和识别
  Future<List<FaceRecognitionResult>> recognizeWithCapturedFace(Uint8List faceImageData) async {
    if (!_isInitialized) {
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别服务尚未初始化',
        )
      );
      return [];
    }
    
    try {
      // 直接调用新实现的方法，将图像数据传递给C++处理
      // 此方法会通过C++层转换为OpenCV Mat，然后调用百度SDK进行识别
      return await BaiduFaceRecognition.identifyWithImageData(
        faceImageData,
        isJpeg: true  // 从FaceCapturePolling获取的是JPEG格式的数据
      );
    } catch (e) {
      final errorMessage = e.toString().replaceFirst('Exception: ', '');
      print('❌ 人脸识别捕获处理错误: $errorMessage');
      
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: errorMessage,
        )
      );
      return [];
    }
  }
  
  /// 使用OpenCV Mat直接进行人脸识别
  /// 该方法直接对接identify_with_all接口
  /// [matPointer] 指向OpenCV Mat对象的指针
  Future<List<FaceRecognitionResult>> recognizeWithMat(Pointer<Void> matPointer, [int type = 0]) async {
    if (!_isInitialized) {
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别服务尚未初始化',
        )
      );
      return [];
    }
    
    if (_isProcessingRecognition) {
      print('正在处理人脸识别，跳过当前请求');
      return [];
    }
    
    _isProcessingRecognition = true;
    
    try {
      print('使用OpenCV Mat执行1:N人脸检索');
      
      // 暂时返回空结果，因为这里需要图像数据而不是Mat指针
      final List<FaceRecognitionResult> results = [];
      
      if (results.isNotEmpty) {
        // 找到匹配的人脸
        final bestMatch = results.first;
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.matchFound,
            message: '找到匹配的人脸: ${bestMatch.userId}',
            recognitionResult: bestMatch,
          )
        );
      } else {
        // 未找到匹配的人脸
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.noMatch,
            message: '未找到匹配的人脸',
          )
        );
      }
      
      return results;
    } catch (e) {
      final errorMessage = e.toString().replaceFirst('Exception: ', '');
      print('❌ Mat人脸识别错误: $errorMessage');
      
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: errorMessage,
        )
      );
      return [];
    } finally {
      _isProcessingRecognition = false;
    }
  }
  
  /// 开始人脸识别
  void startRecognition({Duration interval = const Duration(milliseconds: 1000)}) {
    if (!_isInitialized) {
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: '人脸识别服务尚未初始化',
        )
      );
      return;
    }
    
    _faceCapture.startPolling(interval: interval);
    _recognitionController.add(
      RecognitionEvent(
        type: RecognitionEventType.info,
        message: '启动人脸识别，间隔: ${interval.inMilliseconds}ms',
      )
    );
  }
  
  /// 停止人脸识别
  void stopRecognition() {
    if (!_isInitialized) return;
    
    _faceCapture.stopPolling();
    _recognitionController.add(
      RecognitionEvent(
        type: RecognitionEventType.info,
        message: '停止人脸识别',
      )
    );
  }
  
  /// 注册人脸到数据库
  Future<bool> registerFace(Uint8List faceImage, String userId, String groupId, [String? userInfo]) async {
    if (!_isInitialized) return false;
    
    try {
      final success = await BaiduFaceRecognition.addUser(faceImage, userId, groupId, userInfo);
      
      if (success) {
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.registered,
            message: '成功注册人脸: $userId',
            faceImage: faceImage,
          )
        );
      } else {
        _recognitionController.add(
          RecognitionEvent(
            type: RecognitionEventType.error,
            message: '注册人脸失败: $userId',
          )
        );
      }
      
      return success;
    } catch (e) {
      final errorMessage = e.toString().replaceFirst('Exception: ', '');
      print('❌ 注册人脸错误: $errorMessage');
      
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: errorMessage,
        )
      );
      return false;
    }
  }
  
  /// 手动提交一个人脸进行识别
  Future<List<FaceRecognitionResult>> recognizeFace(Uint8List faceImage) async {
    if (!_isInitialized) return [];
    
    try {
      // 使用新的方法进行识别
      return await BaiduFaceRecognition.identifyWithImageData(
        faceImage,
        isJpeg: true
      );
    } catch (e) {
      final errorMessage = e.toString().replaceFirst('Exception: ', '');
      print('❌ 手动识别人脸错误: $errorMessage');
      
      _recognitionController.add(
        RecognitionEvent(
          type: RecognitionEventType.error,
          message: errorMessage,
        )
      );
      return [];
    }
  }
  
  /// 获取人脸库中的人脸数量
  int getFaceCount([String? groupId]) {
    return BaiduFaceRecognition.getFaceCount();
  }
  
  /// 释放资源
  void dispose() {
    _faceCapture.dispose();
    _recognitionController.close();
    _isInitialized = false;
  }
}

/// 识别事件类型
enum RecognitionEventType {
  info,         // 普通信息
  error,        // 错误
  faceCaptured, // 捕获到人脸
  noFaceDetected, // 没有检测到人脸
  matchFound,   // 找到匹配
  noMatch,      // 无匹配
  registered,   // 注册成功
}

/// 识别事件
class RecognitionEvent {
  final RecognitionEventType type;
  final String message;
  final Uint8List? faceImage;
  final double? confidence;
  final FaceRecognitionResult? recognitionResult;
  
  RecognitionEvent({
    required this.type,
    required this.message,
    this.faceImage,
    this.confidence,
    this.recognitionResult,
  });
} 